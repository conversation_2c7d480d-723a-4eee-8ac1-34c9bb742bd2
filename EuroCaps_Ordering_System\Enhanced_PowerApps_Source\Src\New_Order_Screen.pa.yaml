# ************************************************************************************************
# Enhanced New Order Screen for EuroCaps Ordering System
# Based on detailed mockup specifications
# ************************************************************************************************
Screens:
  New_Order_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(74, 111, 165, 1)
      Fill: =RGBA(245, 245, 245, 1)
    Children:
      # Header Bar (reused from Dashboard)
      - HeaderBar:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(74, 111, 165, 1)
            Height: =60
            Width: =1366
            X: =0
            Y: =0
            BorderThickness: =0

      # Navigation Menu (reused from Dashboard)
      - NavigationMenu:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(58, 90, 128, 1)
            Height: =708
            Width: =200
            X: =0
            Y: =60
            BorderThickness: =0

      # Page Title
      - PageTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="New Order"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =18
            Color: =RGBA(51, 51, 51, 1)
            Height: =40
            Width: =200
            X: =220
            Y: =80

      # Order Information Section
      - OrderInfoTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="ORDER INFORMATION"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =300
            X: =220
            Y: =130

      - OrderInfoPanel:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =200
            Width: =600
            X: =220
            Y: =160
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Customer Selection
      - CustomerLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Customer:"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =100
            X: =240
            Y: =180

      - CustomerDropdown:
          Control: Classic/Dropdown@2.1.0
          Properties:
            Items: =Customers
            DefaultSelectedItems: =[]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =300
            X: =350
            Y: =175

      # Order Date
      - OrderDateLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Order Date:"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =100
            X: =240
            Y: =220

      - OrderDatePicker:
          Control: Classic/DatePicker@2.1.0
          Properties:
            DefaultDate: =Today()
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =150
            X: =350
            Y: =215

      # Delivery Date
      - DeliveryDateLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Requested Delivery:"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =150
            X: =240
            Y: =260

      - DeliveryDatePicker:
          Control: Classic/DatePicker@2.1.0
          Properties:
            DefaultDate: =DateAdd(Today(), 7, Days)
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =150
            X: =400
            Y: =255

      # Notes
      - NotesLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Notes:"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =100
            X: =240
            Y: =300

      - NotesInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            HintText: ="Enter any special instructions..."
            Font: =Font.Arial
            Size: =12
            Mode: =TextMode.MultiLine
            Height: =40
            Width: =400
            X: =240
            Y: =320

      # Order Items Section
      - OrderItemsTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="ORDER ITEMS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =300
            X: =220
            Y: =390

      - OrderItemsPanel:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =120
            Width: =600
            X: =220
            Y: =420
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - NoItemsMessage:
          Control: Label@2.5.1
          Properties:
            Text: ="No items added yet."
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(128, 128, 128, 1)
            Align: =Align.Center
            Height: =30
            Width: =400
            X: =320
            Y: =460

      - AddProductsButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="+ ADD PRODUCTS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(76, 175, 80, 1)
            HoverFill: =RGBA(60, 159, 64, 1)
            Height: =40
            Width: =150
            X: =420
            Y: =490
            OnSelect: =Navigate(Order_Items_Screen, ScreenTransition.Fade)

      # Order Summary Section
      - OrderSummaryTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="ORDER SUMMARY"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =300
            X: =220
            Y: =560

      - OrderSummaryPanel:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =80
            Width: =600
            X: =220
            Y: =590
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - TotalItemsLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Total Items: 0"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =150
            X: =240
            Y: =610

      - TotalQuantityLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Total Quantity: 0"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =150
            X: =500
            Y: =610

      # Action Buttons
      - SaveDraftButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="SAVE AS DRAFT"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(74, 111, 165, 1)
            HoverFill: =RGBA(58, 95, 149, 1)
            Height: =45
            Width: =150
            X: =220
            Y: =700

      - SubmitOrderButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="SUBMIT ORDER"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(76, 175, 80, 1)
            HoverFill: =RGBA(60, 159, 64, 1)
            Height: =45
            Width: =150
            X: =390
            Y: =700
            OnSelect: =Navigate(Order_Confirmation_Screen, ScreenTransition.Fade)

      - CancelButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="CANCEL"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(158, 158, 158, 1)
            HoverFill: =RGBA(142, 142, 142, 1)
            Height: =45
            Width: =100
            X: =560
            Y: =700
            OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)
