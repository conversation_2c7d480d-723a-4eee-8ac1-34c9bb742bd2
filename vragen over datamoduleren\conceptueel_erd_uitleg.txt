# Wat is een conceptueel ERD?

Een conceptueel ERD (Entity Relationship Diagram) is de meest abstracte vorm van een Entity Relationship Diagram. Het is een visuele weergave van de belangrijkste entiteiten (objecten of concepten) in een systeem en de relaties tussen deze entiteiten.

## Kenmerken van een conceptueel ERD:

- Bevat alleen entiteiten (weergegeven als rechthoeken met de naam van de entiteit)
- Bevat relaties tussen entiteiten (weergegeven als lijnen met een beschrijving)
- Toont de cardinaliteit van relaties (één-op-één, één-op-veel, veel-op-veel)
- Bevat GEEN attributen (details van entiteiten)
- Gebruikt eenvoudige notaties die begrijpelijk zijn voor niet-technische personen
- Dient als basis voor het latere logische en fysieke ERD

## Doel van een conceptueel ERD:

- De belangrijkste entiteiten in een domein identificeren
- De relaties tussen deze entiteiten vastleggen
- Een hoog-niveau overzicht geven van de structuur van het systeem
- Communiceren met stakeholders zonder technische details

## Voorbeeld:

In een eenvoudig systeem voor een webwinkel:
- Entiteiten: KLANT, BESTELLING, PRODUCT, CATEGORIE
- Relaties: 
  * KLANT plaatst BESTELLING (één-op-veel)
  * BESTELLING bevat PRODUCT (veel-op-veel)
  * PRODUCT behoort tot CATEGORIE (veel-op-één)

Dit is de eerste stap in het databaseontwerpproces, gevolgd door het logische ERD (met attributen) en uiteindelijk het fysieke ERD (met technische details zoals datatypes en sleutels).
