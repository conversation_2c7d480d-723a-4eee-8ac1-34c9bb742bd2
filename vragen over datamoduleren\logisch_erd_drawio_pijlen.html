<!DOCTYPE html>
<html>
<head>
    <title>Logisch ERD Voorbeeld - Draw.io <PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: white;
        }
        .container {
            width: 900px;
            height: 700px;
            border: 1px solid #ccc;
            position: relative;
            background-color: white;
        }
        .title {
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            margin-top: 20px;
            margin-bottom: 40px;
        }
        .entity {
            width: 200px;
            border: 2px solid black;
            position: absolute;
            background-color: white;
            z-index: 2;
        }
        .entity-header {
            background-color: #f0f0f0;
            padding: 5px;
            text-align: center;
            font-weight: bold;
            border-bottom: 1px solid black;
        }
        .entity-attributes {
            padding: 5px;
        }
        .attribute {
            margin: 3px 0;
            font-size: 12px;
        }
        .pk {
            text-decoration: underline;
            font-weight: bold;
        }
        .relationship {
            position: absolute;
            font-size: 14px;
            z-index: 1;
            background-color: white;
            padding: 0 5px;
        }
        .note {
            position: absolute;
            bottom: 20px;
            left: 20px;
            font-size: 14px;
            width: 860px;
        }
        .legend {
            position: absolute;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #f9f9f9;
            width: 250px;
            z-index: 2;
        }
        .legend-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .legend-item {
            margin: 5px 0;
            font-size: 12px;
        }
        /* SVG styles for better lines */
        svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">Logisch ERD Voorbeeld - Webwinkel (Draw.io Entity Relation Pijlen)</div>
        
        <!-- SVG for lines and relationship symbols -->
        <svg>
            <!-- KLANT to BESTELLING -->
            <path d="M 300,130 H 500" stroke="white" stroke-width="2" fill="none"/>
            <path d="M 300,130 H 500" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- BESTELLING to BESTELLING_PRODUCT -->
            <path d="M 550,200 H 450 V 250" stroke="white" stroke-width="2" fill="none"/>
            <path d="M 550,200 H 450 V 250" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- PRODUCT to BESTELLING_PRODUCT -->
            <path d="M 550,380 H 450 V 300" stroke="white" stroke-width="2" fill="none"/>
            <path d="M 550,380 H 450 V 300" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- CATEGORIE to PRODUCT -->
            <path d="M 300,380 H 500" stroke="white" stroke-width="2" fill="none"/>
            <path d="M 300,380 H 500" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- Draw.io Entity Relation pijlen -->
            <!-- KLANT to BESTELLING (1 to many) -->
            <!-- Exact één (1) -->
            <circle cx="310" cy="130" r="6" stroke="black" stroke-width="1.5" fill="white"/>
            <!-- Eén of meer (1..n) -->
            <path d="M 485,125 L 500,130 L 485,135" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 485,120 L 500,130 L 485,140" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- BESTELLING to BESTELLING_PRODUCT (1 to many) -->
            <!-- Exact één (1) -->
            <circle cx="550" cy="190" r="6" stroke="black" stroke-width="1.5" fill="white"/>
            <!-- Eén of meer (1..n) -->
            <path d="M 455,240 L 450,250 L 445,240" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 460,235 L 450,250 L 440,235" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- PRODUCT to BESTELLING_PRODUCT (1 to many) -->
            <!-- Exact één (1) -->
            <circle cx="550" cy="370" r="6" stroke="black" stroke-width="1.5" fill="white"/>
            <!-- Eén of meer (1..n) -->
            <path d="M 455,310 L 450,300 L 445,310" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 460,315 L 450,300 L 440,315" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- CATEGORIE to PRODUCT (1 to many) -->
            <!-- Exact één (1) -->
            <circle cx="310" cy="380" r="6" stroke="black" stroke-width="1.5" fill="white"/>
            <!-- Eén of meer (1..n) -->
            <path d="M 485,375 L 500,380 L 485,385" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 485,370 L 500,380 L 485,390" stroke="black" stroke-width="1.5" fill="none"/>
        </svg>
        
        <!-- Entities with attributes -->
        <div class="entity" style="top: 100px; left: 100px;">
            <div class="entity-header">KLANT</div>
            <div class="entity-attributes">
                <div class="attribute pk">klant_id</div>
                <div class="attribute">voornaam</div>
                <div class="attribute">achternaam</div>
                <div class="attribute">email</div>
                <div class="attribute">telefoonnummer</div>
                <div class="attribute">adres</div>
                <div class="attribute">postcode</div>
                <div class="attribute">woonplaats</div>
            </div>
        </div>
        
        <div class="entity" style="top: 100px; left: 500px;">
            <div class="entity-header">BESTELLING</div>
            <div class="entity-attributes">
                <div class="attribute pk">bestelling_id</div>
                <div class="attribute">klant_id</div>
                <div class="attribute">datum</div>
                <div class="attribute">status</div>
                <div class="attribute">totaalbedrag</div>
                <div class="attribute">verzendkosten</div>
                <div class="attribute">betaalmethode</div>
            </div>
        </div>
        
        <div class="entity" style="top: 350px; left: 100px;">
            <div class="entity-header">CATEGORIE</div>
            <div class="entity-attributes">
                <div class="attribute pk">categorie_id</div>
                <div class="attribute">naam</div>
                <div class="attribute">beschrijving</div>
                <div class="attribute">hoofdcategorie_id</div>
            </div>
        </div>
        
        <div class="entity" style="top: 350px; left: 500px;">
            <div class="entity-header">PRODUCT</div>
            <div class="entity-attributes">
                <div class="attribute pk">product_id</div>
                <div class="attribute">categorie_id</div>
                <div class="attribute">naam</div>
                <div class="attribute">beschrijving</div>
                <div class="attribute">prijs</div>
                <div class="attribute">voorraad</div>
                <div class="attribute">gewicht</div>
                <div class="attribute">afmetingen</div>
            </div>
        </div>
        
        <div class="entity" style="top: 250px; left: 300px;">
            <div class="entity-header">BESTELLING_PRODUCT</div>
            <div class="entity-attributes">
                <div class="attribute pk">bestelling_id</div>
                <div class="attribute pk">product_id</div>
                <div class="attribute">aantal</div>
                <div class="attribute">prijs_per_stuk</div>
                <div class="attribute">subtotaal</div>
            </div>
        </div>
        
        <!-- Relationships -->
        <div class="relationship" style="top: 110px; left: 370px;">plaatst</div>
        <div class="relationship" style="top: 230px; left: 500px;">bevat</div>
        <div class="relationship" style="top: 330px; left: 450px;">bevat</div>
        <div class="relationship" style="top: 360px; left: 370px;">behoort tot</div>
        
        <!-- Legend for Draw.io Entity Relation Notation -->
        <div class="legend" style="top: 500px; left: 350px;">
            <div class="legend-title">Draw.io Entity Relation Pijlen Legenda</div>
            <div class="legend-item">O — Exact één (1)</div>
            <div class="legend-item">O — Nul of één (0..1)</div>
            <div class="legend-item">>| — Eén of meer (1..n)</div>
            <div class="legend-item">O>| — Nul of meer (0..n)</div>
            <div class="legend-item">Deze pijlen zijn te vinden in de Entity Relation bibliotheek van draw.io</div>
        </div>
        
        <!-- Note -->
        <div class="note">
            <strong>Opmerking:</strong> Een logisch ERD bouwt voort op het conceptuele ERD door attributen toe te voegen aan elke entiteit. 
            Ook is er een koppeltabel (BESTELLING_PRODUCT) toegevoegd om de veel-op-veel relatie tussen BESTELLING en PRODUCT te implementeren.
            Primaire sleutels zijn onderstreept. In een logisch ERD worden nog geen datatypes of implementatiedetails gespecificeerd.
            De relaties worden weergegeven met de pijlen uit de Entity Relation bibliotheek van draw.io.
        </div>
    </div>
    
    <div style="margin-top: 20px;">
        <h2>Verschillen tussen Conceptueel en Logisch ERD</h2>
        <ul>
            <li><strong>Attributen:</strong> Het logische ERD bevat attributen (eigenschappen) voor elke entiteit, terwijl het conceptuele ERD alleen entiteiten toont.</li>
            <li><strong>Primaire sleutels:</strong> Het logische ERD identificeert primaire sleutels (onderstreept), terwijl het conceptuele ERD geen sleutels bevat.</li>
            <li><strong>Koppeltabellen:</strong> Het logische ERD lost veel-op-veel relaties op door koppeltabellen toe te voegen (BESTELLING_PRODUCT).</li>
            <li><strong>Notatie voor relaties:</strong> Het logische ERD gebruikt de Entity Relation pijlen uit draw.io om de cardinaliteit van relaties preciezer weer te geven.</li>
            <li><strong>Detail niveau:</strong> Het logische ERD is gedetailleerder en dichter bij de daadwerkelijke database-implementatie.</li>
        </ul>
    </div>
</body>
</html>
