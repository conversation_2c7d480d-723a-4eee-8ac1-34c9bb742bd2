SELECT DISTINCT 
    fr.klant_id,
    k.<PERSON><PERSON><PERSON>,
    k.<PERSON><PERSON><PERSON><PERSON>,
    k.geb<PERSON>,
    trein_3070.trein_id,
    verdachte.activiteit_id,
    trein_3070.station_id,
    trein_3070.datum  -- <PERSON><PERSON> toegevoegd
FROM (
    -- Klanten die tussen 17 jan en 24 apr 3-5x per week reisden en daarna verdwenen
    SELECT frequent_reizigers.klant_id
    FROM (
        SELECT ov.klant_id, ov.weeknr, COUNT(DISTINCT ov.datum) AS reisfrequentie
        FROM ov_info ov
        WHERE ov.jaar = 2024
          AND ov.datum BETWEEN '2024-01-17' AND '2024-04-24'
        GROUP BY ov.klant_id, ov.weeknr
        HAVING reisfrequentie BETWEEN 3 AND 5
    ) AS frequent_reizigers
    LEFT JOIN ov_info o ON frequent_reizigers.klant_id = o.klant_id AND o.datum > '2024-04-24'
    WHERE o.klant_id IS NULL
) AS fr
INNER JOIN (
    -- Klanten die wel hebben ingecheckt maar nooit uitgecheckt
    SELECT DISTINCT o1.klant_id
    FROM ov_info o1
    WHERE o1.status = 'inchecken'
    AND NOT EXISTS (
        SELECT 1 
        FROM ov_info o2 
        WHERE o2.klant_id = o1.klant_id 
        AND o2.reis_id = o1.reis_id 
        AND o2.status = 'uitchecken'
    )
) AS niet_uitgecheckt
ON fr.klant_id = niet_uitgecheckt.klant_id
INNER JOIN (
    -- Klanten die in trein 3070 zaten, inclusief station_id en datum (filter op 24 april 2024)
    SELECT DISTINCT dr.klant_id, d.trein_id, dr.station_id, dr.datum
    FROM ov_info dr
    JOIN dienstregeling d ON dr.station_id = d.station_id
    WHERE d.trein_id = 3070
      AND dr.datum = '2024-04-24'  -- Filter op de datum 24 april 2024
) AS trein_3070
ON fr.klant_id = trein_3070.klant_id
INNER JOIN (
    -- Klanten met verdachte activiteit (vermomming)
    SELECT DISTINCT ad.klant_id, ad.activiteit_id
    FROM activiteit_detectie ad
    WHERE ad.activiteit_id = 3  -- Vermomming
) AS verdachte
ON fr.klant_id = verdachte.klant_id
INNER JOIN klant k  -- Voeg klantgegevens toe
ON fr.klant_id = k.klant_id;
