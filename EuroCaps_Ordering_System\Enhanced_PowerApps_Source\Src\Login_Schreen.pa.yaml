# ************************************************************************************************
# Enhanced Login Screen for EuroCaps Ordering System
# Based on detailed mockup specifications with role-based authentication
# Color Scheme: Header: 2C3E50, Background: 1B3A4B, Buttons: F39C12, Text: FFFFFF
# ************************************************************************************************
Screens:
  Login_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(243, 156, 18, 1)
      Fill: =RGBA(27, 58, 75, 1)
      OnVisible: |
        =// Initialize login variables
        Set(varUserRole, "");
        Set(varUserName, "");
        Set(varRememberMe, false);
        // Check if user credentials are remembered
        If(
            !IsBlank(Get("RememberedUser")),
            Set(varRememberMe, true);
            UpdateContext({ctxRememberedUser: Get("RememberedUser")})
        )
    Children:
      # Background
      - Background:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(27, 58, 75, 1)
            Height: =768
            Width: =1366
            X: =0
            Y: =0
            BorderThickness: =0

      # EuroCaps Logo
      - Logo:
          Control: Image@2.1.0
          Properties:
            Image: ="eurocaps-logo"
            Height: =150
            Width: =150
            X: =608
            Y: =80

      # Application Title
      - AppTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="Coffee Capsule Ordering System"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =18
            Color: =RGBA(255, 255, 255, 1)
            Align: =Align.Center
            Height: =40
            Width: =400
            X: =483
            Y: =250

      # Login Panel Background
      - LoginPanel:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(44, 62, 80, 1)
            Height: =350
            Width: =400
            X: =483
            Y: =300
            BorderColor: =RGBA(169, 198, 232, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Username Label
      - UsernameLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Username:"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Height: =25
            Width: =100
            X: =503
            Y: =330

      # Username Input
      - UsernameInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            Default: =If(varRememberMe, ctxRememberedUser, "")
            HintText: ="Enter your username"
            Font: =Font.Arial
            Size: =12
            BorderColor: =RGBA(169, 198, 232, 1)
            HoverBorderColor: =RGBA(243, 156, 18, 1)
            Height: =40
            Width: =360
            X: =503
            Y: =355

      # Password Label
      - PasswordLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Password:"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Height: =25
            Width: =100
            X: =503
            Y: =410

      # Password Input
      - PasswordInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            HintText: ="Enter your password"
            Mode: =TextMode.Password
            Font: =Font.Arial
            Size: =12
            BorderColor: =RGBA(169, 198, 232, 1)
            HoverBorderColor: =RGBA(243, 156, 18, 1)
            Height: =40
            Width: =360
            X: =503
            Y: =435

      # Remember Me Checkbox
      - RememberMeCheckbox:
          Control: Classic/CheckBox@2.1.0
          Properties:
            Text: ="Remember me"
            Default: =varRememberMe
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            CheckboxBorderColor: =RGBA(243, 156, 18, 1)
            CheckmarkFill: =RGBA(243, 156, 18, 1)
            Height: =30
            Width: =150
            X: =503
            Y: =490
            OnCheck: =Set(varRememberMe, true)
            OnUncheck: =Set(varRememberMe, false)

      # Login Button
      - LoginButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="LOGIN"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            PressedFill: =RGBA(200, 120, 0, 1)
            BorderColor: =RGBA(243, 156, 18, 1)
            Height: =45
            Width: =200
            X: =503
            Y: =535
            OnSelect: |
              =If(
                  !IsBlank(UsernameInput.Text) && !IsBlank(PasswordInput.Text),
                  // Role-based authentication
                  Switch(
                      Lower(UsernameInput.Text),
                      "sales", Set(varUserRole, "Sales Representative"); Set(varUserName, UsernameInput.Text),
                      "service", Set(varUserRole, "Customer Service"); Set(varUserName, UsernameInput.Text),
                      "manager", Set(varUserRole, "Manager"); Set(varUserName, UsernameInput.Text),
                      "admin", Set(varUserRole, "Admin"); Set(varUserName, UsernameInput.Text),
                      Set(varUserRole, "Sales Representative"); Set(varUserName, UsernameInput.Text)
                  );
                  // Save username if Remember Me is checked
                  If(RememberMeCheckbox.Value, Set("RememberedUser", UsernameInput.Text), Remove("RememberedUser"));
                  // Navigate to Dashboard
                  Navigate(Dashboard_Screen, ScreenTransition.Fade);
                  Notify("Welcome " & varUserName & " (" & varUserRole & ")", NotificationType.Success),
                  Notify("Please enter both username and password", NotificationType.Error)
              )

      # Forgot Password Link
      - ForgotPasswordLink:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="Forgot Password?"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(243, 156, 18, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(0, 0, 0, 0)
            HoverFill: =RGBA(0, 0, 0, 0)
            HoverColor: =RGBA(255, 255, 255, 1)
            Height: =30
            Width: =120
            X: =743
            Y: =545
            OnSelect: =Notify("Password reset functionality will be implemented in production version", NotificationType.Information)

      # Footer
      - Footer:
          Control: Label@2.5.1
          Properties:
            Text: ="© 2025 EuroCaps - Coffee Capsule Ordering System"
            Font: =Font.Arial
            Size: =10
            Color: =RGBA(255, 255, 255, 1)
            Align: =Align.Center
            Height: =20
            Width: =400
            X: =483
            Y: =680
