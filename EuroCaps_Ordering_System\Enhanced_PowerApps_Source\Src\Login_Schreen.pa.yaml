# ************************************************************************************************
# Enhanced Login Screen for EuroCaps Ordering System
# Based on detailed mockup specifications
# ************************************************************************************************
Screens:
  Login_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(74, 111, 165, 1)
      Fill: =RGBA(245, 245, 245, 1)
    Children:
      # Background
      - Background:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(245, 245, 245, 1)
            Height: =768
            Width: =1366
            X: =0
            Y: =0
            BorderThickness: =0

      # EuroCaps Logo
      - Logo:
          Control: Image@2.1.0
          Properties:
            Image: ="eurocaps-logo"
            Height: =150
            Width: =150
            X: =608
            Y: =100

      # Application Title
      - AppTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="Order Management Pro"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =18
            Color: =RGBA(74, 111, 165, 1)
            Align: =Align.Center
            Height: =40
            Width: =400
            X: =483
            Y: =270

      # Login Panel Background
      - LoginPanel:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =300
            Width: =400
            X: =483
            Y: =330
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Username Label
      - UsernameLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Username:"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =100
            X: =503
            Y: =360

      # Username Input
      - UsernameInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            HintText: ="Enter your username"
            Font: =Font.Arial
            Size: =12
            BorderColor: =RGBA(74, 111, 165, 1)
            HoverBorderColor: =RGBA(74, 111, 165, 1)
            Height: =40
            Width: =360
            X: =503
            Y: =385

      # Password Label
      - PasswordLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Password:"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =100
            X: =503
            Y: =440

      # Password Input
      - PasswordInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            HintText: ="Enter your password"
            Mode: =TextMode.Password
            Font: =Font.Arial
            Size: =12
            BorderColor: =RGBA(74, 111, 165, 1)
            HoverBorderColor: =RGBA(74, 111, 165, 1)
            Height: =40
            Width: =360
            X: =503
            Y: =465

      # Remember Me Checkbox
      - RememberMeCheckbox:
          Control: Classic/CheckBox@2.1.0
          Properties:
            Text: ="Remember me"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            CheckboxBorderColor: =RGBA(74, 111, 165, 1)
            CheckmarkFill: =RGBA(74, 111, 165, 1)
            Height: =30
            Width: =150
            X: =503
            Y: =520

      # Login Button
      - LoginButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="LOGIN"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(74, 111, 165, 1)
            HoverFill: =RGBA(58, 95, 149, 1)
            PressedFill: =RGBA(42, 79, 133, 1)
            BorderColor: =RGBA(74, 111, 165, 1)
            Height: =45
            Width: =200
            X: =503
            Y: =565
            OnSelect: |
              =If(
                  !IsBlank(UsernameInput.Text) && !IsBlank(PasswordInput.Text),
                  Navigate(Dashboard_Screen, ScreenTransition.Fade),
                  Notify("Please enter both username and password", NotificationType.Error)
              )

      # Forgot Password Link
      - ForgotPasswordLink:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="Forgot Password?"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(74, 111, 165, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(0, 0, 0, 0)
            HoverFill: =RGBA(0, 0, 0, 0)
            Height: =30
            Width: =120
            X: =743
            Y: =575

      # Footer
      - Footer:
          Control: Label@2.5.1
          Properties:
            Text: ="© 2025 EuroCaps"
            Font: =Font.Arial
            Size: =10
            Color: =RGBA(128, 128, 128, 1)
            Align: =Align.Center
            Height: =20
            Width: =200
            X: =583
            Y: =720
