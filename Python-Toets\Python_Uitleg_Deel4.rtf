{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Courier New;}}
{\colortbl ;\red0\green0\blue255;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 PYTHON UITLEG - DEEL 4\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 8. OBJECT-ORIENTED PROGRAMMING (OOP)\b0\fs22\par

\pard\sa200\sl276\slmult1\b Klassen en objecten\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Basis klasse definitie\par
class Persoon:\par
    # Class docstring\par
    """Een klasse die een persoon representeert."""\par
    \par
    # Constructor (initializer)\par
    def __init__(self, naam, leeftijd):\par
        # Instance variabelen (attributen)\par
        self.naam = naam\par
        self.leeftijd = leeftijd\par
    \par
    # Instance methode\par
    def groet(self):\par
        return f"Hallo, ik ben \{self.naam\} en ik ben \{self.leeftijd\} jaar oud."\par
\par
# Object (instantie) aanmaken\par
p1 = Persoon("Alice", 30)\par
p2 = Persoon("Bob", 25)\par
\par
# Attributen benaderen\par
print(p1.naam)      # "Alice"\par
print(p2.leeftijd)  # 25\par
\par
# Methoden aanroepen\par
print(p1.groet())   # "Hallo, ik ben Alice en ik ben 30 jaar oud."\par
\par
# Attributen wijzigen\par
p1.leeftijd = 31\par
print(p1.leeftijd)  # 31\f0\par

\pard\sa200\sl276\slmult1\b Class variabelen vs. instance variabelen\b0\par
\pard\li360\sa200\sl276\slmult1\f1 class Persoon:\par
    # Class variabele (gedeeld door alle instanties)\par
    soort = "mens"\par
    aantal = 0\par
    \par
    def __init__(self, naam):\par
        # Instance variabele (uniek voor elke instantie)\par
        self.naam = naam\par
        # Class variabele wijzigen\par
        Persoon.aantal += 1\par
\par
# Objecten aanmaken\par
p1 = Persoon("Alice")\par
p2 = Persoon("Bob")\par
\par
# Class variabelen benaderen\par
print(Persoon.soort)   # "mens"\par
print(p1.soort)        # "mens"\par
print(p2.soort)        # "mens"\par
print(Persoon.aantal)  # 2\par
\par
# Let op: als je een class variabele via een instantie wijzigt,\par
# wordt er een instance variabele met dezelfde naam aangemaakt\par
p1.soort = "superheld"\par
print(p1.soort)        # "superheld"\par
print(p2.soort)        # "mens" (ongewijzigd)\par
print(Persoon.soort)   # "mens" (ongewijzigd)\f0\par

\pard\sa200\sl276\slmult1\b Methoden\b0\par
\pard\li360\sa200\sl276\slmult1\f1 class Persoon:\par
    def __init__(self, naam, leeftijd):\par
        self.naam = naam\par
        self.leeftijd = leeftijd\par
    \par
    # Instance methode (heeft toegang tot instance via self)\par
    def groet(self):\par
        return f"Hallo, ik ben \{self.naam\}"\par
    \par
    def verjaar(self):\par
        self.leeftijd += 1\par
        return f"\{self.naam\} is nu \{self.leeftijd\} jaar"\par
    \par
    # Class methode (heeft toegang tot class, niet tot instance)\par
    @classmethod\par
    def van_geboortejaar(cls, naam, geboortejaar):\par
        leeftijd = 2024 - geboortejaar\par
        return cls(naam, leeftijd)  # Maakt een nieuwe instantie\par
    \par
    # Static methode (heeft geen toegang tot class of instance)\par
    @staticmethod\par
    def is_volwassen(leeftijd):\par
        return leeftijd >= 18\par
\par
# Instance methode gebruiken\par
p = Persoon("Alice", 30)\par
print(p.groet())    # "Hallo, ik ben Alice"\par
print(p.verjaar())  # "Alice is nu 31 jaar"\par
\par
# Class methode gebruiken\par
p2 = Persoon.van_geboortejaar("Bob", 1995)\par
print(p2.leeftijd)  # 29 (in 2024)\par
\par
# Static methode gebruiken\par
print(Persoon.is_volwassen(16))  # False\par
print(Persoon.is_volwassen(21))  # True\par
print(p.is_volwassen(p.leeftijd))  # True\f0\par

\pard\sa200\sl276\slmult1\b Overerving (inheritance)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Basis klasse (parent class)\par
class Persoon:\par
    def __init__(self, naam, leeftijd):\par
        self.naam = naam\par
        self.leeftijd = leeftijd\par
    \par
    def groet(self):\par
        return f"Hallo, ik ben \{self.naam\}"\par
    \par
    def beschrijf(self):\par
        return f"\{self.naam\} is \{self.leeftijd\} jaar oud"\par
\par
# Afgeleide klasse (child class)\par
class Student(Persoon):\par
    def __init__(self, naam, leeftijd, studie):\par
        # Roep de constructor van de parent class aan\par
        super().__init__(naam, leeftijd)\par
        # Voeg extra attributen toe\par
        self.studie = studie\par
    \par
    # Override de groet methode\par
    def groet(self):\par
        return f"Hoi, ik ben \{self.naam\} en ik studeer \{self.studie\}"\par
    \par
    # Nieuwe methode\par
    def studeer(self):\par
        return f"\{self.naam\} studeert \{self.studie\}"\par
\par
# Persoon instantie\par
p = Persoon("Alice", 30)\par
print(p.groet())     # "Hallo, ik ben Alice"\par
print(p.beschrijf()) # "Alice is 30 jaar oud"\par
\par
# Student instantie\par
s = Student("Bob", 20, "Informatica")\par
print(s.groet())     # "Hoi, ik ben Bob en ik studeer Informatica"\par
print(s.beschrijf()) # "Bob is 20 jaar oud" (geërfd van Persoon)\par
print(s.studeer())   # "Bob studeert Informatica"\par
\par
# Controleren of een object een instantie is van een klasse\par
print(isinstance(s, Student))  # True\par
print(isinstance(s, Persoon))  # True (Student is een Persoon)\par
print(isinstance(p, Student))  # False (Persoon is geen Student)\par
\par
# Controleren of een klasse een subklasse is van een andere klasse\par
print(issubclass(Student, Persoon))  # True\par
print(issubclass(Persoon, Student))  # False\f0\par

\pard\sa200\sl276\slmult1\b Meervoudige overerving\b0\par
\pard\li360\sa200\sl276\slmult1\f1 class A:\par
    def methode(self):\par
        return "A.methode()"\par
\par
class B:\par
    def methode(self):\par
        return "B.methode()"\par
    \par
    def andere_methode(self):\par
        return "B.andere_methode()"\par
\par
# Meervoudige overerving (erft van zowel A als B)\par
class C(A, B):\par
    pass\par
\par
# Klasse die de methode override\par
class D(A, B):\par
    def methode(self):\par
        return "D.methode()"\par
\par
c = C()\par
print(c.methode())         # "A.methode()" (A komt eerst in de MRO)\par
print(c.andere_methode())  # "B.andere_methode()"\par
\par
d = D()\par
print(d.methode())         # "D.methode()"\par
\par
# Method Resolution Order (MRO) - volgorde waarin Python zoekt naar methoden\par
print(C.__mro__)  # (<class '__main__.C'>, <class '__main__.A'>, <class '__main__.B'>, <class 'object'>)\f0\par

\pard\sa200\sl276\slmult1\b Encapsulation (inkapseling)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 class Persoon:\par
    def __init__(self, naam, leeftijd):\par
        self.naam = naam       # Publiek attribuut\par
        self._leeftijd = leeftijd  # Protected attribuut (conventie)\par
        self.__adres = ""      # Private attribuut (name mangling)\par
    \par
    # Getter methode\par
    def get_leeftijd(self):\par
        return self._leeftijd\par
    \par
    # Setter methode\par
    def set_leeftijd(self, leeftijd):\par
        if leeftijd >= 0:\par
            self._leeftijd = leeftijd\par
        else:\par
            raise ValueError("Leeftijd moet positief zijn")\par
    \par
    # Private methode\par
    def __private_methode(self):\par
        return "Dit is privé"\par
    \par
    # Publieke methode die private methode aanroept\par
    def publieke_methode(self):\par
        return self.__private_methode()\par
\par
p = Persoon("Alice", 30)\par
\par
# Publiek attribuut\par
print(p.naam)  # "Alice"\par
p.naam = "Alicia"\par
print(p.naam)  # "Alicia"\par
\par
# Protected attribuut (conventie, technisch nog steeds toegankelijk)\par
print(p._leeftijd)  # 30\par
p._leeftijd = 31\par
print(p._leeftijd)  # 31\par
\par
# Getter en setter\par
print(p.get_leeftijd())  # 31\par
p.set_leeftijd(32)\par
print(p.get_leeftijd())  # 32\par
\par
# Private attribuut (name mangling)\par
# print(p.__adres)  # AttributeError\par
print(p._Persoon__adres)  # "" (toegang via name mangling)\par
\par
# Private methode\par
# p.__private_methode()  # AttributeError\par
print(p.publieke_methode())  # "Dit is privé"\f0\par

\pard\sa200\sl276\slmult1\b Properties\b0\par
\pard\li360\sa200\sl276\slmult1\f1 class Persoon:\par
    def __init__(self, naam, leeftijd):\par
        self.naam = naam\par
        self._leeftijd = leeftijd\par
    \par
    # Property voor leeftijd\par
    @property\par
    def leeftijd(self):\par
        return self._leeftijd\par
    \par
    @leeftijd.setter\par
    def leeftijd(self, waarde):\par
        if waarde >= 0:\par
            self._leeftijd = waarde\par
        else:\par
            raise ValueError("Leeftijd moet positief zijn")\par
    \par
    # Read-only property\par
    @property\par
    def is_volwassen(self):\par
        return self._leeftijd >= 18\par
\par
p = Persoon("Alice", 30)\par
\par
# Property gebruiken (lijkt op attribuut, maar is methode)\par
print(p.leeftijd)  # 30\par
p.leeftijd = 31    # Gebruikt de setter\par
print(p.leeftijd)  # 31\par
\par
# Read-only property\par
print(p.is_volwassen)  # True\par
# p.is_volwassen = False  # AttributeError (geen setter)\f0\par

\pard\sa200\sl276\slmult1\b Speciale (dunder) methoden\b0\par
\pard\li360\sa200\sl276\slmult1\f1 class Punt:\par
    def __init__(self, x, y):\par
        self.x = x\par
        self.y = y\par
    \par
    # String representatie (voor print)\par
    def __str__(self):\par
        return f"Punt(\{self.x\}, \{self.y\})"\par
    \par
    # Representatie (voor debugging)\par
    def __repr__(self):\par
        return f"Punt(x=\{self.x\}, y=\{self.y\})"\par
    \par
    # Vergelijking (==)\par
    def __eq__(self, other):\par
        if not isinstance(other, Punt):\par
            return False\par
        return self.x == other.x and self.y == other.y\par
    \par
    # Optellen (+)\par
    def __add__(self, other):\par
        if isinstance(other, Punt):\par
            return Punt(self.x + other.x, self.y + other.y)\par
        return NotImplemented\par
    \par
    # Lengte (len())\par
    def __len__(self):\par
        return int((self.x ** 2 + self.y ** 2) ** 0.5)\par
    \par
    # Boolean conversie (bool())\par
    def __bool__(self):\par
        return self.x != 0 or self.y != 0\par
\par
p1 = Punt(3, 4)\par
p2 = Punt(1, 2)\par
p3 = Punt(3, 4)\par
\par
print(p1)          # "Punt(3, 4)"\par
print(repr(p1))    # "Punt(x=3, y=4)"\par
\par
print(p1 == p2)    # False\par
print(p1 == p3)    # True\par
\par
p4 = p1 + p2\par
print(p4)          # "Punt(4, 6)"\par
\par
print(len(p1))     # 5 (afgerond)\par
\par
print(bool(p1))    # True\par
print(bool(Punt(0, 0)))  # False\f0\par

\pard\sa200\sl276\slmult1\b\fs24 9. REGULIERE EXPRESSIES (REGEX)\b0\fs22\par

\pard\sa200\sl276\slmult1\b Basis regex patronen\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import re\par
\par
# Letterlijke tekst\par
tekst = "Python is een programmeertaal"\par
match = re.search("Python", tekst)\par
print(match.group())  # "Python"\par
\par
# Metacharacters\par
# . (punt) - elk karakter behalve newline\par
match = re.search("P.thon", "Python")\par
print(match.group())  # "Python"\par
\par
# \\d - cijfer (0-9)\par
match = re.search("\\d+", "Er zijn 42 appels")\par
print(match.group())  # "42"\par
\par
# \\w - alfanumeriek karakter (a-z, A-Z, 0-9, _)\par
match = re.search("\\w+", "Hello, World!")\par
print(match.group())  # "Hello"\par
\par
# \\s - whitespace (spatie, tab, newline)\par
match = re.search("\\s+", "Hello World")\par
print(match.group())  # " "\par
\par
# \\D, \\W, \\S - negaties (niet-cijfer, niet-alfanumeriek, niet-whitespace)\par
match = re.search("\\D+", "123abc456")\par
print(match.group())  # "abc"\par
\par
# [] - karakterklasse (één van de karakters)\par
match = re.search("[aeiou]", "Hello")\par
print(match.group())  # "e"\par
\par
# [^] - negatie van karakterklasse\par
match = re.search("[^aeiou]+", "Hello")\par
print(match.group())  # "H"\par
\par
# Bereiken\par
match = re.search("[a-z]+", "Hello123")\par
print(match.group())  # "ello"\par
\par
match = re.search("[0-9]+", "Hello123")\par
print(match.group())  # "123"\f0\par

\pard\sa200\sl276\slmult1\b Kwantificeerders\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # * - 0 of meer keer\par
match = re.search("a*b", "aaaab")\par
print(match.group())  # "aaaab"\par
\par
match = re.search("a*b", "b")\par
print(match.group())  # "b"\par
\par
# + - 1 of meer keer\par
match = re.search("a+b", "aaaab")\par
print(match.group())  # "aaaab"\par
\par
# ? - 0 of 1 keer\par
match = re.search("colou?r", "color")\par
print(match.group())  # "color"\par
\par
match = re.search("colou?r", "colour")\par
print(match.group())  # "colour"\par
\par
# \{n\} - exact n keer\par
match = re.search("a\{3\}b", "aaab")\par
print(match.group())  # "aaab"\par
\par
# \{n,\} - minstens n keer\par
match = re.search("a\{2,\}b", "aaaab")\par
print(match.group())  # "aaaab"\par
\par
# \{n,m\} - tussen n en m keer\par
match = re.search("a\{2,4\}b", "aaaab")\par
print(match.group())  # "aaaab"\f0\par

\pard\sa200\sl276\slmult1\b Ankers en grenzen\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # ^ - begin van string of regel\par
match = re.search("^Python", "Python is geweldig")\par
print(match.group())  # "Python"\par
\par
# $ - einde van string of regel\par
match = re.search("geweldig$", "Python is geweldig")\par
print(match.group())  # "geweldig"\par
\par
# \\b - woordgrens\par
match = re.search("\\bPython\\b", "Dit is Python!")\par
print(match.group())  # "Python"\par
\par
# \\B - niet-woordgrens\par
match = re.search("\\Bon\\B", "Python")\par
print(match.group())  # "on"\f0\par

\pard\sa200\sl276\slmult1\b Groepen en alternatieven\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # () - groep\par
match = re.search("(\\d+)-(\\d+)", "Telefoonnummer: 06-12345678")\par
print(match.group())    # "06-12345678"\par
print(match.group(1))   # "06"\par
print(match.group(2))   # "12345678"\par
\par
# | - alternatief (OR)\par
match = re.search("kat|hond", "Ik heb een kat")\par
print(match.group())  # "kat"\par
\par
match = re.search("kat|hond", "Ik heb een hond")\par
print(match.group())  # "hond"\par
\par
# (?:) - non-capturing groep\par
match = re.search("(?:\\d+)-(\\d+)", "Telefoonnummer: 06-12345678")\par
print(match.group(1))   # "12345678" (eerste groep is niet gecaptured)\f0\par

\pard\sa200\sl276\slmult1\b Regex functies\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import re\par
\par
tekst = "Python is een programmeertaal. Python is populair."\par
\par
# re.search() - zoekt naar eerste match\par
match = re.search("Python", tekst)\par
print(match.group())  # "Python"\par
print(match.start())  # 0 (beginpositie)\par
print(match.end())    # 6 (eindpositie)\par
\par
# re.match() - zoekt alleen aan het begin van de string\par
match = re.match("Python", tekst)\par
print(match.group())  # "Python"\par
\par
match = re.match("is", tekst)\par
print(match)  # None (is staat niet aan het begin)\par
\par
# re.findall() - vindt alle matches als lijst\par
matches = re.findall("Python", tekst)\par
print(matches)  # ["Python", "Python"]\par
\par
# re.finditer() - vindt alle matches als iterator\par
for match in re.finditer("Python", tekst):\par
    print(match.group(), match.start(), match.end())\par
# Python 0 6\par
# Python 29 35\par
\par
# re.split() - splitst string op patroon\par
delen = re.split("\\.", tekst)\par
print(delen)  # ["Python is een programmeertaal", " Python is populair", ""]\par
\par
# re.sub() - vervangt matches\par
nieuw_tekst = re.sub("Python", "Java", tekst)\par
print(nieuw_tekst)  # "Java is een programmeertaal. Java is populair."\f0\par

\pard\sa200\sl276\slmult1\b Regex vlaggen\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import re\par
\par
tekst = "Python is geweldig\\nPYTHON is populair"\par
\par
# re.IGNORECASE of re.I - hoofdlettergevoeligheid negeren\par
matches = re.findall("python", tekst, re.IGNORECASE)\par
print(matches)  # ["Python", "PYTHON"]\par
\par
# re.MULTILINE of re.M - ^ en $ matchen begin/einde van elke regel\par
matches = re.findall("^Python", tekst, re.MULTILINE)\par
print(matches)  # ["Python"]\par
\par
# re.DOTALL of re.S - . matcht ook newline\par
match = re.search("geweldig.PYTHON", tekst, re.DOTALL)\par
print(match.group())  # "geweldig\\nPYTHON"\par
\par
# Meerdere vlaggen combineren\par
matches = re.findall("^python", tekst, re.MULTILINE | re.IGNORECASE)\par
print(matches)  # ["Python", "PYTHON"]\f0\par

\pard\sa200\sl276\slmult1\b Regex compileren\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import re\par
\par
# Patroon compileren (voor herhaald gebruik)\par
patroon = re.compile(r"\\d+-\\d+")\par
\par
tekst = "Telefoonnummers: 06-12345678 en 070-9876543"\par
\par
# Gecompileerd patroon gebruiken\par
matches = patroon.findall(tekst)\par
print(matches)  # ["06-12345678", "070-9876543"]\par
\par
# Vlaggen bij compileren\par
patroon = re.compile(r"python", re.IGNORECASE)\par
match = patroon.search("Python is geweldig")\par
print(match.group())  # "Python"\f0\par
}
