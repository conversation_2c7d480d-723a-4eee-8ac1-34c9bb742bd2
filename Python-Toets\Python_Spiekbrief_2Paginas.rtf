{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Courier New;}}
{\colortbl ;\red0\green0\blue255;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa0\sl240\slmult1\qc\b\f0\fs20 PYTHON SPIEKBRIEF - PAGINA 1\b0\fs18\par

\pard\sa0\sl240\slmult1\b\fs18 1. VARIABELEN & DATATYPES\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 int: x=5 | float: y=3.14 | str: s="tekst" | bool: b=True\par
list: l=[1,2,3] | tuple: t=(1,2,3) | dict: d=\{"a":1\} | set: s=\{1,2,3\}\par

\pard\sa0\sl240\slmult1\b\fs18 2. OPERATOREN\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Rekenkundig: + - * / // % **\par
Vergelijking: == != < > <= >=\par
Logisch: and or not\par
Toewijzing: = += -= *= /= //= %= **=\par

\pard\sa0\sl240\slmult1\b\fs18 3. STRINGS\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Indexering: s[0], s[-1] | Slicing: s[1:4], s[:5], s[2:], s[::2]\par
Methoden: upper() lower() strip() replace() split() join() find() count()\par
Format: f"x=\{x\}" | "x=\{\}".format(x) | "x=%d" % x\par

\pard\sa0\sl240\slmult1\b\fs18 4. LIJSTEN\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Toevoegen: append(x) insert(i,x) extend(lst)\par
Verwijderen: remove(x) pop(i) clear()\par
Overig: sort() reverse() index(x) count(x) len(lst)\par
Comprehension: [x*2 for x in range(5) if x>2]\par

\pard\sa0\sl240\slmult1\b\fs18 5. DICTIONARIES\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Toegang: d["key"] d.get("key",default)\par
Methoden: keys() values() items() update() pop() clear()\par
Comprehension: \{x:x**2 for x in range(5)\}\par

\pard\sa0\sl240\slmult1\b\fs18 6. CONTROLESTRUCTUREN\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 if-elif-else:\par
\f1 if x>0: print("pos")\par
elif x<0: print("neg")\par
else: print("nul")\f0\par
for-lus: \f1 for i in range(5): print(i)\f0\par
while-lus: \f1 while x>0: x-=1\f0\par
break, continue, pass\par

\pard\sa0\sl240\slmult1\b\fs18 7. FUNCTIES\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Definitie: \f1 def func(a, b=0): return a+b\f0\par
Args: \f1 def func(*args, **kwargs): print(args, kwargs)\f0\par
Lambda: \f1 f = lambda x,y: x+y\f0\par

\pard\sa0\sl240\slmult1\b\fs18 8. MODULES\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Import: \f1 import math\par
from random import randint\par
import os as besturingssysteem\f0\par
Standaard: math, random, datetime, os, sys, json, re\par

\pard\sa0\sl240\slmult1\b\fs18 9. BESTANDSVERWERKING\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Openen: \f1 with open('bestand.txt', 'r') as f: data = f.read()\f0\par
Modes: 'r' (lezen), 'w' (schrijven), 'a' (toevoegen), 'b' (binair)\par
Methoden: read() readline() readlines() write() writelines()\par

\pard\sa0\sl240\slmult1\b\fs18 10. EXCEPTIONS\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Try-except: \f1 try:\par
    x = int(input())\par
except ValueError as e:\par
    print(f"Fout: \{e\}")\par
finally:\par
    print("Klaar")\f0\par
Types: ValueError, TypeError, IndexError, KeyError, ZeroDivisionError\par

\pard\sa0\sl240\slmult1\b\fs18 11. MATH MODULE\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Constanten: math.pi, math.e\par
Functies: sqrt() pow() sin() cos() tan() log() log10()\par
Afronden: ceil() floor() round()\par

\pard\sa0\sl240\slmult1\b\fs18 12. RANDOM MODULE\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 random() - getal tussen 0-1\par
randint(a,b) - geheel getal tussen a-b (inclusief)\par
choice(lst) - willekeurig element uit lijst\par
shuffle(lst) - schudt lijst (in-place)\par

\pard\sa0\sl240\slmult1\qc\b\fs20 PYTHON SPIEKBRIEF - PAGINA 2\b0\fs18\par

\pard\sa0\sl240\slmult1\b\fs18 13. DATETIME MODULE\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Huidige datum/tijd: \f1 now = datetime.now()\f0\par
Aanmaken: \f1 d = date(2023,5,15)\par
t = time(14,30,0)\par
dt = datetime(2023,5,15,14,30,0)\f0\par
Formatteren: \f1 now.strftime("%d-%m-%Y %H:%M:%S")\f0\par
Parsen: \f1 datetime.strptime("15-05-2023", "%d-%m-%Y")\f0\par
Rekenen: \f1 now + timedelta(days=1, hours=2)\f0\par

\pard\sa0\sl240\slmult1\b\fs18 14. OS MODULE\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Pad: os.getcwd() os.chdir() os.path.join() os.path.exists()\par
Bestanden: os.listdir() os.mkdir() os.remove() os.rename()\par
Info: os.path.isfile() os.path.isdir() os.path.getsize()\par

\pard\sa0\sl240\slmult1\b\fs18 15. JSON MODULE\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Laden: \f1 data = json.loads('\\{"naam":"Jan"\\}')\f0\par
Dumpen: \f1 json_str = json.dumps(data, indent=4)\f0\par
Bestand: \f1 with open("data.json", "r") as f:\par
    data = json.load(f)\par
with open("data.json", "w") as f:\par
    json.dump(data, f, indent=4)\f0\par

\pard\sa0\sl240\slmult1\b\fs18 16. COLLECTIONS MODULE\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Counter: \f1 c = Counter(['a','b','a','c','a'])\f0  # telt items\par
defaultdict: \f1 d = defaultdict(list)\f0  # standaardwaarde voor nieuwe keys\par
namedtuple: \f1 Punt = namedtuple("Punt", ["x", "y"])\f0\par
deque: \f1 d = deque([1,2,3])\f0  # effici\'ebnte dubbele wachtrij\par

\pard\sa0\sl240\slmult1\b\fs18 17. OBJECT-ORIENTED PROGRAMMING\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Klasse definitie:\par
\f1 class Persoon:\par
    def __init__(self, naam, leeftijd):\par
        self.naam = naam\par
        self.leeftijd = leeftijd\par
    \par
    def groet(self):\par
        return f"Hallo, ik ben \{self.naam\}"\f0\par
Instantie: \f1 p = Persoon("Jan", 25)\f0\par
Overerving:\par
\f1 class Student(Persoon):\par
    def __init__(self, naam, leeftijd, studie):\par
        super().__init__(naam, leeftijd)\par
        self.studie = studie\f0\par

\pard\sa0\sl240\slmult1\b\fs18 18. REGULAR EXPRESSIONS (RE)\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Zoeken: \f1 re.search(r"patroon", tekst)\f0\par
Alle matches: \f1 re.findall(r"\\d+", "123 456")\f0  # ['123', '456']\par
Vervangen: \f1 re.sub(r"oud", "nieuw", tekst)\f0\par
Splitsen: \f1 re.split(r"[,;]", "a,b;c")\f0  # ['a', 'b', 'c']\par
Patronen: \\d (cijfer), \\w (letter/cijfer), \\s (whitespace), . (willekeurig)\par
Kwantificeerders: * (0+ keer), + (1+ keer), ? (0-1 keer), \{n\} (exact n keer)\par

\pard\sa0\sl240\slmult1\b\fs18 19. HANDIGE BUILT-IN FUNCTIES\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 print() input() len() range() type() int() float() str() bool()\par
list() tuple() dict() set() min() max() sum() sorted() enumerate()\par
zip() filter() map() any() all() abs() round() dir() help()\par

\pard\sa0\sl240\slmult1\b\fs18 20. CSV MODULE\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 Lezen: \f1 with open("data.csv") as f:\par
    reader = csv.reader(f)\par
    for row in reader: print(row)\f0\par
Met headers: \f1 reader = csv.DictReader(f)\f0\par
Schrijven: \f1 writer = csv.writer(f)\par
writer.writerow(["naam", "leeftijd"])\f0\par

\pard\sa0\sl240\slmult1\b\fs18 21. HANDIGE TIPS\b0\par
\pard\li180\sa0\sl240\slmult1\fs16 List comprehension: [expr for item in iterable if condition]\par
Dict comprehension: \{key:value for item in iterable if condition\}\par
Enumerate: \f1 for i, val in enumerate(lst): print(i, val)\f0\par
Zip: \f1 for a, b in zip(lst1, lst2): print(a, b)\f0\par
Context managers: \f1 with open(...) as f: ...\f0\par
F-strings: \f1 f"Naam: \{naam\}, Leeftijd: \{leeftijd\}"\f0\par
Unpacking: \f1 a, b, c = [1, 2, 3]\f0  of \f1 a, *rest = [1, 2, 3, 4]\f0\par
Slicing: lst[start:stop:step] of lst[::-1] (omgekeerd)\par
Membership: \f1 if x in lst: ...\f0  of \f1 if key in dict: ...\f0\par
Default dict get: \f1 d.get("key", default)\f0  i.p.v. \f1 d["key"]\f0\par
}
