BEDRIJFSPROCESSEN VOLGENS THUIS & STUIVE (3E DRUK)

1. WAT ZIJN BEDRIJFSPROCESSEN?

Een bedrijfsproces is een reeks van logisch samenhangende activiteiten die worden uitgevoerd om een bepaald doel te bereiken. Processen transformeren inputs (grondstoffen, informatie, etc.) naar outputs (producten, diensten, etc.) die waarde hebben voor klanten.

Ken<PERSON><PERSON> van bedrijfsprocessen volgens Thuis & Stuive:
- Ze hebben een duidelijk begin en einde
- Ze hebben een doel
- Ze hebben inputs en outputs
- Ze bestaan uit activiteiten die in een bepaalde volgorde worden uitgevoerd
- Ze overschrijden vaak functionele grenzen
- Ze kunnen worden gemeten en verbeterd
- Ze creëren waarde voor klanten (intern of extern)

2. INPUT-THROUGHPUT-OUTPUT (ITO) MODEL

Het ITO-model is een fundamenteel model voor het begrijpen van processen:

- INPUT: De middelen die nodig zijn om het proces uit te voeren
  * Grondstoffen
  * Arbeid
  * Informatie
  * Kapitaal
  * Energie

- THROUGHPUT: Het transformatieproces waarin de inputs worden omgezet in outputs
  * Productie
  * Dienstverlening
  * Informatieverwerking
  * Waardecreatie

- OUTPUT: De resultaten van het proces
  * Producten
  * Diensten
  * Informatie
  * Afval/Emissies

- FEEDBACK: Informatie over de output die wordt gebruikt om het proces te verbeteren
  * Kwaliteitscontrole
  * Klanttevredenheid
  * Prestatiemetingen
  * Verbetervoorstellen

3. TYPES BEDRIJFSPROCESSEN

Volgens Thuis & Stuive kunnen bedrijfsprocessen worden onderverdeeld in drie hoofdcategorieën:

a) Primaire processen:
- Direct gericht op het creëren van producten of diensten voor externe klanten
- Dragen direct bij aan de waardecreatie
- Voorbeelden:
  * Inkoop
  * Productie
  * Verkoop
  * Distributie
  * Klantenservice

b) Ondersteunende processen:
- Ondersteunen de primaire processen
- Dragen indirect bij aan de waardecreatie
- Voorbeelden:
  * Personeelsbeheer (HRM)
  * Financiële administratie
  * IT-ondersteuning
  * Facilitaire diensten
  * Juridische zaken

c) Managementprocessen:
- Gericht op het plannen, organiseren, leiden en controleren van de organisatie
- Zorgen voor coördinatie tussen verschillende processen
- Voorbeelden:
  * Strategische planning
  * Budgettering
  * Prestatiemanagement
  * Risicomanagement
  * Kwaliteitsmanagement

4. PRIMAIRE PROCESSEN

a) Inkoopproces:
1. Specificeren van behoeften
2. Selecteren van leveranciers
3. Onderhandelen en contracteren
4. Bestellen
5. Bewaken van leveringen
6. Nazorg en evaluatie

b) Productieproces:
1. Productieplanning
2. Werkvoorbereiding
3. Productie-uitvoering
4. Kwaliteitscontrole
5. Verpakking
6. Opslag

c) Verkoopproces:
1. Prospectie (zoeken naar potentiële klanten)
2. Voorbereiding (verzamelen informatie over klant)
3. Benadering (eerste contact)
4. Presentatie (demonstratie product/dienst)
5. Behandelen bezwaren
6. Afsluiting (verkoop)
7. Follow-up (nazorg)

d) Distributieproces:
1. Orderontvangst
2. Orderpicking
3. Verpakking
4. Verzending
5. Transport
6. Aflevering

e) Klantenserviceproces:
1. Ontvangst klantvraag/klacht
2. Registratie
3. Analyse
4. Oplossing
5. Terugkoppeling naar klant
6. Evaluatie en verbetering

5. ONDERSTEUNENDE PROCESSEN

a) HRM-processen:
1. Personeelsplanning
2. Werving en selectie
3. Introductie nieuwe medewerkers
4. Training en ontwikkeling
5. Prestatiebeoordeling
6. Beloning
7. Uitstroom

b) Financiële processen:
1. Budgettering
2. Boekhouding
3. Facturering
4. Debiteurenadministratie
5. Crediteurenadministratie
6. Salarisadministratie
7. Financiële rapportage

c) IT-processen:
1. IT-planning
2. Systeemontwikkeling
3. Implementatie
4. Beheer en onderhoud
5. Gebruikersondersteuning
6. Beveiliging
7. Disaster recovery

d) Facilitaire processen:
1. Gebouwbeheer
2. Onderhoud
3. Beveiliging
4. Schoonmaak
5. Catering
6. Inkoop facilitaire middelen

e) Juridische processen:
1. Contractbeheer
2. Compliance
3. Intellectueel eigendom
4. Juridisch advies
5. Geschillenafhandeling

6. MANAGEMENTPROCESSEN

a) Strategische planningsproces:
1. Analyse externe omgeving
2. Analyse interne omgeving
3. Formuleren missie en visie
4. Bepalen strategische doelen
5. Ontwikkelen strategieën
6. Implementatie
7. Evaluatie en bijsturing

b) Budgetteringsproces:
1. Opstellen budgetrichtlijnen
2. Verzamelen budgetaanvragen
3. Beoordelen aanvragen
4. Consolideren budgetten
5. Goedkeuren budget
6. Communiceren budget
7. Monitoren en bijsturen

c) Prestatiemanagementproces:
1. Bepalen KPI's (Key Performance Indicators)
2. Meten prestaties
3. Analyseren resultaten
4. Rapporteren
5. Feedback geven
6. Verbeteracties definiëren
7. Implementeren verbeteringen

d) Risicomanagementproces:
1. Identificeren risico's
2. Analyseren risico's
3. Evalueren risico's
4. Behandelen risico's
5. Monitoren en reviewen
6. Communiceren en consulteren

e) Kwaliteitsmanagementproces:
1. Kwaliteitsplanning
2. Kwaliteitsborging
3. Kwaliteitscontrole
4. Kwaliteitsverbetering

7. PROCESVISUALISATIE

Volgens Thuis & Stuive zijn er verschillende methoden om processen visueel weer te geven:

a) Flowcharts:
- Tonen de stappen in een proces met symbolen en pijlen
- Geven een duidelijk overzicht van de volgorde van activiteiten
- Symbolen:
  * Ovaal: Start/Einde
  * Rechthoek: Activiteit/Taak
  * Ruit: Beslissing
  * Pijlen: Stroomrichting

b) Swimlane-diagrammen:
- Tonen niet alleen de processtappen, maar ook welke afdeling of persoon verantwoordelijk is
- Nuttig voor processen die meerdere afdelingen omvatten
- Horizontale of verticale "zwembanen" voor elke afdeling of rol

c) Value Stream Maps:
- Tonen de stroom van materialen en informatie
- Inclusief wachttijden en voorraden
- Worden gebruikt in Lean management om verspilling te identificeren

d) BPMN (Business Process Model and Notation):
- Gestandaardiseerde notatie voor het modelleren van bedrijfsprocessen
- Rijke set symbolen voor verschillende aspecten van processen
- Internationaal erkende standaard

8. PROCESMANAGEMENT

Procesmanagement omvat volgens Thuis & Stuive alle activiteiten die nodig zijn om processen te ontwerpen, implementeren, monitoren en verbeteren:

a) Procesontwerp:
- Definiëren van stappen, rollen, regels en resources
- Bepalen van inputs, outputs en prestatie-indicatoren
- Documenteren van het proces

b) Procesimplementatie:
- In gebruik nemen van een nieuw of gewijzigd proces
- Training van medewerkers
- Aanpassing van systemen
- Communicatie over het proces

c) Procesmonitoring:
- Meten van procesprestaties met KPI's
- Analyseren van resultaten
- Identificeren van afwijkingen
- Rapporteren over procesprestaties

d) Procesverbetering:
- Identificeren van verbetermogelijkheden
- Analyseren van oorzaken van problemen
- Ontwikkelen van verbetervoorstellen
- Implementeren van verbeteringen
- Evalueren van resultaten

e) Procesgovernance:
- Definiëren van rollen en verantwoordelijkheden
- Bepalen van besluitvormingsprocessen
- Vastleggen van processtandaarden
- Beheren van proceswijzigingen

9. PROCESBESTURING

Procesbesturing omvat volgens Thuis & Stuive het plannen, uitvoeren, controleren en bijsturen van processen:

a) Plan-Do-Check-Act (PDCA) cyclus:
- Plan: doelen stellen en processen plannen
- Do: processen uitvoeren
- Check: resultaten meten en analyseren
- Act: processen verbeteren

b) Regelkringen:
- Meten van procesoutput
- Vergelijken met norm
- Bijsturen bij afwijkingen

c) Prestatie-indicatoren:
- Effectiviteit: mate waarin doelen worden bereikt
- Efficiëntie: verhouding tussen output en input
- Kwaliteit: mate waarin aan eisen wordt voldaan
- Flexibiliteit: vermogen om aan te passen aan veranderingen
- Innovatie: vermogen om te vernieuwen

10. PROCESVERBETERING

Volgens Thuis & Stuive zijn er verschillende methodologieën voor procesverbetering:

a) Lean:
- Gericht op het elimineren van verspilling (muda)
- Acht soorten verspilling:
  * Overproductie
  * Wachttijd
  * Transport
  * Overbewerking
  * Voorraad
  * Beweging
  * Defecten
  * Onbenutte creativiteit
- Tools:
  * Value Stream Mapping
  * 5S (Sorteren, Schikken, Schoonmaken, Standaardiseren, Standhouden)
  * Kaizen (continue verbetering)
  * Kanban (pull-systeem)

b) Six Sigma:
- Gericht op het verminderen van variatie en defecten
- DMAIC-methodologie:
  * Define: probleem en doelstellingen definiëren
  * Measure: huidige prestaties meten
  * Analyze: oorzaken van problemen analyseren
  * Improve: verbeteringen implementeren
  * Control: verbeteringen borgen
- Statistische methoden om processen te analyseren en te verbeteren

c) Business Process Reengineering (BPR):
- Radicaal herontwerpen van processen
- "Clean sheet" benadering
- Gericht op dramatische verbeteringen

d) Kaizen:
- Continue, incrementele verbeteringen
- Betrokkenheid van alle medewerkers
- Kleine, dagelijkse verbeteringen leiden op lange termijn tot grote resultaten

11. PROCESROLLEN

Volgens Thuis & Stuive zijn er verschillende rollen in procesmanagement:

a) Proceseigenaar:
- Verantwoordelijk voor het gehele proces
- Bepaalt procesdoelen en -strategie
- Zorgt voor resources
- Bewaakt procesprestaties

b) Procesmanager:
- Verantwoordelijk voor de dagelijkse uitvoering
- Coördineert activiteiten
- Lost problemen op
- Rapporteert aan proceseigenaar

c) Procesanalist:
- Analyseert en documenteert processen
- Identificeert verbetermogelijkheden
- Ontwikkelt procesmodellen
- Ondersteunt procesimplementatie

d) Procesdeelnemer:
- Voert activiteiten uit binnen het proces
- Volgt procedures
- Signaleert problemen
- Draagt bij aan verbetering

e) Process Office/Center of Excellence:
- Ontwikkelt processtandaarden
- Biedt methodologieën en tools
- Verzorgt training
- Faciliteert kennisdeling

12. PROCESSEN EN ORGANISATIESTRUCTUUR

De relatie tussen processen en organisatiestructuur volgens Thuis & Stuive:

a) Functionele organisatie:
- Georganiseerd rond functies/afdelingen
- Processen lopen door verschillende afdelingen
- Uitdaging: coördinatie tussen afdelingen

b) Procesgerichte organisatie:
- Georganiseerd rond processen
- Teams verantwoordelijk voor complete processen
- Voordeel: betere afstemming op klantbehoeften

c) Matrixorganisatie:
- Combinatie van functionele en procesgerichte structuur
- Medewerkers rapporteren aan functionele manager én procesmanager
- Uitdaging: dubbele aansturing

d) Evolutie naar procesgerichte organisatie:
1. Functionele silostructuur
2. Procesidentificatie
3. Procesverbetering
4. Horizontale teams
5. Procesgerichte structuur

13. PROCESSEN EN INFORMATIESYSTEMEN

De rol van informatiesystemen in processen volgens Thuis & Stuive:

a) Enterprise Resource Planning (ERP):
- Geïntegreerd systeem voor bedrijfsprocessen
- Modules voor verschillende functionele gebieden
- Gemeenschappelijke database

b) Customer Relationship Management (CRM):
- Beheer van klantinteracties
- Ondersteuning van verkoopprocessen
- Klantservice en -ondersteuning

c) Supply Chain Management (SCM):
- Beheer van goederenstroom
- Planning en forecasting
- Leveranciersmanagement

d) Business Intelligence (BI):
- Analyse van bedrijfsgegevens
- Dashboards en rapportages
- Ondersteuning van besluitvorming

e) Workflow Management Systems (WfMS):
- Coördineren de stroom van werk en informatie tussen processtappen
- Routeren taken naar de juiste personen
- Bewaken deadlines

f) Business Process Management Systems (BPMS):
- Geïntegreerde platforms voor het modelleren, implementeren, uitvoeren en monitoren van processen
- Combineren workflow, regels, integratie en analyse

14. PROCESSEN EN KWALITEITSMANAGEMENT

De relatie tussen processen en kwaliteitsmanagement volgens Thuis & Stuive:

a) ISO 9001:
- Internationale standaard voor kwaliteitsmanagementsystemen
- Procesgerichte benadering
- Eisen voor documentatie, uitvoering en verbetering van processen

b) Total Quality Management (TQM):
- Klantgerichte kwaliteitsfilosofie
- Continue verbetering van processen
- Betrokkenheid van alle medewerkers

c) Lean Six Sigma:
- Combinatie van Lean (elimineren verspilling) en Six Sigma (reduceren variatie)
- DMAIC-methodologie voor procesverbetering
- Data-gedreven aanpak

15. PROCESSEN EN RISICOMANAGEMENT

De integratie van risicomanagement in processen volgens Thuis & Stuive:

a) Procesrisico's:
- Operationele risico's
- Compliance risico's
- Financiële risico's
- Reputatierisico's

b) Risico-identificatie in processen:
- Procesanalyse
- Faalwijze- en effectenanalyse (FMEA)
- Risicosessies met stakeholders

c) Risicobeheersing in processen:
- Preventieve maatregelen
- Detectieve maatregelen
- Correctieve maatregelen
- Monitoring en rapportage
