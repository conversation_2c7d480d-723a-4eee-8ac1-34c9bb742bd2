-------------------------------------------
MANAGEMENTSAMENVATTING
-------------------------------------------

Euro Caps is een toonaangevende producent van koffiecapsules in Nederland die zich richt op het produceren van hoogwaardige koffiecapsules voor verschillende merken en retailers. In het kader van de digitale transformatie en procesoptimalisatie heeft Euro Caps behoefte aan een geïntegreerd databasesysteem dat alle kernprocessen van het bedrijf ondersteunt.

Dit project heeft als doel het ontwerpen en implementeren van een relationeel databasesysteem voor Euro Caps dat de volgende kernprocessen ondersteunt: grinding (malen), filling (vullen), packaging (verpakken), kwaliteitscontrole en logistiek. Het systeem moet alle relevante informatie over deze processen vastleggen, traceren en analyseren om de operationele efficiëntie te verbeteren en waardevolle inzichten te bieden voor besluitvorming.

De aanpak van het project bestond uit verschillende fasen:

1. **Analyse van informatiebehoeften**: Identificatie van de kernprocessen en de bijbehorende informatiebehoeften voor grinding, filling, packaging, kwaliteitscontrole en logistiek.

2. **Conceptueel ontwerp**: Ontwikkeling van een conceptueel Entity-Relationship Diagram (ERD) dat de belangrijkste entiteiten en hun relaties weergeeft.

3. **Logisch ontwerp**: Vertaling van het conceptuele ERD naar een logisch ERD met gedetailleerde attributen, primaire sleutels en relaties.

4. **Fysiek ontwerp**: Implementatie van het logische ERD in een MySQL-database met specifieke datatypen, constraints en indexen.

5. **Implementatie**: Ontwikkeling van Python-scripts voor het genereren en importeren van testdata, en het schrijven van SQL-queries voor operationele en KPI-inzichten.

De belangrijkste resultaten van het project zijn:

- Een volledig genormaliseerd databaseontwerp dat alle kernprocessen van Euro Caps ondersteunt
- Een set van SQL-queries die zowel operationele als KPI-inzichten bieden
- Python-scripts voor het genereren en importeren van testdata
- Een uitgebreide documentatie van het ontwerp, inclusief ERD's en ontwerpkeuzes

De implementatie van dit databasesysteem zal Euro Caps in staat stellen om:

- De traceerbaarheid van producten door de hele productieketen te verbeteren
- De efficiëntie van productieprocessen te monitoren en te optimaliseren
- De kwaliteitscontrole te verbeteren door gedetailleerde procesgegevens vast te leggen
- Betere beslissingen te nemen op basis van KPI's en operationele inzichten
- De communicatie met partners (leveranciers, klanten, transporteurs) te stroomlijnen

Het project heeft aangetoond dat een goed ontworpen relationeel databasesysteem een cruciale rol kan spelen in het optimaliseren van productieprocessen en het ondersteunen van besluitvorming in een productiebedrijf zoals Euro Caps.

-------------------------------------------
VOORWOORD
-------------------------------------------

Voor u ligt het verslag van het databaseontwerp voor Euro Caps, uitgevoerd als onderdeel van mijn studie. Dit project heeft mij de mogelijkheid geboden om theoretische kennis over databaseontwerp toe te passen op een praktische casus in de productiesector.

Euro Caps, als producent van koffiecapsules, staat voor de uitdaging om grote hoeveelheden data over hun productieprocessen efficiënt te beheren. Van het malen van koffiebonen tot het verpakken van de eindproducten, elk proces genereert waardevolle informatie die, indien goed gestructureerd en geanalyseerd, kan leiden tot significante verbeteringen in efficiëntie en kwaliteit.

De reis van conceptueel idee naar een volledig geïmplementeerd databasesysteem was uitdagend maar buitengewoon leerzaam. Het vertalen van bedrijfsprocessen naar een logisch datamodel vereiste niet alleen technische kennis, maar ook inzicht in de operationele aspecten van een productiebedrijf. Het was fascinerend om te zien hoe abstracte entiteiten en relaties uiteindelijk vorm kregen in een concreet systeem dat waardevolle inzichten kan opleveren.

Ik wil graag mijn dank uitspreken aan mijn docenten voor hun begeleiding en feedback tijdens dit project. Hun expertise en kritische blik hebben mij geholpen om mijn ontwerp te verfijnen en te verbeteren. Ook wil ik mijn medestudenten bedanken voor de inspirerende discussies en het delen van inzichten, wat heeft bijgedragen aan de kwaliteit van dit project.

Dit verslag beschrijft het volledige proces van analyse tot implementatie, inclusief de gemaakte ontwerpkeuzes en de lessen die ik heb geleerd. Ik hoop dat het niet alleen voldoet aan de academische vereisten, maar ook een praktische waarde heeft voor vergelijkbare projecten in de toekomst.

[Uw Naam]
[Datum]

-------------------------------------------
LITERATUURLIJST
-------------------------------------------

Connolly, T., & Begg, C. (2020). Database systems: A practical approach to design, implementation, and management (7th ed.). Pearson.

Date, C. J. (2019). Database design and relational theory: Normal forms and all that jazz (2nd ed.). Apress.

Elmasri, R., & Navathe, S. B. (2017). Fundamentals of database systems (7th ed.). Pearson.

Euro Caps. (n.d.). Euro Caps case study documents.

Garcia-Molina, H., Ullman, J. D., & Widom, J. (2020). Database systems: The complete book (3rd ed.). Pearson.

Hoffer, J. A., Ramesh, V., & Topi, H. (2016). Modern database management (12th ed.). Pearson.

Kimball, R., & Ross, M. (2013). The data warehouse toolkit: The definitive guide to dimensional modeling (3rd ed.). Wiley.

Kroenke, D. M., & Auer, D. J. (2016). Database processing: Fundamentals, design, and implementation (14th ed.). Pearson.

McKinney, W. (2017). Python for data analysis: Data wrangling with Pandas, NumPy, and IPython (2nd ed.). O'Reilly Media.

MySQL. (2023). MySQL 8.0 reference manual. https://dev.mysql.com/doc/refman/8.0/en/

Nintex. (n.d.). Process automation maximizes product quality at Euro Caps [PDF case study].

Oppel, A. (2011). Databases: A beginner's guide. McGraw-Hill Education.

Silberschatz, A., Korth, H. F., & Sudarshan, S. (2019). Database system concepts (7th ed.). McGraw-Hill Education.

Teorey, T. J., Lightstone, S. S., Nadeau, T., & Jagadish, H. V. (2011). Database modeling and design: Logical design (5th ed.). Morgan Kaufmann.

VanderPlas, J. (2016). Python data science handbook: Essential tools for working with data. O'Reilly Media.

Wambler, S. (2015). Agile data warehouse design: Collaborative dimensional modeling, from whiteboard to star schema. DecisionOne Press.
