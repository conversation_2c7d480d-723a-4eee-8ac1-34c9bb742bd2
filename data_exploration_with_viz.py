import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import re
import os

# Create a directory for saving visualizations
if not os.path.exists('visualizations'):
    os.makedirs('visualizations')

# Set display options for better readability
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.max_rows', 50)

# Load the CSV file
print("Loading the CSV file...")
df = pd.read_csv('Kikker.csv')

# Basic information about the dataset
print("\n=== BASIC INFORMATION ===")
print(f"Number of rows: {df.shape[0]}")
print(f"Number of columns: {df.shape[1]}")

# Display column names and their data types
print("\n=== COLUMNS AND DATA TYPES ===")
print(df.dtypes)

# Display first few rows to understand the data structure
print("\n=== FIRST 5 ROWS OF DATA ===")
print(df.head())

# Check for missing values
print("\n=== MISSING VALUES ===")
missing_values = df.isnull().sum()
missing_percent = (df.isnull().sum() / len(df)) * 100
missing_data = pd.concat([missing_values, missing_percent], axis=1)
missing_data.columns = ['Missing Values', 'Percentage']
missing_data_sorted = missing_data[missing_data['Missing Values'] > 0].sort_values('Percentage', ascending=False)
print(missing_data_sorted)

# Visualize missing values
plt.figure(figsize=(12, 8))
sns.barplot(x=missing_data_sorted.index, y='Percentage', data=missing_data_sorted)
plt.xticks(rotation=90)
plt.title('Percentage of Missing Values by Column')
plt.tight_layout()
plt.savefig('visualizations/missing_values.png')

# Check for unusual values
print("\n=== UNUSUAL VALUES CHECK ===")
# Check for unusual date formats
date_columns = ['Registratiedatum', 'FillingDatumTijdEind', 'PackagingDatumTijdEind', 
                'FillingDatumTijdStart', 'GrindingDatumTijdEind', 'GrindingDatumTijdStart',
                'PackagingDatumTijdStart', 'Laatste Audit']

print("Checking for unusual date formats...")
unusual_dates_count = {}
for col in date_columns:
    if col in df.columns:
        unusual_dates = df[col].astype(str).str.contains('31-02-2025|0000-00-00|onbekend', na=False).sum()
        if unusual_dates > 0:
            print(f"Column {col} has {unusual_dates} unusual date values")
            unusual_dates_count[col] = unusual_dates

# Visualize unusual dates
if unusual_dates_count:
    plt.figure(figsize=(10, 6))
    plt.bar(unusual_dates_count.keys(), unusual_dates_count.values())
    plt.xticks(rotation=90)
    plt.title('Number of Unusual Date Values by Column')
    plt.tight_layout()
    plt.savefig('visualizations/unusual_dates.png')

# Check for unusual numeric values
numeric_columns = ['Klantretourpercentage', 'Cost', 'Voorraadniveaus', 'Benuttingsgraad', 
                  'Cyclustijd', 'LeverancierLevertijd', 'Leveranciersbeoordeling', 
                  'Klanttevredenheid', 'Gewichtscontrole', 'Duurzaamheid Score', 
                  'CO2-Footprint', 'ProcessTime', 'Fair-Trade Score', 'Defectpercentage', 
                  'Energieverbruik']

print("\nChecking for unusual numeric values...")
unusual_values_count = {}
for col in numeric_columns:
    if col in df.columns:
        # Convert to string to handle mixed types
        col_str = df[col].astype(str)
        # Check for negative values, ERROR, NaN, etc.
        unusual_values = col_str.str.contains('-|ERROR|NaN|abc|onbekend|null', na=False).sum()
        if unusual_values > 0:
            print(f"Column {col} has {unusual_values} unusual values")
            print("Examples:", df[col_str.str.contains('-|ERROR|NaN|abc|onbekend|null', na=False)][col].unique()[:5])
            unusual_values_count[col] = unusual_values

# Visualize unusual numeric values
if unusual_values_count:
    plt.figure(figsize=(12, 8))
    plt.bar(unusual_values_count.keys(), unusual_values_count.values())
    plt.xticks(rotation=90)
    plt.title('Number of Unusual Numeric Values by Column')
    plt.tight_layout()
    plt.savefig('visualizations/unusual_numeric_values.png')

# Clean and convert percentage columns for analysis
print("\n=== CLEANING PERCENTAGE COLUMNS ===")
percentage_columns = ['Klantretourpercentage', 'Benuttingsgraad', 'Defectpercentage']
for col in percentage_columns:
    if col in df.columns and df[col].dtype == 'object':
        # Extract numeric values from percentage strings
        df[col + '_cleaned'] = df[col].str.extract(r'(\d+\.\d+|\d+)').astype(float) / 100
        print(f"Created cleaned column: {col}_cleaned")

# Summary statistics for numeric columns
print("\n=== SUMMARY STATISTICS ===")
# Get numeric columns (including newly created ones)
numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
if numeric_cols:
    print(df[numeric_cols].describe())

# Analyze distribution of key numeric variables
print("\n=== DISTRIBUTION OF KEY NUMERIC VARIABLES ===")
key_numeric_cols = [col for col in numeric_cols if 'cleaned' in col or col in ['Leveranciersbeoordeling', 'Fair-Trade Score']]
if key_numeric_cols:
    fig, axes = plt.subplots(len(key_numeric_cols), 1, figsize=(10, 4*len(key_numeric_cols)))
    if len(key_numeric_cols) == 1:
        axes = [axes]
    
    for i, col in enumerate(key_numeric_cols):
        sns.histplot(df[col].dropna(), ax=axes[i], kde=True)
        axes[i].set_title(f'Distribution of {col}')
    
    plt.tight_layout()
    plt.savefig('visualizations/numeric_distributions.png')

# Unique values for categorical columns
print("\n=== CATEGORICAL COLUMNS ===")
categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
categorical_counts = {}
for col in categorical_cols[:10]:  # Limit to first 10 to avoid too much output
    unique_values = df[col].nunique()
    print(f"{col}: {unique_values} unique values")
    if unique_values < 10:  # Only show all values if there are fewer than 10
        value_counts = df[col].value_counts()
        print(value_counts)
        categorical_counts[col] = value_counts
    print()

# Visualize top categorical variables
for col, counts in categorical_counts.items():
    plt.figure(figsize=(10, 6))
    counts.plot(kind='bar')
    plt.title(f'Distribution of {col}')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(f'visualizations/{col}_distribution.png')

# Analyze coffee bean types and their quality
print("\n=== COFFEE BEAN ANALYSIS ===")
if 'Koffieboon' in df.columns and 'Panel Test' in df.columns:
    bean_quality = pd.crosstab(df['Koffieboon'], df['Panel Test'])
    print("Coffee Bean Types vs. Quality Assessment:")
    print(bean_quality)
    
    # Visualize
    plt.figure(figsize=(10, 6))
    bean_quality.plot(kind='bar', stacked=True)
    plt.title('Coffee Bean Types vs. Quality Assessment')
    plt.xlabel('Coffee Bean Type')
    plt.ylabel('Count')
    plt.legend(title='Quality Assessment')
    plt.tight_layout()
    plt.savefig('visualizations/bean_quality.png')

# Analyze defect percentage by machine
print("\n=== DEFECT ANALYSIS BY MACHINE ===")
if 'PackagingApparaat' in df.columns and 'Defectpercentage_cleaned' in df.columns:
    plt.figure(figsize=(10, 6))
    sns.boxplot(x='PackagingApparaat', y='Defectpercentage_cleaned', data=df)
    plt.title('Defect Percentage by Packaging Machine')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('visualizations/defects_by_machine.png')

# Check for potential data quality issues
print("\n=== POTENTIAL DATA QUALITY ISSUES ===")
# Check for duplicate rows
duplicates = df.duplicated().sum()
print(f"Number of duplicate rows: {duplicates}")

# Check for outliers in numeric columns (using IQR method)
outliers_by_column = {}
for col in numeric_cols:
    try:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
        if outliers > 0:
            print(f"Column {col} has {outliers} outliers")
            outliers_by_column[col] = outliers
    except:
        print(f"Could not check for outliers in {col}")

# Visualize outliers
if outliers_by_column:
    plt.figure(figsize=(10, 6))
    plt.bar(outliers_by_column.keys(), outliers_by_column.values())
    plt.xticks(rotation=90)
    plt.title('Number of Outliers by Column')
    plt.tight_layout()
    plt.savefig('visualizations/outliers.png')

# Correlation analysis
print("\n=== CORRELATION ANALYSIS ===")
# Select only numeric columns for correlation
corr_cols = [col for col in numeric_cols if df[col].notna().sum() > df.shape[0] * 0.5]  # Only include columns with at least 50% non-NA values
if len(corr_cols) > 1:
    corr_matrix = df[corr_cols].corr()
    print("Correlation Matrix:")
    print(corr_matrix)
    
    # Visualize correlation matrix
    plt.figure(figsize=(12, 10))
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt=".2f", linewidths=0.5)
    plt.title('Correlation Matrix of Numeric Variables')
    plt.tight_layout()
    plt.savefig('visualizations/correlation_matrix.png')

print("\nData exploration complete!")
print("Visualizations saved in the 'visualizations' folder.")
