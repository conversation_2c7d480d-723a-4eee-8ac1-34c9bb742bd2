# ************************************************************************************************
# Enhanced Customer List Screen for EuroCaps Ordering System
# Based on detailed mockup specifications with full CRUD functionality
# Color Scheme: Header/Sidebar: 2C3E50, Background: 1B3A4B, Sections: A9C6E8, Actions: F39C12, Text: FFFFFF
# ************************************************************************************************
Screens:
  Customer_List_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(243, 156, 18, 1)
      Fill: =RGBA(27, 58, 75, 1)
      OnVisible: |
        =// Initialize search and filter variables
        Set(varSearchText, "");
        Set(varFilterStatus, "All");
        Set(varSortBy, "Name");
        Set(varSelectedCustomer, Blank());
        // Refresh customer data
        Refresh(colCustomers)
    Children:
      # Header Bar
      - HeaderBar:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(44, 62, 80, 1)
            Height: =60
            Width: =1366
            X: =0
            Y: =0
            BorderThickness: =0

      # Logo in Header
      - HeaderLogo:
          Control: Image@2.1.0
          Properties:
            Image: ="eurocaps-logo"
            Height: =40
            Width: =40
            X: =20
            Y: =10

      # App Title in Header
      - HeaderTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="EuroCaps Order Management Pro"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =300
            X: =70
            Y: =10

      # User Menu
      - UserMenu:
          Control: Classic/Button@2.2.0
          Properties:
            Text: =varUserName & " (" & varUserRole & ") ▼"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =200
            X: =1100
            Y: =10
            OnSelect: =Navigate(Settings_Screen, ScreenTransition.Fade)

      # Navigation Menu
      - NavigationMenu:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(44, 62, 80, 1)
            Height: =708
            Width: =200
            X: =0
            Y: =60
            BorderThickness: =0

      # Menu Items
      - MenuDashboard:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="≡ Dashboard"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =60
            Align: =Align.Left
            OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)

      - MenuCustomers:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="👥 Customers"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =110
            Align: =Align.Left

      - MenuProducts:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📦 Products"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =160
            Align: =Align.Left
            OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)

      - MenuOrders:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📋 Orders"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =210
            Align: =Align.Left
            OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade)

      # Main Content Area
      - MainContent:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(27, 58, 75, 1)
            Height: =708
            Width: =1166
            X: =200
            Y: =60
            BorderThickness: =0

      # Page Title
      - PageTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="Customer Management"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =18
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =300
            X: =220
            Y: =80

      # Search Section
      - SearchSection:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =80
            Width: =1100
            X: =220
            Y: =130
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Search Input
      - SearchInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            Default: =varSearchText
            HintText: ="Search customers by name, contact, or email..."
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =300
            X: =240
            Y: =145
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varSearchText, SearchInput.Text)

      # Filter Dropdown
      - FilterDropdown:
          Control: Classic/Dropdown@2.2.0
          Properties:
            Items: =["All", "Active", "Inactive"]
            DefaultSelectedItems: =["All"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =120
            X: =560
            Y: =145
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varFilterStatus, FilterDropdown.Selected.Value)

      # Sort Dropdown
      - SortDropdown:
          Control: Classic/Dropdown@2.2.0
          Properties:
            Items: =["Name", "Contact", "Email", "Recent"]
            DefaultSelectedItems: =["Name"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =120
            X: =700
            Y: =145
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varSortBy, SortDropdown.Selected.Value)

      # Reset Filters Button
      - ResetFiltersButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="Reset"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =35
            Width: =80
            X: =840
            Y: =145
            OnSelect: |
              =Set(varSearchText, "");
              Set(varFilterStatus, "All");
              Set(varSortBy, "Name");
              Reset(SearchInput);
              Reset(FilterDropdown);
              Reset(SortDropdown)

      # New Customer Button
      - NewCustomerButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="+ New Customer"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =35
            Width: =150
            X: =1150
            Y: =145
            OnSelect: =Navigate(Customer_Details_Screen, ScreenTransition.Fade, {Mode: "New"})

      # Customer List Gallery
      - CustomerGallery:
          Control: Gallery@2.3.0
          Properties:
            Items: |
              =SortByColumns(
                  Filter(
                      colCustomers,
                      (IsBlank(varSearchText) || 
                       varSearchText in CustomerName || 
                       varSearchText in ContactPerson || 
                       varSearchText in Email) &&
                      (varFilterStatus = "All" || 
                       (varFilterStatus = "Active" && Status = "Active") ||
                       (varFilterStatus = "Inactive" && Status = "Inactive"))
                  ),
                  Switch(varSortBy,
                      "Name", "CustomerName",
                      "Contact", "ContactPerson", 
                      "Email", "Email",
                      "Recent", "LastModified",
                      "CustomerName"
                  ),
                  If(varSortBy = "Recent", Descending, Ascending)
              )
            Height: =400
            Width: =1100
            X: =220
            Y: =230
            TemplateSize: =80
            Fill: =RGBA(0, 0, 0, 0)
            BorderThickness: =0
            ShowScrollbar: =true
            TemplateFill: =RGBA(169, 198, 232, 1)
            TemplateSize: =80

      # Customer Gallery Template Items (inside CustomerGallery)
      # Customer Card Background
      - CustomerCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =70
            Width: =1080
            X: =10
            Y: =5
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =1
            RadiusTopLeft: =4
            RadiusTopRight: =4
            RadiusBottomLeft: =4
            RadiusBottomRight: =4

      # Customer Name
      - CustomerNameLabel:
          Control: Label@2.5.1
          Properties:
            Text: =ThisItem.CustomerName
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =200
            X: =20
            Y: =15

      # Contact Person
      - ContactPersonLabel:
          Control: Label@2.5.1
          Properties:
            Text: =ThisItem.ContactPerson
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =20
            Width: =200
            X: =20
            Y: =40

      # Email
      - EmailLabel:
          Control: Label@2.5.1
          Properties:
            Text: =ThisItem.Email
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =20
            Width: =250
            X: =240
            Y: =25

      # Phone
      - PhoneLabel:
          Control: Label@2.5.1
          Properties:
            Text: =ThisItem.Phone
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =20
            Width: =150
            X: =510
            Y: =25

      # Action Buttons
      # View Button
      - ViewButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="👁️ View"
            Font: =Font.Arial
            Size: =10
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =25
            Width: =70
            X: =680
            Y: =15
            OnSelect: |
              =Set(varSelectedCustomer, ThisItem);
              Navigate(Customer_Details_Screen, ScreenTransition.Fade, {Mode: "View", Customer: ThisItem})

      # Edit Button
      - EditButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📝 Edit"
            Font: =Font.Arial
            Size: =10
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =25
            Width: =70
            X: =760
            Y: =15
            OnSelect: |
              =Set(varSelectedCustomer, ThisItem);
              Navigate(Customer_Details_Screen, ScreenTransition.Fade, {Mode: "Edit", Customer: ThisItem})

      # New Order Button
      - NewOrderButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="🛒 Order"
            Font: =Font.Arial
            Size: =10
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =25
            Width: =70
            X: =840
            Y: =15
            OnSelect: |
              =Set(varSelectedCustomer, ThisItem);
              Navigate(New_Order_Screen, ScreenTransition.Fade, {SelectedCustomer: ThisItem})

      # Pagination Section
      - PaginationSection:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =50
            Width: =1100
            X: =220
            Y: =650
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Pagination Info
      - PaginationInfo:
          Control: Label@2.5.1
          Properties:
            Text: ="Showing " & CountRows(CustomerGallery.AllItems) & " customers"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =30
            Width: =200
            X: =240
            Y: =660

      # Previous Button
      - PreviousButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="◀ Previous"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =30
            Width: =100
            X: =1000
            Y: =660
            DisplayMode: =If(CustomerGallery.FirstVisibleItem > 1, DisplayMode.Edit, DisplayMode.Disabled)

      # Next Button
      - NextButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="Next ▶"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =30
            Width: =100
            X: =1110
            Y: =660
            DisplayMode: =If(CustomerGallery.FirstVisibleItem + CustomerGallery.VisibleItemCount < CountRows(CustomerGallery.AllItems), DisplayMode.Edit, DisplayMode.Disabled)

