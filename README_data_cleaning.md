# Kikker.csv Data Cleaning

This project contains scripts to clean the Kikker.csv dataset by addressing various data quality issues.

## Files

- `data_cleaning.py`: Main script that cleans the Kikker.csv data and generates a cleaned version
- `test_data_cleaning.py`: Script to test the cleaning process and compare original vs. cleaned data

## Data Issues Addressed

The cleaning process addresses the following data quality issues:

1. **Missing Values**
   - Fills missing numeric values with median values
   - Fills missing categorical values with mode values
   - Leaves missing dates as NaT (Not a Time)

2. **Invalid Date Formats**
   - Fixes invalid dates like "31-02-2025 25:61:61"
   - Converts all date columns to proper datetime format

3. **Unrealistic Values**
   - Corrects negative values in columns like Cost, Cyclustijd
   - Fixes extremely high values in Energieverbruik (999999 kWh)
   - Handles "ERROR" and other text values in numeric columns

4. **Inconsistent Data**
   - Standardizes percentage columns by extracting numeric values
   - Standardizes categorical columns with consistent values
   - Fixes "Onbekend apparaat" in PackagingApparaat

5. **Duplicates**
   - Removes exact duplicate rows
   - Handles duplicate Batchnr values by keeping only the first occurrence

6. **Encoding Issues**
   - Fixes encoding problems in text columns (e.g., "BraziliÃ«" → "Brazilië")

## Usage

### Running the Data Cleaning Script

To clean the Kikker.csv data and generate a cleaned version:

```bash
python data_cleaning.py
```

This will:
1. Read the original Kikker.csv file
2. Apply all cleaning steps
3. Save the cleaned data to Kikker_cleaned.csv

### Testing and Comparing Results

To run the test script that compares the original and cleaned data:

```bash
python test_data_cleaning.py
```

This will:
1. Run the cleaning process
2. Load both original and cleaned datasets
3. Display comparison statistics
4. Validate the cleaned data for remaining issues

## Customization

You can modify the cleaning parameters by editing the functions in `data_cleaning.py`:

- Adjust thresholds for unrealistic values
- Change how missing values are handled
- Modify the list of valid categorical values
- Add additional encoding fixes

## Requirements

- Python 3.6+
- pandas
- numpy
- matplotlib (for test script)
- seaborn (for test script)
