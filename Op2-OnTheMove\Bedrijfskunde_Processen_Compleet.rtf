{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Arial;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qj\f0\fs24\lang19 \b BEDRIJFSKUNDE: PROCESSEN\b0\par

\pard\sa200\sl276\slmult1\qj Dit document bevat een samenvatting van alle belangrijke processen uit het boek van Peter Thuis & Rienk Stuive (3e druk) en de slides van Bedrijfskunde Lesweek 2.1 t/m 2.7.\par

\pard\sa200\sl276\slmult1\qj\b 1. BASISCONCEPTEN PROCESSEN\b0\par

\pard\sa200\sl276\slmult1\qj Een proces is een reeks van logisch samenhangende activiteiten om een bepaald doel te bereiken. Processen transformeren inputs naar outputs die waarde hebben voor klanten.\par

\pard\sa200\sl276\slmult1\qj\b Input-Throughput-Output (ITO) model:\b0\par

\pard\sa200\sl276\slmult1\qj - Input: grondstoffen, arbeid, informatie, kapitaal, energie\par
- Throughput: transformatieproces (productie, dienstverlening, informatieverwerking)\par
- Output: producten, diensten, informatie\par
- Feedback: informatie over output die wordt gebruikt om het proces te verbeteren\par

\pard\sa200\sl276\slmult1\qj\b 2. TYPES BEDRIJFSPROCESSEN\b0\par

\pard\sa200\sl276\slmult1\qj Bedrijfsprocessen worden onderverdeeld in drie hoofdcategorieën:\par

\pard\sa200\sl276\slmult1\qj\b Primaire processen:\b0\par
- Inkoop\par
- Productie\par
- Verkoop\par
- Distributie\par
- Klantenservice\par

\pard\sa200\sl276\slmult1\qj\b Ondersteunende processen:\b0\par
- Personeelsbeheer (HRM)\par
- Financiële administratie\par
- IT-ondersteuning\par
- Facilitaire diensten\par
- Juridische zaken\par

\pard\sa200\sl276\slmult1\qj\b Managementprocessen:\b0\par
- Strategische planning\par
- Budgettering\par
- Prestatiemanagement\par
- Risicomanagement\par
- Kwaliteitsmanagement\par

\pard\sa200\sl276\slmult1\qj\b 3. PRIMAIRE PROCESSEN\b0\par

\pard\sa200\sl276\slmult1\qj\b Inkoopproces:\b0\par
1. Specificeren van behoeften\par
2. Selecteren van leveranciers\par
3. Onderhandelen en contracteren\par
4. Bestellen\par
5. Bewaken van leveringen\par
6. Nazorg en evaluatie\par

\pard\sa200\sl276\slmult1\qj\b Productieproces:\b0\par
1. Productieplanning\par
2. Werkvoorbereiding\par
3. Productie-uitvoering\par
4. Kwaliteitscontrole\par
5. Verpakking\par
6. Opslag\par

\pard\sa200\sl276\slmult1\qj\b Verkoopproces:\b0\par
1. Prospectie (zoeken naar potentiële klanten)\par
2. Voorbereiding (verzamelen informatie over klant)\par
3. Benadering (eerste contact)\par
4. Presentatie (demonstratie product/dienst)\par
5. Behandelen bezwaren\par
6. Afsluiting (verkoop)\par
7. Follow-up (nazorg)\par

\pard\sa200\sl276\slmult1\qj\b Distributieproces:\b0\par
1. Orderontvangst\par
2. Orderpicking\par
3. Verpakking\par
4. Verzending\par
5. Transport\par
6. Aflevering\par

\pard\sa200\sl276\slmult1\qj\b Klantenserviceproces:\b0\par
1. Ontvangst klantvraag/klacht\par
2. Registratie\par
3. Analyse\par
4. Oplossing\par
5. Terugkoppeling naar klant\par
6. Evaluatie en verbetering\par

\pard\sa200\sl276\slmult1\qj\b 4. ONDERSTEUNENDE PROCESSEN\b0\par

\pard\sa200\sl276\slmult1\qj\b HRM-processen:\b0\par
1. Personeelsplanning\par
2. Werving en selectie\par
3. Introductie nieuwe medewerkers\par
4. Training en ontwikkeling\par
5. Prestatiebeoordeling\par
6. Beloning\par
7. Uitstroom\par

\pard\sa200\sl276\slmult1\qj\b Financiële processen:\b0\par
1. Budgettering\par
2. Boekhouding\par
3. Facturering\par
4. Debiteurenadministratie\par
5. Crediteurenadministratie\par
6. Salarisadministratie\par
7. Financiële rapportage\par

\pard\sa200\sl276\slmult1\qj\b IT-processen:\b0\par
1. IT-planning\par
2. Systeemontwikkeling\par
3. Implementatie\par
4. Beheer en onderhoud\par
5. Gebruikersondersteuning\par
6. Beveiliging\par
7. Disaster recovery\par

\pard\sa200\sl276\slmult1\qj\b Facilitaire processen:\b0\par
1. Gebouwbeheer\par
2. Onderhoud\par
3. Beveiliging\par
4. Schoonmaak\par
5. Catering\par
6. Inkoop facilitaire middelen\par

\pard\sa200\sl276\slmult1\qj\b Juridische processen:\b0\par
1. Contractbeheer\par
2. Compliance\par
3. Intellectueel eigendom\par
4. Juridisch advies\par
5. Geschillenafhandeling\par

\pard\sa200\sl276\slmult1\qj\b 5. MANAGEMENTPROCESSEN\b0\par

\pard\sa200\sl276\slmult1\qj\b Strategische planningsproces:\b0\par
1. Analyse externe omgeving\par
2. Analyse interne omgeving\par
3. Formuleren missie en visie\par
4. Bepalen strategische doelen\par
5. Ontwikkelen strategieën\par
6. Implementatie\par
7. Evaluatie en bijsturing\par

\pard\sa200\sl276\slmult1\qj\b Budgetteringsproces:\b0\par
1. Opstellen budgetrichtlijnen\par
2. Verzamelen budgetaanvragen\par
3. Beoordelen aanvragen\par
4. Consolideren budgetten\par
5. Goedkeuren budget\par
6. Communiceren budget\par
7. Monitoren en bijsturen\par

\pard\sa200\sl276\slmult1\qj\b Prestatiemanagementproces:\b0\par
1. Bepalen KPI's (Key Performance Indicators)\par
2. Meten prestaties\par
3. Analyseren resultaten\par
4. Rapporteren\par
5. Feedback geven\par
6. Verbeteracties definiëren\par
7. Implementeren verbeteringen\par

\pard\sa200\sl276\slmult1\qj\b Risicomanagementproces:\b0\par
1. Identificeren risico's\par
2. Analyseren risico's\par
3. Evalueren risico's\par
4. Behandelen risico's\par
5. Monitoren en reviewen\par
6. Communiceren en consulteren\par

\pard\sa200\sl276\slmult1\qj\b Kwaliteitsmanagementproces:\b0\par
1. Kwaliteitsplanning\par
2. Kwaliteitsborging\par
3. Kwaliteitscontrole\par
4. Kwaliteitsverbetering\par

\pard\sa200\sl276\slmult1\qj\b 6. PROCESVISUALISATIE\b0\par

\pard\sa200\sl276\slmult1\qj Verschillende methoden om processen visueel weer te geven:\par

\pard\sa200\sl276\slmult1\qj\b Flowcharts:\b0\par
- Tonen de stappen in een proces met symbolen en pijlen\par
- Geven een duidelijk overzicht van de volgorde van activiteiten\par

\pard\sa200\sl276\slmult1\qj\b Swimlane-diagrammen:\b0\par
- Tonen niet alleen de processtappen, maar ook welke afdeling of persoon verantwoordelijk is\par
- Nuttig voor processen die meerdere afdelingen omvatten\par

\pard\sa200\sl276\slmult1\qj\b Value Stream Maps:\b0\par
- Tonen de stroom van materialen en informatie, evenals wachttijden en voorraden\par
- Worden gebruikt in Lean management om verspilling te identificeren\par

\pard\sa200\sl276\slmult1\qj\b BPMN (Business Process Model and Notation):\b0\par
- Gestandaardiseerde notatie voor het modelleren van bedrijfsprocessen\par
- Biedt een rijke set symbolen voor verschillende aspecten van processen\par

\pard\sa200\sl276\slmult1\qj\b 7. PROCESVERBETERING\b0\par

\pard\sa200\sl276\slmult1\qj Verschillende methodologieën voor procesverbetering:\par

\pard\sa200\sl276\slmult1\qj\b Lean:\b0\par
- Gericht op het elimineren van verspilling (muda)\par
- Acht soorten verspilling: overproductie, wachttijd, transport, overbewerking, voorraad, beweging, defecten, onbenutte creativiteit\par
- Tools: Value Stream Mapping, 5S, Kaizen, Kanban\par

\pard\sa200\sl276\slmult1\qj\b Six Sigma:\b0\par
- Gericht op het verminderen van variatie en defecten\par
- DMAIC-methodologie: Define, Measure, Analyze, Improve, Control\par
- Statistische methoden om processen te analyseren en te verbeteren\par

\pard\sa200\sl276\slmult1\qj\b Business Process Reengineering (BPR):\b0\par
- Radicaal herontwerpen van processen\par
- "Clean sheet" benadering\par
- Gericht op dramatische verbeteringen\par

\pard\sa200\sl276\slmult1\qj\b Kaizen:\b0\par
- Continue, incrementele verbeteringen\par
- Betrokkenheid van alle medewerkers\par
- Kleine, dagelijkse verbeteringen leiden op lange termijn tot grote resultaten\par

\pard\sa200\sl276\slmult1\qj\b 8. PROCESMANAGEMENT\b0\par

\pard\sa200\sl276\slmult1\qj Procesmanagement omvat alle activiteiten die nodig zijn om processen te ontwerpen, implementeren, monitoren en verbeteren:\par

\pard\sa200\sl276\slmult1\qj\b Procesontwerp:\b0\par
- Definiëren van stappen, rollen, regels en resources\par
- Bepalen van inputs, outputs en prestatie-indicatoren\par
- Documenteren van het proces\par

\pard\sa200\sl276\slmult1\qj\b Procesimplementatie:\b0\par
- In gebruik nemen van een nieuw of gewijzigd proces\par
- Training van medewerkers\par
- Aanpassing van systemen\par
- Communicatie over het proces\par

\pard\sa200\sl276\slmult1\qj\b Procesmonitoring:\b0\par
- Meten van procesprestaties met KPI's\par
- Analyseren van resultaten\par
- Identificeren van afwijkingen\par
- Rapporteren over procesprestaties\par

\pard\sa200\sl276\slmult1\qj\b Procesverbetering:\b0\par
- Identificeren van verbetermogelijkheden\par
- Analyseren van oorzaken van problemen\par
- Ontwikkelen van verbetervoorstellen\par
- Implementeren van verbeteringen\par
- Evalueren van resultaten\par

\pard\sa200\sl276\slmult1\qj\b Procesgovernance:\b0\par
- Definiëren van rollen en verantwoordelijkheden\par
- Bepalen van besluitvormingsprocessen\par
- Vastleggen van processtandaarden\par
- Beheren van proceswijzigingen\par

\pard\sa200\sl276\slmult1\qj\b 9. PROCESAUTOMATISERING\b0\par

\pard\sa200\sl276\slmult1\qj Technologieën voor procesautomatisering:\par

\pard\sa200\sl276\slmult1\qj\b Workflow Management Systems (WfMS):\b0\par
- Coördineren de stroom van werk en informatie tussen processtappen\par
- Routeren taken naar de juiste personen\par
- Bewaken deadlines\par

\pard\sa200\sl276\slmult1\qj\b Business Process Management Systems (BPMS):\b0\par
- Geïntegreerde platforms voor het modelleren, implementeren, uitvoeren en monitoren van processen\par
- Combineren workflow, regels, integratie en analyse\par

\pard\sa200\sl276\slmult1\qj\b Robotic Process Automation (RPA):\b0\par
- Software die menselijke interacties met digitale systemen nabootst\par
- Automatiseren van repetitieve taken\par
- Werkt op gebruikersinterface-niveau\par

\pard\sa200\sl276\slmult1\qj\b Artificial Intelligence (AI):\b0\par
- Machine learning voor patroonherkenning en voorspellingen\par
- Natuurlijke taalverwerking voor communicatie\par
- Computer vision voor beeldherkenning\par
- Beslissingsondersteuning\par

\pard\sa200\sl276\slmult1\qj\b 10. PROCESARCHITECTUUR\b0\par

\pard\sa200\sl276\slmult1\qj Procesarchitectuur is het raamwerk dat de structuur en relaties tussen alle processen in een organisatie beschrijft:\par

\pard\sa200\sl276\slmult1\qj\b Componenten van procesarchitectuur:\b0\par
- Proceslandschap: overzicht van alle processen\par
- Procesmodellen: gedetailleerde beschrijvingen van processen\par
- Procesrollen: verantwoordelijkheden in processen\par
- Procesregels: richtlijnen voor processen\par
- Procesmetrieken: indicatoren voor procesprestaties\par

\pard\sa200\sl276\slmult1\qj\b Voordelen van procesarchitectuur:\b0\par
- Afstemming tussen processen en strategie\par
- Vermindering van duplicatie en inconsistenties\par
- Identificatie van verbetermogelijkheden\par
- Effectieve communicatie over processen\par
- Ondersteuning van veranderingsmanagement\par

\pard\sa200\sl276\slmult1\qj\b 11. PROCESVOLWASSENHEID\b0\par

\pard\sa200\sl276\slmult1\qj Procesvolwassenheid verwijst naar de mate waarin processen zijn gedefinieerd, beheerd, gemeten en geoptimaliseerd:\par

\pard\sa200\sl276\slmult1\qj\b Capability Maturity Model Integration (CMMI):\b0\par
1. Initial: processen zijn ad hoc en chaotisch\par
2. Managed: processen zijn gepland en uitgevoerd volgens beleid\par
3. Defined: processen zijn goed gedefinieerd en gestandaardiseerd\par
4. Quantitatively Managed: processen worden gemeten en gecontroleerd\par
5. Optimizing: focus op continue verbetering\par

\pard\sa200\sl276\slmult1\qj\b Process and Enterprise Maturity Model (PEMM):\b0\par
- Procesvolwassenheid: design, performers, owner, infrastructure, metrics\par
- Organisatievolwassenheid: leadership, culture, expertise, governance\par

\pard\sa200\sl276\slmult1\qj\b 12. PROCESINTEGRATIE\b0\par

\pard\sa200\sl276\slmult1\qj Procesintegratie is het verbinden van verschillende processen binnen en tussen organisaties:\par

\pard\sa200\sl276\slmult1\qj\b Enterprise Application Integration (EAI):\b0\par
- Verbinden van verschillende applicaties en systemen\par
- Mogelijk maken van gegevensuitwisseling\par

\pard\sa200\sl276\slmult1\qj\b Service-Oriented Architecture (SOA):\b0\par
- Functionaliteit aangeboden als services\par
- Services kunnen worden gebruikt door verschillende processen\par

\pard\sa200\sl276\slmult1\qj\b API's (Application Programming Interfaces):\b0\par
- Interfaces voor communicatie tussen systemen\par
- Standaardiseren van gegevensuitwisseling\par

\pard\sa200\sl276\slmult1\qj\b Supply Chain Integration:\b0\par
- Integratie van processen over organisatiegrenzen heen\par
- Delen van informatie met leveranciers en klanten\par
- Gezamenlijke planning en forecasting\par

\pard\sa200\sl276\slmult1\qj\b 13. PROCESBESTURING\b0\par

\pard\sa200\sl276\slmult1\qj Procesbesturing omvat het plannen, uitvoeren, controleren en bijsturen van processen:\par

\pard\sa200\sl276\slmult1\qj\b Plan-Do-Check-Act (PDCA) cyclus:\b0\par
- Plan: doelen stellen en processen plannen\par
- Do: processen uitvoeren\par
- Check: resultaten meten en analyseren\par
- Act: processen verbeteren\par

\pard\sa200\sl276\slmult1\qj\b Regelkringen:\b0\par
- Meten van procesoutput\par
- Vergelijken met norm\par
- Bijsturen bij afwijkingen\par

\pard\sa200\sl276\slmult1\qj\b Prestatie-indicatoren:\b0\par
- Effectiviteit: mate waarin doelen worden bereikt\par
- Efficiëntie: verhouding tussen output en input\par
- Kwaliteit: mate waarin aan eisen wordt voldaan\par
- Flexibiliteit: vermogen om aan te passen aan veranderingen\par
- Innovatie: vermogen om te vernieuwen\par

\pard\sa200\sl276\slmult1\qj\b 14. PROCESCULTUUR\b0\par

\pard\sa200\sl276\slmult1\qj Procescultuur is de set van waarden, overtuigingen en gedragingen die de manier waarop processen worden uitgevoerd en verbeterd beïnvloeden:\par

\pard\sa200\sl276\slmult1\qj\b Kenmerken van een sterke procescultuur:\b0\par
- Procesgericht denken\par
- Klantgerichtheid\par
- Continue verbetering\par
- Samenwerking over afdelingsgrenzen heen\par
- Transparantie en openheid\par
- Leren van fouten\par
- Eigenaarschap en verantwoordelijkheid\par

\pard\sa200\sl276\slmult1\qj\b Ontwikkelen van een procescultuur:\b0\par
- Leiderschap en voorbeeldgedrag\par
- Training en bewustwording\par
- Beloning en erkenning\par
- Communicatie over processen\par
- Betrokkenheid van medewerkers\par

\pard\sa200\sl276\slmult1\qj\b 15. PROCESROLLEN\b0\par

\pard\sa200\sl276\slmult1\qj Verschillende rollen in procesmanagement:\par

\pard\sa200\sl276\slmult1\qj\b Proceseigenaar:\b0\par
- Verantwoordelijk voor het gehele proces\par
- Bepaalt procesdoelen en -strategie\par
- Zorgt voor resources\par
- Bewaakt procesprestaties\par

\pard\sa200\sl276\slmult1\qj\b Procesmanager:\b0\par
- Verantwoordelijk voor de dagelijkse uitvoering\par
- Coördineert activiteiten\par
- Lost problemen op\par
- Rapporteert aan proceseigenaar\par

\pard\sa200\sl276\slmult1\qj\b Procesanalist:\b0\par
- Analyseert en documenteert processen\par
- Identificeert verbetermogelijkheden\par
- Ontwikkelt procesmodellen\par
- Ondersteunt procesimplementatie\par

\pard\sa200\sl276\slmult1\qj\b Procesdeelnemer:\b0\par
- Voert activiteiten uit binnen het proces\par
- Volgt procedures\par
- Signaleert problemen\par
- Draagt bij aan verbetering\par

\pard\sa200\sl276\slmult1\qj\b Process Office/Center of Excellence:\b0\par
- Ontwikkelt processtandaarden\par
- Biedt methodologieën en tools\par
- Verzorgt training\par
- Faciliteert kennisdeling\par

\pard\sa200\sl276\slmult1\qj\b 16. PROCESINNOVATIE\b0\par

\pard\sa200\sl276\slmult1\qj Procesinnovatie is het ontwikkelen en implementeren van nieuwe of significant verbeterde processen:\par

\pard\sa200\sl276\slmult1\qj\b Incrementele innovatie:\b0\par
- Geleidelijke verbetering van bestaande processen\par
- Kleine aanpassingen\par
- Relatief laag risico\par

\pard\sa200\sl276\slmult1\qj\b Radicale innovatie:\b0\par
- Fundamentele verandering van processen\par
- Nieuwe technologieën of concepten\par
- Hoger risico maar potentieel grotere voordelen\par

\pard\sa200\sl276\slmult1\qj\b Disruptieve innovatie:\b0\par
- Doorbreekt bestaande procesparadigma's\par
- Creëert nieuwe markten of businessmodellen\par
- Kan bestaande processen overbodig maken\par

\pard\sa200\sl276\slmult1\qj\b Drivers voor procesinnovatie:\b0\par
- Technologische ontwikkelingen\par
- Veranderende klantbehoeften\par
- Concurrentiedruk\par
- Regelgeving\par
- Duurzaamheidseisen\par

\pard\sa200\sl276\slmult1\qj\b 17. PROCESSEN EN ORGANISATIESTRUCTUUR\b0\par

\pard\sa200\sl276\slmult1\qj De relatie tussen processen en organisatiestructuur:\par

\pard\sa200\sl276\slmult1\qj\b Functionele organisatie:\b0\par
- Georganiseerd rond functies/afdelingen\par
- Processen lopen door verschillende afdelingen\par
- Uitdaging: coördinatie tussen afdelingen\par

\pard\sa200\sl276\slmult1\qj\b Procesgerichte organisatie:\b0\par
- Georganiseerd rond processen\par
- Teams verantwoordelijk voor complete processen\par
- Voordeel: betere afstemming op klantbehoeften\par

\pard\sa200\sl276\slmult1\qj\b Matrixorganisatie:\b0\par
- Combinatie van functionele en procesgerichte structuur\par
- Medewerkers rapporteren aan functionele manager én procesmanager\par
- Uitdaging: dubbele aansturing\par

\pard\sa200\sl276\slmult1\qj\b Evolutie naar procesgerichte organisatie:\b0\par
1. Functionele silostructuur\par
2. Procesidentificatie\par
3. Procesverbetering\par
4. Horizontale teams\par
5. Procesgerichte structuur\par

\pard\sa200\sl276\slmult1\qj\b 18. PROCESSEN EN INFORMATIESYSTEMEN\b0\par

\pard\sa200\sl276\slmult1\qj De rol van informatiesystemen in processen:\par

\pard\sa200\sl276\slmult1\qj\b Enterprise Resource Planning (ERP):\b0\par
- Geïntegreerd systeem voor bedrijfsprocessen\par
- Modules voor verschillende functionele gebieden\par
- Gemeenschappelijke database\par

\pard\sa200\sl276\slmult1\qj\b Customer Relationship Management (CRM):\b0\par
- Beheer van klantinteracties\par
- Ondersteuning van verkoopprocessen\par
- Klantservice en -ondersteuning\par

\pard\sa200\sl276\slmult1\qj\b Supply Chain Management (SCM):\b0\par
- Beheer van goederenstroom\par
- Planning en forecasting\par
- Leveranciersmanagement\par

\pard\sa200\sl276\slmult1\qj\b Business Intelligence (BI):\b0\par
- Analyse van bedrijfsgegevens\par
- Dashboards en rapportages\par
- Ondersteuning van besluitvorming\par

\pard\sa200\sl276\slmult1\qj\b 19. PROCESSEN EN KWALITEITSMANAGEMENT\b0\par

\pard\sa200\sl276\slmult1\qj De relatie tussen processen en kwaliteitsmanagement:\par

\pard\sa200\sl276\slmult1\qj\b ISO 9001:\b0\par
- Internationale standaard voor kwaliteitsmanagementsystemen\par
- Procesgerichte benadering\par
- Eisen voor documentatie, uitvoering en verbetering van processen\par

\pard\sa200\sl276\slmult1\qj\b Total Quality Management (TQM):\b0\par
- Klantgerichte kwaliteitsfilosofie\par
- Continue verbetering van processen\par
- Betrokkenheid van alle medewerkers\par

\pard\sa200\sl276\slmult1\qj\b Lean Six Sigma:\b0\par
- Combinatie van Lean (elimineren verspilling) en Six Sigma (reduceren variatie)\par
- DMAIC-methodologie voor procesverbetering\par
- Data-gedreven aanpak\par

\pard\sa200\sl276\slmult1\qj\b 20. PROCESSEN EN RISICOMANAGEMENT\b0\par

\pard\sa200\sl276\slmult1\qj De integratie van risicomanagement in processen:\par

\pard\sa200\sl276\slmult1\qj\b Procesrisico's:\b0\par
- Operationele risico's\par
- Compliance risico's\par
- Financiële risico's\par
- Reputatierisico's\par

\pard\sa200\sl276\slmult1\qj\b Risico-identificatie in processen:\b0\par
- Procesanalyse\par
- Faalwijze- en effectenanalyse (FMEA)\par
- Risicosessies met stakeholders\par

\pard\sa200\sl276\slmult1\qj\b Risicobeheersing in processen:\b0\par
- Preventieve maatregelen\par
- Detectieve maatregelen\par
- Correctieve maatregelen\par
- Monitoring en rapportage\par

\pard\sa200\sl276\slmult1\qj\b Bronnen\b0\par

\pard\sa200\sl276\slmult1\qj - Thuis, P., & Stuive, R. (2020). Bedrijfskunde Integraal (3e druk). Noordhoff Uitgevers.\par
- Bedrijfskunde Lesweek 2.1 t/m 2.7 (PDF documenten)\par
}
