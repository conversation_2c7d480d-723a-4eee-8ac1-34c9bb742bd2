# AI Image Generator Chatbot

A simple chatbot application that can generate images based on text prompts using various AI image generation APIs.

## Versions

This project comes in two versions:

1. **Flask Web Application** - A server-based application that runs locally on your machine
2. **GitHub Pages Version** - A static web application that can be hosted on GitHub Pages

## Features

- Chat interface for interacting with the AI
- Image generation capabilities using popular AI services
- Support for multiple image generation APIs (OpenAI DALL-E, Stability AI, Replicate)
- Local storage of generated images (Flask version)
- Client-side only implementation (GitHub Pages version)

## Prerequisites

- Python 3.7 or higher
- API key for at least one of the supported image generation services:
  - [OpenAI](https://platform.openai.com/) (for DALL-E)
  - [Stability AI](https://stability.ai/) (for Stable Diffusion)
  - [Replicate](https://replicate.com/) (for various models)

## Installation

1. Clone this repository or download the files
2. Create a virtual environment (recommended):
   ```
   python -m venv venv
   venv\Scripts\activate  # On Windows
   source venv/bin/activate  # On macOS/Linux
   ```
3. Install the required packages:
   ```
   pip install -r requirements.txt
   ```
4. Set up your API keys:
   - Copy the `.env` file and add your API keys
   - Uncomment the line for the service you want to use and add your key

## Usage

1. Start the application:
   ```
   python app.py
   ```
2. Open your web browser and go to `http://127.0.0.1:5000/`
3. Start chatting with the bot
4. To generate an image, type something like "Generate an image of a sunset over mountains"

## Supported Image Generation APIs

### OpenAI DALL-E
- Requires an OpenAI API key
- Set the `OPENAI_API_KEY` in your `.env` file

### Stability AI
- Requires a Stability AI API key
- Set the `STABILITY_API_KEY` in your `.env` file

### Replicate
- Requires a Replicate API key
- Set the `REPLICATE_API_KEY` in your `.env` file

## Customization

- Modify the CSS in `static/css/style.css` to change the appearance
- Adjust image generation parameters in `image_generator.py`
- Add more chat functionality in `app.py`

## Limitations

- The application requires an internet connection to generate images
- Image generation may take some time depending on the API and complexity of the prompt
- API usage may incur costs depending on your subscription plan with the service providers

## GitHub Pages Version

The GitHub Pages version is located in the `github_pages` directory and can be deployed to GitHub Pages for free hosting. This version:

- Runs entirely in the browser (no server required)
- Uses free public APIs (no API keys needed)
- Provides multiple fallback options for reliable image retrieval
- Can be easily deployed to GitHub Pages
- Works with Pixabay, Unsplash, and Lorem Picsum APIs

To deploy the GitHub Pages version:

1. Run the `prepare_for_github.bat` script (Windows) or manually copy the files from the `github_pages` directory
2. Follow the instructions in the `GITHUB_DEPLOYMENT.md` file

## License

This project is open source and available under the MIT License.
