import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.drawing.image import Image
from openpyxl.chart import Bar<PERSON>hart, Reference, Series

def create_toc_excel():
    # Lee<PERSON> de TOC analyse
    with open('TOC_Analyse_Clean.csv', 'r') as f:
        toc_content = f.read()
    
    # Maak een Excel workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "TOC Analyse"
    
    # Voeg de TOC analyse toe aan het Excel bestand
    lines = toc_content.split('\n')
    for i, line in enumerate(lines):
        ws.cell(row=i+1, column=1, value=line)
    
    # Maak een dataframe voor de procestijden
    process_times = {
        'Proces': ['Grinding', 'Filling', 'Packaging'],
        'Procestijd (uur)': [0.34, 0.25, 0.50],
        'IsBottleneck': [0, 0, 1]  # 1 voor bottleneck, 0 voor niet-bottleneck
    }
    df_process = pd.DataFrame(process_times)
    
    # Voeg de data toe aan een nieuw werkblad
    ws_data = wb.create_sheet(title="Data")
    
    # Voeg procestijd data toe
    ws_data.cell(row=1, column=1, value="Procestijd Data")
    for r_idx, row in enumerate(dataframe_to_rows(df_process, index=False, header=True), 2):
        for c_idx, value in enumerate(row, 1):
            ws_data.cell(row=r_idx, column=c_idx, value=value)
    
    # Maak een staafdiagram voor procestijden
    chart = BarChart()
    chart.title = "Procestijden en Bottleneck"
    chart.y_axis.title = "Procestijd (uur)"
    chart.x_axis.title = "Proces"
    
    # Definieer de data voor het staafdiagram
    data = Reference(ws_data, min_col=2, min_row=2, max_row=4, max_col=2)
    cats = Reference(ws_data, min_col=1, min_row=3, max_row=5)
    
    # Voeg de data toe aan het staafdiagram
    chart.add_data(data)
    chart.set_categories(cats)
    
    # Pas de stijl aan
    chart.style = 10  # Kies een stijl
    
    # Voeg het staafdiagram toe aan het TOC Analyse werkblad
    ws.add_chart(chart, "D5")
    
    # Maak een dataframe voor energieverbruik
    energy_data = {
        'Verpakkingsmachine': ['Packager 4', 'Packager 1', 'Packager 5', 'Packager 3', 'Packager 2'],
        'Energieverbruik': [278.37, 277.56, 275.95, 275.83, 273.17]
    }
    df_energy = pd.DataFrame(energy_data)
    
    # Voeg energieverbruik data toe
    ws_data.cell(row=10, column=1, value="Energieverbruik Data")
    for r_idx, row in enumerate(dataframe_to_rows(df_energy, index=False, header=True), 11):
        for c_idx, value in enumerate(row, 1):
            ws_data.cell(row=r_idx, column=c_idx, value=value)
    
    # Maak een staafdiagram voor energieverbruik
    energy_chart = BarChart()
    energy_chart.title = "Energieverbruik per Verpakkingsmachine"
    energy_chart.y_axis.title = "Energieverbruik"
    energy_chart.x_axis.title = "Verpakkingsmachine"
    
    # Definieer de data voor het staafdiagram
    energy_data = Reference(ws_data, min_col=2, min_row=11, max_row=15, max_col=2)
    energy_cats = Reference(ws_data, min_col=1, min_row=12, max_row=16)
    
    # Voeg de data toe aan het staafdiagram
    energy_chart.add_data(energy_data)
    energy_chart.set_categories(energy_cats)
    
    # Pas de stijl aan
    energy_chart.style = 10  # Kies een stijl
    
    # Voeg het staafdiagram toe aan het TOC Analyse werkblad
    ws.add_chart(energy_chart, "D20")
    
    # Maak een werkblad voor de Drum-Buffer-Rope visualisatie
    ws_dbr = wb.create_sheet(title="Drum-Buffer-Rope")
    
    # Voeg uitleg toe over Drum-Buffer-Rope
    ws_dbr.cell(row=1, column=1, value="Drum-Buffer-Rope Concept")
    ws_dbr.cell(row=3, column=1, value="Drum: Het bottleneck proces (Packaging) bepaalt het tempo van de hele productielijn")
    ws_dbr.cell(row=4, column=1, value="Buffer: Voorraad voor de bottleneck om stilstand te voorkomen")
    ws_dbr.cell(row=5, column=1, value="Rope: Communicatiemechanisme dat ervoor zorgt dat materiaal alleen wordt vrijgegeven wanneer de bottleneck het kan verwerken")
    
    ws_dbr.cell(row=7, column=1, value="Processtappen:")
    ws_dbr.cell(row=8, column=1, value="1. Grinding")
    ws_dbr.cell(row=9, column=1, value="2. Buffer")
    ws_dbr.cell(row=10, column=1, value="3. Filling")
    ws_dbr.cell(row=11, column=1, value="4. Buffer")
    ws_dbr.cell(row=12, column=1, value="5. Packaging (Drum/Bottleneck)")
    ws_dbr.cell(row=13, column=1, value="6. Klant")
    
    # Sla het Excel bestand op
    excel_file = 'TOC_Analyse.xlsx'
    wb.save(excel_file)
    print(f"Excel bestand met visualisaties is opgeslagen als {excel_file}")

if __name__ == "__main__":
    create_toc_excel()
