from PIL import Image, ImageDraw, ImageFont
import os

# Functie om een eenvoudige afbeelding te maken met tekst
def create_image_with_text(title, content, output_filename, width=800, height=600):
    # Maak een nieuwe afbeelding met witte achtergrond
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)
    
    # Probeer een font te laden, gebruik standaard font als het niet lukt
    try:
        title_font = ImageFont.truetype("arial.ttf", 24)
        content_font = ImageFont.truetype("arial.ttf", 16)
    except IOError:
        title_font = ImageFont.load_default()
        content_font = ImageFont.load_default()
    
    # Teken de titel
    draw.text((20, 20), title, fill='black', font=title_font)
    
    # Teken een lijn onder de titel
    draw.line([(20, 60), (width-20, 60)], fill='black', width=2)
    
    # Teken de inhoud
    y_position = 80
    for line in content.split('\n'):
        draw.text((20, y_position), line, fill='black', font=content_font)
        y_position += 25
    
    # Sla de afbeelding op
    image.save(output_filename)
    print(f"Afbeelding opgeslagen als {output_filename}")

# Maak afbeeldingen voor elk model
# 1. Input-Throughput-Output (ITO) Model
ito_title = "Input-Throughput-Output (ITO) Model"
ito_content = """Het Input-Throughput-Output (ITO) model is een fundamenteel model voor het begrijpen van processen.

INPUT:
- Grondstoffen
- Arbeid
- Informatie
- Kapitaal
- Energie

THROUGHPUT (Transformatieproces):
- Productie
- Dienstverlening
- Informatieverwerking
- Waardecreatie

OUTPUT:
- Producten
- Diensten
- Informatie
- Afval/Emissies

Het model bevat ook een feedback-loop die informatie over de output terugvoert naar de input
of throughput om continue verbetering mogelijk te maken."""

create_image_with_text(ito_title, ito_content, "bedrijfsprocessen_ito_model.jpg")

# 2. Types Bedrijfsprocessen
types_title = "Types Bedrijfsprocessen"
types_content = """Volgens Thuis & Stuive kunnen bedrijfsprocessen worden onderverdeeld in drie hoofdcategorieën:

PRIMAIRE PROCESSEN:
- Inkoop
- Productie
- Verkoop
- Distributie
- Klantenservice

ONDERSTEUNENDE PROCESSEN:
- Personeelsbeheer (HRM)
- Financiële administratie
- IT-ondersteuning
- Facilitaire diensten
- Juridische zaken

MANAGEMENTPROCESSEN:
- Strategische planning
- Budgettering
- Prestatiemanagement
- Risicomanagement
- Kwaliteitsmanagement

Deze processen werken samen binnen een organisatie en interacteren met klanten,
leveranciers en de externe omgeving (economisch, politiek, sociaal-cultureel,
technologisch, ecologisch en juridisch)."""

create_image_with_text(types_title, types_content, "bedrijfsprocessen_types.jpg")

# 3. Swimlane Diagram: Orderverwerking Proces
swimlane_title = "Swimlane Diagram: Orderverwerking Proces"
swimlane_content = """Een swimlane diagram visualiseert een proces dat meerdere afdelingen omvat.
Voorbeeld van een orderverwerking proces:

VERKOOP:
1. Ontvang bestelling
2. Controleer bestelling
3. Bevestig bestelling

MAGAZIJN:
4. Controleer voorraad
5. Reserveer producten (indien beschikbaar)
6. Verzamel producten

LOGISTIEK:
7. Verpak producten
8. Maak verzendlabel
9. Verzend pakket

FINANCIËN:
10. Verwerk betaling
11. Maak factuur
12. Boek transactie

Dit diagram toont duidelijk welke afdeling verantwoordelijk is voor elke stap in het proces
en hoe het werk tussen afdelingen stroomt."""

create_image_with_text(swimlane_title, swimlane_content, "bedrijfsprocessen_swimlane.jpg")

print("Alle afbeeldingen zijn succesvol aangemaakt.")
