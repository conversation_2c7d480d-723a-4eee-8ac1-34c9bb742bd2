import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def load_cleaned_data():
    """
    Load the cleaned Kikker dataset.
    """
    try:
        # Try to load the cleaned data first
        df = pd.read_csv('Kikker_cleaned_new.csv')
        print(f"Successfully loaded cleaned dataset with {df.shape[0]} rows and {df.shape[1]} columns.")
        return df
    except FileNotFoundError:
        # If cleaned data doesn't exist, try the original data
        try:
            df = pd.read_csv('Kikker.csv')
            print(f"Cleaned dataset not found. Loaded original dataset with {df.shape[0]} rows and {df.shape[1]} columns.")
            print("Note: For best results, run kikker_data_processor.py first to clean the data.")
            return df
        except Exception as e:
            print(f"Error loading dataset: {e}")
            return None

def create_visualizations(df):
    """
    Create visualizations for the Kikker dataset.
    """
    if df is None:
        return
    
    print("\nCreating visualizations...")
    
    # Set the style for all plots
    sns.set(style="whitegrid")
    
    # Create a directory for saving plots if it doesn't exist
    import os
    if not os.path.exists('plots'):
        os.makedirs('plots')
    
    # 1. Coffee Bean Distribution
    print("1. Creating coffee bean distribution plot...")
    plt.figure(figsize=(10, 6))
    if 'Koffieboon' in df.columns:
        bean_counts = df['Koffieboon'].value_counts()
        ax = sns.barplot(x=bean_counts.index, y=bean_counts.values)
        plt.title('Distribution of Coffee Bean Types')
        plt.xlabel('Coffee Bean Type')
        plt.ylabel('Count')
        plt.xticks(rotation=45)
        
        # Add count labels on top of bars
        for i, count in enumerate(bean_counts.values):
            ax.text(i, count + 50, f"{count}", ha='center')
            
        plt.tight_layout()
        plt.savefig('plots/coffee_bean_distribution.png')
        plt.close()
    
    # 2. Quality Metrics
    print("2. Creating quality metrics plots...")
    
    # Panel Test Results
    if 'Panel Test' in df.columns:
        plt.figure(figsize=(10, 6))
        panel_counts = df['Panel Test'].value_counts()
        ax = sns.barplot(x=panel_counts.index, y=panel_counts.values)
        plt.title('Panel Test Results')
        plt.xlabel('Result')
        plt.ylabel('Count')
        
        # Add count labels on top of bars
        for i, count in enumerate(panel_counts.values):
            ax.text(i, count + 50, f"{count}", ha='center')
            
        plt.tight_layout()
        plt.savefig('plots/panel_test_results.png')
        plt.close()
    
    # Defect Percentage Distribution
    if 'Defectpercentage_numeric' in df.columns:
        plt.figure(figsize=(12, 6))
        sns.histplot(df['Defectpercentage_numeric'].dropna() * 100, bins=20, kde=True)
        plt.title('Distribution of Defect Percentage')
        plt.xlabel('Defect Percentage (%)')
        plt.ylabel('Frequency')
        plt.tight_layout()
        plt.savefig('plots/defect_percentage_distribution.png')
        plt.close()
    
    # 3. Machine Performance
    print("3. Creating machine performance plots...")
    
    # Defect percentage by grinding machine
    if 'Defectpercentage_numeric' in df.columns and 'GrindingApparaat' in df.columns:
        plt.figure(figsize=(12, 6))
        defect_by_grinder = df.groupby('GrindingApparaat')['Defectpercentage_numeric'].mean() * 100
        ax = sns.barplot(x=defect_by_grinder.index, y=defect_by_grinder.values)
        plt.title('Average Defect Percentage by Grinding Machine')
        plt.xlabel('Grinding Machine')
        plt.ylabel('Average Defect Percentage (%)')
        
        # Add percentage labels on top of bars
        for i, pct in enumerate(defect_by_grinder.values):
            ax.text(i, pct + 0.05, f"{pct:.2f}%", ha='center')
            
        plt.tight_layout()
        plt.savefig('plots/defect_by_grinding_machine.png')
        plt.close()
    
    # Cycle time by filling machine
    if 'Cyclustijd_numeric' in df.columns and 'FillingApparaat' in df.columns:
        plt.figure(figsize=(12, 6))
        cycle_by_filler = df.groupby('FillingApparaat')['Cyclustijd_numeric'].mean()
        ax = sns.barplot(x=cycle_by_filler.index, y=cycle_by_filler.values)
        plt.title('Average Cycle Time by Filling Machine')
        plt.xlabel('Filling Machine')
        plt.ylabel('Average Cycle Time (hours)')
        
        # Add time labels on top of bars
        for i, time in enumerate(cycle_by_filler.values):
            ax.text(i, time + 0.1, f"{time:.2f} hrs", ha='center')
            
        plt.tight_layout()
        plt.savefig('plots/cycle_time_by_filling_machine.png')
        plt.close()
    
    # 4. Cost Analysis
    print("4. Creating cost analysis plots...")
    
    # Cost distribution
    if 'Cost_numeric' in df.columns:
        plt.figure(figsize=(12, 6))
        sns.histplot(df['Cost_numeric'].dropna(), bins=20, kde=True)
        plt.title('Distribution of Production Costs')
        plt.xlabel('Cost (euros)')
        plt.ylabel('Frequency')
        plt.tight_layout()
        plt.savefig('plots/cost_distribution.png')
        plt.close()
    
    # Cost by coffee bean type
    if 'Cost_numeric' in df.columns and 'Koffieboon' in df.columns:
        plt.figure(figsize=(12, 6))
        sns.boxplot(x='Koffieboon', y='Cost_numeric', data=df)
        plt.title('Production Cost by Coffee Bean Type')
        plt.xlabel('Coffee Bean Type')
        plt.ylabel('Cost (euros)')
        plt.tight_layout()
        plt.savefig('plots/cost_by_bean_type.png')
        plt.close()
    
    # 5. Sustainability Analysis
    print("5. Creating sustainability analysis plots...")
    
    # CO2 footprint by coffee bean type
    if 'CO2_Footprint_numeric' in df.columns and 'Koffieboon' in df.columns:
        plt.figure(figsize=(12, 6))
        sns.boxplot(x='Koffieboon', y='CO2_Footprint_numeric', data=df)
        plt.title('CO2 Footprint by Coffee Bean Type')
        plt.xlabel('Coffee Bean Type')
        plt.ylabel('CO2 Footprint (kg CO2/kg)')
        plt.tight_layout()
        plt.savefig('plots/co2_by_bean_type.png')
        plt.close()
    
    # Fair trade score distribution
    if 'Fair-Trade Score_cleaned' in df.columns:
        plt.figure(figsize=(12, 6))
        sns.histplot(df['Fair-Trade Score_cleaned'].dropna(), bins=20, kde=True)
        plt.title('Distribution of Fair Trade Scores')
        plt.xlabel('Fair Trade Score')
        plt.ylabel('Frequency')
        plt.tight_layout()
        plt.savefig('plots/fair_trade_score_distribution.png')
        plt.close()
    
    # 6. Correlation Analysis
    print("6. Creating correlation analysis plot...")
    
    # Select numeric columns for correlation analysis
    numeric_cols = [
        'Defectpercentage_numeric', 
        'Klantretourpercentage_numeric',
        'Cost_numeric', 
        'Cyclustijd_numeric', 
        'Energieverbruik_numeric',
        'CO2_Footprint_numeric',
        'Fair-Trade Score_cleaned',
        'Gewichtscontrole_numeric'
    ]
    
    # Filter columns that exist in the dataframe
    available_cols = [col for col in numeric_cols if col in df.columns]
    
    if len(available_cols) > 1:
        # Create correlation matrix
        corr_matrix = df[available_cols].corr()
        
        # Plot correlation heatmap
        plt.figure(figsize=(12, 10))
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', vmin=-1, vmax=1, center=0)
        plt.title('Correlation Matrix of Key Metrics')
        plt.tight_layout()
        plt.savefig('plots/correlation_matrix.png')
        plt.close()
    
    print("\nAll visualizations created and saved to the 'plots' directory.")

def main():
    """
    Main function to visualize the Kikker dataset.
    """
    print("=" * 80)
    print("AMERICAPS DATA VISUALIZER - KIKKER.CSV")
    print("=" * 80)
    
    # Load the data
    df = load_cleaned_data()
    if df is None:
        print("Failed to load dataset. Exiting.")
        return
    
    # Create visualizations
    create_visualizations(df)
    
    print("\nData visualization completed successfully.")

if __name__ == "__main__":
    main()
