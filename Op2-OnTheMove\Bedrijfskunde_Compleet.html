<!DOCTYPE html>
<html>
<head>
    <title>Bedrijfskunde: Processen en Theorie</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        h1 {
            color: #0066cc;
            text-align: center;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
        }
        h2 {
            color: #0066cc;
            margin-top: 30px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        h3 {
            color: #009933;
            margin-top: 20px;
        }
        .image-placeholder {
            background-color: #f0f0f0;
            border: 2px dashed #999;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            font-style: italic;
        }
        .note {
            background-color: #ffffcc;
            padding: 10px;
            border-left: 4px solid #ffcc00;
            margin: 15px 0;
        }
        .definition {
            background-color: #e6f2ff;
            padding: 10px;
            border-left: 4px solid #0066cc;
            margin: 15px 0;
        }
        .process-steps {
            margin-left: 20px;
        }
        .process-steps li {
            margin-bottom: 10px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .print-instructions {
            background-color: #f0f0f0;
            padding: 15px;
            margin: 30px 0;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <h1>Bedrijfskunde: Processen en Theorie</h1>
    <p>Gebaseerd op het boek van Peter Thuis & Rienk Stuive (3e druk) en de slides van Bedrijfskunde Lesweek 2.1 t/m 2.7.</p>

    <div class="print-instructions">
        <h3>Instructies voor het toevoegen van afbeeldingen:</h3>
        <ol>
            <li>Open dit HTML-bestand in een tekstverwerker (zoals Notepad++ of Visual Studio Code)</li>
            <li>Zoek naar de secties met "AFBEELDING HIER" tekst</li>
            <li>Vervang deze tekst door HTML-code voor een afbeelding: <code>&lt;img src="pad/naar/afbeelding.jpg" alt="Beschrijving" style="max-width:100%;"&gt;</code></li>
            <li>Sla het bestand op en open het opnieuw in je browser</li>
            <li>Gebruik de functie "Afdrukken" (Ctrl+P) en kies "Opslaan als PDF" om een PDF-bestand te maken</li>
        </ol>
    </div>

    <h2>1. Wat zijn Bedrijfsprocessen?</h2>
    
    <div class="definition">
        <p><strong>Definitie:</strong> Een bedrijfsproces is een reeks van logisch samenhangende activiteiten die worden uitgevoerd om een bepaald doel te bereiken. Processen transformeren inputs (grondstoffen, informatie, etc.) naar outputs (producten, diensten, etc.) die waarde hebben voor klanten.</p>
    </div>

    <p>Volgens Thuis & Stuive hebben bedrijfsprocessen de volgende kenmerken:</p>
    <ul>
        <li>Ze hebben een duidelijk begin en einde</li>
        <li>Ze hebben een doel</li>
        <li>Ze hebben inputs en outputs</li>
        <li>Ze bestaan uit activiteiten die in een bepaalde volgorde worden uitgevoerd</li>
        <li>Ze overschrijden vaak functionele grenzen</li>
        <li>Ze kunnen worden gemeten en verbeterd</li>
        <li>Ze creëren waarde voor klanten (intern of extern)</li>
    </ul>

    <div class="image-placeholder">
        AFBEELDING HIER: Visualisatie van een bedrijfsproces met begin, activiteiten en einde
    </div>

    <h2>2. Input-Throughput-Output (ITO) Model</h2>
    
    <p>Het ITO-model is een fundamenteel model voor het begrijpen van processen. Het beschrijft hoe inputs worden getransformeerd tot outputs via een reeks activiteiten.</p>

    <div class="image-placeholder">
        AFBEELDING HIER: Input-Throughput-Output model met feedback loop
    </div>

    <h3>Componenten van het ITO-model:</h3>
    <ul>
        <li><strong>Input:</strong> De middelen die nodig zijn om het proces uit te voeren
            <ul>
                <li>Grondstoffen</li>
                <li>Arbeid</li>
                <li>Informatie</li>
                <li>Kapitaal</li>
                <li>Energie</li>
            </ul>
        </li>
        <li><strong>Throughput:</strong> Het transformatieproces waarin de inputs worden omgezet in outputs
            <ul>
                <li>Productie</li>
                <li>Dienstverlening</li>
                <li>Informatieverwerking</li>
                <li>Waardecreatie</li>
            </ul>
        </li>
        <li><strong>Output:</strong> De resultaten van het proces
            <ul>
                <li>Producten</li>
                <li>Diensten</li>
                <li>Informatie</li>
                <li>Afval/Emissies</li>
            </ul>
        </li>
        <li><strong>Feedback:</strong> Informatie over de output die wordt gebruikt om het proces te verbeteren
            <ul>
                <li>Kwaliteitscontrole</li>
                <li>Klanttevredenheid</li>
                <li>Prestatiemetingen</li>
                <li>Verbetervoorstellen</li>
            </ul>
        </li>
    </ul>

    <div class="note">
        <p>Het ITO-model wordt vaak uitgebreid met een feedback-loop, die informatie over de output terugvoert naar de input of throughput om continue verbetering mogelijk te maken.</p>
    </div>

    <h2>3. Types Bedrijfsprocessen</h2>
    
    <p>Volgens Thuis & Stuive kunnen bedrijfsprocessen worden onderverdeeld in drie hoofdcategorieën:</p>

    <div class="image-placeholder">
        AFBEELDING HIER: Visualisatie van de drie types bedrijfsprocessen (piramide of concentrische cirkels)
    </div>

    <h3>Primaire processen</h3>
    <p>Deze processen zijn direct gericht op het creëren van producten of diensten voor externe klanten. Ze vormen de kernactiviteiten van een organisatie en dragen direct bij aan de waardecreatie.</p>
    <ul>
        <li>Inkoop</li>
        <li>Productie</li>
        <li>Verkoop</li>
        <li>Distributie</li>
        <li>Klantenservice</li>
    </ul>

    <h3>Ondersteunende processen</h3>
    <p>Deze processen ondersteunen de primaire processen maar dragen niet direct bij aan de waardecreatie voor externe klanten. Ze zijn echter essentieel voor het functioneren van de organisatie.</p>
    <ul>
        <li>Personeelsbeheer (HRM)</li>
        <li>Financiële administratie</li>
        <li>IT-ondersteuning</li>
        <li>Facilitaire diensten</li>
        <li>Juridische zaken</li>
    </ul>

    <h3>Managementprocessen</h3>
    <p>Deze processen zijn gericht op het plannen, organiseren, leiden en controleren van de organisatie. Ze zorgen voor coördinatie tussen verschillende afdelingen en processen.</p>
    <ul>
        <li>Strategische planning</li>
        <li>Budgettering</li>
        <li>Prestatiemanagement</li>
        <li>Risicomanagement</li>
        <li>Kwaliteitsmanagement</li>
    </ul>

    <h2>4. Primaire Processen</h2>

    <h3>Inkoopproces</h3>
    <div class="image-placeholder">
        AFBEELDING HIER: Flowchart van het inkoopproces
    </div>
    <ol class="process-steps">
        <li><strong>Specificeren van behoeften:</strong> Bepalen wat er nodig is, in welke hoeveelheden en met welke specificaties</li>
        <li><strong>Selecteren van leveranciers:</strong> Zoeken naar geschikte leveranciers en deze evalueren</li>
        <li><strong>Onderhandelen en contracteren:</strong> Afspraken maken over prijs, kwaliteit, levertijd, etc.</li>
        <li><strong>Bestellen:</strong> Plaatsen van de bestelling bij de geselecteerde leverancier</li>
        <li><strong>Bewaken van leveringen:</strong> Monitoren van de levering en controleren of deze voldoet aan de afspraken</li>
        <li><strong>Nazorg en evaluatie:</strong> Evalueren van de leverancier en het inkoopproces</li>
    </ol>

    <h3>Productieproces</h3>
    <div class="image-placeholder">
        AFBEELDING HIER: Flowchart van het productieproces
    </div>
    <ol class="process-steps">
        <li><strong>Productieplanning:</strong> Plannen van de productie op basis van vraag en capaciteit</li>
        <li><strong>Werkvoorbereiding:</strong> Voorbereiden van de productie, zoals het klaarzetten van materialen en instellen van machines</li>
        <li><strong>Productie-uitvoering:</strong> Uitvoeren van de productieactiviteiten</li>
        <li><strong>Kwaliteitscontrole:</strong> Controleren of de producten voldoen aan de kwaliteitseisen</li>
        <li><strong>Verpakking:</strong> Verpakken van de producten voor opslag of verzending</li>
        <li><strong>Opslag:</strong> Opslaan van de producten in het magazijn</li>
    </ol>

    <h3>Verkoopproces</h3>
    <div class="image-placeholder">
        AFBEELDING HIER: Flowchart van het verkoopproces
    </div>
    <ol class="process-steps">
        <li><strong>Prospectie:</strong> Zoeken naar potentiële klanten</li>
        <li><strong>Voorbereiding:</strong> Verzamelen van informatie over de klant en voorbereiden van de verkooppresentatie</li>
        <li><strong>Benadering:</strong> Eerste contact met de potentiële klant</li>
        <li><strong>Presentatie:</strong> Demonstratie van het product of de dienst</li>
        <li><strong>Behandelen bezwaren:</strong> Ingaan op vragen en bezwaren van de klant</li>
        <li><strong>Afsluiting:</strong> Afsluiten van de verkoop</li>
        <li><strong>Follow-up:</strong> Nazorg en onderhouden van de klantrelatie</li>
    </ol>

    <h2>5. Procesvisualisatie</h2>
    
    <p>Volgens Thuis & Stuive zijn er verschillende methoden om processen visueel weer te geven:</p>

    <h3>Flowcharts</h3>
    <div class="image-placeholder">
        AFBEELDING HIER: Voorbeeld van een flowchart met verschillende symbolen
    </div>
    <p>Flowcharts tonen de stappen in een proces met symbolen en pijlen. Ze geven een duidelijk overzicht van de volgorde van activiteiten.</p>
    <p>Veelgebruikte symbolen in flowcharts:</p>
    <ul>
        <li>Ovaal: Start/Einde</li>
        <li>Rechthoek: Activiteit/Taak</li>
        <li>Ruit: Beslissing</li>
        <li>Pijlen: Stroomrichting</li>
    </ul>

    <h3>Swimlane-diagrammen</h3>
    <div class="image-placeholder">
        AFBEELDING HIER: Voorbeeld van een swimlane-diagram
    </div>
    <p>Swimlane-diagrammen tonen niet alleen de processtappen, maar ook welke afdeling of persoon verantwoordelijk is voor elke stap. Ze zijn bijzonder nuttig voor het visualiseren van processen die meerdere afdelingen omvatten.</p>

    <h3>Value Stream Maps</h3>
    <div class="image-placeholder">
        AFBEELDING HIER: Voorbeeld van een Value Stream Map
    </div>
    <p>Value Stream Maps tonen de stroom van materialen en informatie, evenals wachttijden en voorraden. Ze worden vaak gebruikt in Lean management om verspilling te identificeren.</p>

    <h2>6. Procesmanagement</h2>
    
    <p>Procesmanagement omvat volgens Thuis & Stuive alle activiteiten die nodig zijn om processen te ontwerpen, implementeren, monitoren en verbeteren:</p>

    <div class="image-placeholder">
        AFBEELDING HIER: Procesmanagementcyclus
    </div>

    <h3>Procesontwerp</h3>
    <ul>
        <li>Definiëren van stappen, rollen, regels en resources</li>
        <li>Bepalen van inputs, outputs en prestatie-indicatoren</li>
        <li>Documenteren van het proces</li>
    </ul>

    <h3>Procesimplementatie</h3>
    <ul>
        <li>In gebruik nemen van een nieuw of gewijzigd proces</li>
        <li>Training van medewerkers</li>
        <li>Aanpassing van systemen</li>
        <li>Communicatie over het proces</li>
    </ul>

    <h3>Procesmonitoring</h3>
    <ul>
        <li>Meten van procesprestaties met KPI's</li>
        <li>Analyseren van resultaten</li>
        <li>Identificeren van afwijkingen</li>
        <li>Rapporteren over procesprestaties</li>
    </ul>

    <h3>Procesverbetering</h3>
    <ul>
        <li>Identificeren van verbetermogelijkheden</li>
        <li>Analyseren van oorzaken van problemen</li>
        <li>Ontwikkelen van verbetervoorstellen</li>
        <li>Implementeren van verbeteringen</li>
        <li>Evalueren van resultaten</li>
    </ul>

    <h2>7. Plan-Do-Check-Act (PDCA) Cyclus</h2>
    
    <p>De PDCA-cyclus is een methodologie voor continue verbetering die vaak wordt gebruikt in procesmanagement:</p>

    <div class="image-placeholder">
        AFBEELDING HIER: PDCA-cyclus diagram
    </div>

    <h3>Plan</h3>
    <ul>
        <li>Identificeer het probleem of de verbetermogelijkheid</li>
        <li>Analyseer de huidige situatie</li>
        <li>Bepaal de oorzaken van het probleem</li>
        <li>Ontwikkel een verbeterplan</li>
    </ul>

    <h3>Do</h3>
    <ul>
        <li>Implementeer het plan op kleine schaal</li>
        <li>Verzamel data over de resultaten</li>
        <li>Documenteer problemen en onverwachte gebeurtenissen</li>
    </ul>

    <h3>Check</h3>
    <ul>
        <li>Vergelijk de resultaten met de doelen</li>
        <li>Bepaal of het plan succesvol was</li>
        <li>Identificeer wat goed ging en wat niet</li>
    </ul>

    <h3>Act</h3>
    <ul>
        <li>Standaardiseer succesvolle verbeteringen</li>
        <li>Pas het plan aan op basis van de resultaten</li>
        <li>Bepaal volgende stappen</li>
        <li>Begin een nieuwe PDCA-cyclus</li>
    </ul>

    <h2>8. Procesverbetering</h2>
    
    <p>Volgens Thuis & Stuive zijn er verschillende methodologieën voor procesverbetering:</p>

    <h3>Lean</h3>
    <div class="image-placeholder">
        AFBEELDING HIER: Visualisatie van Lean principes of de 8 vormen van verspilling
    </div>
    <p>Lean is gericht op het elimineren van verspilling (muda) en het verbeteren van de flow. Volgens Lean zijn er acht soorten verspilling:</p>
    <ol>
        <li>Overproductie</li>
        <li>Wachttijd</li>
        <li>Transport</li>
        <li>Overbewerking</li>
        <li>Voorraad</li>
        <li>Beweging</li>
        <li>Defecten</li>
        <li>Onbenutte creativiteit</li>
    </ol>

    <h3>Six Sigma</h3>
    <div class="image-placeholder">
        AFBEELDING HIER: DMAIC-cyclus of Six Sigma visualisatie
    </div>
    <p>Six Sigma is gericht op het verminderen van variatie en defecten in processen. Het gebruikt de DMAIC-methodologie:</p>
    <ul>
        <li><strong>Define:</strong> Definieer het probleem en de doelstellingen</li>
        <li><strong>Measure:</strong> Meet de huidige prestaties</li>
        <li><strong>Analyze:</strong> Analyseer de oorzaken van problemen</li>
        <li><strong>Improve:</strong> Implementeer verbeteringen</li>
        <li><strong>Control:</strong> Borg de verbeteringen</li>
    </ul>

    <h3>Kaizen</h3>
    <div class="image-placeholder">
        AFBEELDING HIER: Kaizen-principe visualisatie
    </div>
    <p>Kaizen is een Japanse term die "verandering ten goede" of "continue verbetering" betekent. Het is gericht op kleine, incrementele verbeteringen die, wanneer ze consistent worden toegepast, leiden tot significante verbeteringen op de lange termijn.</p>
    <p>Kernprincipes van Kaizen:</p>
    <ul>
        <li>Continue verbetering</li>
        <li>Betrokkenheid van alle medewerkers</li>
        <li>Elimineren van verspilling</li>
        <li>Standaardisatie</li>
        <li>Visueel management</li>
    </ul>

    <h2>9. Processen en Organisatiestructuur</h2>
    
    <p>De relatie tussen processen en organisatiestructuur volgens Thuis & Stuive:</p>

    <div class="image-placeholder">
        AFBEELDING HIER: Verschillende organisatiestructuren (functioneel, procesgericht, matrix)
    </div>

    <h3>Functionele organisatie</h3>
    <ul>
        <li>Georganiseerd rond functies/afdelingen</li>
        <li>Processen lopen door verschillende afdelingen</li>
        <li>Uitdaging: coördinatie tussen afdelingen</li>
    </ul>

    <h3>Procesgerichte organisatie</h3>
    <ul>
        <li>Georganiseerd rond processen</li>
        <li>Teams verantwoordelijk voor complete processen</li>
        <li>Voordeel: betere afstemming op klantbehoeften</li>
    </ul>

    <h3>Matrixorganisatie</h3>
    <ul>
        <li>Combinatie van functionele en procesgerichte structuur</li>
        <li>Medewerkers rapporteren aan functionele manager én procesmanager</li>
        <li>Uitdaging: dubbele aansturing</li>
    </ul>

    <h2>10. Processen en Informatiesystemen</h2>
    
    <p>De rol van informatiesystemen in processen volgens Thuis & Stuive:</p>

    <div class="image-placeholder">
        AFBEELDING HIER: Visualisatie van informatiesystemen en hun relatie tot processen
    </div>

    <h3>Enterprise Resource Planning (ERP)</h3>
    <ul>
        <li>Geïntegreerd systeem voor bedrijfsprocessen</li>
        <li>Modules voor verschillende functionele gebieden</li>
        <li>Gemeenschappelijke database</li>
    </ul>

    <h3>Customer Relationship Management (CRM)</h3>
    <ul>
        <li>Beheer van klantinteracties</li>
        <li>Ondersteuning van verkoopprocessen</li>
        <li>Klantservice en -ondersteuning</li>
    </ul>

    <h3>Supply Chain Management (SCM)</h3>
    <ul>
        <li>Beheer van goederenstroom</li>
        <li>Planning en forecasting</li>
        <li>Leveranciersmanagement</li>
    </ul>

    <h3>Business Intelligence (BI)</h3>
    <ul>
        <li>Analyse van bedrijfsgegevens</li>
        <li>Dashboards en rapportages</li>
        <li>Ondersteuning van besluitvorming</li>
    </ul>

    <h2>Bronnen</h2>
    <ul>
        <li>Thuis, P., & Stuive, R. (2020). Bedrijfskunde Integraal (3e druk). Noordhoff Uitgevers.</li>
        <li>Bedrijfskunde Lesweek 2.1 t/m 2.7 (PDF documenten)</li>
    </ul>

    <div class="print-instructions">
        <h3>Instructies voor het opslaan als PDF:</h3>
        <ol>
            <li>Druk op Ctrl+P (of kies "Afdrukken" in het menu)</li>
            <li>Kies "Opslaan als PDF" als printer</li>
            <li>Klik op "Opslaan" of "Afdrukken"</li>
            <li>Kies een locatie om het PDF-bestand op te slaan en geef het een naam</li>
            <li>Klik op "Opslaan"</li>
        </ol>
    </div>
</body>
</html>
