# Deploying to GitHub Pages

This guide will walk you through the process of deploying your AI Image Generator Chatbot to GitHub Pages.

## Prerequisites

- A GitHub account
- Git installed on your computer

## Step 1: Create a New Repository on GitHub

1. Go to [GitHub](https://github.com) and sign in to your account
2. Click on the "+" icon in the top right corner and select "New repository"
3. Name your repository (e.g., "image-gen-chatbot")
4. Make sure the repository is set to "Public"
5. Click "Create repository"

## Step 2: Prepare Your Local Files

1. Make sure you have all the files from the `github_pages` directory:
   - `index.html`
   - `css/style.css`
   - `js/script.js`
   - `README.md`

2. Initialize a Git repository in your local directory:
   ```
   git init
   ```

3. Add all files to the repository:
   ```
   git add .
   ```

4. Commit the files:
   ```
   git commit -m "Initial commit"
   ```

## Step 3: Connect to GitHub and Push Your Code

1. Connect your local repository to the GitHub repository you created:
   ```
   git remote add origin https://github.com/YOUR_USERNAME/YOUR_REPOSITORY_NAME.git
   ```
   (Replace `YOUR_USERNAME` and `YOUR_REPOSITORY_NAME` with your GitHub username and repository name)

2. Push your code to GitHub:
   ```
   git push -u origin main
   ```
   (If you're using an older version of Git, you might need to use `master` instead of `main`)

## Step 4: Enable GitHub Pages

1. Go to your repository on GitHub
2. Click on "Settings"
3. Scroll down to the "GitHub Pages" section
4. Under "Source", select the branch you want to deploy (usually "main")
5. Click "Save"

## Step 5: Access Your Deployed Site

1. After enabling GitHub Pages, GitHub will provide you with a URL where your site is published
2. The URL will be in the format: `https://YOUR_USERNAME.github.io/YOUR_REPOSITORY_NAME/`
3. It may take a few minutes for your site to be published

## Updating Your Site

Whenever you want to update your site:

1. Make changes to your local files
2. Commit the changes:
   ```
   git add .
   git commit -m "Description of changes"
   ```
3. Push to GitHub:
   ```
   git push origin main
   ```
4. Your site will automatically update (it may take a few minutes)

## Troubleshooting

- If your site doesn't appear, check that you've enabled GitHub Pages correctly
- Make sure your repository is public
- Check that your files are in the root of the repository or in the correct branch
- If images or styles aren't loading, check that the paths in your HTML files are correct
