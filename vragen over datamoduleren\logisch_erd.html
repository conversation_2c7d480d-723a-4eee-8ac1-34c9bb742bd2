<!DOCTYPE html>
<html>
<head>
    <title>Logisch ERD Voorbeeld</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: white;
        }
        .container {
            width: 900px;
            height: 700px;
            border: 1px solid #ccc;
            position: relative;
            background-color: white;
        }
        .title {
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            margin-top: 20px;
        }
        .entity {
            width: 200px;
            border: 2px solid black;
            position: absolute;
            background-color: white;
        }
        .entity-header {
            background-color: #f0f0f0;
            padding: 5px;
            text-align: center;
            font-weight: bold;
            border-bottom: 1px solid black;
        }
        .entity-attributes {
            padding: 5px;
        }
        .attribute {
            margin: 3px 0;
            font-size: 12px;
        }
        .pk {
            text-decoration: underline;
            font-weight: bold;
        }
        .relationship {
            position: absolute;
            font-size: 14px;
        }
        .cardinality {
            position: absolute;
            font-size: 14px;
        }
        .line {
            position: absolute;
            background-color: black;
        }
        .note {
            position: absolute;
            bottom: 20px;
            left: 20px;
            font-size: 14px;
            width: 860px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">Logisch ERD Voorbeeld - Webwinkel</div>
        
        <!-- Entities with attributes -->
        <div class="entity" style="top: 80px; left: 100px;">
            <div class="entity-header">KLANT</div>
            <div class="entity-attributes">
                <div class="attribute pk">klant_id</div>
                <div class="attribute">voornaam</div>
                <div class="attribute">achternaam</div>
                <div class="attribute">email</div>
                <div class="attribute">telefoonnummer</div>
                <div class="attribute">adres</div>
                <div class="attribute">postcode</div>
                <div class="attribute">woonplaats</div>
            </div>
        </div>
        
        <div class="entity" style="top: 80px; left: 550px;">
            <div class="entity-header">BESTELLING</div>
            <div class="entity-attributes">
                <div class="attribute pk">bestelling_id</div>
                <div class="attribute">klant_id</div>
                <div class="attribute">datum</div>
                <div class="attribute">status</div>
                <div class="attribute">totaalbedrag</div>
                <div class="attribute">verzendkosten</div>
                <div class="attribute">betaalmethode</div>
            </div>
        </div>
        
        <div class="entity" style="top: 350px; left: 100px;">
            <div class="entity-header">CATEGORIE</div>
            <div class="entity-attributes">
                <div class="attribute pk">categorie_id</div>
                <div class="attribute">naam</div>
                <div class="attribute">beschrijving</div>
                <div class="attribute">hoofdcategorie_id</div>
            </div>
        </div>
        
        <div class="entity" style="top: 350px; left: 550px;">
            <div class="entity-header">PRODUCT</div>
            <div class="entity-attributes">
                <div class="attribute pk">product_id</div>
                <div class="attribute">categorie_id</div>
                <div class="attribute">naam</div>
                <div class="attribute">beschrijving</div>
                <div class="attribute">prijs</div>
                <div class="attribute">voorraad</div>
                <div class="attribute">gewicht</div>
                <div class="attribute">afmetingen</div>
            </div>
        </div>
        
        <div class="entity" style="top: 230px; left: 325px;">
            <div class="entity-header">BESTELLING_PRODUCT</div>
            <div class="entity-attributes">
                <div class="attribute pk">bestelling_id</div>
                <div class="attribute pk">product_id</div>
                <div class="attribute">aantal</div>
                <div class="attribute">prijs_per_stuk</div>
                <div class="attribute">subtotaal</div>
            </div>
        </div>
        
        <!-- Lines -->
        <div class="line" style="top: 130px; left: 300px; width: 250px; height: 2px;"></div>
        <div class="line" style="top: 260px; left: 425px; width: 2px; height: 90px;"></div>
        <div class="line" style="top: 380px; left: 300px; width: 250px; height: 2px;"></div>
        <div class="line" style="top: 130px; left: 650px; width: 2px; height: 100px;"></div>
        <div class="line" style="top: 230px; left: 525px; width: 125px; height: 2px;"></div>
        <div class="line" style="top: 280px; left: 425px; width: 125px; height: 2px;"></div>
        <div class="line" style="top: 280px; left: 550px; width: 2px; height: 70px;"></div>
        
        <!-- Relationships -->
        <div class="relationship" style="top: 110px; left: 380px;">plaatst</div>
        <div class="relationship" style="top: 210px; left: 570px;">bevat</div>
        <div class="relationship" style="top: 360px; left: 380px;">behoort tot</div>
        
        <!-- Cardinalities -->
        <div class="cardinality" style="top: 110px; left: 310px;">1</div>
        <div class="cardinality" style="top: 110px; left: 530px;">N</div>
        <div class="cardinality" style="top: 210px; left: 650px;">1</div>
        <div class="cardinality" style="top: 260px; left: 530px;">N</div>
        <div class="cardinality" style="top: 360px; left: 310px;">1</div>
        <div class="cardinality" style="top: 360px; left: 530px;">N</div>
        
        <!-- Note -->
        <div class="note">
            <strong>Opmerking:</strong> Een logisch ERD bouwt voort op het conceptuele ERD door attributen toe te voegen aan elke entiteit. 
            Ook is er een koppeltabel (BESTELLING_PRODUCT) toegevoegd om de veel-op-veel relatie tussen BESTELLING en PRODUCT te implementeren.
            Primaire sleutels zijn onderstreept. In een logisch ERD worden nog geen datatypes of implementatiedetails gespecificeerd.
        </div>
    </div>
    
    <div style="margin-top: 20px;">
        <h2>Verschillen tussen Conceptueel en Logisch ERD</h2>
        <ul>
            <li><strong>Attributen:</strong> Het logische ERD bevat attributen (eigenschappen) voor elke entiteit, terwijl het conceptuele ERD alleen entiteiten toont.</li>
            <li><strong>Primaire sleutels:</strong> Het logische ERD identificeert primaire sleutels (onderstreept), terwijl het conceptuele ERD geen sleutels bevat.</li>
            <li><strong>Koppeltabellen:</strong> Het logische ERD lost veel-op-veel relaties op door koppeltabellen toe te voegen (BESTELLING_PRODUCT).</li>
            <li><strong>Detail niveau:</strong> Het logische ERD is gedetailleerder en dichter bij de daadwerkelijke database-implementatie.</li>
            <li><strong>Doelgroep:</strong> Het logische ERD is meer gericht op databaseontwerpers, terwijl het conceptuele ERD ook voor niet-technische stakeholders begrijpelijk is.</li>
        </ul>
    </div>
</body>
</html>
