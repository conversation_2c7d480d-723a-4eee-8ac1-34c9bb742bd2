import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# HTML bestanden die we willen converteren
html_files = [
    "many_to_many_incorrect.html",
    "many_to_many_correct.html",
    "normalisatie_0nf_1nf.html",
    "normalisatie_2nf.html",
    "normalisatie_3nf.html",
    "recursieve_relatie_conceptueel.html",
    "recursieve_relatie_logisch.html",
    "erd_conceptueel.html",
    "erd_logisch.html",
    "erd_fysiek.html"
]

# Configureer Chrome opties
chrome_options = Options()
chrome_options.add_argument("--headless")  # Run in headless mode
chrome_options.add_argument("--window-size=1000,1500")  # Set window size

# Initialiseer de webdriver
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

# Ma<PERSON> de doelmap als deze nog niet bestaat
os.makedirs("DataModuleren/Quiz", exist_ok=True)

# Converteer elk HTML bestand naar JPG
for html_file in html_files:
    # Volledige pad naar het HTML bestand
    html_path = os.path.abspath(html_file)
    
    # Converteer naar file:// URL
    url = f"file:///{html_path}"
    
    # Bestandsnaam voor de JPG
    jpg_file = os.path.join("DataModuleren", "Quiz", html_file.replace(".html", ".jpg"))
    
    print(f"Converting {html_file} to {jpg_file}...")
    
    # Open de HTML in de browser
    driver.get(url)
    
    # Wacht even zodat de pagina volledig kan laden
    time.sleep(2)
    
    # Neem een screenshot
    driver.save_screenshot(jpg_file)
    
    print(f"Saved {jpg_file}")

# Sluit de browser
driver.quit()

print("Conversion complete!")
