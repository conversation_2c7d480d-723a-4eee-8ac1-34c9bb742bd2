<!DOCTYPE html>
<html>
<head>
    <title>Logisch ERD V<PERSON>beeld met <PERSON>'s Foot Notatie</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: white;
        }
        .container {
            width: 900px;
            height: 700px;
            border: 1px solid #ccc;
            position: relative;
            background-color: white;
        }
        .title {
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            margin-top: 20px;
        }
        .entity {
            width: 200px;
            border: 2px solid black;
            position: absolute;
            background-color: white;
        }
        .entity-header {
            background-color: #f0f0f0;
            padding: 5px;
            text-align: center;
            font-weight: bold;
            border-bottom: 1px solid black;
        }
        .entity-attributes {
            padding: 5px;
        }
        .attribute {
            margin: 3px 0;
            font-size: 12px;
        }
        .pk {
            text-decoration: underline;
            font-weight: bold;
        }
        .relationship {
            position: absolute;
            font-size: 14px;
        }
        .note {
            position: absolute;
            bottom: 20px;
            left: 20px;
            font-size: 14px;
            width: 860px;
        }
        .crow-foot {
            position: absolute;
            font-size: 20px;
            color: black;
        }
        .legend {
            position: absolute;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #f9f9f9;
            width: 200px;
        }
        .legend-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .legend-item {
            margin: 5px 0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">Logisch ERD Voorbeeld - Webwinkel (Crow's Foot Notatie)</div>
        
        <!-- Entities with attributes -->
        <div class="entity" style="top: 80px; left: 100px;">
            <div class="entity-header">KLANT</div>
            <div class="entity-attributes">
                <div class="attribute pk">klant_id</div>
                <div class="attribute">voornaam</div>
                <div class="attribute">achternaam</div>
                <div class="attribute">email</div>
                <div class="attribute">telefoonnummer</div>
                <div class="attribute">adres</div>
                <div class="attribute">postcode</div>
                <div class="attribute">woonplaats</div>
            </div>
        </div>
        
        <div class="entity" style="top: 80px; left: 550px;">
            <div class="entity-header">BESTELLING</div>
            <div class="entity-attributes">
                <div class="attribute pk">bestelling_id</div>
                <div class="attribute">klant_id</div>
                <div class="attribute">datum</div>
                <div class="attribute">status</div>
                <div class="attribute">totaalbedrag</div>
                <div class="attribute">verzendkosten</div>
                <div class="attribute">betaalmethode</div>
            </div>
        </div>
        
        <div class="entity" style="top: 350px; left: 100px;">
            <div class="entity-header">CATEGORIE</div>
            <div class="entity-attributes">
                <div class="attribute pk">categorie_id</div>
                <div class="attribute">naam</div>
                <div class="attribute">beschrijving</div>
                <div class="attribute">hoofdcategorie_id</div>
            </div>
        </div>
        
        <div class="entity" style="top: 350px; left: 550px;">
            <div class="entity-header">PRODUCT</div>
            <div class="entity-attributes">
                <div class="attribute pk">product_id</div>
                <div class="attribute">categorie_id</div>
                <div class="attribute">naam</div>
                <div class="attribute">beschrijving</div>
                <div class="attribute">prijs</div>
                <div class="attribute">voorraad</div>
                <div class="attribute">gewicht</div>
                <div class="attribute">afmetingen</div>
            </div>
        </div>
        
        <div class="entity" style="top: 230px; left: 325px;">
            <div class="entity-header">BESTELLING_PRODUCT</div>
            <div class="entity-attributes">
                <div class="attribute pk">bestelling_id</div>
                <div class="attribute pk">product_id</div>
                <div class="attribute">aantal</div>
                <div class="attribute">prijs_per_stuk</div>
                <div class="attribute">subtotaal</div>
            </div>
        </div>
        
        <!-- Lines (simplified for HTML representation) -->
        <div style="position: absolute; top: 130px; left: 300px; width: 250px; height: 1px; background-color: black;"></div>
        <div style="position: absolute; top: 260px; left: 425px; width: 1px; height: 90px; background-color: black;"></div>
        <div style="position: absolute; top: 380px; left: 300px; width: 250px; height: 1px; background-color: black;"></div>
        <div style="position: absolute; top: 130px; left: 650px; width: 1px; height: 100px; background-color: black;"></div>
        <div style="position: absolute; top: 230px; left: 525px; width: 125px; height: 1px; background-color: black;"></div>
        <div style="position: absolute; top: 280px; left: 425px; width: 125px; height: 1px; background-color: black;"></div>
        <div style="position: absolute; top: 280px; left: 550px; width: 1px; height: 70px; background-color: black;"></div>
        
        <!-- Relationships -->
        <div class="relationship" style="top: 110px; left: 380px;">plaatst</div>
        <div class="relationship" style="top: 210px; left: 570px;">bevat</div>
        <div class="relationship" style="top: 360px; left: 380px;">behoort tot</div>
        
        <!-- Crow's Foot Notation (simplified ASCII representation) -->
        <!-- KLANT to BESTELLING (1 to many) -->
        <div class="crow-foot" style="top: 115px; left: 295px;">|</div>
        <div class="crow-foot" style="top: 115px; left: 535px;">&#x3C;|</div>
        
        <!-- BESTELLING to BESTELLING_PRODUCT (1 to many) -->
        <div class="crow-foot" style="top: 215px; left: 645px;">|</div>
        <div class="crow-foot" style="top: 215px; left: 525px;">&#x3C;|</div>
        
        <!-- PRODUCT to BESTELLING_PRODUCT (1 to many) -->
        <div class="crow-foot" style="top: 335px; left: 545px;">|</div>
        <div class="crow-foot" style="top: 265px; left: 425px;">&#x3C;|</div>
        
        <!-- CATEGORIE to PRODUCT (1 to many) -->
        <div class="crow-foot" style="top: 365px; left: 295px;">|</div>
        <div class="crow-foot" style="top: 365px; left: 535px;">&#x3C;|</div>
        
        <!-- Legend for Crow's Foot Notation -->
        <div class="legend" style="top: 500px; left: 350px;">
            <div class="legend-title">Crow's Foot Notatie Legenda</div>
            <div class="legend-item">| — Exact één (1)</div>
            <div class="legend-item">O| — Nul of één (0..1)</div>
            <div class="legend-item">&#x3C;| — Eén of meer (1..n)</div>
            <div class="legend-item">&#x3C; — Nul of meer (0..n)</div>
        </div>
        
        <!-- Note -->
        <div class="note">
            <strong>Opmerking:</strong> Een logisch ERD bouwt voort op het conceptuele ERD door attributen toe te voegen aan elke entiteit. 
            Ook is er een koppeltabel (BESTELLING_PRODUCT) toegevoegd om de veel-op-veel relatie tussen BESTELLING en PRODUCT te implementeren.
            Primaire sleutels zijn onderstreept. In een logisch ERD worden nog geen datatypes of implementatiedetails gespecificeerd.
            De relaties worden weergegeven met de Crow's Foot notatie, zoals ook in draw.io wordt gebruikt.
        </div>
    </div>
    
    <div style="margin-top: 20px;">
        <h2>Verschillen tussen Conceptueel en Logisch ERD</h2>
        <ul>
            <li><strong>Attributen:</strong> Het logische ERD bevat attributen (eigenschappen) voor elke entiteit, terwijl het conceptuele ERD alleen entiteiten toont.</li>
            <li><strong>Primaire sleutels:</strong> Het logische ERD identificeert primaire sleutels (onderstreept), terwijl het conceptuele ERD geen sleutels bevat.</li>
            <li><strong>Koppeltabellen:</strong> Het logische ERD lost veel-op-veel relaties op door koppeltabellen toe te voegen (BESTELLING_PRODUCT).</li>
            <li><strong>Notatie voor relaties:</strong> Het logische ERD gebruikt de Crow's Foot notatie om de cardinaliteit van relaties preciezer weer te geven.</li>
            <li><strong>Detail niveau:</strong> Het logische ERD is gedetailleerder en dichter bij de daadwerkelijke database-implementatie.</li>
            <li><strong>Doelgroep:</strong> Het logische ERD is meer gericht op databaseontwerpers, terwijl het conceptuele ERD ook voor niet-technische stakeholders begrijpelijk is.</li>
        </ul>
    </div>
</body>
</html>
