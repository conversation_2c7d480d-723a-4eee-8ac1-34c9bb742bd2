<!DOCTYPE html>
<html>
<head>
    <title>Recursieve Relatie <PERSON>beeld</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .diagram {
            width: 800px;
            height: 500px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: white;
        }
        .entity {
            position: absolute;
            width: 200px;
            border: 2px solid #333;
            background-color: #f8f8f8;
            border-radius: 5px;
            text-align: center;
        }
        .entity-header {
            background-color: #333;
            color: white;
            padding: 8px;
            font-weight: bold;
        }
        .entity-body {
            padding: 10px;
        }
        .attribute {
            text-align: left;
            margin: 5px;
            padding: 3px;
        }
        .pk {
            font-weight: bold;
            text-decoration: underline;
        }
        .fk {
            font-style: italic;
            color: #555;
        }
        .line {
            position: absolute;
            background-color: #555;
        }
        .cardinality {
            position: absolute;
            font-weight: bold;
            color: #333;
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .explanation {
            margin: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .arrow {
            position: absolute;
            width: 0; 
            height: 0;
        }
    </style>
</head>
<body>
    <div class="title">Recursieve Relatie Voorbeeld</div>
    <div class="subtitle">Manager-Verkoper Relatie</div>
    
    <div class="diagram">
        <!-- Conceptueel ERD -->
        <div style="position: absolute; top: 20px; left: 50px; font-weight: bold;">
            Conceptueel ERD
        </div>
        
        <div class="entity" style="top: 60px; left: 300px;">
            <div class="entity-header">Verkoper</div>
            <div class="entity-body">
                <div class="attribute">ID</div>
                <div class="attribute">Naam</div>
                <div class="attribute">Attribuut1</div>
                <div class="attribute">Attribuut2</div>
            </div>
        </div>
        
        <!-- Recursieve relatie -->
        <div style="position: absolute; top: 150px; left: 500px; width: 100px; height: 100px; border: 2px solid #555; border-radius: 50%; border-style: dashed;"></div>
        <div style="position: absolute; top: 190px; left: 510px; font-weight: bold;">Managet</div>
        
        <!-- Cardinaliteit -->
        <div class="cardinality" style="top: 130px; left: 510px;">1</div>
        <div class="cardinality" style="top: 230px; left: 510px;">N</div>
        
        <!-- Pijl -->
        <div style="position: absolute; top: 110px; left: 500px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-bottom: 15px solid #555;"></div>
        
        <!-- Logisch ERD -->
        <div style="position: absolute; top: 280px; left: 50px; font-weight: bold;">
            Logisch ERD
        </div>
        
        <div class="entity" style="top: 320px; left: 300px;">
            <div class="entity-header">Verkoper</div>
            <div class="entity-body">
                <div class="attribute pk">VerkoperID</div>
                <div class="attribute">Naam</div>
                <div class="attribute">Attribuut1</div>
                <div class="attribute">Attribuut2</div>
                <div class="attribute fk">ManagerID</div>
            </div>
        </div>
        
        <!-- Recursieve relatie -->
        <div style="position: absolute; top: 320px; left: 500px; width: 100px; height: 100px; border: 2px solid #555; border-radius: 0; border-style: solid;"></div>
        <div style="position: absolute; top: 360px; left: 510px; font-weight: bold;">Managet</div>
        
        <!-- Cardinaliteit -->
        <div class="cardinality" style="top: 340px; left: 510px;">1</div>
        <div class="cardinality" style="top: 400px; left: 510px;">N</div>
        
        <!-- Pijl -->
        <div style="position: absolute; top: 320px; left: 500px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-bottom: 15px solid #555;"></div>
    </div>
    
    <div class="explanation">
        <p><strong>Toelichting:</strong></p>
        <p>Dit diagram toont een recursieve relatie in een ERD, gebaseerd op het voorbeeld uit de cursus:</p>
        <ul>
            <li>Een Manager kan veel andere verkopers managen</li>
            <li>Een verkoper wordt beheerd door slechts één manager</li>
        </ul>
        <p>Een recursieve relatie is een relatie van een entiteit met zichzelf. In dit geval is de "Verkoper" entiteit gerelateerd aan zichzelf via de "Managet" relatie.</p>
        <p>In het conceptuele ERD wordt dit weergegeven door een relatie die terugverwijst naar dezelfde entiteit.</p>
        <p>In het logische ERD wordt dit geïmplementeerd door een vreemde sleutel (ManagerID) toe te voegen aan de Verkoper entiteit, die verwijst naar de primaire sleutel (VerkoperID) van dezelfde tabel.</p>
        <p>De kardinaliteit is één-op-veel (1:N):</p>
        <ul>
            <li>Eén manager (1) kan meerdere verkopers (N) managen</li>
            <li>Elke verkoper heeft maximaal één manager (1)</li>
        </ul>
        <p>In een fysiek ERD zou dit worden geïmplementeerd als een tabel met een vreemde sleutel die naar zichzelf verwijst.</p>
    </div>
</body>
</html>
