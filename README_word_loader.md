# Word Document Loader

This is a simple application that allows you to load and extract text from Microsoft Word documents (.docx files).

## Features

- Open and read Microsoft Word (.docx) documents
- Extract and display text content from paragraphs and tables
- View document properties and metadata
- Save extracted text to a text file
- Simple and intuitive graphical user interface

## Requirements

- Python 3.6 or higher
- python-docx library
- tkinter (usually comes with Python)

## Installation

1. Make sure you have Python installed on your system
2. Install the required library:

```bash
pip install python-docx
```

## Usage

### Graphical User Interface (GUI)

To launch the application with a graphical interface:

```bash
python word_document_loader.py
```

This will open a window where you can:
- Click "Open Word Document" to select and load a .docx file
- View the extracted text in the "Document Text" tab
- View document properties in the "Document Properties" tab
- Click "Save Extracted Text" to save the text to a file

### Command Line Interface (CLI)

You can also use the script from the command line to extract text from a Word document:

```bash
python word_document_loader.py path/to/your/document.docx
```

This will print the extracted text to the console.

## How It Works

The application uses the `python-docx` library to:
1. Open and parse .docx files
2. Extract text from paragraphs and tables
3. Retrieve document properties and metadata

## Limitations

- Only works with .docx files (not .doc files from older Word versions)
- Some complex formatting may be lost during text extraction
- Some document features like comments, headers/footers, and text boxes may not be fully extracted

## Troubleshooting

If you encounter issues:

1. Make sure you have the latest version of python-docx installed
2. Verify that your Word document is in .docx format
3. Try with a simpler document if you're having problems with a complex one

## License

This software is provided as-is under the MIT License.
