import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

# Create a directory for saving visualizations
if not os.path.exists('key_insights'):
    os.makedirs('key_insights')

# Set display options for better readability
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)

# Set plot style
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("Set2")

# Load the CSV file
print("Loading the CSV file...")
df = pd.read_csv('Kikker.csv')

# Clean percentage columns
percentage_columns = ['Klantretourpercentage', 'Benuttingsgraad', 'Defectpercentage']
for col in percentage_columns:
    if col in df.columns and df[col].dtype == 'object':
        # Extract numeric values from percentage strings
        df[col + '_cleaned'] = df[col].str.extract(r'(\d+\.\d+|\d+)').astype(float) / 100

# 1. Coffee Bean Distribution and Quality
print("\nAnalyzing coffee bean distribution and quality...")
plt.figure(figsize=(12, 8))

# Create subplot for bean distribution
plt.subplot(2, 2, 1)
bean_counts = df['Koffieboon'].value_counts()
plt.pie(bean_counts, labels=bean_counts.index, autopct='%1.1f%%', startangle=90)
plt.title('Distribution of Coffee Bean Types')

# Create subplot for quality by bean type
plt.subplot(2, 2, 2)
quality_by_bean = pd.crosstab(df['Koffieboon'], df['Panel Test'])
quality_by_bean.plot(kind='bar', stacked=True)
plt.title('Quality Assessment by Bean Type')
plt.xlabel('Coffee Bean Type')
plt.ylabel('Count')
plt.legend(title='Quality Assessment')
plt.xticks(rotation=45)

# Create subplot for defect percentage by bean type
plt.subplot(2, 2, 3)
sns.boxplot(x='Koffieboon', y='Defectpercentage_cleaned', data=df)
plt.title('Defect Percentage by Bean Type')
plt.xlabel('Coffee Bean Type')
plt.ylabel('Defect Percentage')
plt.xticks(rotation=45)

# Create subplot for customer return percentage by bean type
plt.subplot(2, 2, 4)
sns.boxplot(x='Koffieboon', y='Klantretourpercentage_cleaned', data=df)
plt.title('Customer Return Percentage by Bean Type')
plt.xlabel('Coffee Bean Type')
plt.ylabel('Customer Return Percentage')
plt.xticks(rotation=45)

plt.tight_layout()
plt.savefig('key_insights/coffee_bean_analysis.png')
plt.close()

# 2. Machine Performance Analysis
print("Analyzing machine performance...")
plt.figure(figsize=(15, 10))

# Create subplot for defect percentage by packaging machine
plt.subplot(2, 3, 1)
sns.boxplot(x='PackagingApparaat', y='Defectpercentage_cleaned', data=df[df['PackagingApparaat'].isin(['Packager 1', 'Packager 2', 'Packager 3', 'Packager 4', 'Packager 5'])])
plt.title('Defect Percentage by Packaging Machine')
plt.xlabel('Packaging Machine')
plt.ylabel('Defect Percentage')
plt.xticks(rotation=45)

# Create subplot for defect percentage by filling machine
plt.subplot(2, 3, 2)
sns.boxplot(x='FillingApparaat', y='Defectpercentage_cleaned', data=df)
plt.title('Defect Percentage by Filling Machine')
plt.xlabel('Filling Machine')
plt.ylabel('Defect Percentage')
plt.xticks(rotation=45)

# Create subplot for defect percentage by grinding machine
plt.subplot(2, 3, 3)
sns.boxplot(x='GrindingApparaat', y='Defectpercentage_cleaned', data=df)
plt.title('Defect Percentage by Grinding Machine')
plt.xlabel('Grinding Machine')
plt.ylabel('Defect Percentage')
plt.xticks(rotation=45)

# Create subplot for utilization rate by packaging machine
plt.subplot(2, 3, 4)
sns.boxplot(x='PackagingApparaat', y='Benuttingsgraad_cleaned', data=df[df['PackagingApparaat'].isin(['Packager 1', 'Packager 2', 'Packager 3', 'Packager 4', 'Packager 5'])])
plt.title('Utilization Rate by Packaging Machine')
plt.xlabel('Packaging Machine')
plt.ylabel('Utilization Rate')
plt.xticks(rotation=45)

# Create subplot for utilization rate by filling machine
plt.subplot(2, 3, 5)
sns.boxplot(x='FillingApparaat', y='Benuttingsgraad_cleaned', data=df)
plt.title('Utilization Rate by Filling Machine')
plt.xlabel('Filling Machine')
plt.ylabel('Utilization Rate')
plt.xticks(rotation=45)

# Create subplot for utilization rate by grinding machine
plt.subplot(2, 3, 6)
sns.boxplot(x='GrindingApparaat', y='Benuttingsgraad_cleaned', data=df)
plt.title('Utilization Rate by Grinding Machine')
plt.xlabel('Grinding Machine')
plt.ylabel('Utilization Rate')
plt.xticks(rotation=45)

plt.tight_layout()
plt.savefig('key_insights/machine_performance.png')
plt.close()

# 3. Quality Metrics Analysis
print("Analyzing quality metrics...")
plt.figure(figsize=(15, 10))

# Create subplot for distribution of defect percentage
plt.subplot(2, 2, 1)
sns.histplot(df['Defectpercentage_cleaned'].dropna(), kde=True)
plt.title('Distribution of Defect Percentage')
plt.xlabel('Defect Percentage')
plt.ylabel('Frequency')

# Create subplot for distribution of customer return percentage
plt.subplot(2, 2, 2)
sns.histplot(df['Klantretourpercentage_cleaned'].dropna(), kde=True)
plt.title('Distribution of Customer Return Percentage')
plt.xlabel('Customer Return Percentage')
plt.ylabel('Frequency')

# Create subplot for distribution of utilization rate
plt.subplot(2, 2, 3)
sns.histplot(df['Benuttingsgraad_cleaned'].dropna(), kde=True)
plt.title('Distribution of Utilization Rate')
plt.xlabel('Utilization Rate')
plt.ylabel('Frequency')

# Create subplot for quality assessment distribution
plt.subplot(2, 2, 4)
quality_counts = df['Panel Test'].value_counts()
plt.pie(quality_counts, labels=quality_counts.index, autopct='%1.1f%%', startangle=90)
plt.title('Distribution of Quality Assessment')

plt.tight_layout()
plt.savefig('key_insights/quality_metrics.png')
plt.close()

# 4. Origin and Quality Analysis
print("Analyzing coffee origin and quality...")
# Get top 10 origins by count
top_origins = df['Herkomst'].value_counts().nlargest(10).index.tolist()
df_top_origins = df[df['Herkomst'].isin(top_origins)]

plt.figure(figsize=(15, 10))

# Create subplot for origin distribution
plt.subplot(2, 2, 1)
origin_counts = df_top_origins['Herkomst'].value_counts()
plt.bar(origin_counts.index, origin_counts.values)
plt.title('Top 10 Coffee Origins')
plt.xlabel('Country of Origin')
plt.ylabel('Count')
plt.xticks(rotation=45)

# Create subplot for quality by origin
plt.subplot(2, 2, 2)
quality_by_origin = pd.crosstab(df_top_origins['Herkomst'], df_top_origins['Panel Test'])
quality_by_origin.plot(kind='bar', stacked=True)
plt.title('Quality Assessment by Origin (Top 10)')
plt.xlabel('Country of Origin')
plt.ylabel('Count')
plt.legend(title='Quality Assessment')
plt.xticks(rotation=45)

# Create subplot for defect percentage by origin
plt.subplot(2, 2, 3)
sns.boxplot(x='Herkomst', y='Defectpercentage_cleaned', data=df_top_origins)
plt.title('Defect Percentage by Origin (Top 10)')
plt.xlabel('Country of Origin')
plt.ylabel('Defect Percentage')
plt.xticks(rotation=45)

# Create subplot for customer return percentage by origin
plt.subplot(2, 2, 4)
sns.boxplot(x='Herkomst', y='Klantretourpercentage_cleaned', data=df_top_origins)
plt.title('Customer Return Percentage by Origin (Top 10)')
plt.xlabel('Country of Origin')
plt.ylabel('Customer Return Percentage')
plt.xticks(rotation=45)

plt.tight_layout()
plt.savefig('key_insights/origin_analysis.png')
plt.close()

# 5. Risk Qualification Analysis
print("Analyzing risk qualification...")
plt.figure(figsize=(15, 10))

# Create subplot for risk qualification distribution
plt.subplot(2, 2, 1)
risk_counts = df['Risicokwalificatie'].value_counts()
plt.pie(risk_counts, labels=risk_counts.index, autopct='%1.1f%%', startangle=90)
plt.title('Distribution of Risk Qualification')

# Create subplot for defect percentage by risk qualification
plt.subplot(2, 2, 2)
sns.boxplot(x='Risicokwalificatie', y='Defectpercentage_cleaned', data=df)
plt.title('Defect Percentage by Risk Qualification')
plt.xlabel('Risk Qualification')
plt.ylabel('Defect Percentage')
plt.xticks(rotation=45)

# Create subplot for customer return percentage by risk qualification
plt.subplot(2, 2, 3)
sns.boxplot(x='Risicokwalificatie', y='Klantretourpercentage_cleaned', data=df)
plt.title('Customer Return Percentage by Risk Qualification')
plt.xlabel('Risk Qualification')
plt.ylabel('Customer Return Percentage')
plt.xticks(rotation=45)

# Create subplot for quality assessment by risk qualification
plt.subplot(2, 2, 4)
quality_by_risk = pd.crosstab(df['Risicokwalificatie'], df['Panel Test'])
quality_by_risk.plot(kind='bar', stacked=True)
plt.title('Quality Assessment by Risk Qualification')
plt.xlabel('Risk Qualification')
plt.ylabel('Count')
plt.legend(title='Quality Assessment')
plt.xticks(rotation=45)

plt.tight_layout()
plt.savefig('key_insights/risk_analysis.png')
plt.close()

print("\nData visualization complete!")
print("Key insights visualizations saved in the 'key_insights' folder.")
