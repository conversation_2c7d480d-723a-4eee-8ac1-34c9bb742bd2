<mxfile host="Electron" modified="2024-10-22T15:00:00.000Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.1.1 Chrome/132.0.6834.210 Electron/34.3.3 Safari/537.36" version="26.1.1">
  <diagram name="Page-1" id="FysiekERD-EuroCaps-Stap1">
    <mxGraphModel dx="1434" dy="436" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Titel -->
        <mxCell id="title" value="EuroCaps - Fysiek ERD (Stap 1: <PERSON>sis tabellen)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="100" y="20" width="600" height="40" as="geometry" />
        </mxCell>
        
        <!-- Tabellen -->
        <!-- SoortPartner tabel -->
        <mxCell id="table-soort-partner" value="SoortPartner" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=1;fontStyle=1;align=center;resizeLast=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="100" y="100" width="200" height="90" as="geometry" />
        </mxCell>
        <mxCell id="soort-partner-pk" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-soort-partner">
          <mxGeometry y="30" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="soort-partner-pk-key" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" vertex="1" parent="soort-partner-pk">
          <mxGeometry width="40" height="30" as="geometry">
            <mxRectangle width="40" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="soort-partner-pk-name" value="SoortPartnerId INT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=1;overflow=hidden;" vertex="1" parent="soort-partner-pk">
          <mxGeometry x="40" width="160" height="30" as="geometry">
            <mxRectangle width="160" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="soort-partner-omschrijving" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-soort-partner">
          <mxGeometry y="60" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="soort-partner-omschrijving-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" vertex="1" parent="soort-partner-omschrijving">
          <mxGeometry width="40" height="30" as="geometry">
            <mxRectangle width="40" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="soort-partner-omschrijving-name" value="Omschrijving VARCHAR(50)" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" vertex="1" parent="soort-partner-omschrijving">
          <mxGeometry x="40" width="160" height="30" as="geometry">
            <mxRectangle width="160" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        
        <!-- Partner tabel -->
        <mxCell id="table-partner" value="Partner" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=1;fontStyle=1;align=center;resizeLast=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="400" y="100" width="200" height="240" as="geometry" />
        </mxCell>
        <mxCell id="partner-pk" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-partner">
          <mxGeometry y="30" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="partner-pk-key" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" vertex="1" parent="partner-pk">
          <mxGeometry width="40" height="30" as="geometry">
            <mxRectangle width="40" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="partner-pk-name" value="PartnerId INT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=1;overflow=hidden;" vertex="1" parent="partner-pk">
          <mxGeometry x="40" width="160" height="30" as="geometry">
            <mxRectangle width="160" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="partner-fk" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-partner">
          <mxGeometry y="60" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="partner-fk-key" value="FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" vertex="1" parent="partner-fk">
          <mxGeometry width="40" height="30" as="geometry">
            <mxRectangle width="40" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="partner-fk-name" value="SoortPartnerId INT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=1;overflow=hidden;" vertex="1" parent="partner-fk">
          <mxGeometry x="40" width="160" height="30" as="geometry">
            <mxRectangle width="160" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="partner-bedrijfsnaam" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-partner">
          <mxGeometry y="90" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="partner-bedrijfsnaam-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" vertex="1" parent="partner-bedrijfsnaam">
          <mxGeometry width="40" height="30" as="geometry">
            <mxRectangle width="40" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="partner-bedrijfsnaam-name" value="Bedrijfsnaam VARCHAR(100)" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" vertex="1" parent="partner-bedrijfsnaam">
          <mxGeometry x="40" width="160" height="30" as="geometry">
            <mxRectangle width="160" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="partner-straatnaam" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-partner">
          <mxGeometry y="120" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="partner-straatnaam-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" vertex="1" parent="partner-straatnaam">
          <mxGeometry width="40" height="30" as="geometry">
            <mxRectangle width="40" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="partner-straatnaam-name" value="Straatnaam VARCHAR(100)" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" vertex="1" parent="partner-straatnaam">
          <mxGeometry x="40" width="160" height="30" as="geometry">
            <mxRectangle width="160" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="partner-huisnummer" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-partner">
          <mxGeometry y="150" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="partner-huisnummer-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" vertex="1" parent="partner-huisnummer">
          <mxGeometry width="40" height="30" as="geometry">
            <mxRectangle width="40" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="partner-huisnummer-name" value="Huisnummer VARCHAR(10)" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" vertex="1" parent="partner-huisnummer">
          <mxGeometry x="40" width="160" height="30" as="geometry">
            <mxRectangle width="160" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="partner-postcode" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-partner">
          <mxGeometry y="180" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="partner-postcode-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" vertex="1" parent="partner-postcode">
          <mxGeometry width="40" height="30" as="geometry">
            <mxRectangle width="40" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="partner-postcode-name" value="Postcode VARCHAR(10)" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" vertex="1" parent="partner-postcode">
          <mxGeometry x="40" width="160" height="30" as="geometry">
            <mxRectangle width="160" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="partner-plaats" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-partner">
          <mxGeometry y="210" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="partner-plaats-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" vertex="1" parent="partner-plaats">
          <mxGeometry width="40" height="30" as="geometry">
            <mxRectangle width="40" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="partner-plaats-name" value="Plaats VARCHAR(50)" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" vertex="1" parent="partner-plaats">
          <mxGeometry x="40" width="160" height="30" as="geometry">
            <mxRectangle width="160" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        
        <!-- Relatie SoortPartner - Partner -->
        <mxCell id="rel-soort-partner-partner" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERoneToMany;startArrow=ERmandOne;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="soort-partner-pk" target="partner-fk">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="350" y="400" as="sourcePoint" />
            <mxPoint x="450" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Toelichting -->
        <mxCell id="toelichting-titel" value="Toelichting Fysiek ERD - Stap 1" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="100" y="380" width="500" height="30" as="geometry" />
        </mxCell>
        <mxCell id="toelichting-text" value="In deze eerste stap van het fysieke ERD zijn de tabellen SoortPartner en Partner gedefinieerd met:&#xa;&#xa;1. Primaire sleutels (PK) met datatype INT voor efficiënte indexering&#xa;2. Vreemde sleutel (FK) in Partner die verwijst naar SoortPartner&#xa;3. Specifieke datatypes voor alle velden (VARCHAR met geschikte lengtes)&#xa;4. Een-op-veel relatie tussen SoortPartner en Partner&#xa;&#xa;In de volgende stappen worden de overige tabellen toegevoegd." style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="100" y="410" width="500" height="130" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
