<!DOCTYPE html>
<html>
<head>
    <title>Logisch ERD Voorbeeld - Draw.io Stijl</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: white;
        }
        .container {
            width: 1000px;
            height: 800px;
            border: 1px solid #ccc;
            position: relative;
            background-color: white;
        }
        .title {
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            margin-top: 20px;
            margin-bottom: 40px;
        }
        .table {
            position: absolute;
            background-color: white;
            border: 2px solid black;
            z-index: 2;
        }
        .table-header {
            background-color: #f0f0f0;
            text-align: center;
            font-weight: bold;
            padding: 8px;
            border-bottom: 1px solid black;
        }
        .table-content {
            display: table;
            width: 100%;
        }
        .table-row {
            display: table-row;
        }
        .pk-column {
            display: table-cell;
            width: 40px;
            border-right: 1px solid black;
            text-align: center;
            padding: 5px;
            font-weight: bold;
        }
        .attribute-column {
            display: table-cell;
            padding: 5px;
            border-bottom: 1px solid #ddd;
        }
        .pk {
            text-decoration: underline;
        }
        .relationship {
            position: absolute;
            font-size: 14px;
            z-index: 1;
            background-color: white;
            padding: 0 5px;
        }
        .note {
            position: absolute;
            bottom: 20px;
            left: 20px;
            font-size: 14px;
            width: 960px;
        }
        .legend {
            position: absolute;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #f9f9f9;
            width: 250px;
            z-index: 2;
        }
        .legend-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .legend-item {
            margin: 5px 0;
            font-size: 12px;
        }
        /* SVG styles for better lines */
        svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">Logisch ERD Voorbeeld - Webwinkel (Draw.io Stijl)</div>
        
        <!-- SVG for lines and relationship symbols -->
        <svg>
            <!-- KLANT to BESTELLING -->
            <path d="M 350,150 H 550" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- BESTELLING to BESTELLING_PRODUCT -->
            <path d="M 650,200 H 500 V 300" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- PRODUCT to BESTELLING_PRODUCT -->
            <path d="M 650,450 H 500 V 350" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- CATEGORIE to PRODUCT -->
            <path d="M 350,450 H 550" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- Draw.io Entity Relation pijlen -->
            <!-- KLANT to BESTELLING (1 to many) -->
            <!-- Exact één (1) -->
            <circle cx="360" cy="150" r="6" stroke="black" stroke-width="1.5" fill="white"/>
            <!-- Eén of meer (1..n) -->
            <path d="M 535,145 L 550,150 L 535,155" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 535,140 L 550,150 L 535,160" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- BESTELLING to BESTELLING_PRODUCT (1 to many) -->
            <!-- Exact één (1) -->
            <circle cx="650" cy="190" r="6" stroke="black" stroke-width="1.5" fill="white"/>
            <!-- Eén of meer (1..n) -->
            <path d="M 505,290 L 500,300 L 495,290" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 510,285 L 500,300 L 490,285" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- PRODUCT to BESTELLING_PRODUCT (1 to many) -->
            <!-- Exact één (1) -->
            <circle cx="650" cy="440" r="6" stroke="black" stroke-width="1.5" fill="white"/>
            <!-- Eén of meer (1..n) -->
            <path d="M 505,360 L 500,350 L 495,360" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 510,365 L 500,350 L 490,365" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- CATEGORIE to PRODUCT (1 to many) -->
            <!-- Exact één (1) -->
            <circle cx="360" cy="450" r="6" stroke="black" stroke-width="1.5" fill="white"/>
            <!-- Eén of meer (1..n) -->
            <path d="M 535,445 L 550,450 L 535,455" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 535,440 L 550,450 L 535,460" stroke="black" stroke-width="1.5" fill="none"/>
        </svg>
        
        <!-- Entities with draw.io table style -->
        <div class="table" style="top: 100px; left: 150px; width: 200px;">
            <div class="table-header">KLANT</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="pk-column">PK</div>
                    <div class="attribute-column pk">klant_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">voornaam</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">achternaam</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">email</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">telefoonnummer</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">adres</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">postcode</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">woonplaats</div>
                </div>
            </div>
        </div>
        
        <div class="table" style="top: 100px; left: 550px; width: 200px;">
            <div class="table-header">BESTELLING</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="pk-column">PK</div>
                    <div class="attribute-column pk">bestelling_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">klant_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">datum</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">status</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">totaalbedrag</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">verzendkosten</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">betaalmethode</div>
                </div>
            </div>
        </div>
        
        <div class="table" style="top: 400px; left: 150px; width: 200px;">
            <div class="table-header">CATEGORIE</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="pk-column">PK</div>
                    <div class="attribute-column pk">categorie_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">naam</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">beschrijving</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">hoofdcategorie_id</div>
                </div>
            </div>
        </div>
        
        <div class="table" style="top: 400px; left: 550px; width: 200px;">
            <div class="table-header">PRODUCT</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="pk-column">PK</div>
                    <div class="attribute-column pk">product_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">categorie_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">naam</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">beschrijving</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">prijs</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">voorraad</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">gewicht</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">afmetingen</div>
                </div>
            </div>
        </div>
        
        <div class="table" style="top: 300px; left: 350px; width: 200px;">
            <div class="table-header">BESTELLING_PRODUCT</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="pk-column">PK</div>
                    <div class="attribute-column pk">bestelling_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column">PK</div>
                    <div class="attribute-column pk">product_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">aantal</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">prijs_per_stuk</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="attribute-column">subtotaal</div>
                </div>
            </div>
        </div>
        
        <!-- Relationships -->
        <div class="relationship" style="top: 130px; left: 420px;">plaatst</div>
        <div class="relationship" style="top: 250px; left: 580px;">bevat</div>
        <div class="relationship" style="top: 380px; left: 510px;">bevat</div>
        <div class="relationship" style="top: 430px; left: 420px;">behoort tot</div>
        
        <!-- Legend for Draw.io Entity Relation Notation -->
        <div class="legend" style="top: 600px; left: 350px;">
            <div class="legend-title">Draw.io Entity Relation Pijlen Legenda</div>
            <div class="legend-item">O — Exact één (1)</div>
            <div class="legend-item">O — Nul of één (0..1)</div>
            <div class="legend-item">>| — Eén of meer (1..n)</div>
            <div class="legend-item">O>| — Nul of meer (0..n)</div>
            <div class="legend-item">Deze pijlen zijn te vinden in de Entity Relation bibliotheek van draw.io</div>
        </div>
        
        <!-- Note -->
        <div class="note">
            <strong>Opmerking:</strong> Een logisch ERD bouwt voort op het conceptuele ERD door attributen toe te voegen aan elke entiteit. 
            Ook is er een koppeltabel (BESTELLING_PRODUCT) toegevoegd om de veel-op-veel relatie tussen BESTELLING en PRODUCT te implementeren.
            Primaire sleutels zijn gemarkeerd met "PK". In een logisch ERD worden nog geen datatypes of implementatiedetails gespecificeerd.
            De relaties worden weergegeven met de pijlen uit de Entity Relation bibliotheek van draw.io.
        </div>
    </div>
    
    <div style="margin-top: 20px;">
        <h2>Verschillen tussen Conceptueel en Logisch ERD</h2>
        <ul>
            <li><strong>Attributen:</strong> Het logische ERD bevat attributen (eigenschappen) voor elke entiteit, terwijl het conceptuele ERD alleen entiteiten toont.</li>
            <li><strong>Primaire sleutels:</strong> Het logische ERD identificeert primaire sleutels (gemarkeerd met "PK"), terwijl het conceptuele ERD geen sleutels bevat.</li>
            <li><strong>Koppeltabellen:</strong> Het logische ERD lost veel-op-veel relaties op door koppeltabellen toe te voegen (BESTELLING_PRODUCT).</li>
            <li><strong>Notatie voor relaties:</strong> Het logische ERD gebruikt de Entity Relation pijlen uit draw.io om de cardinaliteit van relaties preciezer weer te geven.</li>
            <li><strong>Detail niveau:</strong> Het logische ERD is gedetailleerder en dichter bij de daadwerkelijke database-implementatie.</li>
        </ul>
    </div>
</body>
</html>
