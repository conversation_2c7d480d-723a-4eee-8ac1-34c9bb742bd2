# Toelichting bij de ERD en 3NF Afbeeldingen

Deze afbeeldingen zijn gemaakt als visuele ondersteuning bij de quiz over Entity Relationship Diagrams (ERD) en de Derde Normaalvorm (3NF). Ze illustreren de belangrijkste concepten die in de quiz worden behandeld.

## 1. superhelden_many_to_many.jpg

Deze afbeelding illustreert een veel-op-veel (N:M) relatie tussen Superhelden en Groepen, gebaseerd op het voorbeeld uit de cursus:
- Een Superheld behoort tot 0 of meerdere groepen
- Een Groep kan 1 of meerdere superhelden bevatten

De afbeelding toont:
- De incorrecte implementatie met <PERSON><PERSON> tabel, waarbij de primaire sleutel niet uniek is
- De correcte implementatie met drie tabellen (Superheld, Groep, en een koppeltabel Superheld_Groep)
- De kardinaliteit van de relaties

Dit sluit aan bij vragen 3, 4 en 5 in Hoofdstuk 1 van de quiz en vraag 14 in Hoofdstuk 3.

## 2. normalisatie_3nf_voorbeeld.jpg

Deze afbeelding toont het stapsgewijze proces van normalisatie van 0NF naar 3NF aan de hand van een voorbeeld met orders, klanten en producten:
- 0NF: Niet-genormaliseerde tabel met redundantie en berekende velden
- 1NF: Atomaire attributen, geen repeterende groepen, primaire sleutel geïdentificeerd
- 2NF: Geen partiële afhankelijkheden (attributen die afhankelijk zijn van slechts een deel van de primaire sleutel)
- 3NF: Geen transitieve afhankelijkheden (niet-sleutelattributen die afhankelijk zijn van andere niet-sleutelattributen)

Dit sluit aan bij vragen 6-10 in Hoofdstuk 2 van de quiz en vragen 16-18 in Hoofdstuk 4.

## 3. recursieve_relatie_voorbeeld.jpg

Deze afbeelding illustreert een recursieve relatie, gebaseerd op het voorbeeld uit de cursus:
- Een Manager kan veel andere verkopers managen
- Een verkoper wordt beheerd door slechts één manager

De afbeelding toont:
- De conceptuele weergave van een recursieve relatie
- De logische implementatie met een vreemde sleutel die naar dezelfde tabel verwijst
- De kardinaliteit van de relatie (1:N)

Dit sluit aan bij vragen 15 in Hoofdstuk 3 en vragen 21-22 in Hoofdstuk 5 van de quiz.

## 4. erd_types_vergelijking.jpg

Deze afbeelding vergelijkt de drie typen ERD's:
- Conceptueel ERD: Hoogste abstractieniveau, focus op entiteiten en relaties zonder technische details
- Logisch ERD: Middenniveau van abstractie, voegt attributen en sleutels toe
- Fysiek ERD: Laagste abstractieniveau, bevat alle technische details voor implementatie

De afbeelding toont voor elk type:
- Een voorbeeld van hoe dezelfde database er in dat type ERD uitziet
- De belangrijkste kenmerken
- Een vergelijkingstabel met de verschillen tussen de drie typen

Dit sluit aan bij vragen 11-13 in Hoofdstuk 3 en vraag 20 in Hoofdstuk 4 van de quiz.

## Gebruik van de afbeeldingen

Deze afbeeldingen kunnen worden gebruikt als:
- Visuele ondersteuning bij het bestuderen van de concepten in de quiz
- Referentiemateriaal tijdens het beantwoorden van de quizvragen
- Hulpmiddel om de relatie tussen verschillende concepten beter te begrijpen

De afbeeldingen zijn specifiek ontworpen om de concepten uit de "IT Datamodelleren 2.7.pdf" cursus te illustreren en sluiten direct aan bij de vragen in de quiz.
