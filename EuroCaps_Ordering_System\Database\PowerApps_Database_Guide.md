# Using the Database Files with Power<PERSON>pps

This guide explains how to use the CSV database files with PowerApps to create the EuroCaps Ordering System.

## Step 1: Prepare the Data Source

### Option A: Using Excel (Recommended)

1. **Create an Excel workbook**:
   - Open Microsoft Excel
   - Create a new workbook
   - Create four sheets named: Customers, Products, Orders, and OrderItems

2. **Import the CSV data**:
   - For each sheet, go to Data > From Text/CSV
   - Select the corresponding CSV file (e.g., Customers.csv for the Customers sheet)
   - Click Import
   - Select "Load" to add the data to the sheet

3. **Format as tables**:
   - Select all data in each sheet
   - Go to Insert > Table
   - Ensure "My table has headers" is checked
   - Click OK
   - In the Table Design tab, rename each table to match its sheet name (Customers, Products, Orders, OrderItems)

4. **Save the workbook**:
   - Save as "EuroCaps_Database.xlsx"
   - Upload this file to your OneDrive for Business or SharePoint site

### Option B: Using CSV Files Directly

1. **Upload the CSV files**:
   - Sign in to your OneDrive for Business or SharePoint site
   - Create a folder named "EuroCaps_Database"
   - Upload all four CSV files to this folder

## Step 2: Connect to the Data Source in PowerApps

### Connecting to Excel:

1. **Create a new Canvas app**:
   - Go to https://make.powerapps.com
   - Click Create > Canvas app from blank > Tablet format
   - Name it "EuroCaps Ordering System"

2. **Add the data connection**:
   - In the PowerApps Studio, go to the Data panel (icon looks like a database)
   - Click "Add data"
   - Select "Excel Online (Business)"
   - Browse to the location where you saved the Excel file
   - Select the file
   - Choose all four tables (Customers, Products, Orders, OrderItems)
   - Click "Connect"

### Connecting to CSV Files:

1. **Create a new Canvas app** as described above

2. **Add the data connections**:
   - In the PowerApps Studio, go to the Data panel
   - Click "Add data"
   - Select "OneDrive for Business" or "SharePoint"
   - Browse to the location where you uploaded the CSV files
   - Connect to each CSV file individually

## Step 3: Use the Data in Your App

### Displaying Customer Data:

1. **Add a gallery control**:
   - Insert > Gallery > Vertical
   - In the Properties panel, set the Items property to: `Customers`
   - Customize the gallery to display customer information

### Creating a Product Catalog:

1. **Add a gallery control**:
   - Insert > Gallery > Flexible grid
   - Set the Items property to: `Products`
   - Customize to display product information, images, and prices

### Managing Orders:

1. **Display orders in a gallery**:
   - Insert a gallery control
   - Set the Items property to: `Orders`
   - Customize to show order details

2. **Show order items**:
   - Create a detail screen
   - Add a gallery
   - Set the Items property to: `Filter(OrderItems, OrderID = varSelectedOrder.OrderID)`
   - This will show items for the selected order

### Creating a New Order:

1. **Add a form for order header**:
   - Insert > Form > Edit
   - Set the Data source to: `Orders`
   - Configure required fields

2. **Add controls for order items**:
   - Create a mechanism to select products
   - Use a collection to temporarily store selected items
   - When the order is submitted, patch both the Orders and OrderItems tables

## Step 4: Implement Data Operations

### Creating Records:

```
// Create a new customer
Patch(
    Customers,
    Defaults(Customers),
    {
        CustomerName: txtName.Text,
        ContactPerson: txtContact.Text,
        Email: txtEmail.Text,
        Phone: txtPhone.Text,
        Address: txtAddress.Text
    }
);

// Create a new order
Set(
    newOrderID,
    Patch(
        Orders,
        Defaults(Orders),
        {
            OrderNumber: "ORD-" & Text(Max(Orders, OrderID) + 1),
            CustomerID: drpCustomer.Selected.CustomerID,
            OrderDate: Today(),
            DeliveryDate: datDelivery.SelectedDate,
            Status: "new",
            Notes: txtNotes.Text
        }
    ).OrderID
);

// Create order items
ForAll(
    colOrderItems,
    Patch(
        OrderItems,
        Defaults(OrderItems),
        {
            OrderID: newOrderID,
            ProductID: ProductID,
            Quantity: Quantity
        }
    )
);
```

### Reading Records:

```
// Filter orders by status
Filter(Orders, Status = "new" || Status = "processing")

// Get order details with customer information
LookUp(Customers, CustomerID = varSelectedOrder.CustomerID)

// Get order items with product details
AddColumns(
    Filter(OrderItems, OrderID = varSelectedOrder.OrderID),
    "ProductName", LookUp(Products, ProductID = OrderItems.ProductID).ProductName,
    "ProductType", LookUp(Products, ProductID = OrderItems.ProductID).ProductType,
    "UnitPrice", LookUp(Products, ProductID = OrderItems.ProductID).Price,
    "TotalPrice", LookUp(Products, ProductID = OrderItems.ProductID).Price * Quantity
)
```

### Updating Records:

```
// Update order status
Patch(
    Orders,
    LookUp(Orders, OrderID = varSelectedOrder.OrderID),
    {
        Status: drpStatus.Selected.Value
    }
);
```

### Deleting Records:

```
// Remove an order item
Remove(
    OrderItems,
    LookUp(OrderItems, OrderItemID = varSelectedItem.OrderItemID)
);
```

## Best Practices

1. **Use variables and collections** for temporary data storage
2. **Implement proper validation** before saving data
3. **Use ForAll for batch operations** when working with multiple records
4. **Create reusable functions** for common data operations
5. **Implement error handling** for data operations

## Limitations of the Prototype Database

1. **No automatic ID generation**: In a real database, IDs would be auto-generated
2. **Limited data validation**: The CSV/Excel format doesn't enforce data types or relationships
3. **No transaction support**: Changes to related tables aren't handled as transactions
4. **Performance limitations**: Large datasets may cause performance issues

For a production app, consider using a more robust database solution like Dataverse or SQL Server.
