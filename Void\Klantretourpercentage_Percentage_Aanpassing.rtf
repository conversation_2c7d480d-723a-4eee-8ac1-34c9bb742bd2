{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Aanpassing voor Klantretourpercentage naar Percentages\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft de aanpassing die is gemaakt om de Klantretourpercentage-waarden om te zetten naar percentages zonder decimalen.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Geïdentificeerde Probleem\b0\fs22\par
In de opgeschoonde dataset waren de Klantretourpercentage-waarden opgeslagen als decimale getallen (bijvoorbeeld 0.1, 0.2, 0.401949628, etc.). Dit maakte de waarden moeilijk leesbaar als percentages. Het doel was om deze waarden om te zetten naar hele getallen die percentages vertegenwoordigen, zonder decimalen of komma's.\par

\pard\sa200\sl276\slmult1\b\fs26 2. Aanpassing aan het Script\b0\fs22\par
Er is een speciaal script \i fix_klantretourpercentage_percentage.py\i0 gemaakt om de Klantretourpercentage-waarden om te zetten naar percentages zonder decimalen:\par

\i import pandas as pd\par
\par
# Laad de dataset\par
df = pd.read_csv('Kikker_cleaned_fixed.csv')\par
\par
# Converteer de waarden naar percentages zonder decimalen\par
# Vermenigvuldig met 100 en rond af naar gehele getallen\par
df['Klantretourpercentage'] = (df['Klantretourpercentage'] * 100).round().astype(int)\i0\par

Deze code doet het volgende:\par
\bullet Vermenigvuldigt de decimale waarden met 100 om ze om te zetten naar percentages\par
\bullet Rondt de waarden af naar gehele getallen\par
\bullet Converteert de waarden naar het integer-datatype om decimalen te verwijderen\par

\pard\sa200\sl276\slmult1\b\fs26 3. Resultaten\b0\fs22\par
Na het uitvoeren van het aangepaste script zijn de Klantretourpercentage-waarden als volgt aangepast:\par

\b Vóór de aanpassing (voorbeeld van waarden):\b0\par
\bullet 0.1\par
\bullet 0.2\par
\bullet 0.3\par
\bullet 0.4\par
\bullet 0.401949628\par
\bullet 0.407503227\par
\bullet 0.5\par
\bullet 1.0\par
\bullet 1.5\par
\bullet 2.0\par

\b Na de aanpassing (voorbeeld van waarden):\b0\par
\bullet 10\par
\bullet 20\par
\bullet 30\par
\bullet 40\par
\bullet 41\par
\bullet 50\par
\bullet 100\par
\bullet 150\par
\bullet 200\par

\b Statistieken van de aangepaste Klantretourpercentage-kolom:\b0\par
\bullet Minimum: 10\par
\bullet Maximum: 200\par
\bullet Gemiddelde: 102.31\par
\bullet Mediaan: 100\par
\bullet 25e percentiel: 70\par
\bullet 75e percentiel: 130\par
\bullet Standaarddeviatie: 45.03\par

\pard\sa200\sl276\slmult1\b\fs26 4. Verificatie\b0\fs22\par
De aangepaste Klantretourpercentage-waarden zijn nu veel duidelijker en beter leesbaar als percentages:\par

\bullet \b Geen decimalen meer:\b0 Alle waarden zijn nu gehele getallen, wat de leesbaarheid verbetert.\par
\bullet \b Duidelijke percentages:\b0 De waarden vertegenwoordigen nu direct percentages (bijvoorbeeld 10 betekent 10%).\par
\bullet \b Consistente notatie:\b0 Alle waarden zijn nu in dezelfde notatie (gehele getallen).\par
\bullet \b Intuïtieve interpretatie:\b0 De waarden zijn nu gemakkelijker te interpreteren als percentages zonder mentale conversie.\par

\pard\sa200\sl276\slmult1\b\fs26 5. Conclusie\b0\fs22\par
De aanpassing voor de Klantretourpercentage-kolom heeft succesvol alle decimale waarden omgezet naar percentages zonder decimalen. Dit maakt de dataset beter leesbaar en bruikbaarder voor analyses en visualisaties.\par

Deze aanpassing draagt bij aan een nog betere datakwaliteit voor de analyse van het productieproces van Americaps koffiecapsules, en zorgt ervoor dat de analyses en conclusies gebaseerd op de Klantretourpercentage-kolom intuïtiever en betrouwbaarder zijn.\par

\pard\sa200\sl276\slmult1\i\fs20 Deze aanpassing is gemaakt in aanvulling op de eerder beschreven opschoningsstappen, en is specifiek gericht op het verbeteren van de presentatie van de Klantretourpercentage-kolom in de dataset.\i0\fs22\par
}
