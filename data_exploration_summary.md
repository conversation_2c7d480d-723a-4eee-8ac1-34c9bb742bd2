# Data Exploration Summary - Kikker.csv

## Dataset Overview
- **Number of rows**: 8,000
- **Number of columns**: 37
- **Data period**: The data appears to span from 2020 to 2023

## Key Columns and Their Meanings

### Machine and Production Information
- **PackagingApparaat**: Packaging machine identifier (Packager 1-5)
- **FillingApparaat**: Filling machine identifier (Filling Machine 1-5)
- **GrindingApparaat**: Grinding machine identifier (Grinder 1-5)
- **FillingID**, **PackagingID**, **GrindingID**: Unique identifiers for specific operations
- **Batchnr**: Batch number for production

### Product Information
- **Koffieboon**: Type of coffee bean (Arabica, Robusta, Excelsa, Liberica)
- **Herkomst**: Country of origin for the coffee beans
- **Roosterprofiel**: Roasting profile (Light, Medium, Medium-Dark, Dark)

### Quality Metrics
- **Panel Test**: Quality assessment (Voldoet, Voldoet gede<PERSON>ijk, Voldoet niet)
- **Klantretourpercentage**: Customer return percentage
- **Defectpercentage**: Percentage of defects
- **Klanttevredenheid**: Customer satisfaction score
- **Fair-Trade Score**: Score related to fair trade practices
- **Duurzaamheid Score**: Sustainability score
- **CO2-Footprint**: Carbon footprint measurement

### Process Metrics
- **Benuttingsgraad**: Utilization rate
- **Cyclustijd**: Cycle time
- **ProcessTime**: Total process time
- **Energieverbruik**: Energy consumption
- **Cost**: Production cost

### Timestamps
- **Registratiedatum**: Registration date
- **FillingDatumTijdStart/Eind**: Start/end time of filling process
- **GrindingDatumTijdStart/Eind**: Start/end time of grinding process
- **PackagingDatumTijdStart/Eind**: Start/end time of packaging process

## Data Quality Issues

### Missing Values
- Several columns have missing values, with the highest percentages in:
  - **Registratiedatum**: 5.59%
  - **Leveranciersbeoordeling**: 5.04%
  - **CO2-Footprint**: 4.95%
  - **Gewichtscontrole**: 4.88%
  - **Laatste Audit**: 4.78%

### Unusual Values
1. **Date format issues**:
   - Several date columns contain invalid dates like "31-02-2025 25:61:61" or "0000-00-00 00:00:00"
   - Each date column has approximately 400 unusual date values

2. **Unusual numeric values**:
   - **Cost**: Contains values like "ERROR", "-100.0", "-999999"
   - **Voorraadniveaus**: Contains values like "-999", "ERROR", "-999999"
   - **Cyclustijd**: Contains values like "abc uur", "-5 uur", "onbekend"
   - **Energieverbruik**: Contains values like "onbekend", "-100 kWh", "abc kWh"
   - **Leveranciersbeoordeling**: Contains negative values like -9, -15, -14
   - **Klanttevredenheid**: Contains "onbekend" and negative values

3. **Outliers**:
   - **Leveranciersbeoordeling**: 14 outliers
   - **Fair-Trade Score**: 4 outliers
   - **Defectpercentage**: 23 outliers

## Key Insights

### Coffee Bean Analysis
- **Arabica** is the most common bean type (49.7% of all records)
- **Robusta** is the second most common (29.9%)
- **Excelsa** and **Liberica** each represent about 10% of the data
- The quality assessment ("Panel Test") is fairly consistent across bean types, with approximately 70% passing ("Voldoet"), 15% partially passing ("Voldoet gedeeltelijk"), and 15% failing ("Voldoet niet")

### Machine Performance
- There are 5 packaging machines, 5 filling machines, and 5 grinding machines
- The distribution of work across machines is relatively even
- Defect percentages vary by machine, but the differences are not dramatic

### Process Metrics
- The average utilization rate (**Benuttingsgraad**) is approximately 75%
- The average defect percentage (**Defectpercentage**) is about 2%
- The average customer return percentage (**Klantretourpercentage**) is about 1%

### Correlations
- There are no strong correlations between the numeric variables in the dataset
- This suggests that the factors affecting quality and performance may be complex and multifaceted

## Recommendations for Further Analysis

1. **Data Cleaning**:
   - Fix or remove invalid date formats
   - Handle unusual numeric values (negative values, text in numeric fields)
   - Address missing values through imputation or exclusion

2. **Deeper Analysis**:
   - Investigate the relationship between coffee bean origin and quality
   - Analyze machine performance over time to identify trends or degradation
   - Examine the impact of process parameters on defect rates and customer returns
   - Study the relationship between sustainability metrics and product quality

3. **Potential Focus Areas**:
   - Quality improvement: Analyze factors contributing to failed quality tests
   - Efficiency optimization: Identify bottlenecks in the production process
   - Cost reduction: Analyze the relationship between energy consumption, process time, and cost
   - Sustainability: Investigate how to improve fair trade and sustainability scores
