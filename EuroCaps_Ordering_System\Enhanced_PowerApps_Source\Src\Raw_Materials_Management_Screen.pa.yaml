# ************************************************************************************************
# Raw Materials Management Screen for EuroCaps Ordering System
# Manages inventory, suppliers, reorder alerts, and material costs
# Color Scheme: Header/Sidebar: 2C3E50, Background: 1B3A4B, Cards: A9C6E8, Buttons: F39C12, Text: FFFFFF
# ************************************************************************************************
Screens:
  Raw_Materials_Management_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(243, 156, 18, 1)
      Fill: =RGBA(27, 58, 75, 1)
      OnVisible: |
        =// Initialize material management variables
        Set(varMaterialSearch, "");
        Set(varMaterialType, "All");
        Set(varSupplierFilter, "All");
        Set(varStockFilter, "All");
        Set(varSelectedMaterial, Blank());
        // Refresh material data
        Refresh(colRawMaterials);
        Refresh(colSuppliers);
        // Calculate reorder alerts
        Set(varReorderAlerts, Filter(colRawMaterials, StockLevel <= ReorderPoint))
    Children:
      # Header Bar
      - HeaderBar:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(44, 62, 80, 1)
            Height: =60
            Width: =1366
            X: =0
            Y: =0
            BorderThickness: =0

      # Logo in Header
      - HeaderLogo:
          Control: Image@2.1.0
          Properties:
            Image: ="eurocaps-logo"
            Height: =40
            Width: =40
            X: =20
            Y: =10

      # App Title in Header
      - HeaderTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="EuroCaps Order Management Pro - Raw Materials"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =400
            X: =70
            Y: =10

      # User Menu
      - UserMenu:
          Control: Classic/Button@2.2.0
          Properties:
            Text: =varUserName & " (" & varUserRole & ") ▼"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =200
            X: =1100
            Y: =10

      # Navigation Menu
      - NavigationMenu:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(44, 62, 80, 1)
            Height: =708
            Width: =200
            X: =0
            Y: =60
            BorderThickness: =0

      # Menu Items
      - MenuDashboard:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="≡ Dashboard"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =60
            Align: =Align.Left
            OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)

      - MenuCustomers:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="👥 Customers"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =110
            Align: =Align.Left
            OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)

      - MenuProducts:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📦 Products"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =160
            Align: =Align.Left
            OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)

      - MenuMaterials:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="🏭 Raw Materials"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =210
            Align: =Align.Left

      - MenuSuppliers:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="🚚 Suppliers"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =260
            Align: =Align.Left
            OnSelect: =Navigate(Supplier_Management_Screen, ScreenTransition.Fade)

      - MenuOrders:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📋 Orders"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =310
            Align: =Align.Left
            OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade)

      # Main Content Area
      - MainContent:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(27, 58, 75, 1)
            Height: =708
            Width: =1166
            X: =200
            Y: =60
            BorderThickness: =0

      # Page Title
      - PageTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="Raw Materials Management"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =18
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =300
            X: =220
            Y: =80

      # Reorder Alerts Section
      - ReorderAlertsPanel:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(220, 53, 69, 1)
            Height: =50
            Width: =1100
            X: =220
            Y: =130
            BorderColor: =RGBA(255, 255, 255, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8
            Visible: =CountRows(varReorderAlerts) > 0

      - ReorderAlertsText:
          Control: Label@2.5.1
          Properties:
            Text: ="⚠️ REORDER ALERT: " & CountRows(varReorderAlerts) & " materials below reorder point!"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Height: =30
            Width: =800
            X: =240
            Y: =140
            Visible: =CountRows(varReorderAlerts) > 0

      - ViewAlertsButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="View Details"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =30
            Width: =120
            X: =1180
            Y: =140
            Visible: =CountRows(varReorderAlerts) > 0
            OnSelect: =Set(varMaterialType, "Reorder"); Set(varStockFilter, "Low Stock")

      # Search and Filter Section
      - SearchFilterSection:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =80
            Width: =1100
            X: =220
            Y: =If(CountRows(varReorderAlerts) > 0, 190, 140)
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Material Search Input
      - MaterialSearchInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            Default: =varMaterialSearch
            HintText: ="Search materials by name..."
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =250
            X: =240
            Y: =If(CountRows(varReorderAlerts) > 0, 205, 155)
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varMaterialSearch, MaterialSearchInput.Text)

      # Material Type Filter
      - MaterialTypeFilter:
          Control: Classic/Dropdown@2.2.0
          Properties:
            Items: =["All", "Coffee", "Packaging", "Flavoring"]
            DefaultSelectedItems: =["All"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =120
            X: =510
            Y: =If(CountRows(varReorderAlerts) > 0, 205, 155)
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varMaterialType, MaterialTypeFilter.Selected.Value)

      # Supplier Filter
      - SupplierFilter:
          Control: Classic/Dropdown@2.2.0
          Properties:
            Items: =Distinct(colSuppliers, SupplierName)
            DefaultSelectedItems: =["All"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =150
            X: =650
            Y: =If(CountRows(varReorderAlerts) > 0, 205, 155)
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varSupplierFilter, SupplierFilter.Selected.Value)

      # Stock Level Filter
      - StockFilter:
          Control: Classic/Dropdown@2.2.0
          Properties:
            Items: =["All", "In Stock", "Low Stock", "Out of Stock"]
            DefaultSelectedItems: =["All"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =120
            X: =820
            Y: =If(CountRows(varReorderAlerts) > 0, 205, 155)
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varStockFilter, StockFilter.Selected.Value)

      # Reset Filters Button
      - ResetFiltersButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="Reset"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =35
            Width: =80
            X: =960
            Y: =If(CountRows(varReorderAlerts) > 0, 205, 155)
            OnSelect: |
              =Set(varMaterialSearch, "");
              Set(varMaterialType, "All");
              Set(varSupplierFilter, "All");
              Set(varStockFilter, "All");
              Reset(MaterialSearchInput);
              Reset(MaterialTypeFilter);
              Reset(SupplierFilter);
              Reset(StockFilter)

      # New Material Button
      - NewMaterialButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="+ New Material"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =35
            Width: =130
            X: =1170
            Y: =If(CountRows(varReorderAlerts) > 0, 205, 155)
            OnSelect: =Navigate(Material_Details_Screen, ScreenTransition.Fade, {Mode: "New"})

      # Materials Gallery
      - MaterialsGallery:
          Control: Gallery@2.3.0
          Properties:
            Items: |
              =Filter(
                  AddColumns(
                      colRawMaterials,
                      "SupplierName", LookUp(colSuppliers, SupplierID = ThisRecord.SupplierID).SupplierName,
                      "StockStatus", 
                      If(StockLevel <= 0, "Out of Stock",
                         If(StockLevel <= ReorderPoint, "Low Stock", "In Stock"))
                  ),
                  (IsBlank(varMaterialSearch) || varMaterialSearch in MaterialName) &&
                  (varMaterialType = "All" || MaterialType = varMaterialType) &&
                  (varSupplierFilter = "All" || SupplierName = varSupplierFilter) &&
                  (varStockFilter = "All" || StockStatus = varStockFilter)
              )
            Height: =400
            Width: =1100
            X: =220
            Y: =If(CountRows(varReorderAlerts) > 0, 290, 240)
            TemplateSize: =80
            Fill: =RGBA(0, 0, 0, 0)
            BorderThickness: =0
            ShowScrollbar: =true

      # Materials Gallery Template Items
      # Material Card Background
      - MaterialCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =70
            Width: =1080
            X: =10
            Y: =5
            BorderColor: |
              =Switch(
                  ThisItem.StockStatus,
                  "Out of Stock", RGBA(220, 53, 69, 1),
                  "Low Stock", RGBA(255, 193, 7, 1),
                  "In Stock", RGBA(243, 156, 18, 1)
              )
            BorderThickness: =2
            RadiusTopLeft: =4
            RadiusTopRight: =4
            RadiusBottomLeft: =4
            RadiusBottomRight: =4

      # Material Name
      - MaterialNameLabel:
          Control: Label@2.5.1
          Properties:
            Text: =ThisItem.MaterialName
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =250
            X: =20
            Y: =15

      # Material Type
      - MaterialTypeLabel:
          Control: Label@2.5.1
          Properties:
            Text: =ThisItem.MaterialType
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =20
            Width: =100
            X: =20
            Y: =40

      # Supplier Name
      - SupplierNameLabel:
          Control: Label@2.5.1
          Properties:
            Text: =ThisItem.SupplierName
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =20
            Width: =150
            X: =280
            Y: =25

      # Stock Level
      - StockLevelLabel:
          Control: Label@2.5.1
          Properties:
            Text: =ThisItem.StockLevel & " " & ThisItem.Unit
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: |
              =Switch(
                  ThisItem.StockStatus,
                  "Out of Stock", RGBA(220, 53, 69, 1),
                  "Low Stock", RGBA(255, 193, 7, 1),
                  "In Stock", RGBA(40, 167, 69, 1)
              )
            Height: =20
            Width: =100
            X: =450
            Y: =25

      # Unit Cost
      - UnitCostLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="€" & Text(ThisItem.UnitCost, "0.00")
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =20
            Width: =80
            X: =570
            Y: =25

      # Reorder Point
      - ReorderPointLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Reorder: " & ThisItem.ReorderPoint
            Font: =Font.Arial
            Size: =11
            Color: =RGBA(44, 62, 80, 1)
            Height: =20
            Width: =100
            X: =670
            Y: =25

      # Stock Status Indicator
      - StockStatusLabel:
          Control: Label@2.5.1
          Properties:
            Text: =ThisItem.StockStatus
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =11
            Color: |
              =Switch(
                  ThisItem.StockStatus,
                  "Out of Stock", RGBA(220, 53, 69, 1),
                  "Low Stock", RGBA(255, 193, 7, 1),
                  "In Stock", RGBA(40, 167, 69, 1)
              )
            Height: =20
            Width: =100
            X: =780
            Y: =25

      # Action Buttons
      # View Details Button
      - ViewDetailsButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="👁️ View"
            Font: =Font.Arial
            Size: =10
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =25
            Width: =60
            X: =900
            Y: =15
            OnSelect: |
              =Set(varSelectedMaterial, ThisItem);
              Navigate(Material_Details_Screen, ScreenTransition.Fade, {Mode: "View", Material: ThisItem})

      # Edit Button
      - EditMaterialButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📝 Edit"
            Font: =Font.Arial
            Size: =10
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =25
            Width: =60
            X: =970
            Y: =15
            OnSelect: |
              =Set(varSelectedMaterial, ThisItem);
              Navigate(Material_Details_Screen, ScreenTransition.Fade, {Mode: "Edit", Material: ThisItem})

      # Reorder Button
      - ReorderButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="🔄 Reorder"
            Font: =Font.Arial
            Size: =10
            Color: =RGBA(255, 255, 255, 1)
            Fill: =If(ThisItem.StockLevel <= ThisItem.ReorderPoint, RGBA(220, 53, 69, 1), RGBA(108, 117, 125, 1))
            HoverFill: =If(ThisItem.StockLevel <= ThisItem.ReorderPoint, RGBA(200, 35, 51, 1), RGBA(90, 98, 104, 1))
            Height: =25
            Width: =70
            X: =1040
            Y: =15
            OnSelect: |
              =Set(varSelectedMaterial, ThisItem);
              Navigate(Material_Order_Screen, ScreenTransition.Fade, {Material: ThisItem, Supplier: LookUp(colSuppliers, SupplierID = ThisItem.SupplierID)})

      # Summary Statistics Section
      - SummaryPanel:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =80
            Width: =1100
            X: =220
            Y: =If(CountRows(varReorderAlerts) > 0, 710, 660)
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Total Materials Count
      - TotalMaterialsLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Total Materials: " & CountRows(MaterialsGallery.AllItems)
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =200
            X: =240
            Y: =If(CountRows(varReorderAlerts) > 0, 725, 675)

      # Low Stock Count
      - LowStockLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Low Stock: " & CountRows(Filter(MaterialsGallery.AllItems, StockLevel <= ReorderPoint))
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(255, 193, 7, 1)
            Height: =25
            Width: =150
            X: =460
            Y: =If(CountRows(varReorderAlerts) > 0, 725, 675)

      # Total Inventory Value
      - InventoryValueLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Inventory Value: €" & Text(Sum(MaterialsGallery.AllItems, StockLevel * UnitCost), "0,000.00")
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(40, 167, 69, 1)
            Height: =25
            Width: =200
            X: =630
            Y: =If(CountRows(varReorderAlerts) > 0, 725, 675)

      # Export Data Button
      - ExportDataButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📊 Export Data"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =30
            Width: =130
            X: =1170
            Y: =If(CountRows(varReorderAlerts) > 0, 720, 670)
            OnSelect: |
              =// Export materials data to Excel
              Notify("Export functionality will be implemented in production version", NotificationType.Information)
