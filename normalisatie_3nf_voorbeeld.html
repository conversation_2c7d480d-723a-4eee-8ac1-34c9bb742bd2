<!DOCTYPE html>
<html>
<head>
    <title>Normalisatie naar 3NF Voorbeeld</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .diagram {
            width: 800px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: white;
            padding: 20px;
        }
        .table {
            border-collapse: collapse;
            margin: 20px 0;
            width: 100%;
        }
        .table th, .table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #333;
            color: white;
        }
        .pk {
            font-weight: bold;
            text-decoration: underline;
        }
        .fk {
            font-style: italic;
            color: #555;
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .step {
            margin: 30px 0;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .step-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }
        .arrow {
            text-align: center;
            font-size: 24px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="title">Normalisatie naar 3NF</div>
    <div class="subtitle">Stapsgewijs proces van 0NF naar 3NF</div>
    
    <div class="diagram">
        <div class="step">
            <div class="step-title">0NF: Niet-genormaliseerde tabel</div>
            <p>Gegevens zijn op geen enkele manier genormaliseerd.</p>
            <table class="table">
                <tr>
                    <th>OrderID</th>
                    <th>KlantID</th>
                    <th>KlantNaam</th>
                    <th>KlantAdres</th>
                    <th>ProductID</th>
                    <th>ProductNaam</th>
                    <th>Prijs</th>
                    <th>Aantal</th>
                    <th>Subtotaal</th>
                </tr>
                <tr>
                    <td>1001</td>
                    <td>K100</td>
                    <td>Jan Jansen</td>
                    <td>Hoofdstraat 1, Amsterdam</td>
                    <td>P200</td>
                    <td>Laptop</td>
                    <td>899.99</td>
                    <td>1</td>
                    <td>899.99</td>
                </tr>
                <tr>
                    <td>1001</td>
                    <td>K100</td>
                    <td>Jan Jansen</td>
                    <td>Hoofdstraat 1, Amsterdam</td>
                    <td>P201</td>
                    <td>Muis</td>
                    <td>19.99</td>
                    <td>2</td>
                    <td>39.98</td>
                </tr>
                <tr>
                    <td>1002</td>
                    <td>K101</td>
                    <td>Piet Pietersen</td>
                    <td>Kerkstraat 10, Rotterdam</td>
                    <td>P200</td>
                    <td>Laptop</td>
                    <td>899.99</td>
                    <td>1</td>
                    <td>899.99</td>
                </tr>
            </table>
            <p>Problemen: Redundantie (klantgegevens worden herhaald), berekende velden (Subtotaal).</p>
        </div>
        
        <div class="arrow">↓</div>
        
        <div class="step">
            <div class="step-title">1NF: Eerste Normaalvorm</div>
            <p>Alle attributen zijn atomair, geen repeterende groepen, primaire sleutel geïdentificeerd.</p>
            <table class="table">
                <tr>
                    <th class="pk">OrderID</th>
                    <th class="pk">ProductID</th>
                    <th>KlantID</th>
                    <th>KlantNaam</th>
                    <th>KlantAdres</th>
                    <th>ProductNaam</th>
                    <th>Prijs</th>
                    <th>Aantal</th>
                </tr>
                <tr>
                    <td>1001</td>
                    <td>P200</td>
                    <td>K100</td>
                    <td>Jan Jansen</td>
                    <td>Hoofdstraat 1, Amsterdam</td>
                    <td>Laptop</td>
                    <td>899.99</td>
                    <td>1</td>
                </tr>
                <tr>
                    <td>1001</td>
                    <td>P201</td>
                    <td>K100</td>
                    <td>Jan Jansen</td>
                    <td>Hoofdstraat 1, Amsterdam</td>
                    <td>Muis</td>
                    <td>19.99</td>
                    <td>2</td>
                </tr>
                <tr>
                    <td>1002</td>
                    <td>P200</td>
                    <td>K101</td>
                    <td>Piet Pietersen</td>
                    <td>Kerkstraat 10, Rotterdam</td>
                    <td>Laptop</td>
                    <td>899.99</td>
                    <td>1</td>
                </tr>
            </table>
            <p>Veranderingen: Berekende velden (Subtotaal) verwijderd, samengestelde primaire sleutel (OrderID, ProductID) gedefinieerd.</p>
        </div>
        
        <div class="arrow">↓</div>
        
        <div class="step">
            <div class="step-title">2NF: Tweede Normaalvorm</div>
            <p>Geen partiële afhankelijkheden (attributen die afhankelijk zijn van slechts een deel van de primaire sleutel).</p>
            
            <div style="display: flex; justify-content: space-between;">
                <div style="width: 48%;">
                    <p><strong>Order tabel:</strong></p>
                    <table class="table">
                        <tr>
                            <th class="pk">OrderID</th>
                            <th>KlantID</th>
                            <th>KlantNaam</th>
                            <th>KlantAdres</th>
                        </tr>
                        <tr>
                            <td>1001</td>
                            <td>K100</td>
                            <td>Jan Jansen</td>
                            <td>Hoofdstraat 1, Amsterdam</td>
                        </tr>
                        <tr>
                            <td>1002</td>
                            <td>K101</td>
                            <td>Piet Pietersen</td>
                            <td>Kerkstraat 10, Rotterdam</td>
                        </tr>
                    </table>
                </div>
                
                <div style="width: 48%;">
                    <p><strong>OrderDetail tabel:</strong></p>
                    <table class="table">
                        <tr>
                            <th class="pk fk">OrderID</th>
                            <th class="pk">ProductID</th>
                            <th>ProductNaam</th>
                            <th>Prijs</th>
                            <th>Aantal</th>
                        </tr>
                        <tr>
                            <td>1001</td>
                            <td>P200</td>
                            <td>Laptop</td>
                            <td>899.99</td>
                            <td>1</td>
                        </tr>
                        <tr>
                            <td>1001</td>
                            <td>P201</td>
                            <td>Muis</td>
                            <td>19.99</td>
                            <td>2</td>
                        </tr>
                        <tr>
                            <td>1002</td>
                            <td>P200</td>
                            <td>Laptop</td>
                            <td>899.99</td>
                            <td>1</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <p>Veranderingen: Tabel gesplitst om partiële afhankelijkheden te verwijderen. KlantID, KlantNaam en KlantAdres zijn alleen afhankelijk van OrderID, niet van ProductID.</p>
        </div>
        
        <div class="arrow">↓</div>
        
        <div class="step">
            <div class="step-title">3NF: Derde Normaalvorm</div>
            <p>Geen transitieve afhankelijkheden (niet-sleutelattributen die afhankelijk zijn van andere niet-sleutelattributen).</p>
            
            <div style="display: flex; flex-wrap: wrap; justify-content: space-between;">
                <div style="width: 30%;">
                    <p><strong>Order tabel:</strong></p>
                    <table class="table">
                        <tr>
                            <th class="pk">OrderID</th>
                            <th class="fk">KlantID</th>
                        </tr>
                        <tr>
                            <td>1001</td>
                            <td>K100</td>
                        </tr>
                        <tr>
                            <td>1002</td>
                            <td>K101</td>
                        </tr>
                    </table>
                </div>
                
                <div style="width: 30%;">
                    <p><strong>Klant tabel:</strong></p>
                    <table class="table">
                        <tr>
                            <th class="pk">KlantID</th>
                            <th>KlantNaam</th>
                            <th>KlantAdres</th>
                        </tr>
                        <tr>
                            <td>K100</td>
                            <td>Jan Jansen</td>
                            <td>Hoofdstraat 1, Amsterdam</td>
                        </tr>
                        <tr>
                            <td>K101</td>
                            <td>Piet Pietersen</td>
                            <td>Kerkstraat 10, Rotterdam</td>
                        </tr>
                    </table>
                </div>
                
                <div style="width: 30%;">
                    <p><strong>Product tabel:</strong></p>
                    <table class="table">
                        <tr>
                            <th class="pk">ProductID</th>
                            <th>ProductNaam</th>
                            <th>Prijs</th>
                        </tr>
                        <tr>
                            <td>P200</td>
                            <td>Laptop</td>
                            <td>899.99</td>
                        </tr>
                        <tr>
                            <td>P201</td>
                            <td>Muis</td>
                            <td>19.99</td>
                        </tr>
                    </table>
                </div>
                
                <div style="width: 100%; margin-top: 20px;">
                    <p><strong>OrderDetail tabel:</strong></p>
                    <table class="table">
                        <tr>
                            <th class="pk fk">OrderID</th>
                            <th class="pk fk">ProductID</th>
                            <th>Aantal</th>
                        </tr>
                        <tr>
                            <td>1001</td>
                            <td>P200</td>
                            <td>1</td>
                        </tr>
                        <tr>
                            <td>1001</td>
                            <td>P201</td>
                            <td>2</td>
                        </tr>
                        <tr>
                            <td>1002</td>
                            <td>P200</td>
                            <td>1</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <p>Veranderingen: Transitieve afhankelijkheden verwijderd door het creëren van aparte tabellen voor Klant en Product. KlantNaam en KlantAdres zijn afhankelijk van KlantID, niet direct van OrderID. ProductNaam en Prijs zijn afhankelijk van ProductID.</p>
        </div>
    </div>
</body>
</html>
