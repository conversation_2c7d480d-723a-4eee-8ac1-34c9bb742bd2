<!DOCTYPE html>
<html>
<head>
    <title>Logisch ERD Voorbeeld - Correct</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: white;
        }
        .container {
            width: 1000px;
            height: 800px;
            border: 1px solid #ccc;
            position: relative;
            background-color: white;
        }
        .title {
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            margin-top: 20px;
            margin-bottom: 40px;
        }
        .table {
            position: absolute;
            border: 1px solid black;
            background-color: white;
            z-index: 2;
        }
        .table-header {
            text-align: center;
            font-weight: bold;
            padding: 8px;
            border-bottom: 1px solid black;
        }
        .table-content {
            display: flex;
        }
        .pk-column {
            width: 40px;
            border-right: 1px solid black;
            text-align: center;
            padding: 5px;
            font-weight: bold;
        }
        .attribute-column {
            padding: 5px;
            min-width: 120px;
        }
        .relationship {
            position: absolute;
            font-size: 14px;
            z-index: 3;
            background-color: white;
            padding: 0 5px;
        }
        .note {
            position: absolute;
            bottom: 20px;
            left: 20px;
            font-size: 14px;
            width: 960px;
        }
        .legend {
            position: absolute;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #f9f9f9;
            width: 250px;
            z-index: 2;
        }
        .legend-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .legend-item {
            margin: 5px 0;
            font-size: 12px;
        }
        /* SVG styles for better lines */
        svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">Logisch ERD Voorbeeld - Webwinkel</div>
        
        <!-- SVG for lines and relationship symbols -->
        <svg>
            <!-- KLANT to BESTELLING -->
            <path d="M 350,150 H 550" stroke="black" stroke-width="1" fill="none"/>
            
            <!-- BESTELLING to BESTELLING_PRODUCT -->
            <path d="M 650,200 H 500 V 300" stroke="black" stroke-width="1" fill="none"/>
            
            <!-- PRODUCT to BESTELLING_PRODUCT -->
            <path d="M 650,450 H 500 V 350" stroke="black" stroke-width="1" fill="none"/>
            
            <!-- CATEGORIE to PRODUCT -->
            <path d="M 350,450 H 550" stroke="black" stroke-width="1" fill="none"/>
            
            <!-- Relationship symbols -->
            <!-- KLANT to BESTELLING (1 to many) -->
            <line x1="340" y1="150" x2="360" y2="150" stroke="black" stroke-width="1.5"/>
            <line x1="340" y1="140" x2="340" y2="160" stroke="black" stroke-width="1.5"/>
            <line x1="540" y1="140" x2="540" y2="160" stroke="black" stroke-width="1.5"/>
            <path d="M 540,150 L 560,150" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 550,145 L 560,150 L 550,155" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- BESTELLING to BESTELLING_PRODUCT (1 to many) -->
            <line x1="650" y1="190" x2="650" y2="210" stroke="black" stroke-width="1.5"/>
            <line x1="640" y1="190" x2="660" y2="190" stroke="black" stroke-width="1.5"/>
            <line x1="490" y1="300" x2="510" y2="300" stroke="black" stroke-width="1.5"/>
            <path d="M 500,290 L 500,310" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 495,300 L 505,310" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- PRODUCT to BESTELLING_PRODUCT (1 to many) -->
            <line x1="650" y1="440" x2="650" y2="460" stroke="black" stroke-width="1.5"/>
            <line x1="640" y1="440" x2="660" y2="440" stroke="black" stroke-width="1.5"/>
            <line x1="490" y1="350" x2="510" y2="350" stroke="black" stroke-width="1.5"/>
            <path d="M 500,340 L 500,360" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 495,350 L 505,340" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- CATEGORIE to PRODUCT (1 to many) -->
            <line x1="340" y1="450" x2="360" y2="450" stroke="black" stroke-width="1.5"/>
            <line x1="340" y1="440" x2="340" y2="460" stroke="black" stroke-width="1.5"/>
            <line x1="540" y1="440" x2="540" y2="460" stroke="black" stroke-width="1.5"/>
            <path d="M 540,450 L 560,450" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 550,445 L 560,450 L 550,455" stroke="black" stroke-width="1.5" fill="none"/>
        </svg>
        
        <!-- Entities with correct table style -->
        <div class="table" style="top: 120px; left: 150px; width: 200px;">
            <div class="table-header">KLANT</div>
            <div class="table-content">
                <div class="pk-column">PK</div>
                <div class="attribute-column">klant_id</div>
            </div>
        </div>
        
        <div class="table" style="top: 120px; left: 550px; width: 200px;">
            <div class="table-header">BESTELLING</div>
            <div class="table-content">
                <div class="pk-column">PK</div>
                <div class="attribute-column">bestelling_id</div>
            </div>
        </div>
        
        <div class="table" style="top: 420px; left: 150px; width: 200px;">
            <div class="table-header">CATEGORIE</div>
            <div class="table-content">
                <div class="pk-column">PK</div>
                <div class="attribute-column">categorie_id</div>
            </div>
        </div>
        
        <div class="table" style="top: 420px; left: 550px; width: 200px;">
            <div class="table-header">PRODUCT</div>
            <div class="table-content">
                <div class="pk-column">PK</div>
                <div class="attribute-column">product_id</div>
            </div>
        </div>
        
        <div class="table" style="top: 300px; left: 350px; width: 200px;">
            <div class="table-header">BESTELLING_PRODUCT</div>
            <div class="table-content">
                <div class="pk-column">PK</div>
                <div class="attribute-column">bestelling_id</div>
            </div>
            <div class="table-content">
                <div class="pk-column">PK</div>
                <div class="attribute-column">product_id</div>
            </div>
        </div>
        
        <!-- Relationships -->
        <div class="relationship" style="top: 130px; left: 420px;">plaatst</div>
        <div class="relationship" style="top: 250px; left: 660px;">bevat</div>
        <div class="relationship" style="top: 380px; left: 510px;">bevat</div>
        <div class="relationship" style="top: 430px; left: 420px;">behoort tot</div>
        
        <!-- Legend -->
        <div class="legend" style="top: 600px; left: 350px;">
            <div class="legend-title">Relatie Symbolen Legenda</div>
            <div class="legend-item">—||— Exact één (1)</div>
            <div class="legend-item">—|O— Nul of één (0..1)</div>
            <div class="legend-item">—|<— Eén of meer (1..n)</div>
            <div class="legend-item">—O<— Nul of meer (0..n)</div>
        </div>
        
        <!-- Note -->
        <div class="note">
            <strong>Opmerking:</strong> Een logisch ERD bouwt voort op het conceptuele ERD door attributen toe te voegen aan elke entiteit. 
            Ook is er een koppeltabel (BESTELLING_PRODUCT) toegevoegd om de veel-op-veel relatie tussen BESTELLING en PRODUCT te implementeren.
            Primaire sleutels zijn gemarkeerd met "PK". In een logisch ERD worden nog geen datatypes of implementatiedetails gespecificeerd.
            De relaties worden weergegeven met de symbolen uit de Entity Relation bibliotheek van draw.io.
        </div>
    </div>
    
    <div style="margin-top: 20px;">
        <h2>Verschillen tussen Conceptueel en Logisch ERD</h2>
        <ul>
            <li><strong>Attributen:</strong> Het logische ERD bevat attributen (eigenschappen) voor elke entiteit, terwijl het conceptuele ERD alleen entiteiten toont.</li>
            <li><strong>Primaire sleutels:</strong> Het logische ERD identificeert primaire sleutels (gemarkeerd met "PK"), terwijl het conceptuele ERD geen sleutels bevat.</li>
            <li><strong>Koppeltabellen:</strong> Het logische ERD lost veel-op-veel relaties op door koppeltabellen toe te voegen (BESTELLING_PRODUCT).</li>
            <li><strong>Notatie voor relaties:</strong> Het logische ERD gebruikt specifieke symbolen om de cardinaliteit van relaties preciezer weer te geven.</li>
            <li><strong>Detail niveau:</strong> Het logische ERD is gedetailleerder en dichter bij de daadwerkelijke database-implementatie.</li>
        </ul>
    </div>
</body>
</html>
