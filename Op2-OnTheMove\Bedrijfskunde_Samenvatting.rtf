{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Arial;}{\f1\fnil\fcharset0 Calibri;}}
{\colortbl ;\red0\green0\blue255;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qj\f0\fs24\lang19 \b Bedrijfskunde: Samenvatting van Processen en Procesmanagement\b0\par

\pard\sa200\sl276\slmult1\qj Dit document bevat een samenvatting van de belangrijkste concepten uit de Bedrijfskunde lesmateriaal (Lesweek 2.1 t/m 2.7) en het boek van Peter Thuis & Rienk Stuive (3e druk), met een focus op bedrijfsprocessen en procesmanagement.\par

\pard\sa200\sl276\slmult1\qj\b Inleiding\b0\par

\pard\sa200\sl276\slmult1\qj Bedrijfsprocessen vormen de kern van elke organisatie. Ze bepalen hoe producten worden gemaakt, diensten worden geleverd en waarde wordt gecreëerd voor klanten. Een goed begrip van processen en procesmanagement is daarom essentieel voor elke manager of professional.\par

\pard\sa200\sl276\slmult1\qj In deze samenvatting worden de volgende onderwerpen behandeld:\par

\pard\sa200\sl276\slmult1\qj 1. Wat zijn bedrijfsprocessen?\par
2. Het Input-Throughput-Output (ITO) model\par
3. Types bedrijfsprocessen\par
4. Procesvisualisatie\par
5. Procesverbetering methodologieën\par
6. Procesmanagement\par
7. Procesautomatisering en -digitalisering\par

\pard\sa200\sl276\slmult1\qj\b 1. Wat zijn bedrijfsprocessen?\b0\par

\pard\sa200\sl276\slmult1\qj Bedrijfsprocessen zijn gestructureerde, samenhangende reeksen van activiteiten die worden uitgevoerd om een specifiek doel te bereiken. Ze vormen de ruggengraat van elke organisatie en bepalen hoe producten worden gemaakt of diensten worden geleverd.\par

\pard\sa200\sl276\slmult1\qj Kenmerken van bedrijfsprocessen:\par

\pard\sa200\sl276\slmult1\qj - Ze hebben een duidelijk begin en einde\par
- Ze transformeren inputs naar outputs\par
- Ze creëren waarde voor klanten (intern of extern)\par
- Ze kunnen worden gemeten en verbeterd\par
- Ze overschrijden vaak functionele grenzen binnen een organisatie\par

\pard\sa200\sl276\slmult1\qj\b 2. Het Input-Throughput-Output (ITO) model\b0\par

\pard\sa200\sl276\slmult1\qj Het Input-Throughput-Output (ITO) model is een fundamenteel model voor het begrijpen van processen. Het beschrijft hoe inputs worden getransformeerd tot outputs via een reeks activiteiten:\par

\pard\sa200\sl276\slmult1\qj\b Input\b0: De middelen die nodig zijn om het proces uit te voeren, zoals grondstoffen, informatie, energie of arbeid.\par

\pard\sa200\sl276\slmult1\qj\b Throughput\b0: De transformatie van inputs naar outputs via een reeks activiteiten of bewerkingen.\par

\pard\sa200\sl276\slmult1\qj\b Output\b0: De resultaten van het proces, zoals producten, diensten of informatie.\par

\pard\sa200\sl276\slmult1\qj Dit model wordt vaak uitgebreid met feedback-loops, die informatie over de output terugvoeren naar de input of throughput om continue verbetering mogelijk te maken.\par

\pard\sa200\sl276\slmult1\qj\b 3. Types bedrijfsprocessen\b0\par

\pard\sa200\sl276\slmult1\qj Volgens Thuis & Stuive kunnen bedrijfsprocessen worden onderverdeeld in drie hoofdcategorieën:\par

\pard\sa200\sl276\slmult1\qj\b Primaire processen\b0: Deze processen zijn direct gericht op het creëren van producten of diensten voor externe klanten. Ze vormen de kernactiviteiten van een organisatie en dragen direct bij aan de waardecreatie. Voorbeelden zijn:\par

\pard\sa200\sl276\slmult1\qj - Inkoop\par
- Productie\par
- Verkoop\par
- Distributie\par
- Klantenservice\par

\pard\sa200\sl276\slmult1\qj\b Ondersteunende processen\b0: Deze processen ondersteunen de primaire processen maar dragen niet direct bij aan de waardecreatie voor externe klanten. Ze zijn echter essentieel voor het functioneren van de organisatie. Voorbeelden zijn:\par

\pard\sa200\sl276\slmult1\qj - Personeelsbeheer (HRM)\par
- Financiële administratie\par
- IT-ondersteuning\par
- Facilitaire diensten\par
- Juridische zaken\par

\pard\sa200\sl276\slmult1\qj\b Managementprocessen\b0: Deze processen zijn gericht op het plannen, organiseren, leiden en controleren van de organisatie. Ze zorgen voor coördinatie tussen verschillende afdelingen en processen. Voorbeelden zijn:\par

\pard\sa200\sl276\slmult1\qj - Strategische planning\par
- Budgettering\par
- Prestatiemanagement\par
- Risicomanagement\par
- Kwaliteitsmanagement\par

\pard\sa200\sl276\slmult1\qj\b 4. Procesvisualisatie\b0\par

\pard\sa200\sl276\slmult1\qj Om processen te begrijpen en te verbeteren, is het belangrijk om ze visueel weer te geven. Er zijn verschillende methoden om processen in kaart te brengen:\par

\pard\sa200\sl276\slmult1\qj\b Flowcharts\b0: Eenvoudige diagrammen die de stappen in een proces tonen met behulp van symbolen en pijlen. Ze geven een duidelijk overzicht van de volgorde van activiteiten.\par

\pard\sa200\sl276\slmult1\qj\b Swimlane-diagrammen\b0: Deze diagrammen tonen niet alleen de processtappen, maar ook welke afdeling of persoon verantwoordelijk is voor elke stap. Ze zijn bijzonder nuttig voor het visualiseren van processen die meerdere afdelingen omvatten.\par

\pard\sa200\sl276\slmult1\qj Een voorbeeld van een swimlane-diagram is het orderverwerking proces, waarbij verschillende afdelingen (Verkoop, Magazijn, Logistiek en Financiën) samenwerken om een bestelling te verwerken en te leveren aan de klant.\par

\pard\sa200\sl276\slmult1\qj\b Value Stream Maps\b0: Deze kaarten tonen niet alleen de processtappen, maar ook de stroom van materialen en informatie, evenals wachttijden en voorraden. Ze worden vaak gebruikt in Lean management om verspilling te identificeren.\par

\pard\sa200\sl276\slmult1\qj\b BPMN (Business Process Model and Notation)\b0: Een gestandaardiseerde notatie voor het modelleren van bedrijfsprocessen, die een rijke set symbolen biedt voor het weergeven van verschillende aspecten van processen.\par

\pard\sa200\sl276\slmult1\qj\b 5. Procesverbetering methodologieën\b0\par

\pard\sa200\sl276\slmult1\qj Thuis & Stuive bespreken verschillende methodologieën voor procesverbetering:\par

\pard\sa200\sl276\slmult1\qj\b Lean\b0: Gericht op het elimineren van verspilling (activiteiten die geen waarde toevoegen) en het verbeteren van de flow. Lean identificeert acht soorten verspilling (muda):\par

\pard\sa200\sl276\slmult1\qj - Overproductie\par
- Wachttijd\par
- Transport\par
- Overbewerking\par
- Voorraad\par
- Beweging\par
- Defecten\par
- Onbenutte creativiteit\par

\pard\sa200\sl276\slmult1\qj\b Six Sigma\b0: Gericht op het verminderen van variatie en defecten in processen door middel van statistische methoden. Six Sigma gebruikt de DMAIC-methodologie:\par

\pard\sa200\sl276\slmult1\qj - Define (Definiëren): Het probleem en de doelstellingen definiëren\par
- Measure (Meten): De huidige prestaties meten\par
- Analyze (Analyseren): De oorzaken van problemen analyseren\par
- Improve (Verbeteren): Verbeteringen implementeren\par
- Control (Controleren): De verbeteringen borgen\par

\pard\sa200\sl276\slmult1\qj\b Business Process Reengineering (BPR)\b0: Gericht op het radicaal herontwerpen van processen om dramatische verbeteringen te realiseren. BPR begint vaak met een "clean sheet" benadering, waarbij bestaande processen volledig worden herzien.\par

\pard\sa200\sl276\slmult1\qj\b Kaizen\b0: Gericht op continue, incrementele verbeteringen door betrokkenheid van alle medewerkers. Kaizen benadrukt dat kleine, dagelijkse verbeteringen op de lange termijn tot grote resultaten kunnen leiden.\par

\pard\sa200\sl276\slmult1\qj\b 6. Procesmanagement\b0\par

\pard\sa200\sl276\slmult1\qj Procesmanagement omvat alle activiteiten die nodig zijn om processen te ontwerpen, implementeren, monitoren en verbeteren. Het doel is om de prestaties van processen te optimaliseren en ze af te stemmen op de strategische doelen van de organisatie.\par

\pard\sa200\sl276\slmult1\qj Volgens Thuis & Stuive omvat procesmanagement de volgende aspecten:\par

\pard\sa200\sl276\slmult1\qj\b Procesontwerp\b0: Het definiëren van de stappen, rollen, regels en resources die nodig zijn voor een proces.\par

\pard\sa200\sl276\slmult1\qj\b Procesimplementatie\b0: Het in gebruik nemen van een nieuw of gewijzigd proces, inclusief training van medewerkers en aanpassing van systemen.\par

\pard\sa200\sl276\slmult1\qj\b Procesmonitoring\b0: Het meten en analyseren van de prestaties van processen aan de hand van KPI's (Key Performance Indicators).\par

\pard\sa200\sl276\slmult1\qj\b Procesverbetering\b0: Het identificeren en implementeren van verbeteringen om de efficiëntie en effectiviteit van processen te verhogen.\par

\pard\sa200\sl276\slmult1\qj\b Procesgovernance\b0: Het definiëren van rollen, verantwoordelijkheden en besluitvormingsprocessen voor procesmanagement.\par

\pard\sa200\sl276\slmult1\qj\b 7. Procesautomatisering en -digitalisering\b0\par

\pard\sa200\sl276\slmult1\qj Thuis & Stuive besteden ook aandacht aan de rol van technologie in procesmanagement. Ze bespreken hoe automatisering en digitalisering processen kunnen verbeteren door:\par

\pard\sa200\sl276\slmult1\qj\b Efficiëntie\b0: Automatisering kan handmatige, repetitieve taken elimineren en de doorlooptijd van processen verkorten.\par

\pard\sa200\sl276\slmult1\qj\b Kwaliteit\b0: Geautomatiseerde processen zijn vaak consistenter en minder gevoelig voor menselijke fouten.\par

\pard\sa200\sl276\slmult1\qj\b Schaalbaarheid\b0: Digitale processen kunnen gemakkelijker worden opgeschaald om grotere volumes te verwerken.\par

\pard\sa200\sl276\slmult1\qj\b Inzicht\b0: Digitale systemen kunnen data verzamelen over procesuitvoering, wat waardevolle inzichten oplevert voor verdere verbetering.\par

\pard\sa200\sl276\slmult1\qj\b Integratie\b0: Digitale technologieën maken het mogelijk om processen over verschillende afdelingen en systemen heen te integreren.\par

\pard\sa200\sl276\slmult1\qj Moderne technologieën voor procesautomatisering omvatten:\par

\pard\sa200\sl276\slmult1\qj\b Workflow Management Systems (WfMS)\b0: Systemen die de stroom van werk en informatie tussen processtappen coördineren.\par

\pard\sa200\sl276\slmult1\qj\b Business Process Management Systems (BPMS)\b0: Geïntegreerde platforms voor het modelleren, implementeren, uitvoeren en monitoren van bedrijfsprocessen.\par

\pard\sa200\sl276\slmult1\qj\b Robotic Process Automation (RPA)\b0: Software die menselijke interacties met digitale systemen nabootst om repetitieve taken te automatiseren.\par

\pard\sa200\sl276\slmult1\qj\b Artificial Intelligence (AI)\b0: Geavanceerde technologieën die intelligente besluitvorming en verwerking mogelijk maken, zoals machine learning en natuurlijke taalverwerking.\par

\pard\sa200\sl276\slmult1\qj\b Conclusie\b0\par

\pard\sa200\sl276\slmult1\qj Processen vormen de kern van elke organisatie en bepalen hoe effectief en efficiënt een organisatie haar doelen kan bereiken. Een goed begrip van processen en procesmanagement is daarom essentieel voor elke manager of professional.\par

\pard\sa200\sl276\slmult1\qj Door processen systematisch te analyseren, visualiseren, verbeteren en beheren, kunnen organisaties hun prestaties verbeteren, kosten verlagen, kwaliteit verhogen en beter inspelen op veranderende klantbehoeften en marktomstandigheden.\par

\pard\sa200\sl276\slmult1\qj De concepten en methoden die in dit document zijn beschreven, bieden een solide basis voor het begrijpen en verbeteren van processen in elke organisatie.\par

\pard\sa200\sl276\slmult1\qj\b Bijlagen\b0\par

\pard\sa200\sl276\slmult1\qj Bij dit document horen drie diagrammen die de belangrijkste concepten visualiseren:\par

\pard\sa200\sl276\slmult1\qj 1. Input-Throughput-Output (ITO) Model (Bedrijfsprocessen_ITO_Model.draw.io)\par
2. Types Bedrijfsprocessen (Bedrijfsprocessen_Types.draw.io)\par
3. Swimlane Diagram: Orderverwerking Proces (Bedrijfsprocessen_Swimlane.draw.io)\par

\pard\sa200\sl276\slmult1\qj Deze diagrammen kunnen worden geopend en bewerkt met draw.io (https://app.diagrams.net/).\par

\pard\sa200\sl276\slmult1\qj\b Bronnen\b0\par

\pard\sa200\sl276\slmult1\qj - Thuis, P., & Stuive, R. (2020). Bedrijfskunde Integraal (3e druk). Noordhoff Uitgevers.\par
- Bedrijfskunde Lesweek 2.1 t/m 2.7 (PDF documenten)\par

\pard\sa200\sl276\slmult1\f1\fs22\par
}
