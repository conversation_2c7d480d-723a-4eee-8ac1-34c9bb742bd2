BEDRIJFSKUNDE: PROCESSEN UIT HET BOEK VAN PETER THUIS & RIENK STUIVE (3E DRUK)
===========================================================================

Dit document bevat een samenvatting van de belangrijkste processen uit het boek "Bedrijfskunde Integraal" van Peter Thuis & Rienk Stuive (3e druk), zonder aannames te doen over wat er in de slides staat.

1. BASISCONCEPTEN PROCESSEN
---------------------------

Een proces is een reeks van logisch samenhangende activiteiten om een bepaald doel te bereiken. Processen transformeren inputs naar outputs die waarde hebben voor klanten.

Input-Throughput-Output (ITO) model:
- Input: grondstoffen, arbeid, informatie, kapitaal, energie
- Throughput: transformatieproces (productie, dienstverlening, informatieverwerking)
- Output: producten, diensten, informatie
- Feedback: informatie over output die wordt gebruikt om het proces te verbeteren

2. TYPES BEDRIJFSPROCESSEN
--------------------------

Bedrijfsprocessen worden onderverdeeld in drie hoofdcategorieën:

Primaire processen:
- Inkoop
- Productie
- Verkoop
- Distributie
- Klantenservice

Ondersteunende processen:
- Personeelsbeheer (HRM)
- Financiële administratie
- IT-ondersteuning
- Facilitaire diensten
- Juridische zaken

Managementprocessen:
- Strategische planning
- Budgettering
- Prestatiemanagement
- Risicomanagement
- Kwaliteitsmanagement

3. PRIMAIRE PROCESSEN
---------------------

Inkoopproces:
1. Specificeren van behoeften
2. Selecteren van leveranciers
3. Onderhandelen en contracteren
4. Bestellen
5. Bewaken van leveringen
6. Nazorg en evaluatie

Productieproces:
1. Productieplanning
2. Werkvoorbereiding
3. Productie-uitvoering
4. Kwaliteitscontrole
5. Verpakking
6. Opslag

Verkoopproces:
1. Prospectie (zoeken naar potentiële klanten)
2. Voorbereiding (verzamelen informatie over klant)
3. Benadering (eerste contact)
4. Presentatie (demonstratie product/dienst)
5. Behandelen bezwaren
6. Afsluiting (verkoop)
7. Follow-up (nazorg)

Distributieproces:
1. Orderontvangst
2. Orderpicking
3. Verpakking
4. Verzending
5. Transport
6. Aflevering

Klantenserviceproces:
1. Ontvangst klantvraag/klacht
2. Registratie
3. Analyse
4. Oplossing
5. Terugkoppeling naar klant
6. Evaluatie en verbetering

4. ONDERSTEUNENDE PROCESSEN
---------------------------

HRM-processen:
1. Personeelsplanning
2. Werving en selectie
3. Introductie nieuwe medewerkers
4. Training en ontwikkeling
5. Prestatiebeoordeling
6. Beloning
7. Uitstroom

Financiële processen:
1. Budgettering
2. Boekhouding
3. Facturering
4. Debiteurenadministratie
5. Crediteurenadministratie
6. Salarisadministratie
7. Financiële rapportage

IT-processen:
1. IT-planning
2. Systeemontwikkeling
3. Implementatie
4. Beheer en onderhoud
5. Gebruikersondersteuning
6. Beveiliging
7. Disaster recovery

Facilitaire processen:
1. Gebouwbeheer
2. Onderhoud
3. Beveiliging
4. Schoonmaak
5. Catering
6. Inkoop facilitaire middelen

Juridische processen:
1. Contractbeheer
2. Compliance
3. Intellectueel eigendom
4. Juridisch advies
5. Geschillenafhandeling

5. MANAGEMENTPROCESSEN
----------------------

Strategische planningsproces:
1. Analyse externe omgeving
2. Analyse interne omgeving
3. Formuleren missie en visie
4. Bepalen strategische doelen
5. Ontwikkelen strategieën
6. Implementatie
7. Evaluatie en bijsturing

Budgetteringsproces:
1. Opstellen budgetrichtlijnen
2. Verzamelen budgetaanvragen
3. Beoordelen aanvragen
4. Consolideren budgetten
5. Goedkeuren budget
6. Communiceren budget
7. Monitoren en bijsturen

Prestatiemanagementproces:
1. Bepalen KPI's (Key Performance Indicators)
2. Meten prestaties
3. Analyseren resultaten
4. Rapporteren
5. Feedback geven
6. Verbeteracties definiëren
7. Implementeren verbeteringen

Risicomanagementproces:
1. Identificeren risico's
2. Analyseren risico's
3. Evalueren risico's
4. Behandelen risico's
5. Monitoren en reviewen
6. Communiceren en consulteren

Kwaliteitsmanagementproces:
1. Kwaliteitsplanning
2. Kwaliteitsborging
3. Kwaliteitscontrole
4. Kwaliteitsverbetering

6. PROCESVISUALISATIE
---------------------

Verschillende methoden om processen visueel weer te geven:

Flowcharts:
- Tonen de stappen in een proces met symbolen en pijlen
- Geven een duidelijk overzicht van de volgorde van activiteiten

Processtroomdiagrammen:
- Gedetailleerde weergave van processtappen
- Inclusief beslispunten en alternatieve paden

7. PROCESMANAGEMENT
------------------

Procesmanagement omvat alle activiteiten die nodig zijn om processen te ontwerpen, implementeren, monitoren en verbeteren:

Procesontwerp:
- Definiëren van stappen, rollen, regels en resources
- Bepalen van inputs, outputs en prestatie-indicatoren
- Documenteren van het proces

Procesimplementatie:
- In gebruik nemen van een nieuw of gewijzigd proces
- Training van medewerkers
- Aanpassing van systemen
- Communicatie over het proces

Procesmonitoring:
- Meten van procesprestaties met KPI's
- Analyseren van resultaten
- Identificeren van afwijkingen
- Rapporteren over procesprestaties

Procesverbetering:
- Identificeren van verbetermogelijkheden
- Analyseren van oorzaken van problemen
- Ontwikkelen van verbetervoorstellen
- Implementeren van verbeteringen
- Evalueren van resultaten

8. PROCESBESTURING
-----------------

Procesbesturing omvat het plannen, uitvoeren, controleren en bijsturen van processen:

Plan-Do-Check-Act (PDCA) cyclus:
- Plan: doelen stellen en processen plannen
- Do: processen uitvoeren
- Check: resultaten meten en analyseren
- Act: processen verbeteren

Regelkringen:
- Meten van procesoutput
- Vergelijken met norm
- Bijsturen bij afwijkingen

Prestatie-indicatoren:
- Effectiviteit: mate waarin doelen worden bereikt
- Efficiëntie: verhouding tussen output en input
- Kwaliteit: mate waarin aan eisen wordt voldaan
- Flexibiliteit: vermogen om aan te passen aan veranderingen
- Innovatie: vermogen om te vernieuwen

9. PROCESSEN EN ORGANISATIESTRUCTUUR
-----------------------------------

De relatie tussen processen en organisatiestructuur:

Functionele organisatie:
- Georganiseerd rond functies/afdelingen
- Processen lopen door verschillende afdelingen
- Uitdaging: coördinatie tussen afdelingen

Procesgerichte organisatie:
- Georganiseerd rond processen
- Teams verantwoordelijk voor complete processen
- Voordeel: betere afstemming op klantbehoeften

Matrixorganisatie:
- Combinatie van functionele en procesgerichte structuur
- Medewerkers rapporteren aan functionele manager én procesmanager
- Uitdaging: dubbele aansturing

10. PROCESSEN EN INFORMATIESYSTEMEN
----------------------------------

De rol van informatiesystemen in processen:

Enterprise Resource Planning (ERP):
- Geïntegreerd systeem voor bedrijfsprocessen
- Modules voor verschillende functionele gebieden
- Gemeenschappelijke database

Customer Relationship Management (CRM):
- Beheer van klantinteracties
- Ondersteuning van verkoopprocessen
- Klantservice en -ondersteuning

Supply Chain Management (SCM):
- Beheer van goederenstroom
- Planning en forecasting
- Leveranciersmanagement

Business Intelligence (BI):
- Analyse van bedrijfsgegevens
- Dashboards en rapportages
- Ondersteuning van besluitvorming

BRONNEN
-------

- Thuis, P., & Stuive, R. (2020). Bedrijfskunde Integraal (3e druk). Noordhoff Uitgevers.
