<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="26.2.12">
  <diagram id="C5RBs43oDa-KdzZeNtuy" name="Logisch ERD">
    <mxGraphModel dx="959" dy="376" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title" value="EuroCaps - Logisch ERD" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="160" y="20" width="520" height="40" as="geometry" />
        </mxCell>
        <mxCell id="supplier" value="Leverancier&#xa;&#xa;PK: LeverancierID&#xa;LeverancierNaam&#xa;ContactPersoon&#xa;Email&#xa;Telefoon&#xa;Adres" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="40" y="100" width="160" height="120" as="geometry" />
        </mxCell>
        <mxCell id="material" value="Materiaal&#xa;&#xa;PK: MateriaalID&#xa;MateriaalNaam&#xa;MateriaalType&#xa;Eenheid&#xa;HuidigeVoorraad&#xa;MinimumVoorraad&#xa;FK: LeverancierID" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="280" y="100" width="160" height="140" as="geometry" />
        </mxCell>
        <mxCell id="production_batch" value="Productiebatch&#xa;&#xa;PK: BatchID&#xa;BatchNummer&#xa;BatchDatum&#xa;FK: ProductID&#xa;GeplandAantal&#xa;WerkelijkAantal&#xa;Status" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="280" y="280" width="160" height="140" as="geometry" />
        </mxCell>
        <mxCell id="product" value="Product&#xa;&#xa;PK: ProductID&#xa;ProductNaam&#xa;ProductType&#xa;VerpakkingsGrootte&#xa;Beschrijving" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="520" y="280" width="160" height="120" as="geometry" />
        </mxCell>
        <mxCell id="production_step" value="Productiestap&#xa;&#xa;PK: StapID&#xa;FK: BatchID&#xa;StapType&#xa;StartTijd&#xa;EindTijd&#xa;FK: MachineID&#xa;FK: MedewerkerID&#xa;VerwerktAantal&#xa;AfgekeurdAantal&#xa;Notities" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="280" y="460" width="160" height="160" as="geometry" />
        </mxCell>
        <mxCell id="quality_check" value="Kwaliteitscontrole&#xa;&#xa;PK: ControleID&#xa;FK: StapID&#xa;ControleTijd&#xa;ControleType&#xa;Resultaat&#xa;GemetenWaarde&#xa;MinimumWaarde&#xa;MaximumWaarde&#xa;FK: MedewerkerID&#xa;Notities" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="40" y="460" width="160" height="160" as="geometry" />
        </mxCell>
        <mxCell id="machine" value="Machine&#xa;&#xa;PK: MachineID&#xa;MachineNaam&#xa;MachineType&#xa;Locatie&#xa;InstallatieDate&#xa;OnderhoudsInterval&#xa;LaatsteOnderhoud" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="280" y="660" width="160" height="140" as="geometry" />
        </mxCell>
        <mxCell id="employee" value="Medewerker&#xa;&#xa;PK: MedewerkerID&#xa;Voornaam&#xa;Achternaam&#xa;Afdeling&#xa;Functie&#xa;IndienstDatum" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="520" y="460" width="160" height="120" as="geometry" />
        </mxCell>
        <mxCell id="customer" value="Klant&#xa;&#xa;PK: KlantID&#xa;KlantNaam&#xa;ContactPersoon&#xa;Email&#xa;Telefoon&#xa;Adres" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="520" y="100" width="160" height="120" as="geometry" />
        </mxCell>
        <mxCell id="order" value="Bestelling&#xa;&#xa;PK: BestellingID&#xa;BestellingNummer&#xa;FK: KlantID&#xa;BestellingDatum&#xa;LeveringsDatum&#xa;Status&#xa;Notities" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="520" y="660" width="160" height="140" as="geometry" />
        </mxCell>
        <mxCell id="material_usage" value="MateriaalGebruik&#xa;&#xa;PK: GebruikID&#xa;FK: BatchID&#xa;FK: MateriaalID&#xa;Hoeveelheid&#xa;Datum" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="40" y="280" width="160" height="120" as="geometry" />
        </mxCell>
        <mxCell id="order_item" value="BestellingItem&#xa;&#xa;PK: ItemID&#xa;FK: BestellingID&#xa;FK: ProductID&#xa;Aantal&#xa;Prijs" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="520" y="840" width="160" height="100" as="geometry" />
        </mxCell>
        <mxCell id="supplier_material" value="1" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;startArrow=none;startFill=0;endFill=0;" parent="1" source="supplier" target="material" edge="1">
          <mxGeometry x="-0.9" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="supplier_material_n" value="n" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;startArrow=classic;startFill=1;endFill=0;" parent="1" edge="1">
          <mxGeometry x="0.9" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="160" as="sourcePoint" />
            <mxPoint x="280" y="160" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="material_usage_material" value="1" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;startArrow=none;startFill=0;endFill=0;" parent="1" source="material" target="material_usage" edge="1">
          <mxGeometry x="-0.7143" y="-10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <Array as="points">
              <mxPoint x="360" y="260" />
              <mxPoint x="120" y="260" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="material_usage_material_n" value="n" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;startArrow=classic;startFill=1;endFill=0;" parent="1" edge="1">
          <mxGeometry x="0.7143" y="-10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="240" as="sourcePoint" />
            <mxPoint x="120" y="280" as="targetPoint" />
            <Array as="points">
              <mxPoint x="360" y="260" />
              <mxPoint x="120" y="260" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="material_usage_batch" value="n" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;startArrow=classic;startFill=1;endFill=0;" parent="1" source="material_usage" target="production_batch" edge="1">
          <mxGeometry x="0.9" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="material_usage_batch_1" value="1" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;startArrow=none;startFill=0;endFill=0;" parent="1" edge="1">
          <mxGeometry x="-0.9" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="340" as="sourcePoint" />
            <mxPoint x="280" y="340" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="batch_product" value="n" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;startArrow=classic;startFill=1;endFill=0;" parent="1" source="production_batch" target="product" edge="1">
          <mxGeometry x="0.9" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="batch_product_1" value="1" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;startArrow=none;startFill=0;endFill=0;" parent="1" edge="1">
          <mxGeometry x="-0.9" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="340" as="sourcePoint" />
            <mxPoint x="520" y="340" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="batch_step" value="1" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;startArrow=none;startFill=0;endFill=0;" parent="1" source="production_batch" target="production_step" edge="1">
          <mxGeometry x="-0.7143" y="-10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="batch_step_n" value="n" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;startArrow=classic;startFill=1;endFill=0;" parent="1" edge="1">
          <mxGeometry x="0.7143" y="-10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="420" as="sourcePoint" />
            <mxPoint x="360" y="460" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="step_quality" value="1" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;startArrow=none;startFill=0;endFill=0;" parent="1" source="production_step" target="quality_check" edge="1">
          <mxGeometry x="-0.9" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="step_quality_n" value="n" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;startArrow=classic;startFill=1;endFill=0;" parent="1" edge="1">
          <mxGeometry x="0.9" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="280" y="540" as="sourcePoint" />
            <mxPoint x="200" y="540" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="step_machine" value="n" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;startArrow=classic;startFill=1;endFill=0;" parent="1" source="production_step" target="machine" edge="1">
          <mxGeometry x="0.7143" y="-10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="step_machine_1" value="1" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;startArrow=none;startFill=0;endFill=0;" parent="1" edge="1">
          <mxGeometry x="-0.7143" y="-10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="620" as="sourcePoint" />
            <mxPoint x="360" y="660" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="step_employee" value="n" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;startArrow=classic;startFill=1;endFill=0;" parent="1" source="production_step" target="employee" edge="1">
          <mxGeometry x="0.9" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="step_employee_1" value="1" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;startArrow=none;startFill=0;endFill=0;" parent="1" edge="1">
          <mxGeometry x="-0.9" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="520" as="sourcePoint" />
            <mxPoint x="520" y="520" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="customer_order" value="1" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;startArrow=none;startFill=0;endFill=0;" parent="1" source="customer" target="order" edge="1">
          <mxGeometry x="-0.7143" y="-10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <Array as="points">
              <mxPoint x="600" y="400" />
              <mxPoint x="600" y="640" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="customer_order_n" value="n" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;startArrow=classic;startFill=1;endFill=0;" parent="1" edge="1">
          <mxGeometry x="0.7143" y="-10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="220" as="sourcePoint" />
            <mxPoint x="600" y="660" as="targetPoint" />
            <Array as="points">
              <mxPoint x="600" y="400" />
              <mxPoint x="600" y="640" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="order_item_order" value="1" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;startArrow=none;startFill=0;endFill=0;" parent="1" source="order" target="order_item" edge="1">
          <mxGeometry x="-0.7143" y="-10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="order_item_order_n" value="n" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;startArrow=classic;startFill=1;endFill=0;" parent="1" edge="1">
          <mxGeometry x="0.7143" y="-10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="800" as="sourcePoint" />
            <mxPoint x="600" y="840" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="order_item_product" value="n" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontSize=12;startArrow=classic;startFill=1;endFill=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="order_item" target="product" edge="1">
          <mxGeometry x="0.9" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <Array as="points">
              <mxPoint x="480" y="890" />
              <mxPoint x="480" y="440" />
              <mxPoint x="600" y="440" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="order_item_product_1" value="1" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontSize=12;startArrow=none;startFill=0;endFill=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" edge="1">
          <mxGeometry x="-0.9" y="10" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="520" y="890" as="sourcePoint" />
            <mxPoint x="600" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="480" y="890" />
              <mxPoint x="480" y="440" />
              <mxPoint x="600" y="440" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
