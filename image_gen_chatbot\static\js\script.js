document.addEventListener('DOMContentLoaded', function() {
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');

    // Function to add a message to the chat
    function addMessage(message, isUser) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        // Check if the message contains an image tag
        if (!isUser && message.includes('[IMAGE:')) {
            // Extract the text part and image URL
            const parts = message.split('[IMAGE:');
            const textPart = parts[0];
            const imageUrl = parts[1].replace(']', '');
            
            // Add the text part
            messageContent.textContent = textPart;
            
            // Create and add the image
            const image = document.createElement('img');
            image.src = imageUrl;
            image.alt = 'Generated Image';
            image.className = 'generated-image';
            
            // Add both to the message
            messageDiv.appendChild(messageContent);
            messageDiv.appendChild(image);
        } else {
            messageContent.textContent = message;
            messageDiv.appendChild(messageContent);
        }
        
        chatMessages.appendChild(messageDiv);
        
        // Scroll to the bottom of the chat
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Function to send a message
    function sendMessage() {
        const message = userInput.value.trim();
        
        if (message) {
            // Add user message to chat
            addMessage(message, true);
            
            // Clear input field
            userInput.value = '';
            
            // Send message to server
            fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                // Add bot response to chat
                addMessage(data.response, false);
            })
            .catch(error => {
                console.error('Error:', error);
                addMessage('Sorry, there was an error processing your request.', false);
            });
        }
    }

    // Event listeners
    sendButton.addEventListener('click', sendMessage);
    
    userInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
});
