import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

def load_data():
    """Load the Kikker.csv dataset"""
    try:
        df = pd.read_csv('americaps/Kikker.csv')
        print(f"Successfully loaded dataset with {df.shape[0]} rows and {df.shape[1]} columns.")
        return df
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        return None

def clean_data(df):
    """Basic cleaning of the dataset"""
    # Create a copy to avoid modifying the original
    df_clean = df.copy()

    # Convert percentage strings to float values
    for col in ['Klantretourpercentage', 'Defectpercentage', 'Benuttingsgraad']:
        if col in df_clean.columns:
            df_clean[col] = df_clean[col].str.rstrip('%').astype('float') / 100

    # Convert CO2-Footprint to numeric
    if 'CO2-Footprint' in df_clean.columns:
        df_clean['CO2-Footprint'] = df_clean['CO2-Footprint'].str.extract(r'(\d+\.\d+)').astype(float)

    # Convert Energieverbruik to numeric
    if 'Energieverbruik' in df_clean.columns:
        df_clean['Energieverbruik'] = pd.to_numeric(df_clean['Energieverbruik'].str.extract(r'(\d+)')[0], errors='coerce')
        # Replace unrealistic values (999999 kWh)
        df_clean.loc[df_clean['Energieverbruik'] > 10000, 'Energieverbruik'] = np.nan

    # Convert Gewichtscontrole to numeric
    if 'Gewichtscontrole' in df_clean.columns:
        df_clean['Gewichtscontrole'] = df_clean['Gewichtscontrole'].str.extract(r'(\d+\.\d+)').astype(float)

    # Convert Cost to numeric
    if 'Cost' in df_clean.columns:
        df_clean['Cost'] = df_clean['Cost'].str.extract(r'(\d+\.\d+)').astype(float)

    # Convert Cyclustijd to hours
    if 'Cyclustijd' in df_clean.columns:
        df_clean['Cyclustijd'] = df_clean['Cyclustijd'].str.extract(r'(\d+\.\d+)').astype(float)

    # Convert ProcessTime to hours
    if 'ProcessTime' in df_clean.columns:
        df_clean['ProcessTime'] = df_clean['ProcessTime'].str.extract(r'(\d+\.\d+)').astype(float)

    return df_clean

def six_sigma_analysis(df):
    """
    Six Sigma analysis focusing on defect reduction and process capability
    """
    print("\n=== SIX SIGMA ANALYSIS ===")

    # Calculate defect rate statistics
    defect_rate = df['Defectpercentage']

    print(f"Defect Rate Statistics:")
    print(f"- Mean: {defect_rate.mean():.2%}")
    print(f"- Median: {defect_rate.median():.2%}")
    print(f"- Min: {defect_rate.min():.2%}")
    print(f"- Max: {defect_rate.max():.2%}")
    print(f"- Standard Deviation: {defect_rate.std():.2%}")

    # Calculate process capability for weight control
    weight = df['Gewichtscontrole'].dropna()

    # Assuming target weight is around the mean with ±0.1 kg tolerance
    target = weight.mean()
    lsl = target - 0.1  # Lower specification limit
    usl = target + 0.1  # Upper specification limit

    # Calculate Cp and Cpk
    sigma = weight.std()
    cp = (usl - lsl) / (6 * sigma)
    cpu = (usl - target) / (3 * sigma)
    cpl = (target - lsl) / (3 * sigma)
    cpk = min(cpu, cpl)

    print(f"\nProcess Capability Analysis for Weight Control:")
    print(f"- Target weight: {target:.2f} kg")
    print(f"- Specification limits: {lsl:.2f} kg to {usl:.2f} kg")
    print(f"- Process capability (Cp): {cp:.2f}")
    print(f"- Process capability index (Cpk): {cpk:.2f}")

    # Interpret Cpk
    if cpk < 1.0:
        print("- Interpretation: Process is not capable of meeting specifications")
    elif cpk < 1.33:
        print("- Interpretation: Process is marginally capable")
    elif cpk < 1.67:
        print("- Interpretation: Process is capable")
    else:
        print("- Interpretation: Process is highly capable")

    # Calculate DPMO (Defects Per Million Opportunities)
    dpmo = defect_rate.mean() * 1000000

    # Estimate Sigma level
    if dpmo > 0:
        # Approximate sigma level calculation
        sigma_level = 0.8406 + np.sqrt(29.37 - 2.221 * np.log(dpmo))
    else:
        sigma_level = 6.0

    print(f"\nSix Sigma Performance Metrics:")
    print(f"- DPMO (Defects Per Million Opportunities): {dpmo:.0f}")
    print(f"- Estimated Sigma Level: {sigma_level:.2f}")

    return {
        "defect_rate_mean": defect_rate.mean(),
        "defect_rate_std": defect_rate.std(),
        "process_capability": cp,
        "process_capability_index": cpk,
        "dpmo": dpmo,
        "sigma_level": sigma_level
    }

def lean_analysis(df):
    """
    Lean analysis focusing on cycle time and process efficiency
    """
    print("\n=== LEAN ANALYSIS ===")

    # Analyze cycle time
    cycle_time = df['Cyclustijd'].dropna()

    print(f"Cycle Time Statistics:")
    print(f"- Mean: {cycle_time.mean():.2f} hours")
    print(f"- Median: {cycle_time.median():.2f} hours")
    print(f"- Min: {cycle_time.min():.2f} hours")
    print(f"- Max: {cycle_time.max():.2f} hours")
    print(f"- Standard Deviation: {cycle_time.std():.2f} hours")

    # Calculate value-added time vs. total time
    # Assuming ProcessTime is total time and Cyclustijd is value-added time
    if 'ProcessTime' in df.columns:
        process_time = df['ProcessTime'].dropna()

        # Calculate only for rows where both values exist
        mask = df['Cyclustijd'].notna() & df['ProcessTime'].notna()
        value_added_ratio = df.loc[mask, 'Cyclustijd'] / df.loc[mask, 'ProcessTime']

        print(f"\nValue Stream Analysis:")
        print(f"- Average Process Time: {process_time.mean():.2f} hours")
        print(f"- Value-Added Ratio: {value_added_ratio.mean():.2%}")
        print(f"- Non-Value-Added Time: {(1 - value_added_ratio.mean()) * 100:.2f}%")

    # Analyze utilization rate
    utilization = df['Benuttingsgraad'].dropna()

    print(f"\nUtilization Rate Statistics:")
    print(f"- Mean: {utilization.mean():.2%}")
    print(f"- Median: {utilization.median():.2%}")
    print(f"- Min: {utilization.min():.2%}")
    print(f"- Max: {utilization.max():.2%}")

    # Analyze by machine type
    if 'PackagingApparaat' in df.columns:
        print("\nCycle Time by Packaging Machine:")
        for machine in df['PackagingApparaat'].unique():
            machine_cycle = df[df['PackagingApparaat'] == machine]['Cyclustijd'].dropna()
            if len(machine_cycle) > 0:
                print(f"- {machine}: {machine_cycle.mean():.2f} hours (n={len(machine_cycle)})")

    return {
        "cycle_time_mean": cycle_time.mean(),
        "cycle_time_std": cycle_time.std(),
        "utilization_mean": utilization.mean(),
        "value_added_ratio": value_added_ratio.mean() if 'ProcessTime' in df.columns else None
    }

def toc_analysis(df):
    """
    Theory of Constraints analysis focusing on bottlenecks and constraints
    """
    print("\n=== THEORY OF CONSTRAINTS ANALYSIS ===")

    # Calculate time spent in each process
    # Assuming we can calculate process times from start/end timestamps
    process_times = {}

    # Calculate grinding time
    if 'GrindingDatumTijdStart' in df.columns and 'GrindingDatumTijdEind' in df.columns:
        df['GrindingTime'] = pd.to_datetime(df['GrindingDatumTijdEind'], errors='coerce') - pd.to_datetime(df['GrindingDatumTijdStart'], errors='coerce')
        df['GrindingTimeHours'] = df['GrindingTime'].dt.total_seconds() / 3600
        process_times['Grinding'] = df['GrindingTimeHours'].dropna()

    # Calculate filling time
    if 'FillingDatumTijdStart' in df.columns and 'FillingDatumTijdEind' in df.columns:
        df['FillingTime'] = pd.to_datetime(df['FillingDatumTijdEind'], errors='coerce') - pd.to_datetime(df['FillingDatumTijdStart'], errors='coerce')
        df['FillingTimeHours'] = df['FillingTime'].dt.total_seconds() / 3600
        process_times['Filling'] = df['FillingTimeHours'].dropna()

    # Calculate packaging time
    if 'PackagingDatumTijdStart' in df.columns and 'PackagingDatumTijdEind' in df.columns:
        df['PackagingTime'] = pd.to_datetime(df['PackagingDatumTijdEind'], errors='coerce') - pd.to_datetime(df['PackagingDatumTijdStart'], errors='coerce')
        df['PackagingTimeHours'] = df['PackagingTime'].dt.total_seconds() / 3600
        process_times['Packaging'] = df['PackagingTimeHours'].dropna()

    # Identify the bottleneck process
    process_means = {process: times.mean() for process, times in process_times.items()}
    bottleneck = max(process_means.items(), key=lambda x: x[1])

    print("Process Time Analysis:")
    for process, times in process_times.items():
        print(f"- {process}: {times.mean():.2f} hours (n={len(times)})")

    print(f"\nBottleneck Identification:")
    print(f"- Bottleneck Process: {bottleneck[0]}")
    print(f"- Average Process Time: {bottleneck[1]:.2f} hours")

    # Analyze inventory levels
    if 'Voorraadniveaus' in df.columns:
        inventory = df['Voorraadniveaus'].str.extract(r'(\d+)').astype(float)

        print(f"\nInventory Analysis:")
        print(f"- Average Inventory Level: {inventory.mean().item():.0f} units")
        print(f"- Median Inventory Level: {inventory.median().item():.0f} units")
        print(f"- Min Inventory Level: {inventory.min().item():.0f} units")
        print(f"- Max Inventory Level: {inventory.max().item():.0f} units")

    # Analyze supplier lead time
    if 'LeverancierLevertijd' in df.columns:
        lead_time = df['LeverancierLevertijd'].str.extract(r'(\d+)').astype(float)

        print(f"\nSupplier Lead Time Analysis:")
        print(f"- Average Lead Time: {lead_time.mean().item():.1f} days")
        print(f"- Median Lead Time: {lead_time.median().item():.1f} days")
        print(f"- Min Lead Time: {lead_time.min().item():.1f} days")
        print(f"- Max Lead Time: {lead_time.max().item():.1f} days")

    # Prepare return values
    result = {
        "process_times": process_means,
        "bottleneck_process": bottleneck[0],
        "bottleneck_time": bottleneck[1]
    }

    if 'Voorraadniveaus' in df.columns:
        result["inventory_mean"] = inventory.mean().item()

    if 'LeverancierLevertijd' in df.columns:
        result["lead_time_mean"] = lead_time.mean().item()

    return result

def kaizen_analysis(df):
    """
    Kaizen analysis focusing on continuous improvement opportunities
    """
    print("\n=== KAIZEN ANALYSIS ===")

    # Analyze customer satisfaction and return rates
    # Convert Klanttevredenheid to numeric, coercing errors to NaN
    df['Klanttevredenheid_numeric'] = pd.to_numeric(df['Klanttevredenheid'], errors='coerce')
    customer_satisfaction = df['Klanttevredenheid_numeric'].dropna()
    return_rate = df['Klantretourpercentage'].dropna()

    print(f"Customer Metrics:")
    print(f"- Average Customer Satisfaction: {customer_satisfaction.mean():.2f}/5")
    print(f"- Average Return Rate: {return_rate.mean():.2%}")

    # Analyze panel test results
    if 'Panel Test' in df.columns:
        panel_results = df['Panel Test'].value_counts(normalize=True)

        print(f"\nQuality Assessment (Panel Test):")
        for result, percentage in panel_results.items():
            print(f"- {result}: {percentage:.2%}")

    # Analyze energy consumption
    if 'Energieverbruik' in df.columns:
        energy = df['Energieverbruik'].dropna()

        print(f"\nEnergy Consumption:")
        print(f"- Average Energy Consumption: {energy.mean():.2f} kWh")
        print(f"- Median Energy Consumption: {energy.median():.2f} kWh")

    # Analyze sustainability metrics
    if 'Duurzaamheid Score' in df.columns and 'CO2-Footprint' in df.columns:
        # Convert to numeric
        df['Duurzaamheid_Score_numeric'] = pd.to_numeric(df['Duurzaamheid Score'], errors='coerce')
        sustainability = df['Duurzaamheid_Score_numeric'].dropna()
        co2 = df['CO2-Footprint'].dropna()

        print(f"\nSustainability Metrics:")
        print(f"- Average Sustainability Score: {sustainability.mean():.2f}/100")
        print(f"- Average CO2 Footprint: {co2.mean():.2f} kg CO2/kg")

    # Analyze cost
    if 'Cost' in df.columns:
        cost = df['Cost'].dropna()

        print(f"\nCost Analysis:")
        print(f"- Average Cost: {cost.mean():.2f} euros")
        print(f"- Median Cost: {cost.median():.2f} euros")

    # Identify improvement opportunities
    print("\nImprovement Opportunities:")

    # Check defect rates by coffee bean type
    if 'Koffieboon' in df.columns and 'Defectpercentage' in df.columns:
        defects_by_bean = df.groupby('Koffieboon')['Defectpercentage'].mean().sort_values(ascending=False)

        print("Defect Rate by Coffee Bean Type:")
        for bean, rate in defects_by_bean.items():
            print(f"- {bean}: {rate:.2%}")

    # Check customer satisfaction by roasting profile
    if 'Roosterprofiel' in df.columns and 'Klanttevredenheid_numeric' in df.columns:
        satisfaction_by_profile = df.groupby('Roosterprofiel')['Klanttevredenheid_numeric'].mean().sort_values()

        print("\nCustomer Satisfaction by Roasting Profile:")
        for profile, score in satisfaction_by_profile.items():
            print(f"- {profile}: {score:.2f}/5")

    # Prepare return values
    result = {
        "customer_satisfaction": customer_satisfaction.mean(),
        "return_rate": return_rate.mean()
    }

    if 'Panel Test' in df.columns:
        result["panel_results"] = panel_results.to_dict()

    if 'Energieverbruik' in df.columns:
        result["energy_consumption"] = energy.mean()

    if 'Duurzaamheid_Score_numeric' in df.columns:
        result["sustainability_score"] = sustainability.mean()

    if 'Cost' in df.columns:
        result["cost_mean"] = cost.mean()

    return result

def main():
    """Main function to run all analyses"""
    print("=== AMERICAPS QUALITY MANAGEMENT ANALYSIS ===")

    # Load data
    df = load_data()
    if df is None:
        return

    # Clean data
    df_clean = clean_data(df)

    # Run analyses
    six_sigma_results = six_sigma_analysis(df_clean)
    lean_results = lean_analysis(df_clean)
    toc_results = toc_analysis(df_clean)
    kaizen_results = kaizen_analysis(df_clean)

    # Print summary of findings and recommendations
    print("\n=== SUMMARY OF FINDINGS AND RECOMMENDATIONS ===")

    print("\n1. Six Sigma Recommendation:")
    print("   Focus on reducing weight variation in the filling process to improve process capability.")
    print(f"   Current Cpk: {six_sigma_results['process_capability_index']:.2f}, Target: > 1.33")
    print("   Implement Statistical Process Control (SPC) charts for weight monitoring.")

    print("\n2. Lean Recommendation:")
    print("   Reduce non-value-added time in the production process to improve efficiency.")
    print(f"   Current value-added ratio: {lean_results['value_added_ratio']:.2%}, Target: > 80%")
    print("   Implement value stream mapping to identify and eliminate waste.")

    print("\n3. Theory of Constraints Recommendation:")
    print(f"   Focus on improving the {toc_results['bottleneck_process']} process to increase throughput.")
    print(f"   Current bottleneck time: {toc_results['bottleneck_time']:.2f} hours")
    print("   Implement drum-buffer-rope scheduling to manage the constraint.")

    print("\n4. Kaizen Recommendation:")
    print("   Implement continuous improvement for sustainability and energy efficiency.")
    print("   Focus on improving processes for coffee beans with higher defect rates.")
    print("   Establish cross-functional teams for regular kaizen events.")
    if 'customer_satisfaction' in kaizen_results:
        print(f"   Current customer satisfaction: {kaizen_results['customer_satisfaction']:.2f}/5, Target: > 4.5/5")

if __name__ == "__main__":
    main()
