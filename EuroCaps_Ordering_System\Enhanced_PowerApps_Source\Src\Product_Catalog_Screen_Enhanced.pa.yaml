# ************************************************************************************************
# Enhanced Product Catalog Screen for EuroCaps Ordering System
# Based on detailed mockup specifications with full product management
# Color Scheme: Header/Sidebar: 2C3E50, Background: 1B3A4B, Search/Cards: A9C6E8, Buttons: F39C12, Text: FFFFFF
# ************************************************************************************************
Screens:
  Product_Catalog_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(243, 156, 18, 1)
      Fill: =RGBA(27, 58, 75, 1)
      OnVisible: |
        =// Initialize product search and filter variables
        Set(varProductSearch, "");
        Set(varProductType, "All");
        Set(varPackageSize, "All");
        Set(varCurrentOrder, If(IsBlank(varCurrentOrder), {Items: []}, varCurrentOrder));
        // Refresh product data
        Refresh(colProducts)
    Children:
      # Header Bar
      - HeaderBar:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(44, 62, 80, 1)
            Height: =60
            Width: =1366
            X: =0
            Y: =0
            BorderThickness: =0

      # Logo in Header
      - HeaderLogo:
          Control: Image@2.1.0
          Properties:
            Image: ="eurocaps-logo"
            Height: =40
            Width: =40
            X: =20
            Y: =10

      # App Title in Header
      - HeaderTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="EuroCaps Order Management Pro"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =300
            X: =70
            Y: =10

      # User Menu
      - UserMenu:
          Control: Classic/Button@2.2.0
          Properties:
            Text: =varUserName & " (" & varUserRole & ") ▼"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =200
            X: =1100
            Y: =10

      # Navigation Menu
      - NavigationMenu:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(44, 62, 80, 1)
            Height: =708
            Width: =200
            X: =0
            Y: =60
            BorderThickness: =0

      # Menu Items
      - MenuDashboard:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="≡ Dashboard"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =60
            Align: =Align.Left
            OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)

      - MenuCustomers:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="👥 Customers"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =110
            Align: =Align.Left
            OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)

      - MenuProducts:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📦 Products"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =160
            Align: =Align.Left

      - MenuOrders:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📋 Orders"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =210
            Align: =Align.Left
            OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade)

      # Main Content Area
      - MainContent:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(27, 58, 75, 1)
            Height: =708
            Width: =1166
            X: =200
            Y: =60
            BorderThickness: =0

      # Page Title
      - PageTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="Product Catalog"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =18
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =300
            X: =220
            Y: =80

      # Search and Filter Section
      - SearchFilterSection:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =80
            Width: =1100
            X: =220
            Y: =130
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Product Search Input
      - ProductSearchInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            Default: =varProductSearch
            HintText: ="Search products by name or description..."
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =300
            X: =240
            Y: =145
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varProductSearch, ProductSearchInput.Text)

      # Product Type Filter
      - ProductTypeFilter:
          Control: Classic/Dropdown@2.2.0
          Properties:
            Items: =["All", "espresso", "lungo", "ristretto", "flavored"]
            DefaultSelectedItems: =["All"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =150
            X: =560
            Y: =145
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varProductType, ProductTypeFilter.Selected.Value)

      # Package Size Filter
      - PackageSizeFilter:
          Control: Classic/Dropdown@2.2.0
          Properties:
            Items: =["All", "10", "20", "44"]
            DefaultSelectedItems: =["All"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =100
            X: =730
            Y: =145
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varPackageSize, PackageSizeFilter.Selected.Value)

      # Reset Filters Button
      - ResetFiltersButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="Reset"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =35
            Width: =80
            X: =850
            Y: =145
            OnSelect: |
              =Set(varProductSearch, "");
              Set(varProductType, "All");
              Set(varPackageSize, "All");
              Reset(ProductSearchInput);
              Reset(ProductTypeFilter);
              Reset(PackageSizeFilter)

      # Current Order Summary
      - OrderSummaryButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="🛒 Cart (" & CountRows(varCurrentOrder.Items) & ")"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =35
            Width: =120
            X: =1180
            Y: =145
            OnSelect: =Navigate(Order_Items_Screen, ScreenTransition.Fade)

      # Product Grid Gallery
      - ProductGallery:
          Control: Gallery@2.3.0
          Properties:
            Items: |
              =Filter(
                  colProducts,
                  (IsBlank(varProductSearch) || 
                   varProductSearch in ProductName || 
                   varProductSearch in Description) &&
                  (varProductType = "All" || ProductType = varProductType) &&
                  (varPackageSize = "All" || Text(PackageSize) = varPackageSize)
              )
            Height: =450
            Width: =1100
            X: =220
            Y: =230
            TemplateSize: =250
            TemplatePadding: =10
            WrapCount: =4
            Fill: =RGBA(0, 0, 0, 0)
            BorderThickness: =0
            ShowScrollbar: =true

      # Product Gallery Template Items (inside ProductGallery)
      # Product Card Background
      - ProductCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =230
            Width: =240
            X: =5
            Y: =5
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Product Image (Color-coded by type)
      - ProductImage:
          Control: Rectangle@2.3.0
          Properties:
            Fill: |
              =Switch(
                  ThisItem.ProductType,
                  "espresso", RGBA(93, 64, 55, 1),     // Dark brown
                  "lungo", RGBA(141, 110, 99, 1),      // Medium brown
                  "ristretto", RGBA(33, 33, 33, 1),    // Black
                  "flavored", RGBA(243, 156, 18, 1),   // Orange
                  RGBA(93, 64, 55, 1)                  // Default brown
              )
            Height: =80
            Width: =200
            X: =25
            Y: =15
            RadiusTopLeft: =4
            RadiusTopRight: =4
            RadiusBottomLeft: =4
            RadiusBottomRight: =4

      # Product Type Icon
      - ProductTypeIcon:
          Control: Label@2.5.1
          Properties:
            Text: |
              =Switch(
                  ThisItem.ProductType,
                  "espresso", "☕",
                  "lungo", "🫖",
                  "ristretto", "⚫",
                  "flavored", "🌟",
                  "☕"
              )
            Font: =Font.Arial
            Size: =24
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =40
            X: =125
            Y: =35
            Align: =Align.Center

      # Product Name
      - ProductNameLabel:
          Control: Label@2.5.1
          Properties:
            Text: =ThisItem.ProductName
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =200
            X: =25
            Y: =105
            Align: =Align.Center

      # Product Type
      - ProductTypeLabel:
          Control: Label@2.5.1
          Properties:
            Text: =Upper(ThisItem.ProductType)
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =20
            Width: =200
            X: =25
            Y: =125
            Align: =Align.Center

      # Package Size
      - PackageSizeLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Size: " & ThisItem.PackageSize & " capsules"
            Font: =Font.Arial
            Size: =11
            Color: =RGBA(44, 62, 80, 1)
            Height: =20
            Width: =200
            X: =25
            Y: =145
            Align: =Align.Center

      # Price
      - PriceLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="€" & Text(ThisItem.Price, "0.00")
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(243, 156, 18, 1)
            Height: =25
            Width: =200
            X: =25
            Y: =165
            Align: =Align.Center

      # Add to Order Button
      - AddToOrderButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="+ Add"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =30
            Width: =200
            X: =25
            Y: =195
            OnSelect: |
              =// Add product to current order
              Set(varCurrentOrder,
                  {Items:
                      If(
                          CountRows(Filter(varCurrentOrder.Items, ProductID = ThisItem.ProductID)) > 0,
                          // Product exists, increase quantity
                          ForAll(varCurrentOrder.Items,
                              If(ProductID = ThisItem.ProductID,
                                  {ProductID: ProductID, ProductName: ProductName, Price: Price, Quantity: Quantity + 1},
                                  {ProductID: ProductID, ProductName: ProductName, Price: Price, Quantity: Quantity}
                              )
                          ),
                          // Product doesn't exist, add new
                          Collect(varCurrentOrder.Items,
                              {ProductID: ThisItem.ProductID, ProductName: ThisItem.ProductName, Price: ThisItem.Price, Quantity: 1}
                          )
                      )
                  }
              );
              Notify("Added " & ThisItem.ProductName & " to cart", NotificationType.Success)

      # Pagination Section
      - PaginationSection:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =50
            Width: =1100
            X: =220
            Y: =700
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Pagination Info
      - PaginationInfo:
          Control: Label@2.5.1
          Properties:
            Text: ="Showing " & CountRows(ProductGallery.AllItems) & " products"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =30
            Width: =200
            X: =240
            Y: =710

      # Continue Shopping Button
      - ContinueShoppingButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="Continue to Order"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =30
            Width: =150
            X: =1170
            Y: =710
            DisplayMode: =If(CountRows(varCurrentOrder.Items) > 0, DisplayMode.Edit, DisplayMode.Disabled)
            OnSelect: =Navigate(New_Order_Screen, ScreenTransition.Fade)
