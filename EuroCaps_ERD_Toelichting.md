# EuroCaps Database Ontwerp - Toelichting ERD's

Dit document bevat een toelichting op de Entity Relationship Diagrams (ERD's) die zijn ontwikkeld voor EuroCaps' koffiecapsuleproductieproces. De ERD's zijn opgebouwd in drie niveaus: conceptueel, logisch en fysiek.

## Conceptueel ERD

Het conceptuele ERD toont de belangrijkste entiteiten en hun onderlinge relaties in het productieproces van EuroCaps, zonder technische details. Dit model geeft een overzicht van de bedrijfsprocessen en informatiebehoefte.

### Belangrijkste entiteiten:

- **Leverancier**: Bedrijven die grondstoffen leveren aan EuroCaps
- **Materiaal**: Grondstoffen zoals koffiebonen, capsules, folie, etc.
- **Productiebatch**: Een specifieke productierun van een bepaald product
- **Product**: De verschillende koffiecapsuleproducten die EuroCaps produceert
- **Productiestap**: De verschillende stappen in het productieproces (malen, vullen, verpakken)
- **Kwaliteitscontrole**: Kwaliteitscontroles uitgevoerd tijdens het productieproces
- **Machine**: Apparatuur gebruikt in het productieproces
- **Medewerker**: Personeel betrokken bij het productieproces
- **Klant**: Afnemers van EuroCaps producten
- **Bestelling**: Orders geplaatst door klanten

### Relaties:

Het model toont hoe deze entiteiten met elkaar verbonden zijn, zoals:
- Leveranciers leveren materialen
- Materialen worden gebruikt in productiebatches
- Productiebatches bestaan uit productiestappen
- Productiestappen ondergaan kwaliteitscontroles
- Etc.

## Logisch ERD

Het logische ERD bouwt voort op het conceptuele model door attributen toe te voegen aan elke entiteit en de relaties te specificeren met cardinaliteit (1:n, n:m). Dit model is genormaliseerd volgens de principes van databaseontwerp.

### Toegevoegde elementen:

- **Attributen**: Elk entiteit heeft specifieke attributen (bijv. LeverancierID, LeverancierNaam voor Leverancier)
- **Primaire sleutels**: Identificatie van unieke records (bijv. LeverancierID)
- **Vreemde sleutels**: Verwijzingen naar andere entiteiten (bijv. LeverancierID in Materiaal)
- **Cardinaliteit**: Specificatie van relaties (1:n, n:m)
- **Koppeltabellen**: Voor n:m relaties (bijv. MateriaalGebruik tussen Materiaal en Productiebatch)

## Fysiek ERD

Het fysieke ERD vertaalt het logische model naar een implementeerbare databasestructuur met specifieke datatypen, constraints en andere technische details.

### Technische specificaties:

- **Tabelnamen**: Engelse namen volgens databaseconventies (bijv. Suppliers, Materials)
- **Datatypen**: Specifieke SQL-datatypen (INT, VARCHAR, ENUM, etc.)
- **Constraints**: NOT NULL, UNIQUE, DEFAULT waarden
- **Vreemde sleutel definities**: FOREIGN KEY constraints
- **Timestamps**: CreatedAt en UpdatedAt velden voor audit-doeleinden

### Ontwerpkeuzes en constraints:

1. **Primaire sleutels**: Alle tabellen gebruiken een auto-increment integer als primaire sleutel voor eenvoudige referentie en indexering.

2. **Vreemde sleutels**: Relaties worden geïmplementeerd met vreemde sleutels die verwijzen naar de primaire sleutels van gerelateerde tabellen.

3. **ENUM types**: Voor velden met een beperkte set waarden (zoals MaterialType, ProductType, StepType) worden ENUM types gebruikt om data-integriteit te waarborgen.

4. **NOT NULL constraints**: Essentiële velden zijn gemarkeerd als NOT NULL om te garanderen dat kritieke informatie altijd wordt vastgelegd.

5. **DEFAULT waarden**: Waar relevant zijn standaardwaarden ingesteld (bijv. CurrentStock DEFAULT 0).

6. **Timestamps**: Alle tabellen bevatten CreatedAt en UpdatedAt velden voor audit-doeleinden.

7. **Unieke constraints**: Velden zoals BatchNumber en OrderNumber hebben UNIQUE constraints om duplicaten te voorkomen.

8. **Decimale precisie**: Voor numerieke waarden die precisie vereisen (zoals gewichten en hoeveelheden) wordt DECIMAL(10,2) gebruikt.

9. **Indexen**: Naast primaire sleutels worden indexen toegevoegd op veelgebruikte zoekvelden en vreemde sleutels voor optimale prestaties.

## Ondersteuning van bedrijfsprocessen

Deze database is specifiek ontworpen om de drie hoofdprocessen van EuroCaps te ondersteunen:

### 1. Maalproces
- Tracking van koffiebonen (grondstoffen)
- Registratie van maalparameters
- Kwaliteitscontroles (temperatuur, maalgraad)

### 2. Vulproces
- Registratie van vulgewichten (4.0-6.0g per capsule)
- Tracking van capsule- en folieverbruik
- Kwaliteitscontroles (gewicht, vulniveau)

### 3. Verpakkingsproces
- Registratie van verpakkingsformaten (10, 20, 44 capsules)
- Tracking van verpakkingsmaterialen
- Kwaliteitscontroles (eindproduct)

### Kwaliteitscontrole
De database ondersteunt het OK/NOK kwaliteitsclassificatiesysteem door:
- Registratie van kwaliteitscontroles in elke productiefase
- Tracking van afgekeurde producten
- Vastleggen van meetwaarden en tolerantiegrenzen

### Supply Chain Management
De database faciliteert supply chain management door:
- Voorraadregistratie van grondstoffen
- Leveranciersinformatie
- Orderverwerking en klantgegevens

## Conclusie

Deze database biedt EuroCaps een robuuste basis voor het vastleggen en analyseren van alle aspecten van hun productieproces. Het ontwerp is geoptimaliseerd voor zowel operationele efficiëntie als strategische besluitvorming, met bijzondere aandacht voor kwaliteitscontrole en traceerbaarheid.
