<!DOCTYPE html>
<html>
<head>
    <title>Fysiek ERD Voorbeeld - Met FK</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: white;
        }
        .container {
            width: 1200px;
            height: 1000px;
            border: 1px solid #ccc;
            position: relative;
            background-color: white;
        }
        .title {
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            margin-top: 20px;
            margin-bottom: 40px;
        }
        .table {
            position: absolute;
            border: 1px solid black;
            background-color: white;
            z-index: 2;
        }
        .table-header {
            text-align: center;
            font-weight: bold;
            padding: 8px;
            border-bottom: 1px solid black;
            background-color: #f0f0f0;
        }
        .table-content {
            display: table;
            width: 100%;
        }
        .table-row {
            display: table-row;
        }
        .pk-column {
            display: table-cell;
            width: 40px;
            border-right: 1px solid black;
            text-align: center;
            padding: 5px;
            font-weight: bold;
        }
        .fk-column {
            display: table-cell;
            width: 40px;
            border-right: 1px solid black;
            text-align: center;
            padding: 5px;
            font-style: italic;
        }
        .datatype-column {
            display: table-cell;
            width: 100px;
            border-right: 1px solid black;
            padding: 5px;
            font-family: monospace;
            text-align: center;
        }
        .name-column {
            display: table-cell;
            padding: 5px;
            min-width: 120px;
        }
        .pk {
            text-decoration: underline;
            font-weight: bold;
        }
        .fk {
            font-style: italic;
        }
        .relationship {
            position: absolute;
            font-size: 14px;
            z-index: 3;
            background-color: white;
            padding: 0 5px;
        }
        .note {
            position: absolute;
            bottom: 20px;
            left: 20px;
            font-size: 14px;
            width: 960px;
        }
        .legend {
            position: absolute;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #f9f9f9;
            width: 300px;
            z-index: 2;
        }
        .legend-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .legend-item {
            margin: 5px 0;
            font-size: 12px;
        }
        /* SVG styles for better lines */
        svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">Fysiek ERD Voorbeeld - Webwinkel</div>
        
        <!-- SVG for lines and relationship symbols -->
        <svg>
            <!-- KLANT to BESTELLING -->
            <path d="M 350,180 H 650" stroke="black" stroke-width="1" fill="none"/>
            
            <!-- BESTELLING to BESTELLING_PRODUCT -->
            <path d="M 750,250 H 600 V 400" stroke="black" stroke-width="1" fill="none"/>
            
            <!-- PRODUCT to BESTELLING_PRODUCT -->
            <path d="M 750,600 H 600 V 450" stroke="black" stroke-width="1" fill="none"/>
            
            <!-- CATEGORIE to PRODUCT -->
            <path d="M 350,600 H 650" stroke="black" stroke-width="1" fill="none"/>
            
            <!-- Relationship symbols -->
            <!-- KLANT to BESTELLING (1 to many) -->
            <line x1="340" y1="180" x2="360" y2="180" stroke="black" stroke-width="1.5"/>
            <line x1="340" y1="170" x2="340" y2="190" stroke="black" stroke-width="1.5"/>
            <line x1="640" y1="170" x2="640" y2="190" stroke="black" stroke-width="1.5"/>
            <path d="M 640,180 L 660,180" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 650,175 L 660,180 L 650,185" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- BESTELLING to BESTELLING_PRODUCT (1 to many) -->
            <line x1="750" y1="240" x2="750" y2="260" stroke="black" stroke-width="1.5"/>
            <line x1="740" y1="240" x2="760" y2="240" stroke="black" stroke-width="1.5"/>
            <line x1="590" y1="400" x2="610" y2="400" stroke="black" stroke-width="1.5"/>
            <path d="M 600,390 L 600,410" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 595,400 L 605,410" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- PRODUCT to BESTELLING_PRODUCT (1 to many) -->
            <line x1="750" y1="590" x2="750" y2="610" stroke="black" stroke-width="1.5"/>
            <line x1="740" y1="590" x2="760" y2="590" stroke="black" stroke-width="1.5"/>
            <line x1="590" y1="450" x2="610" y2="450" stroke="black" stroke-width="1.5"/>
            <path d="M 600,440 L 600,460" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 595,450 L 605,440" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- CATEGORIE to PRODUCT (1 to many) -->
            <line x1="340" y1="600" x2="360" y2="600" stroke="black" stroke-width="1.5"/>
            <line x1="340" y1="590" x2="340" y2="610" stroke="black" stroke-width="1.5"/>
            <line x1="640" y1="590" x2="640" y2="610" stroke="black" stroke-width="1.5"/>
            <path d="M 640,600 L 660,600" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 650,595 L 660,600 L 650,605" stroke="black" stroke-width="1.5" fill="none"/>
        </svg>
        
        <!-- Entities with correct physical table structure -->
        <div class="table" style="top: 100px; left: 100px; width: 250px;">
            <div class="table-header">KLANT</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="pk-column">PK</div>
                    <div class="datatype-column">INT</div>
                    <div class="name-column pk">klant_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="datatype-column">VARCHAR(50)</div>
                    <div class="name-column">voornaam</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="datatype-column">VARCHAR(50)</div>
                    <div class="name-column">achternaam</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="datatype-column">VARCHAR(100)</div>
                    <div class="name-column">email</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="datatype-column">VARCHAR(15)</div>
                    <div class="name-column">telefoonnummer</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="datatype-column">VARCHAR(100)</div>
                    <div class="name-column">adres</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="datatype-column">VARCHAR(10)</div>
                    <div class="name-column">postcode</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="datatype-column">VARCHAR(50)</div>
                    <div class="name-column">woonplaats</div>
                </div>
            </div>
        </div>
        
        <div class="table" style="top: 100px; left: 650px; width: 300px;">
            <div class="table-header">BESTELLING</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="pk-column">PK</div>
                    <div class="datatype-column">INT</div>
                    <div class="name-column pk">bestelling_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column">FK</div>
                    <div class="datatype-column">INT</div>
                    <div class="name-column fk">klant_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">DATETIME</div>
                    <div class="name-column">datum</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">ENUM</div>
                    <div class="name-column">status</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">DECIMAL(10,2)</div>
                    <div class="name-column">totaalbedrag</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">DECIMAL(6,2)</div>
                    <div class="name-column">verzendkosten</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">VARCHAR(20)</div>
                    <div class="name-column">betaalmethode</div>
                </div>
            </div>
        </div>
        
        <div class="table" style="top: 520px; left: 100px; width: 250px;">
            <div class="table-header">CATEGORIE</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="pk-column">PK</div>
                    <div class="datatype-column">INT</div>
                    <div class="name-column pk">categorie_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="datatype-column">VARCHAR(50)</div>
                    <div class="name-column">naam</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="datatype-column">TEXT</div>
                    <div class="name-column">beschrijving</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column">FK</div>
                    <div class="datatype-column">INT</div>
                    <div class="name-column fk">hoofdcategorie_id</div>
                </div>
            </div>
        </div>
        
        <div class="table" style="top: 520px; left: 650px; width: 300px;">
            <div class="table-header">PRODUCT</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="pk-column">PK</div>
                    <div class="datatype-column">INT</div>
                    <div class="name-column pk">product_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column">FK</div>
                    <div class="datatype-column">INT</div>
                    <div class="name-column fk">categorie_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">VARCHAR(100)</div>
                    <div class="name-column">naam</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">TEXT</div>
                    <div class="name-column">beschrijving</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">DECIMAL(10,2)</div>
                    <div class="name-column">prijs</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">INT</div>
                    <div class="name-column">voorraad</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">DECIMAL(6,2)</div>
                    <div class="name-column">gewicht</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">VARCHAR(50)</div>
                    <div class="name-column">afmetingen</div>
                </div>
            </div>
        </div>
        
        <div class="table" style="top: 350px; left: 350px; width: 300px;">
            <div class="table-header">BESTELLING_PRODUCT</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="pk-column">PK</div>
                    <div class="fk-column">FK</div>
                    <div class="datatype-column">INT</div>
                    <div class="name-column pk fk">bestelling_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column">PK</div>
                    <div class="fk-column">FK</div>
                    <div class="datatype-column">INT</div>
                    <div class="name-column pk fk">product_id</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">INT</div>
                    <div class="name-column">aantal</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">DECIMAL(10,2)</div>
                    <div class="name-column">prijs_per_stuk</div>
                </div>
                <div class="table-row">
                    <div class="pk-column"></div>
                    <div class="fk-column"></div>
                    <div class="datatype-column">DECIMAL(10,2)</div>
                    <div class="name-column">subtotaal</div>
                </div>
            </div>
        </div>
        
        <!-- Relationships -->
        <div class="relationship" style="top: 160px; left: 450px;">plaatst</div>
        <div class="relationship" style="top: 300px; left: 760px;">bevat</div>
        <div class="relationship" style="top: 480px; left: 610px;">bevat</div>
        <div class="relationship" style="top: 580px; left: 450px;">behoort tot</div>
        
        <!-- Legend -->
        <div class="legend" style="top: 800px; left: 350px;">
            <div class="legend-title">Fysiek ERD Legenda</div>
            <div class="legend-item"><strong>PK</strong> - Primaire sleutel</div>
            <div class="legend-item"><strong>FK</strong> - Vreemde sleutel (Foreign Key)</div>
            <div class="legend-item"><strong>INT</strong> - Integer (geheel getal)</div>
            <div class="legend-item"><strong>VARCHAR</strong> - Variabele tekst met maximale lengte</div>
            <div class="legend-item"><strong>DECIMAL</strong> - Decimaal getal met precisie</div>
            <div class="legend-item"><strong>DATETIME</strong> - Datum en tijd</div>
            <div class="legend-item"><strong>TEXT</strong> - Lange tekst zonder lengtebeperkingen</div>
            <div class="legend-item"><strong>ENUM</strong> - Voorgedefinieerde waarden</div>
        </div>
        
        <!-- Note -->
        <div class="note">
            <strong>Opmerking:</strong> Een fysiek ERD bouwt voort op het logische ERD door technische details toe te voegen zoals datatypes, 
            lengtebeperkingen, indexen en andere implementatiespecifieke elementen. Het fysieke ERD is direct gerelateerd aan de daadwerkelijke 
            implementatie in een specifiek databasemanagementsysteem (zoals MySQL, PostgreSQL, SQL Server, etc.).
        </div>
    </div>
    
    <div style="margin-top: 20px;">
        <h2>Verschillen tussen Logisch en Fysiek ERD</h2>
        <ul>
            <li><strong>Datatypes:</strong> Het fysieke ERD specificeert exacte datatypes (VARCHAR, INT, DECIMAL, etc.) en lengtebeperkingen.</li>
            <li><strong>Constraints:</strong> Het fysieke ERD bevat constraints zoals NOT NULL, UNIQUE, DEFAULT waarden, etc.</li>
            <li><strong>Vreemde sleutels:</strong> Het fysieke ERD toont expliciet de vreemde sleutels (FK) en hun relaties.</li>
            <li><strong>Indexen:</strong> Het fysieke ERD kan indexen bevatten voor performantie-optimalisatie.</li>
            <li><strong>Implementatiedetails:</strong> Het fysieke ERD bevat specifieke details voor het gekozen databasemanagementsysteem.</li>
        </ul>
    </div>
</body>
</html>
