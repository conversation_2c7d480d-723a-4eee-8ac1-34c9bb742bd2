<!DOCTYPE html>
<html>
<head>
    <title>Normalisatie: 2NF</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .diagram {
            width: 800px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: white;
            padding: 20px;
        }
        .table {
            border-collapse: collapse;
            margin: 20px 0;
            width: 100%;
        }
        .table th, .table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #333;
            color: white;
        }
        .pk {
            font-weight: bold;
            text-decoration: underline;
        }
        .fk {
            font-style: italic;
            color: #555;
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .step {
            margin: 30px 0;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .step-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="title">Normalisatie: 2NF (Tweede Normaalvorm)</div>
    <div class="subtitle">Geen partiële afhankelijkheden</div>
    
    <div class="diagram">
        <div class="step">
            <div class="step-title">2NF: Tweede Normaalvorm</div>
            <p>Geen partiële afhankelijkheden (attributen die afhankelijk zijn van slechts een deel van de primaire sleutel).</p>
            
            <div style="display: flex; justify-content: space-between;">
                <div style="width: 48%;">
                    <p><strong>Order tabel:</strong></p>
                    <table class="table">
                        <tr>
                            <th class="pk">OrderID</th>
                            <th>KlantID</th>
                            <th>KlantNaam</th>
                            <th>KlantAdres</th>
                        </tr>
                        <tr>
                            <td>1001</td>
                            <td>K100</td>
                            <td>Jan Jansen</td>
                            <td>Hoofdstraat 1, Amsterdam</td>
                        </tr>
                        <tr>
                            <td>1002</td>
                            <td>K101</td>
                            <td>Piet Pietersen</td>
                            <td>Kerkstraat 10, Rotterdam</td>
                        </tr>
                    </table>
                </div>
                
                <div style="width: 48%;">
                    <p><strong>OrderDetail tabel:</strong></p>
                    <table class="table">
                        <tr>
                            <th class="pk fk">OrderID</th>
                            <th class="pk">ProductID</th>
                            <th>ProductNaam</th>
                            <th>Prijs</th>
                            <th>Aantal</th>
                        </tr>
                        <tr>
                            <td>1001</td>
                            <td>P200</td>
                            <td>Laptop</td>
                            <td>899.99</td>
                            <td>1</td>
                        </tr>
                        <tr>
                            <td>1001</td>
                            <td>P201</td>
                            <td>Muis</td>
                            <td>19.99</td>
                            <td>2</td>
                        </tr>
                        <tr>
                            <td>1002</td>
                            <td>P200</td>
                            <td>Laptop</td>
                            <td>899.99</td>
                            <td>1</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <p>Veranderingen:</p>
            <ul>
                <li>Tabel gesplitst om partiële afhankelijkheden te verwijderen</li>
                <li>KlantID, KlantNaam en KlantAdres zijn alleen afhankelijk van OrderID, niet van ProductID</li>
                <li>Deze attributen zijn daarom verplaatst naar een aparte Order tabel</li>
            </ul>
            
            <p>Regels voor 2NF volgens de cursus:</p>
            <ul>
                <li>Volg de regels voor de eerste normaal vorm</li>
                <li>Zoek naar de attributen die geen deel uitmaken van de identifier en die afhankelijk zijn van slechts een deel van de identifier</li>
                <li>Maak nieuwe entiteiten met gevonden attributen samen met dat deel van de sleutel waarvan ze afhankelijk zijn</li>
            </ul>
            
            <p>Probleem dat nog overblijft:</p>
            <ul>
                <li>Er zijn nog steeds transitieve afhankelijkheden in beide tabellen</li>
                <li>KlantNaam en KlantAdres zijn afhankelijk van KlantID, niet direct van OrderID</li>
                <li>ProductNaam en Prijs zijn afhankelijk van ProductID</li>
            </ul>
        </div>
    </div>
</body>
</html>
