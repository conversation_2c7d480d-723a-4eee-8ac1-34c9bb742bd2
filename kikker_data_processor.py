import pandas as pd
import numpy as np

def load_kikker_data():
    """
    Load the Kikker.csv dataset and return the raw dataframe.
    """
    print("Loading Kikker.csv dataset...")
    try:
        df = pd.read_csv('Kikker.csv', sep=',')
        print(f"Successfully loaded dataset with {df.shape[0]} rows and {df.shape[1]} columns.")
        return df
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        return None

def clean_kikker_data(df):
    """
    Clean the Kikker dataset by handling missing values, invalid formats, and other data quality issues.
    """
    if df is None:
        return None

    print("\nCleaning dataset...")
    df_clean = df.copy()

    # 1. Handle missing values
    print("Handling missing values...")

    # For categorical columns, replace NaN with 'Unknown'
    categorical_cols = df_clean.select_dtypes(include=['object']).columns
    for col in categorical_cols:
        df_clean[col] = df_clean[col].fillna('Unknown')

    # For numeric columns, use appropriate strategies
    # Leveranciersbeoordeling (Supplier rating) - replace with median
    if 'Leveranciersbeoordeling' in df_clean.columns:
        median_rating = df_clean['Leveranciersbeoordeling'].median()
        df_clean['Leveranciersbeoordeling'] = df_clean['Leveranciersbeoordeling'].fillna(median_rating)

    # 2. Clean date columns
    print("Cleaning date columns...")
    date_columns = [col for col in df_clean.columns if 'Datum' in col or 'datum' in col]

    for col in date_columns:
        # Convert to datetime, coercing invalid values to NaT
        df_clean[col + '_clean'] = pd.to_datetime(df_clean[col], errors='coerce')

        # Flag invalid dates
        df_clean[col + '_is_valid'] = ~pd.isna(df_clean[col + '_clean'])

        # Keep original column for reference
        df_clean[col + '_original'] = df_clean[col]

    # 3. Clean percentage columns
    print("Cleaning percentage columns...")
    percentage_cols = [col for col in df_clean.columns if 'percentage' in col.lower()]

    for col in percentage_cols:
        # Create a new column for cleaned percentages
        df_clean[col + '_numeric'] = df_clean[col].apply(lambda x:
            float(str(x).replace('%', '')) / 100 if isinstance(x, str) and '%' in str(x)
            else np.nan)

    # 4. Clean numeric columns with units
    print("Cleaning numeric columns with units...")

    # Cost column (remove 'euros' and convert to float)
    if 'Cost' in df_clean.columns:
        def extract_cost_value(x):
            try:
                if pd.isna(x):
                    return np.nan
                if isinstance(x, (int, float)):
                    return float(x)
                if isinstance(x, str):
                    if 'euros' in x:
                        # Extract the numeric part before 'euros'
                        numeric_part = x.split('euros')[0].strip()
                        # Remove any non-numeric characters except decimal point
                        numeric_part = ''.join(c for c in numeric_part if c.isdigit() or c == '.')
                        if numeric_part:
                            return float(numeric_part)
                return np.nan
            except:
                return np.nan

        df_clean['Cost_numeric'] = df_clean['Cost'].apply(extract_cost_value)

    # Voorraadniveaus (Inventory levels) - extract numeric values
    if 'Voorraadniveaus' in df_clean.columns:
        def extract_inventory_value(x):
            try:
                if pd.isna(x) or x in ['ERROR', '###']:
                    return np.nan
                if isinstance(x, (int, float)):
                    return float(x)
                if isinstance(x, str):
                    if 'units' in x:
                        # Extract the numeric part before 'units'
                        numeric_part = x.split('units')[0].strip()
                        # Remove any non-numeric characters except decimal point
                        numeric_part = ''.join(c for c in numeric_part if c.isdigit() or c == '.')
                        if numeric_part:
                            return float(numeric_part)
                return np.nan
            except:
                return np.nan

        df_clean['Voorraadniveaus_numeric'] = df_clean['Voorraadniveaus'].apply(extract_inventory_value)

    # Energieverbruik (Energy consumption) - extract numeric values
    if 'Energieverbruik' in df_clean.columns:
        def extract_energy_value(x):
            try:
                if pd.isna(x):
                    return np.nan
                if isinstance(x, (int, float)):
                    return float(x)
                if isinstance(x, str):
                    if 'kWh' in x:
                        # Extract the numeric part before 'kWh'
                        numeric_part = x.split('kWh')[0].strip()
                        # Remove any non-numeric characters except decimal point
                        numeric_part = ''.join(c for c in numeric_part if c.isdigit() or c == '.')
                        if numeric_part:
                            return float(numeric_part)
                return np.nan
            except:
                return np.nan

        df_clean['Energieverbruik_numeric'] = df_clean['Energieverbruik'].apply(extract_energy_value)

    # CO2-Footprint - extract numeric values
    if 'CO2-Footprint' in df_clean.columns:
        def extract_co2_value(x):
            try:
                if pd.isna(x):
                    return np.nan
                if isinstance(x, (int, float)):
                    return float(x)
                if isinstance(x, str):
                    if 'kg CO2/kg' in x:
                        # Extract the numeric part before 'kg CO2/kg'
                        numeric_part = x.split('kg')[0].strip()
                        # Remove any non-numeric characters except decimal point
                        numeric_part = ''.join(c for c in numeric_part if c.isdigit() or c == '.')
                        if numeric_part:
                            return float(numeric_part)
                return np.nan
            except:
                return np.nan

        df_clean['CO2_Footprint_numeric'] = df_clean['CO2-Footprint'].apply(extract_co2_value)

    # 5. Clean cycle time
    if 'Cyclustijd' in df_clean.columns:
        def extract_cycle_time(x):
            try:
                if pd.isna(x) or x == 'abc uur':
                    return np.nan
                if isinstance(x, (int, float)):
                    return float(x)
                if isinstance(x, str):
                    if 'uur' in x:
                        # Extract the numeric part before 'uur'
                        numeric_part = x.split('uur')[0].strip()
                        # Remove any non-numeric characters except decimal point
                        numeric_part = ''.join(c for c in numeric_part if c.isdigit() or c == '.')
                        if numeric_part:
                            return float(numeric_part)
                return np.nan
            except:
                return np.nan

        df_clean['Cyclustijd_numeric'] = df_clean['Cyclustijd'].apply(extract_cycle_time)

    # 6. Clean weight control
    if 'Gewichtscontrole' in df_clean.columns:
        def extract_weight_value(x):
            try:
                if pd.isna(x):
                    return np.nan
                if isinstance(x, (int, float)):
                    return float(x)
                if isinstance(x, str):
                    if 'kg' in x:
                        # Extract the numeric part before 'kg'
                        numeric_part = x.split('kg')[0].strip()
                        # Remove any non-numeric characters except decimal point
                        numeric_part = ''.join(c for c in numeric_part if c.isdigit() or c == '.')
                        if numeric_part:
                            return float(numeric_part)
                return np.nan
            except:
                return np.nan

        df_clean['Gewichtscontrole_numeric'] = df_clean['Gewichtscontrole'].apply(extract_weight_value)

    # 7. Handle negative values in scores
    if 'Fair-Trade Score' in df_clean.columns:
        # Replace negative values with 0 and cap at 100
        df_clean['Fair-Trade Score_cleaned'] = df_clean['Fair-Trade Score'].apply(
            lambda x: max(0, min(100, x)) if not pd.isna(x) else np.nan)

    if 'Leveranciersbeoordeling' in df_clean.columns:
        # Replace negative values with 0 and cap at 10 (assuming 0-10 scale)
        df_clean['Leveranciersbeoordeling_cleaned'] = df_clean['Leveranciersbeoordeling'].apply(
            lambda x: max(0, min(10, x)) if not pd.isna(x) else np.nan)

    print("Data cleaning completed.")
    return df_clean

def explore_kikker_data(df_clean):
    """
    Explore the cleaned Kikker dataset and print insights.
    """
    if df_clean is None:
        return

    print("\nExploring cleaned dataset...")

    # 1. Basic information
    print(f"\n1. Dataset dimensions: {df_clean.shape[0]} rows x {df_clean.shape[1]} columns")

    # 2. Production process analysis
    print("\n2. Production process analysis:")

    # Machine usage
    print("\nMachine usage:")
    for machine_col in ['PackagingApparaat', 'FillingApparaat', 'GrindingApparaat']:
        if machine_col in df_clean.columns:
            valid_values = df_clean[df_clean[machine_col] != 'Unknown'][machine_col]
            print(f"- {machine_col}: {valid_values.nunique()} unique machines")
            top_machines = valid_values.value_counts().head(3)
            print(f"  Top 3 most used: {', '.join([f'{m} ({c} times)' for m, c in top_machines.items()])}")

    # Coffee bean types
    if 'Koffieboon' in df_clean.columns:
        print("\nCoffee bean types:")
        bean_counts = df_clean['Koffieboon'].value_counts()
        for bean, count in bean_counts.items():
            print(f"- {bean}: {count} records ({count/len(df_clean)*100:.1f}%)")

    # 3. Quality indicators
    print("\n3. Quality indicators:")

    # Defect percentage
    if 'Defectpercentage_numeric' in df_clean.columns:
        defect_data = df_clean['Defectpercentage_numeric'].dropna()
        print(f"\nDefect percentage:")
        print(f"- Average: {defect_data.mean():.2%}")
        print(f"- Median: {defect_data.median():.2%}")
        print(f"- Min: {defect_data.min():.2%}")
        print(f"- Max: {defect_data.max():.2%}")

    # Customer return percentage
    if 'Klantretourpercentage_numeric' in df_clean.columns:
        return_data = df_clean['Klantretourpercentage_numeric'].dropna()
        print(f"\nCustomer return percentage:")
        print(f"- Average: {return_data.mean():.2%}")
        print(f"- Median: {return_data.median():.2%}")
        print(f"- Min: {return_data.min():.2%}")
        print(f"- Max: {return_data.max():.2%}")

    # Panel test results
    if 'Panel Test' in df_clean.columns:
        print("\nPanel test results:")
        panel_counts = df_clean['Panel Test'].value_counts()
        for result, count in panel_counts.items():
            print(f"- {result}: {count} records ({count/len(df_clean)*100:.1f}%)")

    # 4. Cost and efficiency analysis
    print("\n4. Cost and efficiency analysis:")

    # Production cost
    if 'Cost_numeric' in df_clean.columns:
        cost_data = df_clean['Cost_numeric'].dropna()
        print(f"\nProduction cost (euros):")
        print(f"- Average: {cost_data.mean():.2f}")
        print(f"- Median: {cost_data.median():.2f}")
        print(f"- Min: {cost_data.min():.2f}")
        print(f"- Max: {cost_data.max():.2f}")

    # Cycle time
    if 'Cyclustijd_numeric' in df_clean.columns:
        cycle_data = df_clean['Cyclustijd_numeric'].dropna()
        print(f"\nCycle time (hours):")
        print(f"- Average: {cycle_data.mean():.2f}")
        print(f"- Median: {cycle_data.median():.2f}")
        print(f"- Min: {cycle_data.min():.2f}")
        print(f"- Max: {cycle_data.max():.2f}")

    # Energy consumption
    if 'Energieverbruik_numeric' in df_clean.columns:
        # Filter out extreme values (e.g., 999999)
        energy_data = df_clean[df_clean['Energieverbruik_numeric'] < 10000]['Energieverbruik_numeric'].dropna()
        print(f"\nEnergy consumption (kWh):")
        print(f"- Average: {energy_data.mean():.2f}")
        print(f"- Median: {energy_data.median():.2f}")
        print(f"- Min: {energy_data.min():.2f}")
        print(f"- Max: {energy_data.max():.2f}")

    # 5. Sustainability metrics
    print("\n5. Sustainability metrics:")

    # CO2 footprint
    if 'CO2_Footprint_numeric' in df_clean.columns:
        co2_data = df_clean['CO2_Footprint_numeric'].dropna()
        print(f"\nCO2 footprint (kg CO2/kg):")
        print(f"- Average: {co2_data.mean():.2f}")
        print(f"- Median: {co2_data.median():.2f}")
        print(f"- Min: {co2_data.min():.2f}")
        print(f"- Max: {co2_data.max():.2f}")

    # Fair trade score
    if 'Fair-Trade Score_cleaned' in df_clean.columns:
        fair_trade_data = df_clean['Fair-Trade Score_cleaned'].dropna()
        print(f"\nFair trade score (0-100):")
        print(f"- Average: {fair_trade_data.mean():.2f}")
        print(f"- Median: {fair_trade_data.median():.2f}")
        print(f"- Min: {fair_trade_data.min():.2f}")
        print(f"- Max: {fair_trade_data.max():.2f}")

    print("\nData exploration completed.")
    return

def save_cleaned_data(df_clean):
    """
    Save the cleaned dataset to a new CSV file.
    """
    if df_clean is None:
        return

    try:
        output_file = 'Kikker_cleaned_new.csv'
        df_clean.to_csv(output_file, index=False)
        print(f"\nCleaned dataset saved to {output_file}")
    except Exception as e:
        print(f"Error saving cleaned dataset: {e}")

def main():
    """
    Main function to process the Kikker.csv dataset.
    """
    print("=" * 80)
    print("AMERICAPS DATA PROCESSOR - KIKKER.CSV")
    print("=" * 80)

    # Load the data
    df = load_kikker_data()
    if df is None:
        print("Failed to load dataset. Exiting.")
        return

    # Clean the data
    df_clean = clean_kikker_data(df)
    if df_clean is None:
        print("Failed to clean dataset. Exiting.")
        return

    # Explore the cleaned data
    explore_kikker_data(df_clean)

    # Save the cleaned data
    save_cleaned_data(df_clean)

    print("\nData processing completed successfully.")

if __name__ == "__main__":
    main()
