#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data Cleaning Script for Kikker.csv

This script processes the raw Kikker.csv data, cleans it according to identified issues,
and generates a new CSV file with the cleaned data.

Cleaning steps include:
1. Handling missing values
2. Fixing invalid date formats
3. Correcting unrealistic values
4. Standardizing inconsistent data
5. Removing duplicates
6. Fixing encoding issues
"""

import pandas as pd
import numpy as np
import re
from datetime import datetime
import os

def clean_kikker_data(input_file='Kikker.csv', output_file='Kikker_cleaned.csv'):
    """
    Main function to clean the Kikker.csv data
    
    Parameters:
    -----------
    input_file : str
        Path to the input CSV file
    output_file : str
        Path to save the cleaned CSV file
    
    Returns:
    --------
    pd.DataFrame
        The cleaned dataframe (also saved to output_file)
    """
    print(f"Loading data from {input_file}...")
    df = pd.read_csv(input_file)
    
    print(f"Original data shape: {df.shape}")
    
    # Make a copy to preserve original data
    df_cleaned = df.copy()
    
    # Step 1: Clean percentage columns
    df_cleaned = clean_percentage_columns(df_cleaned)
    
    # Step 2: Clean date columns
    df_cleaned = clean_date_columns(df_cleaned)
    
    # Step 3: Clean numeric columns with errors
    df_cleaned = clean_numeric_columns(df_cleaned)
    
    # Step 4: Clean categorical columns
    df_cleaned = clean_categorical_columns(df_cleaned)
    
    # Step 5: Handle missing values
    df_cleaned = handle_missing_values(df_cleaned)
    
    # Step 6: Remove duplicates
    df_cleaned = remove_duplicates(df_cleaned)
    
    # Step 7: Fix encoding issues
    df_cleaned = fix_encoding_issues(df_cleaned)
    
    # Save the cleaned data
    print(f"Saving cleaned data to {output_file}...")
    df_cleaned.to_csv(output_file, index=False)
    
    print(f"Cleaned data shape: {df_cleaned.shape}")
    print(f"Data cleaning complete. Cleaned data saved to {output_file}")
    
    return df_cleaned

def clean_percentage_columns(df):
    """Clean percentage columns by extracting numeric values"""
    print("Cleaning percentage columns...")
    percentage_columns = ['Klantretourpercentage', 'Benuttingsgraad', 'Defectpercentage']
    
    for col in percentage_columns:
        if col in df.columns and df[col].dtype == 'object':
            # Extract numeric values from percentage strings and convert to float
            df[col] = df[col].astype(str).str.extract(r'(\d+\.\d+|\d+)').astype(float) / 100
            print(f"  - Cleaned {col}")
    
    return df

def clean_date_columns(df):
    """Clean date columns by fixing invalid formats"""
    print("Cleaning date columns...")
    date_columns = [
        'Registratiedatum', 
        'FillingDatumTijdEind', 
        'PackagingDatumTijdEind', 
        'FillingDatumTijdStart', 
        'GrindingDatumTijdEind', 
        'GrindingDatumTijdStart',
        'PackagingDatumTijdStart', 
        'Laatste Audit'
    ]
    
    for col in date_columns:
        if col in df.columns:
            # Replace invalid dates with NaT
            invalid_mask = df[col].astype(str).str.contains('31-02-2025|25:61:61|0000-00-00|onbekend', na=False)
            invalid_count = invalid_mask.sum()
            
            if invalid_count > 0:
                print(f"  - Found {invalid_count} invalid dates in {col}")
                df.loc[invalid_mask, col] = pd.NaT
            
            # Try to convert to datetime
            try:
                # For simple date columns (no time)
                if col in ['Registratiedatum', 'Laatste Audit']:
                    df[col] = pd.to_datetime(df[col], errors='coerce')
                else:
                    # For datetime columns
                    df[col] = pd.to_datetime(df[col], errors='coerce')
            except Exception as e:
                print(f"  - Error converting {col} to datetime: {e}")
    
    return df

def clean_numeric_columns(df):
    """Clean numeric columns by handling errors and unrealistic values"""
    print("Cleaning numeric columns...")
    
    # Cost column
    if 'Cost' in df.columns:
        # Extract numeric values from Cost column
        df['Cost'] = df['Cost'].astype(str).str.extract(r'(-?\d+\.\d+|-?\d+)').astype(float)
        # Replace negative or unrealistic values with NaN
        df.loc[df['Cost'] < 0, 'Cost'] = np.nan
        print(f"  - Cleaned Cost column")
    
    # Voorraadniveaus column
    if 'Voorraadniveaus' in df.columns:
        # Extract numeric values
        df['Voorraadniveaus'] = df['Voorraadniveaus'].astype(str).str.extract(r'(\d+)').astype(float)
        print(f"  - Cleaned Voorraadniveaus column")
    
    # Cyclustijd column
    if 'Cyclustijd' in df.columns:
        # Extract numeric values and handle 'uur' suffix
        df['Cyclustijd'] = df['Cyclustijd'].astype(str).str.extract(r'(-?\d+\.\d+|-?\d+)').astype(float)
        # Replace negative values with NaN
        df.loc[df['Cyclustijd'] < 0, 'Cyclustijd'] = np.nan
        print(f"  - Cleaned Cyclustijd column")
    
    # Energieverbruik column
    if 'Energieverbruik' in df.columns:
        # Extract numeric values and handle 'kWh' suffix
        df['Energieverbruik'] = df['Energieverbruik'].astype(str).str.extract(r'(-?\d+\.\d+|-?\d+)').astype(float)
        # Replace unrealistic values (>10000 or <0) with NaN
        df.loc[(df['Energieverbruik'] > 10000) | (df['Energieverbruik'] < 0), 'Energieverbruik'] = np.nan
        print(f"  - Cleaned Energieverbruik column")
    
    # Klanttevredenheid column
    if 'Klanttevredenheid' in df.columns:
        # Convert to string first to handle mixed types
        df['Klanttevredenheid'] = df['Klanttevredenheid'].astype(str)
        # Replace 'onbekend' with NaN
        df.loc[df['Klanttevredenheid'] == 'onbekend', 'Klanttevredenheid'] = np.nan
        # Convert to float
        df['Klanttevredenheid'] = pd.to_numeric(df['Klanttevredenheid'], errors='coerce')
        # Ensure values are in a reasonable range (1-10)
        df.loc[(df['Klanttevredenheid'] < 1) | (df['Klanttevredenheid'] > 10), 'Klanttevredenheid'] = np.nan
        print(f"  - Cleaned Klanttevredenheid column")
    
    # Leveranciersbeoordeling column
    if 'Leveranciersbeoordeling' in df.columns:
        # Convert to numeric, coercing errors to NaN
        df['Leveranciersbeoordeling'] = pd.to_numeric(df['Leveranciersbeoordeling'], errors='coerce')
        # Replace negative values with NaN
        df.loc[df['Leveranciersbeoordeling'] < 0, 'Leveranciersbeoordeling'] = np.nan
        print(f"  - Cleaned Leveranciersbeoordeling column")
    
    # CO2-Footprint column
    if 'CO2-Footprint' in df.columns:
        # Extract numeric values
        df['CO2-Footprint'] = df['CO2-Footprint'].astype(str).str.extract(r'(\d+\.\d+|\d+)').astype(float)
        print(f"  - Cleaned CO2-Footprint column")
    
    return df

def clean_categorical_columns(df):
    """Clean categorical columns by standardizing values"""
    print("Cleaning categorical columns...")
    
    # PackagingApparaat column
    if 'PackagingApparaat' in df.columns:
        # Replace 'Onbekend apparaat' with NaN
        df.loc[df['PackagingApparaat'] == 'Onbekend apparaat', 'PackagingApparaat'] = np.nan
        print(f"  - Cleaned PackagingApparaat column")
    
    # Panel Test column
    if 'Panel Test' in df.columns:
        # Standardize values
        valid_values = ['Voldoet', 'Voldoet gedeeltelijk', 'Voldoet niet']
        df.loc[~df['Panel Test'].isin(valid_values), 'Panel Test'] = np.nan
        print(f"  - Cleaned Panel Test column")
    
    # Roosterprofiel column
    if 'Roosterprofiel' in df.columns:
        # Standardize values
        valid_values = ['Licht', 'Medium', 'Medium-Donker', 'Donker']
        df.loc[~df['Roosterprofiel'].isin(valid_values), 'Roosterprofiel'] = np.nan
        print(f"  - Cleaned Roosterprofiel column")
    
    # Koffieboon column
    if 'Koffieboon' in df.columns:
        # Standardize values
        valid_values = ['Arabica', 'Robusta', 'Excelsa', 'Liberica']
        df.loc[~df['Koffieboon'].isin(valid_values), 'Koffieboon'] = np.nan
        print(f"  - Cleaned Koffieboon column")
    
    return df

def handle_missing_values(df):
    """Handle missing values in the dataframe"""
    print("Handling missing values...")
    
    # Count missing values before
    missing_before = df.isnull().sum().sum()
    
    # For numeric columns, fill with median
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    for col in numeric_cols:
        missing = df[col].isnull().sum()
        if missing > 0:
            df[col] = df[col].fillna(df[col].median())
            print(f"  - Filled {missing} missing values in {col} with median")
    
    # For categorical columns, fill with mode
    categorical_cols = df.select_dtypes(include=['object']).columns
    for col in categorical_cols:
        missing = df[col].isnull().sum()
        if missing > 0 and not df[col].empty:
            mode_value = df[col].mode()[0] if not df[col].mode().empty else np.nan
            df[col] = df[col].fillna(mode_value)
            print(f"  - Filled {missing} missing values in {col} with mode")
    
    # For date columns, don't fill (leave as NaT)
    date_cols = df.select_dtypes(include=['datetime64']).columns
    for col in date_cols:
        missing = df[col].isnull().sum()
        if missing > 0:
            print(f"  - Left {missing} missing values in date column {col}")
    
    # Count missing values after
    missing_after = df.isnull().sum().sum()
    print(f"  - Reduced missing values from {missing_before} to {missing_after}")
    
    return df

def remove_duplicates(df):
    """Remove duplicate rows based on Batchnr"""
    print("Removing duplicates...")
    
    # Count rows before
    rows_before = len(df)
    
    # Check for exact duplicates
    exact_duplicates = df.duplicated().sum()
    if exact_duplicates > 0:
        df = df.drop_duplicates()
        print(f"  - Removed {exact_duplicates} exact duplicate rows")
    
    # Check for duplicates based on Batchnr
    if 'Batchnr' in df.columns:
        batch_duplicates = df['Batchnr'].duplicated().sum()
        if batch_duplicates > 0:
            print(f"  - Found {batch_duplicates} duplicate Batchnr values")
            # Keep the first occurrence of each Batchnr
            df = df.drop_duplicates(subset=['Batchnr'], keep='first')
            print(f"  - Kept only the first occurrence of each Batchnr")
    
    # Count rows after
    rows_after = len(df)
    print(f"  - Reduced rows from {rows_before} to {rows_after}")
    
    return df

def fix_encoding_issues(df):
    """Fix encoding issues in text columns"""
    print("Fixing encoding issues...")
    
    # Fix encoding in Herkomst column
    if 'Herkomst' in df.columns:
        # Replace specific known encoding issues
        encoding_fixes = {
            'BraziliÃ«': 'Brazilië',
            'MÃ©xico': 'México',
            'PerÃº': 'Perú',
            'HaÃ¯ti': 'Haïti'
        }
        
        for wrong, correct in encoding_fixes.items():
            if df['Herkomst'].str.contains(wrong).any():
                df['Herkomst'] = df['Herkomst'].str.replace(wrong, correct)
                print(f"  - Fixed encoding: {wrong} → {correct}")
    
    return df

if __name__ == "__main__":
    # Run the cleaning process
    clean_kikker_data()
