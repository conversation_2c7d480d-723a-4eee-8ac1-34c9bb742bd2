import pandas as pd
import numpy as np

# Stap 1: Laad de dataset
def load_data():
    print("Kikker.csv dataset laden...")
    try:
        # Probeer verschillende paden voor het bestand
        paths_to_try = [
            '../americaps/Kikker.csv',
            '../Kikker.csv',
            '../../americaps/Kikker.csv',
            '../Void/Kikker.csv',
            '../../Void/Kikker.csv'
        ]

        for path in paths_to_try:
            try:
                df = pd.read_csv(path)
                print(f"Dataset geladen van {path}: {df.shape[0]} rijen, {df.shape[1]} kolommen")
                return df
            except:
                continue

        # Als geen van de paden werkt, probeer een absoluut pad
        df = pd.read_csv('C:/Users/<USER>/Documents/augment-projects/Americaps/americaps/Kikker.csv')
        print(f"Dataset geladen van absoluut pad: {df.shape[0]} rijen, {df.shape[1]} kolommen")
        return df
    except Exception as e:
        print(f"Fout bij laden van dataset: {e}")
        return None

# Stap 2: Opschonen van percentages
def clean_percentages(df):
    print("Percentages opschonen...")

    # Kolommen met percentages
    percentage_cols = ['Klantretourpercentage', 'Defectpercentage', 'Benuttingsgraad']

    for col in percentage_cols:
        if col in df.columns:
            # Verwijder % teken en converteer naar float
            df[col] = df[col].str.rstrip('%').astype('float') / 100

            # Specifiek voor Benuttingsgraad: filter negatieve waarden
            if col == 'Benuttingsgraad':
                # Vervang negatieve waarden door NaN
                df.loc[df['Benuttingsgraad'] < 0, 'Benuttingsgraad'] = np.nan
                # Vervang waarden > 1 (100%) door NaN
                df.loc[df['Benuttingsgraad'] > 1, 'Benuttingsgraad'] = np.nan
                print(f"Aantal negatieve of onrealistische waarden verwijderd uit {col}: {df['Benuttingsgraad'].isna().sum()}")

    return df

# Stap 3: Opschonen van numerieke waarden
def clean_numeric_values(df):
    print("Numerieke waarden opschonen...")

    # Gewichtscontrole opschonen
    if 'Gewichtscontrole' in df.columns:
        df['Gewichtscontrole'] = df['Gewichtscontrole'].str.extract(r'(\d+\.\d+)').astype(float)

    # Energieverbruik opschonen
    if 'Energieverbruik' in df.columns:
        # Verwijder 'kWh' en converteer naar float
        df['Energieverbruik'] = pd.to_numeric(df['Energieverbruik'].str.extract(r'(\d+)')[0], errors='coerce')
        # Vervang onrealistische waarden (999999 kWh)
        df.loc[df['Energieverbruik'] > 10000, 'Energieverbruik'] = np.nan
        # Maak een kopie van de kolom zonder eenheid voor TOC analyse
        df['Energieverbruik_Numeriek'] = df['Energieverbruik']

    # Cost opschonen
    if 'Cost' in df.columns:
        # Extract numerieke waarde en verwijder 'euros'
        df['Cost'] = df['Cost'].str.extract(r'(\d+\.\d+)').astype(float)
        # Vervang onrealistische waarden (>10000 euros)
        df.loc[df['Cost'] > 10000, 'Cost'] = np.nan

    # Cyclustijd opschonen
    if 'Cyclustijd' in df.columns:
        df['Cyclustijd'] = df['Cyclustijd'].str.extract(r'(\d+\.\d+)').astype(float)

    # ProcessTime opschonen
    if 'ProcessTime' in df.columns:
        df['ProcessTime'] = df['ProcessTime'].str.extract(r'(\d+\.\d+)').astype(float)

    # Voorraadniveaus opschonen
    if 'Voorraadniveaus' in df.columns:
        # Extract numerieke waarde en verwijder 'units'
        df['Voorraadniveaus'] = df['Voorraadniveaus'].str.extract(r'(\d+)').astype(float)
        # Vervang onrealistische waarden (>10000 units)
        df.loc[df['Voorraadniveaus'] > 10000, 'Voorraadniveaus'] = np.nan

    return df

# Stap 4: Opschonen van categorische waarden
def clean_categorical_values(df):
    print("Categorische waarden opschonen...")

    # Panel Test standaardiseren
    if 'Panel Test' in df.columns:
        panel_test_mapping = {
            'voldoet': 'Voldoet',
            'Voldoet gedeeltelijk': 'Voldoet gedeeltelijk',
            'voldoet gedeeltelijk': 'Voldoet gedeeltelijk',
            'Voldoet niet': 'Voldoet niet',
            'voldoet niet': 'Voldoet niet',
            'onbekend': 'Onbekend'
        }
        df['Panel Test'] = df['Panel Test'].replace(panel_test_mapping)

    # PackagingApparaat standaardiseren
    if 'PackagingApparaat' in df.columns:
        # Vervang '###' en 'Onbekend apparaat' door NaN in plaats van 'Onbekend'
        df['PackagingApparaat'] = df['PackagingApparaat'].replace(['###', 'Onbekend apparaat', 'Onbekend'], np.nan)

    # Klanttevredenheid opschonen
    if 'Klanttevredenheid' in df.columns:
        # Converteer naar numeriek, errors worden NaN
        df['Klanttevredenheid'] = pd.to_numeric(df['Klanttevredenheid'], errors='coerce')
        # Verwijder onrealistische waarden (>5 voor een 5-puntsschaal of negatieve waarden)
        df.loc[(df['Klanttevredenheid'] > 5) | (df['Klanttevredenheid'] < 0), 'Klanttevredenheid'] = np.nan
        print(f"Aantal onrealistische waarden verwijderd uit Klanttevredenheid: {df['Klanttevredenheid'].isna().sum()}")

    return df

# Stap 5: Opschonen van datums
def clean_dates(df):
    print("Datums opschonen...")

    date_cols = [
        'Registratiedatum',
        'FillingDatumTijdStart', 'FillingDatumTijdEind',
        'GrindingDatumTijdStart', 'GrindingDatumTijdEind',
        'PackagingDatumTijdStart', 'PackagingDatumTijdEind',
        'Laatste Audit'
    ]

    for col in date_cols:
        if col in df.columns:
            df[col] = pd.to_datetime(df[col], errors='coerce')

    return df

# Stap 6: Hoofdfunctie om alle stappen uit te voeren
def clean_kikker_data():
    # Laad de data
    df = load_data()
    if df is None:
        return

    # Voer alle opschoonacties uit
    df = clean_percentages(df)
    df = clean_numeric_values(df)
    df = clean_categorical_values(df)
    df = clean_dates(df)

    # Sla de opgeschoonde data op
    output_path = 'Kikker_cleaned.csv'
    df.to_csv(output_path, index=False)
    print(f"Opgeschoonde data opgeslagen in {output_path}")

    # Toon een samenvatting van de opgeschoonde data
    print("\nSamenvatting van opgeschoonde data:")
    print(f"Aantal rijen: {df.shape[0]}")
    print(f"Aantal kolommen: {df.shape[1]}")
    print(f"Ontbrekende waarden: {df.isnull().sum().sum()}")

    # Toon statistieken voor belangrijke kolommen
    print("\nStatistieken voor kwaliteitsmanagement kolommen:")

    # Six Sigma kolommen
    if 'Defectpercentage' in df.columns:
        print(f"\nDefectpercentage:")
        print(f"- Gemiddelde: {df['Defectpercentage'].mean():.2%}")
        print(f"- Mediaan: {df['Defectpercentage'].median():.2%}")
        print(f"- Min: {df['Defectpercentage'].min():.2%}")
        print(f"- Max: {df['Defectpercentage'].max():.2%}")

    # Lean kolommen
    if 'Cyclustijd' in df.columns:
        print(f"\nCyclustijd (uren):")
        print(f"- Gemiddelde: {df['Cyclustijd'].mean():.2f}")
        print(f"- Mediaan: {df['Cyclustijd'].median():.2f}")
        print(f"- Min: {df['Cyclustijd'].min():.2f}")
        print(f"- Max: {df['Cyclustijd'].max():.2f}")

    # TOC kolommen
    if 'Benuttingsgraad' in df.columns:
        print(f"\nBenuttingsgraad:")
        print(f"- Gemiddelde: {df['Benuttingsgraad'].mean():.2%}")
        print(f"- Mediaan: {df['Benuttingsgraad'].median():.2%}")
        print(f"- Min: {df['Benuttingsgraad'].min():.2%}")
        print(f"- Max: {df['Benuttingsgraad'].max():.2%}")

    # Kaizen kolommen
    if 'Klanttevredenheid' in df.columns:
        print(f"\nKlanttevredenheid:")
        print(f"- Gemiddelde: {df['Klanttevredenheid'].mean():.2f}")
        print(f"- Mediaan: {df['Klanttevredenheid'].median():.2f}")

    if 'Klantretourpercentage' in df.columns:
        print(f"\nKlantretourpercentage:")
        print(f"- Gemiddelde: {df['Klantretourpercentage'].mean():.2%}")
        print(f"- Mediaan: {df['Klantretourpercentage'].median():.2%}")

    return df

# Voer het script uit als het direct wordt uitgevoerd
if __name__ == "__main__":
    clean_kikker_data()
