{"CurrentTheme": "defaultTheme", "CustomThemes": [{"name": "defaultTheme", "palette": [{"name": "ScreenBkgColor", "value": "%Color.RESERVED%.White", "type": "c"}, {"name": "InvertedBkgColor", "value": "RGBA(56, 96, 178, 1)", "type": "c"}, {"name": "PrimaryColor1", "value": "RGBA(56, 96, 178, 1)", "type": "c"}, {"name": "PrimaryColor2", "value": "RGBA(0, 18, 107, 1)", "type": "c"}, {"name": "PrimaryColor3", "value": "RGBA(186, 202, 226, 1)", "type": "c"}, {"name": "PrimaryColor1Light", "value": "RGBA(56, 96, 178, .2)", "type": "c"}, {"name": "PrimaryColor2Light", "value": "RGBA(0, 18, 107, .2)", "type": "c"}, {"name": "PrimaryColor3Light", "value": "RGBA(186, 202, 226, .2)", "type": "c"}, {"name": "PrimaryColor3Fade", "value": "ColorFade(RGBA(186, 202, 226, 1), 70%)", "type": "c"}, {"name": "Transparency", "value": "RGBA(0, 0, 0, 0)", "type": "c"}, {"name": "TextMainColor", "value": "RGBA(0, 0, 0, 1)", "type": "c"}, {"name": "TextMainColorInverted", "value": "RGBA(255, 255, 255, 1)", "type": "c"}, {"name": "TextLinkColor", "value": "RGBA(0, 134, 208, 1)", "type": "c"}, {"name": "TextFooterFontColor", "value": "RGBA(117, 117, 117, 1)", "type": "c"}, {"name": "InputBkgColor", "value": "RGBA(255, 255, 255, 1)", "type": "c"}, {"name": "InputTextColor", "value": "RGBA(0, 0, 0, 1)", "type": "c"}, {"name": "InputBorderColor", "value": "RGBA(0, 18, 107, 1)", "type": "c"}, {"name": "RailBkgColor", "value": "RGBA(128, 130, 133, 1)", "type": "c"}, {"name": "HandleBkgColor", "value": "RGBA(255, 255, 255, 1)", "type": "c"}, {"name": "InnerCircleBkgColor", "value": "RGBA(255, 255, 255, 1)", "type": "c"}, {"name": "DisabledBorderColor", "value": "RGBA(166, 166, 166, 1)", "type": "c"}, {"name": "DisabledTextMainColor", "value": "RGBA(166, 166, 166, 1)", "type": "c"}, {"name": "DisabledInputBkgColor", "value": "RGBA(244, 244, 244, 1)", "type": "c"}, {"name": "DisabledButtonBkgColor", "value": "RGBA(244, 244, 244, 1)", "type": "c"}, {"name": "HoverButtonBkgColor", "value": "ColorFade(RGBA(56, 96, 178, 1), -20%)", "type": "c"}, {"name": "HoverCancelButtonBkgColor", "value": "ColorFade(RGBA(62, 96, 170, 1), 20%)", "type": "c"}, {"name": "HoverInputBkgColor", "value": "RGBA(186, 202, 226, 1)", "type": "c"}, {"name": "OverlayBkgColor", "value": "RGBA(0, 0, 0, 0.4)", "type": "c"}, {"name": "ReservedInfoColor", "value": "RGBA(0, 134, 208, 1)", "type": "c"}, {"name": "ReservedSuccessColor", "value": "RGBA(141, 198, 63, 1)", "type": "c"}, {"name": "ReservedWarningColor", "value": "RGBA(252, 219, 2, 1)", "type": "c"}, {"name": "ReservedErrorColor", "value": "RGBA(246, 88, 16, 1)", "type": "c"}, {"name": "ReservedCriticalErrorColor", "value": "RGBA(168, 0, 0, 1)", "type": "c"}, {"name": "ReservedDisabledStatusColor", "value": "RGBA(193, 193, 193, 1)", "type": "c"}, {"name": "ReservedWhiteColor", "value": "RGBA(255, 255, 255, 1)", "type": "c"}, {"name": "ReservedGrayColor", "value": "RGBA(240, 240, 240, 1)", "type": "c"}, {"name": "ReservedBlackColor", "value": "RGBA(47, 41, 43, 1)", "type": "c"}, {"name": "ReservedChartColorSet", "value": "[RGBA(49, 130, 93, 1),RGBA(48,166,103, 1), RGBA(94,193,108,1), RGBA(246,199,144,1), RGBA(247,199,114,1), RGBA(247,180,91,1), RGBA(246,143,100,1), RGBA(212,96,104,1), RGBA(148, 110, 176, 1), RGBA(118, 154, 204, 1), RGBA(96, 197, 234, 1)]", "type": "![]"}, {"name": "TextBodyFontWeight", "value": "%FontWeight.RESERVED%.Normal", "type": "e"}, {"name": "TextEmphasisFontWeight", "value": "%FontWeight.RESERVED%.Semibold", "type": "e"}, {"name": "TextBodyFontFace", "value": "%Font.RESERVED%.'Open Sans'", "type": "e"}, {"name": "InputBorderThickness", "value": "2", "type": "n"}, {"name": "InputFocusedBorderThickness", "value": "4", "type": "n"}, {"name": "TextHeaderFontSize", "value": "18", "phoneValue": "27", "type": "n"}, {"name": "TextTitleFontSize", "value": "20", "type": "n"}, {"name": "TextSubtitleFontSize", "value": "18", "type": "n"}, {"name": "TextContentFontSize", "value": "16", "type": "n"}, {"name": "TextTitleFontSize_galleryLayouts_ver5", "value": "14", "type": "n"}, {"name": "TextSubtitleFontSize_galleryLayouts_ver5", "value": "12", "type": "n"}, {"name": "TextContentFontSize_galleryLayouts_ver5", "value": "12", "type": "n"}, {"name": "DividerColor2020", "value": "RGBA(255, 255, 255, 1)", "type": "c"}, {"name": "TextTitleColor_galleryLayouts_ver5", "value": "RGBA(50, 49, 48, 1)", "type": "c"}, {"name": "TableNameLabelPadding_copilotAppSinglePage", "value": "16", "type": "n"}, {"name": "SearchContainerFill_copilotAppPage", "value": "RGBA(245, 245, 245, 1)", "type": "c"}, {"name": "ContainerRadius", "value": "4", "type": "n"}, {"name": "TextHeaderFontSize2020", "value": "16", "type": "n"}, {"name": "TextEmphasisFontSize", "value": "15", "phoneValue": "24", "type": "n"}, {"name": "TextBodyFontSize", "value": "13", "phoneValue": "21", "type": "n"}, {"name": "TextFooterFontSize", "value": "11", "phoneValue": "18", "type": "n"}, {"name": "TextMiniFontSize", "value": "9", "phoneValue": "15", "type": "n"}, {"name": "IconFillColorInverted", "value": "RGBA(255, 255, 255, 1)", "type": "c"}, {"name": "IconPressedFillColorInverted", "value": "RGBA(255, 255, 255, 0.3)", "type": "c"}, {"name": "DatePickerSelectedColor", "value": "RGBA(37, 70, 148, 1)", "type": "c"}, {"name": "DatePickerHeaderColor", "value": "RGBA(68, 97, 165, 1)", "type": "c"}, {"name": "NoAttachmentPaddingLeft", "value": "12", "phoneValue": "20", "type": "n"}, {"name": "DefaultSize", "value": "14", "phoneValue": "24", "type": "n"}, {"name": "DefaultSize2", "value": "13", "type": "n"}, {"name": "DropTargetBorderColor", "value": "RGBA(0, 0, 0, 1)", "type": "c"}, {"name": "DropTargetBackgroundColor", "value": "RGBA(255, 255, 255, 0.8)", "type": "c"}, {"name": "DropTargetTextColor", "value": "RGBA(0, 0, 0, 1)", "type": "c"}, {"name": "DropTargetBorderThickness", "value": "2", "type": "n"}], "styles": [{"name": "defaultScreenStyle", "controlTemplateName": "screen", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.ScreenBkgColor%"}, {"property": "LoadingSpinnerColor", "value": "%Palette.PrimaryColor1%"}]}, {"name": "defaultLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "LineHeight", "value": "1.2"}, {"property": "Overflow", "value": "%Overflow.RESERVED%.Hidden"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "0"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "5"}, {"property": "PaddingRight", "value": "5"}, {"property": "PaddingBottom", "value": "5"}, {"property": "PaddingLeft", "value": "5"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"name": "basicNoSizeLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}]}, {"name": "basicNoSizeInvertedBkgLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}]}, {"name": "basicNoSizeWeightColorLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}]}, {"name": "invertedBkgHeaderLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextHeaderFontSize%"}]}, {"name": "invertedBkgTitleLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextTitleFontSize%"}]}, {"name": "linkLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextLinkColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}]}, {"name": "headerLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextHeaderFontSize%"}]}, {"name": "subHeaderLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}]}, {"name": "titleLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextTitleFontSize%"}]}, {"name": "overlayTitleLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextTitleFontSize%"}]}, {"name": "subtitleLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextSubtitleFontSize%"}]}, {"name": "overlaySubtitleLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextSubtitleFontSize%"}]}, {"name": "contentLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextContentFontSize%"}]}, {"name": "titleLabelStyle_galleryLayouts_ver5", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextTitleColor_galleryLayouts_ver5%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "Size", "value": "%Palette.TextTitleFontSize_galleryLayouts_ver5%"}]}, {"name": "subtitleLabelStyle_galleryLayouts_ver5", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "Size", "value": "%Palette.TextSubtitleFontSize_galleryLayouts_ver5%"}]}, {"name": "contentLabelStyle_galleryLayouts_ver5", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "Size", "value": "%Palette.TextContentFontSize_galleryLayouts_ver5%"}]}, {"name": "dividerStyle2020", "controlTemplateName": "rectangle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.DividerColor2020%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "tableNameLabelStyle_copilotAppSinglePage", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedWhiteColor%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "PaddingLeft", "value": "%Palette.TableNameLabelPadding_copilotAppSinglePage%"}, {"property": "PaddingRight", "value": "%Palette.TableNameLabelPadding_copilotAppSinglePage%"}, {"property": "PaddingTop", "value": "%Palette.TableNameLabelPadding_copilotAppSinglePage%"}, {"property": "PaddingBottom", "value": "%Palette.TableNameLabelPadding_copilotAppSinglePage%"}]}, {"name": "containerStyle_copilotAppPage", "controlTemplateName": "groupContainer", "propertyValuesMap": [{"property": "DropShadow", "value": "%DropShadow.RESERVED%.None"}]}, {"name": "searchContainerStyle_copilotAppPage", "controlTemplateName": "groupContainer", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.SearchContainerFill_copilotAppPage%"}]}, {"name": "searchInputStyle_copilotAppPage", "controlTemplateName": "text", "propertyValuesMap": [{"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.None"}, {"property": "Fill", "value": "%Palette.Transparency%"}, {"property": "HoverFill", "value": "%Palette.Transparency%"}, {"property": "PressedFill", "value": "%Palette.Transparency%"}]}, {"name": "searchIconStyle_copilotAppPage", "controlTemplateName": "icon", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor1%"}, {"property": "Fill", "value": "%Palette.Transparency%"}]}, {"name": "headerIconStyle_copilotAppSinglePage", "controlTemplateName": "icon", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedWhiteColor%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}]}, {"name": "accentLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "LineHeight", "value": "1.2"}, {"property": "Overflow", "value": "%Overflow.RESERVED%.Hidden"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "0"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "5"}, {"property": "PaddingRight", "value": "5"}, {"property": "PaddingBottom", "value": "5"}, {"property": "PaddingLeft", "value": "5"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"name": "pickerEmphasisLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}]}, {"name": "pickerEmphasisWithAccentLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}]}, {"name": "pickerEmphasisSecondaryLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextFooterFontColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}]}, {"name": "footerAccentLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextFooterFontSize%"}]}, {"name": "footerLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextFooterFontColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextFooterFontSize%"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "LineHeight", "value": "1.2"}, {"property": "Overflow", "value": "%Overflow.RESERVED%.Hidden"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "0"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "5"}, {"property": "PaddingRight", "value": "5"}, {"property": "PaddingBottom", "value": "5"}, {"property": "PaddingLeft", "value": "5"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"name": "miniLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextMiniFontSize%"}]}, {"name": "miniInvertedBkgLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextMiniFontSize%"}]}, {"name": "disabled<PERSON><PERSON><PERSON><PERSON><PERSON>le", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedDisabledStatusColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}]}, {"name": "infoLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextLinkColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}]}, {"name": "successLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedSuccessColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}]}, {"name": "warningLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedWarningColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}]}, {"name": "errorLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedErrorColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}]}, {"name": "criticalErrorLabelStyle", "controlTemplateName": "label", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedCriticalErrorColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "LineHeight", "value": "1.2"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "Overflow", "value": "%Overflow.RESERVED%.Hidden"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "0"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}, {"property": "Size", "value": "%Palette.DefaultSize%"}]}, {"name": "defaultToggleSwitchStyle", "controlTemplateName": "toggleSwitch", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "HandleFill", "value": "%Palette.HandleBkgColor%"}, {"property": "FalseFill", "value": "%Palette.RailBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "TrueFill", "value": "%Palette.PrimaryColor1%"}, {"property": "FalseHoverFill", "value": "ColorFade(Self.FalseFill, 15%)"}, {"property": "TrueHoverFill", "value": "ColorFade(<PERSON><PERSON>Fill, 15%)"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -15%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 15%)"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"name": "defaultRatingStyle", "controlTemplateName": "rating", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "RatingFill", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"name": "defaultCheckboxStyle", "controlTemplateName": "checkbox", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "CheckboxBackgroundFill", "value": "%Palette.InnerCircleBkgColor%"}, {"property": "CheckboxBorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "CheckmarkFill", "value": "%Palette.InputTextColor%"}, {"property": "HoverColor", "value": "%Palette.PrimaryColor2%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -30%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 30%)"}, {"property": "DisabledColor", "value": "RGBA(186, 186, 186, 1)"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "PressedColor", "value": "RGBA(70, 68, 64, 1)"}, {"property": "PressedFill", "value": "ColorFade(Self<PERSON>ll, -30%)"}, {"property": "HoverFill", "value": "ColorFade(Self.Fill, 30%)"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "0"}, {"property": "PaddingRight", "value": "0"}, {"property": "PaddingBottom", "value": "0"}, {"property": "PaddingLeft", "value": "0"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"name": "defaultRadioStyle", "controlTemplateName": "radio", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HoverColor", "value": "%Palette.PrimaryColor2%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "RadioBackgroundFill", "value": "%Palette.InnerCircleBkgColor%"}, {"property": "RadioBorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "RadioSelectionFill", "value": "%Palette.InputTextColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}, {"property": "DisabledColor", "value": "RGBA(186, 186, 186, 1)"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "10"}, {"property": "PaddingRight", "value": "0"}, {"property": "PaddingBottom", "value": "10"}, {"property": "PaddingLeft", "value": "0"}, {"property": "Align", "value": "%Align.RESERVED%.Left"}, {"property": "PressedColor", "value": "Self.Color"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}]}, {"name": "defaultListboxStyle", "controlTemplateName": "listbox", "propertyValuesMap": [{"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledSelectionColor", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "DisabledSelectionFill", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "PressedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "PressedFill", "value": "%Palette.PrimaryColor2%"}, {"property": "SelectionColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "SelectionFill", "value": "%Palette.PrimaryColor1%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "DisabledColor", "value": "RGBA(186, 186, 186, 1)"}, {"property": "DisabledFill", "value": "RGBA(242, 242, 242, 1)"}, {"property": "PressedBorderColor", "value": "Self.HoverBorderColor"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 15%)"}, {"property": "PaddingTop", "value": "0"}, {"property": "PaddingRight", "value": "0"}, {"property": "PaddingBottom", "value": "0"}, {"property": "PaddingLeft", "value": "0"}]}, {"name": "defaultDropdownStyle", "controlTemplateName": "dropdown", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "ChevronBackground", "value": "%Palette.PrimaryColor1%"}, {"property": "ChevronFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "ChevronHoverBackground", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "ChevronHoverFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "ChevronDisabledBackground", "value": "%Palette.DisabledBorderColor%"}, {"property": "ChevronDisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "PressedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "PressedFill", "value": "%Palette.PrimaryColor2%"}, {"property": "SelectionColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "SelectionFill", "value": "%Palette.PrimaryColor1%"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 15%)"}, {"property": "PressedBorderColor", "value": "Self.HoverBorderColor"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "10"}, {"property": "PaddingRight", "value": "10"}, {"property": "PaddingBottom", "value": "10"}, {"property": "PaddingLeft", "value": "10"}]}, {"name": "defaultComboBoxStyle", "controlTemplateName": "combobox", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "ChevronBackground", "value": "%Palette.PrimaryColor1%"}, {"property": "ChevronFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "ChevronHoverBackground", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "ChevronHoverFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "ChevronDisabledBackground", "value": "%Palette.DisabledBorderColor%"}, {"property": "ChevronDisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "PressedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "PressedFill", "value": "%Palette.PrimaryColor2%"}, {"property": "SelectionColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "SelectionFill", "value": "%Palette.PrimaryColor1%"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 15%)"}, {"property": "PressedBorderColor", "value": "Self.HoverBorderColor"}, {"property": "MoreItemsButtonColor", "value": "Self.ChevronBackground"}]}, {"name": "defaultAttachmentsStyle", "controlTemplateName": "attachments", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "PressedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "PressedFill", "value": "%Palette.PrimaryColor2%"}, {"property": "ItemColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "ItemFill", "value": "%Palette.PrimaryColor1%"}, {"property": "ItemHoverColor", "value": "%Palette.InputTextColor%"}, {"property": "ItemHoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "ItemSpacing", "value": "0"}, {"property": "NoAttachmentsColor", "value": "Self.Color"}, {"property": "NoAttachmentsPaddingLeft", "value": "%Palette.NoAttachmentPaddingLeft%"}, {"property": "DropTargetBorderThickness", "value": "%Palette.DropTargetBorderThickness%"}, {"property": "DropTargetBorderStyle", "value": "%BorderStyle.RESERVED%.Dotted"}, {"property": "DropTargetBorderColor", "value": "%Palette.DropTargetBorderColor%"}, {"property": "DropTargetBackgroundColor", "value": "%Palette.DropTargetBackgroundColor%"}, {"property": "DropTargetTextColor", "value": "%Palette.DropTargetTextColor%"}]}, {"name": "defaultDatePickerStyle", "controlTemplateName": "datepicker", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "IconFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "IconBackground", "value": "%Palette.PrimaryColor1%"}, {"property": "SelectedDateFill", "value": "%Palette.PrimaryColor1%"}, {"property": "HoverDateFill", "value": "%Palette.PrimaryColor3%"}, {"property": "CalendarHeaderFill", "value": "%Palette.PrimaryColor1%"}, {"property": "Size", "value": "%Palette.DefaultSize%"}, {"property": "Italic", "value": "false"}, {"property": "PaddingTop", "value": "0"}, {"property": "PaddingRight", "value": "5"}, {"property": "PaddingBottom", "value": "5"}, {"property": "PaddingLeft", "value": "12"}]}, {"name": "defaultLookupStyle", "controlTemplateName": "lookup", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "ChevronBackground", "value": "%Palette.PrimaryColor1%"}, {"property": "ChevronFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "ChevronHoverBackground", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "ChevronHoverFill", "value": "%Palette.TextMainColorInverted%"}, {"property": "ChevronDisabledBackground", "value": "%Palette.DisabledBorderColor%"}, {"property": "ChevronDisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "PressedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "PressedFill", "value": "%Palette.PrimaryColor2%"}, {"property": "SelectionColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "SelectionFill", "value": "%Palette.PrimaryColor1%"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 15%)"}, {"property": "PressedBorderColor", "value": "Self.HoverBorderColor"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "PaddingTop", "value": "10"}, {"property": "PaddingRight", "value": "10"}, {"property": "PaddingBottom", "value": "10"}, {"property": "PaddingLeft", "value": "10"}, {"property": "FooterSize", "value": "Self.Size - 3"}]}, {"name": "defaultTextStyle", "controlTemplateName": "text", "propertyValuesMap": [{"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "HoverBorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "RadiusTopLeft", "value": "5"}, {"property": "RadiusBottomRight", "value": "5"}, {"property": "RadiusTopRight", "value": "5"}, {"property": "RadiusBottomLeft", "value": "5"}, {"property": "PressedBorderColor", "value": "Self.HoverBorderColor"}, {"property": "PressedColor", "value": "Self.Color"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "Align", "value": "%Align.RESERVED%.Left"}]}, {"name": "searchTextStyle", "controlTemplateName": "text", "propertyValuesMap": [{"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.None"}, {"property": "BorderThickness", "value": "%Palette.InputBorderThickness%"}, {"property": "FocusedBorderThickness", "value": "%Palette.InputFocusedBorderThickness%"}, {"property": "HoverBorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "HoverColor", "value": "%Palette.InputTextColor%"}, {"property": "HoverFill", "value": "%Palette.HoverInputBkgColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}]}, {"name": "defaultSliderStyle", "controlTemplateName": "slider", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "HandleFill", "value": "%Palette.HandleBkgColor%"}, {"property": "RailFill", "value": "%Palette.RailBkgColor%"}, {"property": "ValueFill", "value": "%Palette.PrimaryColor2%"}, {"property": "HandleHoverFill", "value": "<PERSON><PERSON>"}, {"property": "HandleActiveFill", "value": "<PERSON><PERSON>"}, {"property": "RailHoverFill", "value": "ColorFade(Self.RailFill, 15%)"}, {"property": "ValueHoverFill", "value": "ColorFade(Self.ValueFill, 15%)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -30%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 30%)"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"name": "defaultButtonStyle", "controlTemplateName": "button", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}, {"property": "HoverColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "HoverFill", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "BorderColor", "value": "ColorFade(Self.Fill, -15%)"}, {"property": "RadiusTopLeft", "value": "10"}, {"property": "RadiusBottomRight", "value": "10"}, {"property": "RadiusTopRight", "value": "10"}, {"property": "RadiusBottomLeft", "value": "10"}, {"property": "PressedBorderColor", "value": "Self<PERSON>ll"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "2"}, {"property": "FocusedBorderThickness", "value": "4"}, {"property": "PressedColor", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self.Color"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "Align", "value": "%Align.RESERVED%.Center"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"name": "cancelButtonStyle", "controlTemplateName": "button", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor1%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.PrimaryColor3%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}]}, {"name": "rezervedOkButtonStyle", "controlTemplateName": "button", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedWhiteColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.ReservedInfoColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}]}, {"name": "rezervedCancelButtonStyle", "controlTemplateName": "button", "propertyValuesMap": [{"property": "Color", "value": "%Palette.ReservedInfoColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Fill", "value": "%Palette.ReservedWhiteColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}]}, {"name": "defaultLineChartStyle", "controlTemplateName": "lineChart", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "ItemColorSet", "value": "%Palette.ReservedChartColorSet%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -30%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 30%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "Font", "value": "%Font.RESERVED%.'Open Sans'"}, {"property": "Size", "value": "11"}]}, {"name": "monochromeAccentLineChartStyle", "controlTemplateName": "lineChart", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "ItemColorSet", "value": "[%Palette.PrimaryColor1%]"}, {"property": "Color", "value": "%Palette.TextMainColor%"}]}, {"name": "defaultPieChartStyle", "controlTemplateName": "<PERSON><PERSON><PERSON>", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "ItemColorSet", "value": "%Palette.ReservedChartColorSet%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -30%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 30%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "Font", "value": "%Font.RESERVED%.'Open Sans'"}, {"property": "Size", "value": "10"}]}, {"name": "monochromeAccentPieChartStyle", "controlTemplateName": "<PERSON><PERSON><PERSON>", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "ItemColorSet", "value": "[%Palette.PrimaryColor1%]"}, {"property": "Color", "value": "%Palette.TextMainColor%"}]}, {"name": "defaultBarChartStyle", "controlTemplateName": "<PERSON><PERSON><PERSON>", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "ItemColorSet", "value": "%Palette.ReservedChartColorSet%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -30%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 30%)"}, {"property": "Size", "value": "10"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "Font", "value": "%Font.RESERVED%.'Open Sans'"}]}, {"name": "monochromeAccentBarChartStyle", "controlTemplateName": "<PERSON><PERSON><PERSON>", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "ItemColorSet", "value": "[%Palette.PrimaryColor1%]"}, {"property": "Color", "value": "%Palette.TextMainColor%"}]}, {"name": "defaultLegendStyle", "controlTemplateName": "legend", "propertyValuesMap": [{"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextFooterFontSize%"}, {"property": "BorderColor", "value": "RGBA(0, 0, 0, 1)"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "PressedBorderColor", "value": "Self.BorderColor"}, {"property": "HoverBorderColor", "value": "Self.BorderColor"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "DisabledFill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "Italic", "value": "false"}]}, {"name": "separatorShapeStyle", "controlTemplateName": "rectangle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultVideoPlaybackStyle", "controlTemplateName": "videoPlayback", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}]}, {"name": "defaultTimerStyle", "controlTemplateName": "timer", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "HoverFill", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "HoverColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "BorderColor", "value": "ColorFade(Self.Fill, -15%)"}, {"property": "DisabledBorderColor", "value": "ColorFade(Self.BorderColor, 70%)"}, {"property": "PressedBorderColor", "value": "Self<PERSON>ll"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "2"}, {"property": "FocusedBorderThickness", "value": "4"}, {"property": "DisabledColor", "value": "ColorFade(Self.Fill, 90%)"}, {"property": "PressedColor", "value": "Self<PERSON>ll"}, {"property": "DisabledFill", "value": "ColorFade(Self.Fill, 70%)"}, {"property": "PressedFill", "value": "Self.Color"}, {"property": "Size", "value": "%Palette.DefaultSize2%"}]}, {"name": "defaultTriangleStyle", "controlTemplateName": "triangle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"name": "defaultStarStyle", "controlTemplateName": "star", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultPentagonStyle", "controlTemplateName": "pentagon", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"name": "defaultPartialCircleStyle", "controlTemplateName": "partialCircle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultOctagonStyle", "controlTemplateName": "octagon", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"name": "defaultHexagonStyle", "controlTemplateName": "hexagon", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"name": "primary2HexagonStyle", "controlTemplateName": "hexagon", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor2%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "primary3HexagonStyle", "controlTemplateName": "hexagon", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "primary3FadeHexagonStyle", "controlTemplateName": "hexagon", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3Fade%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "screenHexagonStyle", "controlTemplateName": "hexagon", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.ScreenBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultCircleStyle", "controlTemplateName": "circle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}]}, {"name": "primary2CircleStyle", "controlTemplateName": "circle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor2%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "primary3CircleStyle", "controlTemplateName": "circle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "primary3FadeCircleStyle", "controlTemplateName": "circle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3Fade%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultArrowStyle", "controlTemplateName": "arrow", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}]}, {"name": "defaultIconStyle", "controlTemplateName": "icon", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor2%"}, {"property": "DisabledColor", "value": "%Palette.DisabledButtonBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "PressedColor", "value": "ColorFade(Self.Color, -20%)"}, {"property": "HoverColor", "value": "ColorFade(Self.Color, 20%)"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -20%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"name": "primary1IconStyle", "controlTemplateName": "icon", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor1%"}, {"property": "DisabledColor", "value": "%Palette.DisabledButtonBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}]}, {"name": "primary3IconStyle", "controlTemplateName": "icon", "propertyValuesMap": [{"property": "Color", "value": "%Palette.PrimaryColor3%"}, {"property": "DisabledColor", "value": "%Palette.DisabledButtonBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}]}, {"name": "invertedBkgHeaderIconStyle", "controlTemplateName": "icon", "propertyValuesMap": [{"property": "Color", "value": "%Palette.IconFillColorInverted%"}, {"property": "PressedFill", "value": "%Palette.IconPressedFillColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledButtonBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}]}, {"name": "defaultMicrophoneStyle", "controlTemplateName": "microphone", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "HoverFill", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "HoverColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledColor", "value": "RGBA(186, 186, 186, 1)"}, {"property": "PressedColor", "value": "Self.Color"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -15%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 15%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "DisabledFill", "value": "RGBA(119, 119, 119, 1)"}, {"property": "PressedFill", "value": "ColorFade(Self.Fill, -15%)"}]}, {"name": "defaultBarcodeStyle", "controlTemplateName": "barcode", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultGroupContainerStyle", "controlTemplateName": "groupContainer", "propertyValuesMap": [{"property": "RadiusTopLeft", "value": "%Palette.ContainerRadius%"}, {"property": "RadiusBottomRight", "value": "%Palette.ContainerRadius%"}, {"property": "RadiusTopRight", "value": "%Palette.ContainerRadius%"}, {"property": "RadiusBottomLeft", "value": "%Palette.ContainerRadius%"}, {"property": "DropShadow", "value": "%DropShadow.RESERVED%.Light"}]}, {"name": "defaultBarcodeScannerStyle", "controlTemplateName": "barcodeScanner", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}, {"property": "HoverColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "HoverFill", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "BorderColor", "value": "ColorFade(Self.Fill, -15%)"}, {"property": "RadiusTopLeft", "value": "10"}, {"property": "RadiusBottomRight", "value": "10"}, {"property": "RadiusTopRight", "value": "10"}, {"property": "RadiusBottomLeft", "value": "10"}, {"property": "PressedBorderColor", "value": "Self<PERSON>ll"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "2"}, {"property": "FocusedBorderThickness", "value": "4"}, {"property": "PressedColor", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self.Color"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "Align", "value": "%Align.RESERVED%.Center"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"name": "defaultCameraStyle", "controlTemplateName": "camera", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultInkControlStyle", "controlTemplateName": "inkControl", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "Size", "value": "2"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "2"}]}, {"name": "defaultImportStyle", "controlTemplateName": "import", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "HoverFill", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "HoverColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}, {"property": "RadiusTopLeft", "value": "10"}, {"property": "RadiusTopRight", "value": "10"}, {"property": "RadiusBottomLeft", "value": "10"}, {"property": "RadiusBottomRight", "value": "10"}, {"property": "BorderColor", "value": "ColorFade(Self.Fill, -15%)"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -20%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "2"}, {"property": "FocusedBorderThickness", "value": "4"}, {"property": "PressedColor", "value": "Self.Color"}, {"property": "PressedFill", "value": "ColorFade(Self.Fill, -20%)"}]}, {"name": "defaultImageStyle", "controlTemplateName": "image", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledButtonBkgColor%"}, {"property": "Fill", "value": "RGBA(0, 0, 0, 0)"}, {"property": "PressedFill", "value": "ColorFade(Self.Fill, -20%)"}, {"property": "HoverFill", "value": "ColorFade(Self.Fill, 20%)"}, {"property": "RadiusTopLeft", "value": "0"}, {"property": "RadiusTopRight", "value": "0"}, {"property": "RadiusBottomLeft", "value": "0"}, {"property": "RadiusBottomRight", "value": "0"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -20%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "PaddingTop", "value": "0"}, {"property": "PaddingRight", "value": "0"}, {"property": "PaddingBottom", "value": "0"}, {"property": "PaddingLeft", "value": "0"}]}, {"name": "defaultHtmlViewerStyle", "controlTemplateName": "htmlviewer", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}]}, {"name": "typedDataCardHtmlViewerStyle", "controlTemplateName": "htmlviewer", "propertyValuesMap": [{"property": "DisabledBorderColor", "value": "%Palette.DisabledBorderColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "Color", "value": "%Palette.TextLinkColor%"}]}, {"name": "defaultExportStyle", "controlTemplateName": "export", "propertyValuesMap": [{"property": "Color", "value": "%Palette.TextMainColorInverted%"}, {"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextEmphasisFontWeight%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "DisabledFill", "value": "%Palette.DisabledInputBkgColor%"}, {"property": "HoverFill", "value": "%Palette.HoverButtonBkgColor%"}, {"property": "HoverColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "Size", "value": "%Palette.TextEmphasisFontSize%"}, {"property": "RadiusTopLeft", "value": "10"}, {"property": "RadiusTopRight", "value": "10"}, {"property": "RadiusBottomLeft", "value": "10"}, {"property": "RadiusBottomRight", "value": "10"}, {"property": "BorderColor", "value": "ColorFade(Self.Fill, -15%)"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -20%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "2"}, {"property": "FocusedBorderThickness", "value": "4"}, {"property": "PressedColor", "value": "Self.Color"}, {"property": "PressedFill", "value": "ColorFade(Self.Fill, -20%)"}]}, {"name": "defaultAddMediaStyle", "controlTemplateName": "addMedia", "propertyValuesMap": [{"property": "Color", "value": "%Palette.InputTextColor%"}, {"property": "Fill", "value": "%Palette.InputBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}, {"property": "Font", "value": "%Font.RESERVED%.'Open Sans'"}, {"property": "Size", "value": "11"}, {"property": "Italic", "value": "false"}, {"property": "Underline", "value": "false"}, {"property": "Strikethrough", "value": "false"}, {"property": "DisabledBorderColor", "value": "RGBA(56, 56, 56, 1)"}, {"property": "PressedBorderColor", "value": "ColorFade(Self.BorderColor, -20%)"}, {"property": "HoverBorderColor", "value": "ColorFade(Self.BorderColor, 20%)"}, {"property": "DisabledColor", "value": "RGBA(186, 186, 186, 1)"}, {"property": "PressedColor", "value": "Self.Color"}, {"property": "HoverColor", "value": "Self.Color"}, {"property": "DisabledFill", "value": "RGBA(119, 119, 119, 1)"}, {"property": "PressedFill", "value": "ColorFade(Self.Fill, -20%)"}, {"property": "HoverFill", "value": "ColorFade(Self.Fill, 20%)"}, {"property": "FontWeight", "value": "%FontWeight.RESERVED%.Semibold"}, {"property": "Align", "value": "%Align.RESERVED%.Center"}, {"property": "VerticalAlign", "value": "%VerticalAlign.RESERVED%.Middle"}]}, {"name": "defaultAudioPlaybackStyle", "controlTemplateName": "audioPlayback", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}]}, {"name": "defaultRectangleStyle", "controlTemplateName": "rectangle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor1%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "DisabledFill", "value": "Self<PERSON>ll"}, {"property": "PressedFill", "value": "Self<PERSON>ll"}, {"property": "HoverFill", "value": "Self<PERSON>ll"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "FocusedBorderThickness", "value": "2"}]}, {"name": "primary2RectangleStyle", "controlTemplateName": "rectangle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor2%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "primary3RectangleStyle", "controlTemplateName": "rectangle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "primary3FadeRectangleStyle", "controlTemplateName": "rectangle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.PrimaryColor3Fade%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "grayRectangleStyle", "controlTemplateName": "rectangle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.ReservedGrayColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "invertedBackgroundRectangleStyle", "controlTemplateName": "rectangle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.InvertedBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "overlayRectangleStyle", "controlTemplateName": "rectangle", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.OverlayBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultTypedDataCardStyle", "controlTemplateName": "typedDataCard", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultEntityFormStyle", "controlTemplateName": "entityForm", "propertyValuesMap": [{"property": "TextColor", "value": "%Palette.TextMainColor%"}, {"property": "InputTextColor", "value": "%Palette.InputTextColor%"}, {"property": "DisabledTextColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "PrimaryColor1", "value": "%Palette.PrimaryColor1%"}, {"property": "PrimaryColor2", "value": "%Palette.PrimaryColor2%"}, {"property": "PrimaryColor3", "value": "%Palette.PrimaryColor3%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "InputBackgroundColor", "value": "%Palette.InputBkgColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "Font", "value": "%Font.RESERVED%.'Open Sans'"}, {"property": "FontWeight", "value": "%FontWeight.RESERVED%.Normal"}]}, {"name": "defaultDataGridStyle", "controlTemplateName": "dataGrid", "propertyValuesMap": [{"property": "LinkColor", "value": "%Palette.TextLinkColor%"}, {"property": "PrimaryColor1", "value": "%Palette.PrimaryColor1%"}, {"property": "PrimaryColor2", "value": "%Palette.PrimaryColor2%"}, {"property": "PrimaryColor3", "value": "%Palette.PrimaryColor3%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "InvertedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "SelectedFill", "value": "%Palette.PrimaryColor1Light%"}, {"property": "SelectedColor", "value": "%Palette.TextMainColor%"}, {"property": "HoverFill", "value": "%Palette.PrimaryColor3Light%"}, {"property": "HoverColor", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "InputFill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "HeadingFont", "value": "%Palette.TextBodyFontFace%"}, {"property": "HeadingFontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HeadingSize", "value": "%Palette.TextBodyFontSize%"}, {"property": "HeadingColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "HeadingFill", "value": "%Palette.PrimaryColor1%"}]}, {"name": "defaultPowerbiStyle", "controlTemplateName": "powerbi", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultMicrosoftStreamPlaybackStyle", "controlTemplateName": "microsoftStreamPlayback", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "BorderStyle", "value": "%BorderStyle.RESERVED%.Solid"}, {"property": "BorderThickness", "value": "0"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "Fill", "value": "%Palette.ScreenBkgColor%"}]}, {"name": "defaultFormStyle", "controlTemplateName": "form", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultFormViewerStyle", "controlTemplateName": "formViewer", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultPdfViewerStyle", "controlTemplateName": "pdfViewer", "propertyValuesMap": [{"property": "Fill", "value": "%Palette.ScreenBkgColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultGalleryStyle", "controlTemplateName": "gallery", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultRichTextEditorStyle", "controlTemplateName": "richTextEditor", "propertyValuesMap": [{"property": "BorderColor", "value": "%Palette.InputBorderColor%"}]}, {"name": "defaultDataTableStyle", "controlTemplateName": "dataTable", "propertyValuesMap": [{"property": "LinkColor", "value": "%Palette.TextLinkColor%"}, {"property": "PrimaryColor1", "value": "%Palette.PrimaryColor1%"}, {"property": "PrimaryColor2", "value": "%Palette.PrimaryColor2%"}, {"property": "PrimaryColor3", "value": "%Palette.PrimaryColor3%"}, {"property": "Color", "value": "%Palette.TextMainColor%"}, {"property": "InvertedColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "DisabledColor", "value": "%Palette.DisabledTextMainColor%"}, {"property": "SelectedFill", "value": "%Palette.PrimaryColor1Light%"}, {"property": "SelectedColor", "value": "%Palette.TextMainColor%"}, {"property": "HoverFill", "value": "%Palette.PrimaryColor3Light%"}, {"property": "HoverColor", "value": "%Palette.TextMainColor%"}, {"property": "BorderColor", "value": "%Palette.InputBorderColor%"}, {"property": "InputFill", "value": "%Palette.InputBkgColor%"}, {"property": "Font", "value": "%Palette.TextBodyFontFace%"}, {"property": "FontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "Size", "value": "%Palette.TextBodyFontSize%"}, {"property": "HeadingFont", "value": "%Palette.TextBodyFontFace%"}, {"property": "HeadingFontWeight", "value": "%Palette.TextBodyFontWeight%"}, {"property": "HeadingSize", "value": "%Palette.TextBodyFontSize%"}, {"property": "HeadingColor", "value": "%Palette.TextMainColorInverted%"}, {"property": "HeadingFill", "value": "%Palette.PrimaryColor1%"}]}]}]}