SWIMLANE DIAGRAM: ORDERVERWERKING PROCES
=======================================

Een swimlane diagram visualiseert een proces dat meerdere afdelingen omvat.
<PERSON>ke "swimlane" (zwembaan) vertegenwoordigt een afdeling of rol, en toont de activiteiten
waarvoor die afdeling verantwoordelijk is.

Voorbeeld van een orderverwerking proces:

VERKOOP:
-------
1. Ontvang bestelling
2. Controleer bestelling
3. Bevestig bestelling
   |
   v

MAGAZIJN:
--------
4. Controleer voorraad
5. Reserveer producten (indien beschikbaar)
   - Als niet beschikbaar: Ga terug naar Verkoop (stap 2)
   - Als beschikbaar: Ga verder
6. Verzamel producten
   |
   v

LOGISTIEK:
---------
7. Verpak producten
8. Maak verzendlabel
9. Verzend pakket
   |
   v

FINANCIËN:
---------
10. Verwerk betaling
11. Maak factuur
12. Boek transactie

START ---> [Verkoop] ---> [<PERSON><PERSON><PERSON><PERSON>] ---> [Logistiek] ---> [Financiën] ---> EINDE

Dit diagram toont duidelijk:
- Welke afdeling verantwoordelijk is voor elke stap in het proces
- De volgorde van activiteiten
- Hoe het werk tussen afdelingen stroomt
- Beslispunten in het proces (zoals de controle of producten beschikbaar zijn)

Voordelen van swimlane diagrammen:
- Verduidelijken van verantwoordelijkheden
- Identificeren van knelpunten en inefficiënties, vooral op de grensvlakken tussen afdelingen
- Verbeteren van communicatie tussen afdelingen
- Standaardiseren van processen
- Ondersteunen van training voor nieuwe medewerkers
