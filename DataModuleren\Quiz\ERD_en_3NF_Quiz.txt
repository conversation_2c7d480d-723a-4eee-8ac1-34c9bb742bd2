# Quiz over Entity Relationship Diagrams (ERD) en Derde Normaalvorm (3NF)

## Deel 1: Entity Relationship Diagrams (ERD)

1. Wat zijn de drie hoofdcomponenten van een Entity Relationship Diagram?
   a) Entiteiten, attributen en relaties
   b) Tabellen, rijen en kolommen
   c) Databases, schema's en views
   d) Primaire sleutels, vreemde sleutels en indexen

2. Welk symbool wordt meestal gebruikt om een entiteit weer te geven in een ERD?
   a) Ovaal
   b) Rechthoek
   c) Ruit
   d) Cirkel

3. Wat geeft een "crow's foot" (kraai<PERSON>poot) notatie aan in een ERD?
   a) Een één-op-één relatie
   b) Een één-op-veel relatie
   c) Een veel-op-veel relatie
   d) Een recursieve relatie

4. Wat is een zwakke entiteit in een ERD?
   a) Een entiteit die niet vaak wordt gebruikt
   b) Een entiteit die afhankelijk is van een andere entiteit voor zijn bestaan
   c) E<PERSON> entiteit met weinig attributen
   d) Een entiteit zonder relaties

5. <PERSON><PERSON><PERSON> van de volgende relatie-types bestaat NIET in een ERD?
   a) Één-op-één (1:1)
   b) Één-op-veel (1:N)
   c) Veel-op-veel (M:N)
   d) Nul-op-nul (0:0)

6. Wat is het doel van een associatieve entiteit (junction table) in een ERD?
   a) Om een één-op-één relatie te implementeren
   b) Om een veel-op-veel relatie om te zetten naar twee één-op-veel relaties
   c) Om attributen van een zwakke entiteit op te slaan
   d) Om primaire sleutels te genereren

7. In welke fase van databaseontwerp wordt een ERD meestal gemaakt?
   a) Implementatiefase
   b) Conceptueel en logisch ontwerp
   c) Fysiek ontwerp
   d) Testfase

8. Wat is een samengestelde sleutel in een ERD?
   a) Een sleutel die uit meerdere attributen bestaat
   b) Een sleutel die gedeeld wordt tussen meerdere entiteiten
   c) Een sleutel die automatisch wordt gegenereerd
   d) Een sleutel die alleen numerieke waarden bevat

9. Wat is het verschil tussen een conceptueel ERD en een logisch ERD?
   a) Een conceptueel ERD bevat meer technische details dan een logisch ERD
   b) Een logisch ERD bevat meer technische details dan een conceptueel ERD
   c) Een conceptueel ERD is specifiek voor één databasemanagementsysteem
   d) Er is geen verschil tussen deze twee types

10. Welke van de volgende is GEEN voordeel van het gebruik van ERD's?
    a) Ze helpen bij het visualiseren van de databasestructuur
    b) Ze verbeteren de communicatie tussen stakeholders
    c) Ze optimaliseren automatisch database queries
    d) Ze documenteren de relaties tussen entiteiten

## Deel 2: Derde Normaalvorm (3NF)

11. Wat is normalisatie in de context van databaseontwerp?
    a) Het proces van het optimaliseren van databaseprestaties
    b) Het proces van het organiseren van data om redundantie te verminderen
    c) Het proces van het beveiligen van een database
    d) Het proces van het maken van backups van een database

12. Welke normaalvorm moet een database bereiken voordat deze in 3NF kan zijn?
    a) 0NF
    b) 1NF
    c) 2NF
    d) 4NF

13. Een tabel is in de Eerste Normaalvorm (1NF) als:
    a) Alle attributen atomair (ondeelbaar) zijn
    b) Er geen partiële afhankelijkheden zijn
    c) Er geen transitieve afhankelijkheden zijn
    d) Alle niet-sleutel attributen functioneel afhankelijk zijn van de primaire sleutel

14. Een tabel is in de Tweede Normaalvorm (2NF) als:
    a) Alle attributen atomair zijn
    b) De tabel in 1NF is en er geen partiële afhankelijkheden zijn
    c) De tabel in 1NF is en er geen transitieve afhankelijkheden zijn
    d) De tabel in 1NF is en alle attributen functioneel onafhankelijk zijn

15. Een tabel is in de Derde Normaalvorm (3NF) als:
    a) De tabel in 2NF is en er geen transitieve afhankelijkheden zijn
    b) De tabel in 2NF is en alle attributen atomair zijn
    c) De tabel in 1NF is en er geen partiële afhankelijkheden zijn
    d) De tabel in 1NF is en alle attributen functioneel onafhankelijk zijn

16. Wat is een transitieve afhankelijkheid?
    a) Wanneer een niet-sleutel attribuut afhankelijk is van een deel van een samengestelde sleutel
    b) Wanneer een niet-sleutel attribuut afhankelijk is van een ander niet-sleutel attribuut
    c) Wanneer een sleutel attribuut afhankelijk is van een niet-sleutel attribuut
    d) Wanneer twee tabellen een relatie hebben

17. Welk probleem lost de Derde Normaalvorm (3NF) op?
    a) Duplicatie van gegevens door herhalende groepen
    b) Partiële afhankelijkheden van een samengestelde sleutel
    c) Transitieve afhankelijkheden tussen niet-sleutel attributen
    d) Problemen met multi-waarde attributen

18. Gegeven de tabel: BESTELLING(OrderID, KlantID, KlantNaam, KlantAdres, ProductID, ProductNaam, Aantal, Prijs)
    Welke van de volgende afhankelijkheden zorgt ervoor dat deze tabel NIET in 3NF is?
    a) OrderID → KlantID
    b) KlantID → KlantNaam, KlantAdres
    c) ProductID → ProductNaam
    d) Zowel b als c

19. Wat is een potentieel nadeel van het normaliseren van een database tot 3NF?
    a) Het verhoogt de kans op data-inconsistentie
    b) Het kan leiden tot complexere queries met meer joins
    c) Het vermindert de mogelijkheden voor indexering
    d) Het maakt het moeilijker om de database te beveiligen

20. Welke uitspraak over 3NF is WAAR?
    a) Een tabel in 3NF heeft altijd minder rijen dan dezelfde gegevens in een niet-genormaliseerde tabel
    b) Een tabel in 3NF heeft meestal meer tabellen dan een database in 1NF
    c) 3NF garandeert optimale queryprestaties in alle gevallen
    d) 3NF elimineert alle vormen van redundantie in een database

## Deel 3: Praktische Toepassingen

21. Je hebt een tabel MEDEWERKER(MedewerkerID, Naam, AfdelingID, AfdelingNaam, LocatieID, LocatieAdres).
    Hoe zou je deze tabel normaliseren naar 3NF?
    a) Eén tabel is voldoende omdat alle attributen afhankelijk zijn van MedewerkerID
    b) Twee tabellen: MEDEWERKER(MedewerkerID, Naam, AfdelingID) en AFDELING(AfdelingID, AfdelingNaam, LocatieID, LocatieAdres)
    c) Drie tabellen: MEDEWERKER(MedewerkerID, Naam, AfdelingID), AFDELING(AfdelingID, AfdelingNaam, LocatieID) en LOCATIE(LocatieID, LocatieAdres)
    d) Vier tabellen zijn nodig om 3NF te bereiken

22. Welke van de volgende relaties zou je in een ERD modelleren als een veel-op-veel relatie?
    a) Student en StudentID
    b) Student en Klas (elke student zit in één klas, elke klas heeft meerdere studenten)
    c) Student en Cursus (studenten volgen meerdere cursussen, cursussen hebben meerdere studenten)
    d) Student en Adres (elke student heeft één adres)

23. Je moet een database ontwerpen voor een bibliotheek. Welke van de volgende zou een geschikte set entiteiten zijn voor een ERD?
    a) Boeken, Auteurs, Leden, Uitleningen
    b) Boektitels, Boeknummers, Ledennamen, Uitleendatums
    c) Bibliotheek, Collectie, Lidmaatschap, Boekenkast
    d) ISBN, Auteursnaam, Ledennummer, Retourneren

24. Wat is de beste manier om een één-op-één relatie te implementeren in een relationele database?
    a) Gebruik dezelfde primaire sleutel in beide tabellen
    b) Creëer een derde tabel om de relatie te beheren
    c) Voeg een vreemde sleutel toe aan één van de tabellen die verwijst naar de andere tabel
    d) Combineer beide tabellen tot één tabel

25. Je hebt een tabel FACTUUR(FactuurID, KlantID, KlantNaam, Datum, ProductID, ProductNaam, Prijs, Aantal, Subtotaal).
    Welke functionele afhankelijkheden bestaan er in deze tabel?
    a) FactuurID → KlantID, Datum
    b) KlantID → KlantNaam
    c) ProductID → ProductNaam, Prijs
    d) Alle bovenstaande

## Antwoorden

### Deel 1: Entity Relationship Diagrams (ERD)
1. a) Entiteiten, attributen en relaties
2. b) Rechthoek
3. b) Een één-op-veel relatie
4. b) Een entiteit die afhankelijk is van een andere entiteit voor zijn bestaan
5. d) Nul-op-nul (0:0)
6. b) Om een veel-op-veel relatie om te zetten naar twee één-op-veel relaties
7. b) Conceptueel en logisch ontwerp
8. a) Een sleutel die uit meerdere attributen bestaat
9. b) Een logisch ERD bevat meer technische details dan een conceptueel ERD
10. c) Ze optimaliseren automatisch database queries

### Deel 2: Derde Normaalvorm (3NF)
11. b) Het proces van het organiseren van data om redundantie te verminderen
12. c) 2NF
13. a) Alle attributen atomair (ondeelbaar) zijn
14. b) De tabel in 1NF is en er geen partiële afhankelijkheden zijn
15. a) De tabel in 2NF is en er geen transitieve afhankelijkheden zijn
16. b) Wanneer een niet-sleutel attribuut afhankelijk is van een ander niet-sleutel attribuut
17. c) Transitieve afhankelijkheden tussen niet-sleutel attributen
18. d) Zowel b als c
19. b) Het kan leiden tot complexere queries met meer joins
20. b) Een tabel in 3NF heeft meestal meer tabellen dan een database in 1NF

### Deel 3: Praktische Toepassingen
21. c) Drie tabellen: MEDEWERKER(MedewerkerID, Naam, AfdelingID), AFDELING(AfdelingID, AfdelingNaam, LocatieID) en LOCATIE(LocatieID, LocatieAdres)
22. c) Student en Cursus (studenten volgen meerdere cursussen, cursussen hebben meerdere studenten)
23. a) Boeken, Auteurs, Leden, Uitleningen
24. c) Voeg een vreemde sleutel toe aan één van de tabellen die verwijst naar de andere tabel
25. d) Alle bovenstaande
