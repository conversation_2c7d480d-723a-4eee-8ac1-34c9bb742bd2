-- Euro Caps Database Implementation
-- This script creates the database schema for Euro Caps' production process

-- Create database
CREATE DATABASE IF NOT EXISTS euro_caps;
USE euro_caps;

-- Create Suppliers table
CREATE TABLE Suppliers (
    SupplierID INT AUTO_INCREMENT PRIMARY KEY,
    SupplierName VARCHAR(100) NOT NULL,
    Contact<PERSON>erson VARCHAR(100),
    Email VARCHAR(100),
    Phone VARCHAR(20),
    Address VARCHAR(255),
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create Materials table
CREATE TABLE Materials (
    MaterialID INT AUTO_INCREMENT PRIMARY KEY,
    MaterialName VARCHAR(100) NOT NULL,
    MaterialType ENUM('coffee_beans', 'capsules', 'foil', 'additives', 'milk', 'nitrogen', 'packaging') NOT NULL,
    UnitOfMeasure VARCHAR(20) NOT NULL,
    CurrentStock DECIMAL(10,2) NOT NULL DEFAULT 0,
    MinimumStock DECIMAL(10,2) NOT NULL DEFAULT 0,
    SupplierID INT,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)
);

-- Create Products table
CREATE TABLE Products (
    ProductID INT AUTO_INCREMENT PRIMARY KEY,
    ProductName VARCHAR(100) NOT NULL,
    ProductType ENUM('espresso', 'lungo', 'ristretto', 'flavored') NOT NULL,
    PackageSize ENUM('10', '20', '44') NOT NULL,
    Description TEXT,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create Customers table
CREATE TABLE Customers (
    CustomerID INT AUTO_INCREMENT PRIMARY KEY,
    CustomerName VARCHAR(100) NOT NULL,
    ContactPerson VARCHAR(100),
    Email VARCHAR(100),
    Phone VARCHAR(20),
    Address VARCHAR(255),
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create Employees table
CREATE TABLE Employees (
    EmployeeID INT AUTO_INCREMENT PRIMARY KEY,
    FirstName VARCHAR(50) NOT NULL,
    LastName VARCHAR(50) NOT NULL,
    Department ENUM('grinding', 'filling', 'packing', 'quality', 'logistics', 'management') NOT NULL,
    Position VARCHAR(100),
    HireDate DATE,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create Machines table
CREATE TABLE Machines (
    MachineID INT AUTO_INCREMENT PRIMARY KEY,
    MachineName VARCHAR(100) NOT NULL,
    MachineType ENUM('grinder', 'filler', 'sealer', 'packer', 'quality_control') NOT NULL,
    Location VARCHAR(100),
    InstallationDate DATE,
    MaintenanceInterval INT COMMENT 'Days between maintenance',
    LastMaintenance DATE,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create ProductionBatches table
CREATE TABLE ProductionBatches (
    BatchID INT AUTO_INCREMENT PRIMARY KEY,
    BatchNumber VARCHAR(20) NOT NULL UNIQUE,
    StartDate DATETIME NOT NULL,
    EndDate DATETIME,
    ProductID INT NOT NULL,
    PlannedQuantity INT NOT NULL,
    ActualQuantity INT,
    Status ENUM('planned', 'in_progress', 'completed', 'cancelled') NOT NULL DEFAULT 'planned',
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- Create ProductionSteps table
CREATE TABLE ProductionSteps (
    StepID INT AUTO_INCREMENT PRIMARY KEY,
    BatchID INT NOT NULL,
    StepType ENUM('grinding', 'filling', 'packing') NOT NULL,
    StartTime DATETIME,
    EndTime DATETIME,
    MachineID INT,
    EmployeeID INT,
    QuantityProcessed INT,
    QuantityRejected INT DEFAULT 0,
    Notes TEXT,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (BatchID) REFERENCES ProductionBatches(BatchID),
    FOREIGN KEY (MachineID) REFERENCES Machines(MachineID),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID)
);

-- Create QualityChecks table
CREATE TABLE QualityChecks (
    CheckID INT AUTO_INCREMENT PRIMARY KEY,
    StepID INT NOT NULL,
    CheckTime DATETIME NOT NULL,
    CheckType ENUM('weight', 'fill_level', 'seal_integrity', 'visual_inspection') NOT NULL,
    Result ENUM('OK', 'NOK') NOT NULL,
    MeasuredValue DECIMAL(10,2),
    MinThreshold DECIMAL(10,2),
    MaxThreshold DECIMAL(10,2),
    EmployeeID INT,
    Notes TEXT,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (StepID) REFERENCES ProductionSteps(StepID),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID)
);

-- Create MaterialUsage table
CREATE TABLE MaterialUsage (
    UsageID INT AUTO_INCREMENT PRIMARY KEY,
    BatchID INT NOT NULL,
    MaterialID INT NOT NULL,
    QuantityUsed DECIMAL(10,2) NOT NULL,
    UsageDate DATETIME NOT NULL,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (BatchID) REFERENCES ProductionBatches(BatchID),
    FOREIGN KEY (MaterialID) REFERENCES Materials(MaterialID)
);

-- Create Orders table
CREATE TABLE Orders (
    OrderID INT AUTO_INCREMENT PRIMARY KEY,
    OrderNumber VARCHAR(20) NOT NULL UNIQUE,
    CustomerID INT NOT NULL,
    OrderDate DATETIME NOT NULL,
    DeliveryDate DATETIME,
    Status ENUM('new', 'processing', 'shipped', 'delivered', 'cancelled') NOT NULL DEFAULT 'new',
    Notes TEXT,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID)
);

-- Create OrderItems table
CREATE TABLE OrderItems (
    OrderItemID INT AUTO_INCREMENT PRIMARY KEY,
    OrderID INT NOT NULL,
    ProductID INT NOT NULL,
    Quantity INT NOT NULL,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (OrderID) REFERENCES Orders(OrderID),
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- Create Shipments table
CREATE TABLE Shipments (
    ShipmentID INT AUTO_INCREMENT PRIMARY KEY,
    ShipmentNumber VARCHAR(20) NOT NULL UNIQUE,
    OrderID INT NOT NULL,
    ShipmentDate DATETIME NOT NULL,
    DeliveryDate DATETIME,
    Status ENUM('prepared', 'shipped', 'delivered') NOT NULL DEFAULT 'prepared',
    Notes TEXT,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (OrderID) REFERENCES Orders(OrderID)
);

-- Create views for common queries

-- View for production efficiency
CREATE VIEW ProductionEfficiency AS
SELECT 
    pb.BatchID,
    pb.BatchNumber,
    p.ProductName,
    pb.PlannedQuantity,
    pb.ActualQuantity,
    (pb.ActualQuantity / pb.PlannedQuantity) * 100 AS EfficiencyPercentage,
    SUM(ps.QuantityRejected) AS TotalRejected,
    (SUM(ps.QuantityRejected) / pb.PlannedQuantity) * 100 AS RejectionRate
FROM 
    ProductionBatches pb
JOIN 
    Products p ON pb.ProductID = p.ProductID
JOIN 
    ProductionSteps ps ON pb.BatchID = ps.BatchID
WHERE 
    pb.Status = 'completed'
GROUP BY 
    pb.BatchID, pb.BatchNumber, p.ProductName, pb.PlannedQuantity, pb.ActualQuantity;

-- View for quality control statistics
CREATE VIEW QualityControlStats AS
SELECT 
    ps.StepType,
    COUNT(qc.CheckID) AS TotalChecks,
    SUM(CASE WHEN qc.Result = 'OK' THEN 1 ELSE 0 END) AS OKChecks,
    SUM(CASE WHEN qc.Result = 'NOK' THEN 1 ELSE 0 END) AS NOKChecks,
    (SUM(CASE WHEN qc.Result = 'OK' THEN 1 ELSE 0 END) / COUNT(qc.CheckID)) * 100 AS OKPercentage
FROM 
    QualityChecks qc
JOIN 
    ProductionSteps ps ON qc.StepID = ps.StepID
GROUP BY 
    ps.StepType;

-- View for material consumption
CREATE VIEW MaterialConsumption AS
SELECT 
    m.MaterialName,
    m.MaterialType,
    SUM(mu.QuantityUsed) AS TotalUsed,
    m.UnitOfMeasure,
    COUNT(DISTINCT mu.BatchID) AS BatchCount,
    SUM(mu.QuantityUsed) / COUNT(DISTINCT mu.BatchID) AS AveragePerBatch
FROM 
    MaterialUsage mu
JOIN 
    Materials m ON mu.MaterialID = m.MaterialID
GROUP BY 
    m.MaterialID, m.MaterialName, m.MaterialType, m.UnitOfMeasure;

-- View for order fulfillment
CREATE VIEW OrderFulfillment AS
SELECT 
    o.OrderID,
    o.OrderNumber,
    c.CustomerName,
    o.OrderDate,
    o.DeliveryDate,
    DATEDIFF(o.DeliveryDate, o.OrderDate) AS LeadTimeDays,
    o.Status,
    COUNT(oi.OrderItemID) AS NumberOfItems,
    SUM(oi.Quantity) AS TotalQuantity
FROM 
    Orders o
JOIN 
    Customers c ON o.CustomerID = c.CustomerID
JOIN 
    OrderItems oi ON o.OrderID = oi.OrderID
GROUP BY 
    o.OrderID, o.OrderNumber, c.CustomerName, o.OrderDate, o.DeliveryDate, o.Status;

-- Create indexes for performance optimization
CREATE INDEX idx_production_batches_product ON ProductionBatches(ProductID);
CREATE INDEX idx_production_steps_batch ON ProductionSteps(BatchID);
CREATE INDEX idx_production_steps_machine ON ProductionSteps(MachineID);
CREATE INDEX idx_production_steps_employee ON ProductionSteps(EmployeeID);
CREATE INDEX idx_quality_checks_step ON QualityChecks(StepID);
CREATE INDEX idx_quality_checks_result ON QualityChecks(Result);
CREATE INDEX idx_material_usage_batch ON MaterialUsage(BatchID);
CREATE INDEX idx_material_usage_material ON MaterialUsage(MaterialID);
CREATE INDEX idx_orders_customer ON Orders(CustomerID);
CREATE INDEX idx_order_items_order ON OrderItems(OrderID);
CREATE INDEX idx_order_items_product ON OrderItems(ProductID);
CREATE INDEX idx_shipments_order ON Shipments(OrderID);
