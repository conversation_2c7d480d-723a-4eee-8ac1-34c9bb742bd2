<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.9 Chrome/134.0.6998.205 Electron/35.4.0 Safari/537.36" version="27.0.9">
  <diagram name="Use Case Voorraadsysteem" id="2BdrEmJUut4IFllcqw_Q">
    <mxGraphModel dx="1425" dy="603" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="3XBIm7cWViiK3fwtdbjv-26" value="Voorraadsysteem" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="310" y="720" width="600" height="600" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-27" value="Magazijnbeheerder" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="160" y="740" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-28" value="Productiemedewerker" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="160" y="890" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-29" value="Manager Inkoop" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="160" y="1040" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-30" value="Manager Logistiek" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="160" y="1190" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-31" value="Login" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="690" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-32" value="Voorraad bekijken" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="760" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-33" value="Voorraad aanvullen" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="830" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-34" value="Grondstof afboeken" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="900" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-35" value="Bestelling plaatsen" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="970" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-36" value="Spoedbestelling plaatsen" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="660" y="970" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-37" value="Leveranciers beheren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="1040" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-38" value="Rapporten genereren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="1110" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-39" value="Gebruikers beheren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="1180" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-40" value="Minimale voorraad instellen" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="1250" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-41" value="Bestellingen goedkeuren" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="660" y="1110" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-42" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-27" target="3XBIm7cWViiK3fwtdbjv-31">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="770" as="sourcePoint" />
            <mxPoint x="460" y="770" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-43" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-28" target="3XBIm7cWViiK3fwtdbjv-31">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="920" as="sourcePoint" />
            <mxPoint x="460" y="920" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-44" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-29" target="3XBIm7cWViiK3fwtdbjv-31">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="1070" as="sourcePoint" />
            <mxPoint x="460" y="1070" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-45" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-30" target="3XBIm7cWViiK3fwtdbjv-31">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="1220" as="sourcePoint" />
            <mxPoint x="460" y="1220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-46" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-27" target="3XBIm7cWViiK3fwtdbjv-32">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="770" as="sourcePoint" />
            <mxPoint x="460" y="770" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-47" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-27" target="3XBIm7cWViiK3fwtdbjv-33">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="770" as="sourcePoint" />
            <mxPoint x="460" y="770" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-48" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-28" target="3XBIm7cWViiK3fwtdbjv-34">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="920" as="sourcePoint" />
            <mxPoint x="460" y="920" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-49" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-28" target="3XBIm7cWViiK3fwtdbjv-35">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="920" as="sourcePoint" />
            <mxPoint x="460" y="920" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-50" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-29" target="3XBIm7cWViiK3fwtdbjv-37">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="1070" as="sourcePoint" />
            <mxPoint x="460" y="1070" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-51" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-29" target="3XBIm7cWViiK3fwtdbjv-38">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="1070" as="sourcePoint" />
            <mxPoint x="460" y="1070" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-52" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-30" target="3XBIm7cWViiK3fwtdbjv-39">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="1220" as="sourcePoint" />
            <mxPoint x="460" y="1220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-53" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-30" target="3XBIm7cWViiK3fwtdbjv-40">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="1220" as="sourcePoint" />
            <mxPoint x="460" y="1220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-54" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-30" target="3XBIm7cWViiK3fwtdbjv-41">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="1220" as="sourcePoint" />
            <mxPoint x="460" y="1220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3XBIm7cWViiK3fwtdbjv-55" value="&lt;&lt;extends&gt;&gt;" style="endArrow=open;endFill=1;endSize=12;html=1;rounded=0;dashed=1;" edge="1" parent="1" source="3XBIm7cWViiK3fwtdbjv-36" target="3XBIm7cWViiK3fwtdbjv-35">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="660" y="995" as="sourcePoint" />
            <mxPoint x="580" y="995" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
