{"Author": "", "Name": "EuroCaps Ordering System", "Id": "5af492a0-0e4f-4d5d-9f2f-9f505b5e5384", "FileID": "5af492a0-0e4f-4d5d-9f2f-9f505b5e5384", "LocalConnectionReferences": "{}", "LocalDatabaseReferences": "", "LibraryDependencies": "[]", "AppPreviewFlagsMap": {"delayloadscreens": true, "errorhandling": true, "enableappembeddingux": false, "blockmovingcontrol": true, "projectionmapping": true, "usedisplaynamemetadata": true, "usenonblockingonstartrule": true, "longlivingcache": false, "useguiddatatypes": true, "useexperimentalcdsconnector": true, "useenforcesavedatalimits": true, "webbarcodescanner": false, "reliableconcurrent": true, "datatablev2control": true, "nativecdsexperimental": true, "delaycontrolrendering": true, "useexperimentalsqlconnector": true, "disablecdsfileandlargeimage": false, "formuladataprefetch": true, "generatedebugpublishedapp": false, "keeprecentscreensloaded": false, "aibuilderserviceenrollment": false, "dynamicschema": false, "enablerowscopeonetonexpand": false, "herocontrols": false, "classiccontrols": false, "fluentv9controls": false, "fluentv9controlspreview": false, "onegrid": false, "enablepcfmoderndatasets": true, "optimizedforteamsmeeting": false, "behaviorpropertyui": true, "autocreateenvironmentvariables": false, "externalmessage": false, "enableonstartnavigate": false, "enableexcelonlinebusinessv2connector": true, "enableonstart": true, "exportimportcomponents2": true, "copyandmerge": false, "allowmultiplescreensincanvaspages": false, "enablecomponentscopeoldbehavior": false, "enablecomponentnamemaps": false, "enableideaspanel": true, "enablecreateaformula": true, "enablesaveloadcleardataonweb": true, "rtlsupport": false, "improvedtabstopbehavior": false, "rtlinstudiopreview": false, "cdsdataformatting": false, "enablerpawarecomponentdependency": true, "enableeditcacherefreshfrequency": false, "formularepair": false, "expandedsavedatasupport": true, "appinsightserrortracing": false, "appinstrumentationcorrelationtracing": false, "powerfxv1": false, "zeroalltabindexes": true, "dataverseactionsenabled": true, "pdffunction": false, "packagemodernruntime": false, "enablecopilotcontrol": true, "enablecopilotanswercontrol": true, "disableruntimepolicies": false, "enablelegacybarcodescanner": false, "enabledataverseoffline": false, "mobilenativerendering": false, "smartemaildatacard": false, "enableupdateifdelegation": true, "dataflowanalysisenabled": true, "showclassicthemes": false, "userdefinedfunctions": false, "userdefinedtypes": false, "enableeditinmcs": true, "disablebehaviorreturntypecheck": false, "commentgeneratedformulasv2": true, "enablecanvasappruntimecopilot": true, "offlineprofilegenerationemitscolumns": false, "proactivecontrolrename": true, "preferpayamlv2inux": false, "disablefallbacktopayamlv2": false, "enablelegacydatatable": false, "sharepointselectsenabled": false, "disableloadcomponentdefinitionsondemand": false, "enableideaspanelbyexample": false, "adaptivepaging": false, "optimizestartscreenpublishedappload": false, "tabledoesntwraprecords": true, "consistentreturnschemafortabularfunctions": true, "supportcolumnnamesasidentifiers": true, "powerfxdecimal": false, "reservedkeywords": false, "isemptyrequirestableargument": true, "primaryoutputpropertycoerciondeprecated": true}, "DocumentLayoutWidth": 1366, "DocumentLayoutHeight": 768, "DocumentLayoutOrientation": "landscape", "DocumentLayoutScaleToFit": true, "DocumentLayoutMaintainAspectRatio": true, "DocumentLayoutLockOrientation": false, "ShowStatusBar": false, "AppCopilotSchemaName": null, "OriginatingVersion": "1.345", "DocumentAppType": "DesktopOrTablet", "DocumentType": "App", "AppCreationSource": "AppFromScratch", "AppDescription": "", "ManualOfflineProfileId": null, "DefaultConnectedDataSourceMaxGetRowsCount": 500, "ContainsThirdPartyPcfControls": false, "ParserErrorCount": 0, "BindingErrorCount": 0, "ConnectionString": "", "EnableInstrumentation": false, "ControlCount": {"screen": 1, "rectangle": 2, "label": 2, "text": 1, "button": 1}, "AnalysisLoadTime": 0.0929471}