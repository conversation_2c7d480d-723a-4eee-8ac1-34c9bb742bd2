<!DOCTYPE html>
<html>
<head>
    <title>Fysiek ERD Voorbeeld</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: white;
        }
        .container {
            width: 1000px;
            height: 900px;
            border: 1px solid #ccc;
            position: relative;
            background-color: white;
        }
        .title {
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            margin-top: 20px;
            margin-bottom: 40px;
        }
        .table {
            position: absolute;
            border: 1px solid black;
            background-color: white;
            z-index: 2;
        }
        .table-header {
            text-align: center;
            font-weight: bold;
            padding: 8px;
            border-bottom: 1px solid black;
            background-color: #f0f0f0;
        }
        .table-content {
            display: table;
            width: 100%;
        }
        .table-row {
            display: table-row;
            border-bottom: 1px solid #ddd;
        }
        .column-name {
            display: table-cell;
            padding: 5px;
            border-right: 1px solid #ddd;
            width: 120px;
        }
        .datatype {
            display: table-cell;
            padding: 5px;
            border-right: 1px solid #ddd;
            width: 100px;
            font-family: monospace;
            font-size: 12px;
        }
        .constraints {
            display: table-cell;
            padding: 5px;
            font-size: 12px;
        }
        .pk {
            font-weight: bold;
        }
        .fk {
            font-style: italic;
        }
        .relationship {
            position: absolute;
            font-size: 14px;
            z-index: 3;
            background-color: white;
            padding: 0 5px;
        }
        .note {
            position: absolute;
            bottom: 20px;
            left: 20px;
            font-size: 14px;
            width: 960px;
        }
        .legend {
            position: absolute;
            border: 1px solid #ccc;
            padding: 10px;
            background-color: #f9f9f9;
            width: 300px;
            z-index: 2;
        }
        .legend-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .legend-item {
            margin: 5px 0;
            font-size: 12px;
        }
        /* SVG styles for better lines */
        svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">Fysiek ERD Voorbeeld - Webwinkel</div>
        
        <!-- SVG for lines and relationship symbols -->
        <svg>
            <!-- KLANT to BESTELLING -->
            <path d="M 350,180 H 550" stroke="black" stroke-width="1" fill="none"/>
            
            <!-- BESTELLING to BESTELLING_PRODUCT -->
            <path d="M 650,250 H 500 V 350" stroke="black" stroke-width="1" fill="none"/>
            
            <!-- PRODUCT to BESTELLING_PRODUCT -->
            <path d="M 650,550 H 500 V 450" stroke="black" stroke-width="1" fill="none"/>
            
            <!-- CATEGORIE to PRODUCT -->
            <path d="M 350,550 H 550" stroke="black" stroke-width="1" fill="none"/>
            
            <!-- Relationship symbols -->
            <!-- KLANT to BESTELLING (1 to many) -->
            <line x1="340" y1="180" x2="360" y2="180" stroke="black" stroke-width="1.5"/>
            <line x1="340" y1="170" x2="340" y2="190" stroke="black" stroke-width="1.5"/>
            <line x1="540" y1="170" x2="540" y2="190" stroke="black" stroke-width="1.5"/>
            <path d="M 540,180 L 560,180" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 550,175 L 560,180 L 550,185" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- BESTELLING to BESTELLING_PRODUCT (1 to many) -->
            <line x1="650" y1="240" x2="650" y2="260" stroke="black" stroke-width="1.5"/>
            <line x1="640" y1="240" x2="660" y2="240" stroke="black" stroke-width="1.5"/>
            <line x1="490" y1="350" x2="510" y2="350" stroke="black" stroke-width="1.5"/>
            <path d="M 500,340 L 500,360" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 495,350 L 505,360" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- PRODUCT to BESTELLING_PRODUCT (1 to many) -->
            <line x1="650" y1="540" x2="650" y2="560" stroke="black" stroke-width="1.5"/>
            <line x1="640" y1="540" x2="660" y2="540" stroke="black" stroke-width="1.5"/>
            <line x1="490" y1="450" x2="510" y2="450" stroke="black" stroke-width="1.5"/>
            <path d="M 500,440 L 500,460" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 495,450 L 505,440" stroke="black" stroke-width="1.5" fill="none"/>
            
            <!-- CATEGORIE to PRODUCT (1 to many) -->
            <line x1="340" y1="550" x2="360" y2="550" stroke="black" stroke-width="1.5"/>
            <line x1="340" y1="540" x2="340" y2="560" stroke="black" stroke-width="1.5"/>
            <line x1="540" y1="540" x2="540" y2="560" stroke="black" stroke-width="1.5"/>
            <path d="M 540,550 L 560,550" stroke="black" stroke-width="1.5" fill="none"/>
            <path d="M 550,545 L 560,550 L 550,555" stroke="black" stroke-width="1.5" fill="none"/>
        </svg>
        
        <!-- Entities with physical table structure -->
        <div class="table" style="top: 100px; left: 100px; width: 250px;">
            <div class="table-header">KLANT</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="column-name pk">klant_id</div>
                    <div class="datatype">INT</div>
                    <div class="constraints">PK, AUTO_INCREMENT</div>
                </div>
                <div class="table-row">
                    <div class="column-name">voornaam</div>
                    <div class="datatype">VARCHAR(50)</div>
                    <div class="constraints">NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">achternaam</div>
                    <div class="datatype">VARCHAR(50)</div>
                    <div class="constraints">NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">email</div>
                    <div class="datatype">VARCHAR(100)</div>
                    <div class="constraints">UNIQUE, NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">telefoonnummer</div>
                    <div class="datatype">VARCHAR(15)</div>
                    <div class="constraints">NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">adres</div>
                    <div class="datatype">VARCHAR(100)</div>
                    <div class="constraints">NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">postcode</div>
                    <div class="datatype">VARCHAR(10)</div>
                    <div class="constraints">NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">woonplaats</div>
                    <div class="datatype">VARCHAR(50)</div>
                    <div class="constraints">NOT NULL</div>
                </div>
            </div>
        </div>
        
        <div class="table" style="top: 100px; left: 550px; width: 300px;">
            <div class="table-header">BESTELLING</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="column-name pk">bestelling_id</div>
                    <div class="datatype">INT</div>
                    <div class="constraints">PK, AUTO_INCREMENT</div>
                </div>
                <div class="table-row">
                    <div class="column-name fk">klant_id</div>
                    <div class="datatype">INT</div>
                    <div class="constraints">FK (KLANT), NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">datum</div>
                    <div class="datatype">DATETIME</div>
                    <div class="constraints">NOT NULL, DEFAULT CURRENT_TIMESTAMP</div>
                </div>
                <div class="table-row">
                    <div class="column-name">status</div>
                    <div class="datatype">ENUM</div>
                    <div class="constraints">NOT NULL, DEFAULT 'nieuw'</div>
                </div>
                <div class="table-row">
                    <div class="column-name">totaalbedrag</div>
                    <div class="datatype">DECIMAL(10,2)</div>
                    <div class="constraints">NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">verzendkosten</div>
                    <div class="datatype">DECIMAL(6,2)</div>
                    <div class="constraints">NOT NULL, DEFAULT 0.00</div>
                </div>
                <div class="table-row">
                    <div class="column-name">betaalmethode</div>
                    <div class="datatype">VARCHAR(20)</div>
                    <div class="constraints">NOT NULL</div>
                </div>
            </div>
        </div>
        
        <div class="table" style="top: 470px; left: 100px; width: 250px;">
            <div class="table-header">CATEGORIE</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="column-name pk">categorie_id</div>
                    <div class="datatype">INT</div>
                    <div class="constraints">PK, AUTO_INCREMENT</div>
                </div>
                <div class="table-row">
                    <div class="column-name">naam</div>
                    <div class="datatype">VARCHAR(50)</div>
                    <div class="constraints">UNIQUE, NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">beschrijving</div>
                    <div class="datatype">TEXT</div>
                    <div class="constraints">NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name fk">hoofdcategorie_id</div>
                    <div class="datatype">INT</div>
                    <div class="constraints">FK (CATEGORIE), NULL</div>
                </div>
            </div>
        </div>
        
        <div class="table" style="top: 470px; left: 550px; width: 300px;">
            <div class="table-header">PRODUCT</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="column-name pk">product_id</div>
                    <div class="datatype">INT</div>
                    <div class="constraints">PK, AUTO_INCREMENT</div>
                </div>
                <div class="table-row">
                    <div class="column-name fk">categorie_id</div>
                    <div class="datatype">INT</div>
                    <div class="constraints">FK (CATEGORIE), NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">naam</div>
                    <div class="datatype">VARCHAR(100)</div>
                    <div class="constraints">NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">beschrijving</div>
                    <div class="datatype">TEXT</div>
                    <div class="constraints">NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">prijs</div>
                    <div class="datatype">DECIMAL(10,2)</div>
                    <div class="constraints">NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">voorraad</div>
                    <div class="datatype">INT</div>
                    <div class="constraints">NOT NULL, DEFAULT 0</div>
                </div>
                <div class="table-row">
                    <div class="column-name">gewicht</div>
                    <div class="datatype">DECIMAL(6,2)</div>
                    <div class="constraints">NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">afmetingen</div>
                    <div class="datatype">VARCHAR(50)</div>
                    <div class="constraints">NULL</div>
                </div>
            </div>
        </div>
        
        <div class="table" style="top: 350px; left: 300px; width: 300px;">
            <div class="table-header">BESTELLING_PRODUCT</div>
            <div class="table-content">
                <div class="table-row">
                    <div class="column-name pk fk">bestelling_id</div>
                    <div class="datatype">INT</div>
                    <div class="constraints">PK, FK (BESTELLING), NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name pk fk">product_id</div>
                    <div class="datatype">INT</div>
                    <div class="constraints">PK, FK (PRODUCT), NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">aantal</div>
                    <div class="datatype">INT</div>
                    <div class="constraints">NOT NULL, DEFAULT 1</div>
                </div>
                <div class="table-row">
                    <div class="column-name">prijs_per_stuk</div>
                    <div class="datatype">DECIMAL(10,2)</div>
                    <div class="constraints">NOT NULL</div>
                </div>
                <div class="table-row">
                    <div class="column-name">subtotaal</div>
                    <div class="datatype">DECIMAL(10,2)</div>
                    <div class="constraints">NOT NULL</div>
                </div>
            </div>
        </div>
        
        <!-- Relationships -->
        <div class="relationship" style="top: 160px; left: 420px;">plaatst</div>
        <div class="relationship" style="top: 300px; left: 660px;">bevat</div>
        <div class="relationship" style="top: 480px; left: 510px;">bevat</div>
        <div class="relationship" style="top: 530px; left: 420px;">behoort tot</div>
        
        <!-- Legend -->
        <div class="legend" style="top: 700px; left: 350px;">
            <div class="legend-title">Fysiek ERD Legenda</div>
            <div class="legend-item"><strong>PK</strong> - Primaire sleutel</div>
            <div class="legend-item"><em>FK</em> - Vreemde sleutel (Foreign Key)</div>
            <div class="legend-item"><strong>NOT NULL</strong> - Waarde mag niet leeg zijn</div>
            <div class="legend-item"><strong>UNIQUE</strong> - Waarde moet uniek zijn in de tabel</div>
            <div class="legend-item"><strong>DEFAULT</strong> - Standaardwaarde als geen waarde wordt opgegeven</div>
            <div class="legend-item"><strong>AUTO_INCREMENT</strong> - Waarde wordt automatisch verhoogd</div>
        </div>
        
        <!-- Note -->
        <div class="note">
            <strong>Opmerking:</strong> Een fysiek ERD bouwt voort op het logische ERD door technische details toe te voegen zoals datatypes, 
            lengtebeperkingen, indexen en andere implementatiespecifieke elementen. Het fysieke ERD is direct gerelateerd aan de daadwerkelijke 
            implementatie in een specifiek databasemanagementsysteem (zoals MySQL, PostgreSQL, SQL Server, etc.).
        </div>
    </div>
    
    <div style="margin-top: 20px;">
        <h2>Verschillen tussen Logisch en Fysiek ERD</h2>
        <ul>
            <li><strong>Datatypes:</strong> Het fysieke ERD specificeert exacte datatypes (VARCHAR, INT, DECIMAL, etc.) en lengtebeperkingen.</li>
            <li><strong>Constraints:</strong> Het fysieke ERD bevat constraints zoals NOT NULL, UNIQUE, DEFAULT waarden, etc.</li>
            <li><strong>Vreemde sleutels:</strong> Het fysieke ERD toont expliciet de vreemde sleutels en hun relaties.</li>
            <li><strong>Indexen:</strong> Het fysieke ERD kan indexen bevatten voor performantie-optimalisatie.</li>
            <li><strong>Implementatiedetails:</strong> Het fysieke ERD bevat specifieke details voor het gekozen databasemanagementsysteem.</li>
        </ul>
    </div>
</body>
</html>
