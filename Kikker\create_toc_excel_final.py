from openpyxl import Workbook
from openpyxl.chart import <PERSON><PERSON><PERSON>, Reference
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.chart.label import DataLabelList
import os

def create_toc_excel():
    # Gebruik een nieuwe bestandsnaam
    output_file = 'TOC_Analyse_Compleet.xlsx'
    if os.path.exists(output_file):
        try:
            os.remove(output_file)
            print(f"Bestaand bestand {output_file} verwijderd")
        except:
            print(f"Kon bestaand bestand {output_file} niet verwijderen")

    # Maak een Excel workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "TOC Analyse"

    # Definieer stijlen
    header_font = Font(bold=True, size=12)
    normal_font = Font(size=11)

    # Definieer borders
    thin_border = Border(left=Side(style='thin'),
                         right=Side(style='thin'),
                         top=Side(style='thin'),
                         bottom=Side(style='thin'))

    # Definieer vulkleuren
    header_fill = PatternFill(start_color="8EAADB", end_color="8EAADB", fill_type="solid")
    subheader_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
    data_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    bottleneck_fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")

    # Maak de tabelheaders
    ws.cell(row=1, column=1, value="Column1").font = header_font
    ws.cell(row=1, column=1).fill = header_fill
    ws.cell(row=1, column=1).alignment = Alignment(horizontal='left')
    ws.cell(row=1, column=1).border = thin_border

    ws.cell(row=1, column=2, value="Column2").font = header_font
    ws.cell(row=1, column=2).fill = header_fill
    ws.cell(row=1, column=2).alignment = Alignment(horizontal='left')
    ws.cell(row=1, column=2).border = thin_border

    # Titel
    ws.cell(row=2, column=1, value="TOC ANALYSE").font = header_font
    ws.cell(row=2, column=1).fill = subheader_fill
    ws.cell(row=2, column=1).border = thin_border
    ws.cell(row=2, column=2).fill = subheader_fill
    ws.cell(row=2, column=2).border = thin_border

    # Subtitel
    ws.cell(row=3, column=1, value="Procestijd Analyse").font = Font(italic=True)
    ws.cell(row=3, column=1).fill = subheader_fill
    ws.cell(row=3, column=1).border = thin_border
    ws.cell(row=3, column=2).fill = subheader_fill
    ws.cell(row=3, column=2).border = thin_border

    # Lege rij
    ws.cell(row=4, column=1).fill = subheader_fill
    ws.cell(row=4, column=1).border = thin_border
    ws.cell(row=4, column=2).fill = subheader_fill
    ws.cell(row=4, column=2).border = thin_border

    # Procestijd Analyse
    ws.cell(row=5, column=1, value="Procestijd Analyse:").font = header_font
    ws.cell(row=5, column=1).fill = subheader_fill
    ws.cell(row=5, column=1).border = thin_border
    ws.cell(row=5, column=2).fill = subheader_fill
    ws.cell(row=5, column=2).border = thin_border

    # Procestijd data
    process_data = [
        ("- Grinding", 0.34),
        ("- Filling", 0.25),
        ("- Packaging", 0.50)
    ]

    for i, (label, value) in enumerate(process_data):
        row = 6 + i
        ws.cell(row=row, column=1, value=label).font = normal_font
        ws.cell(row=row, column=1).border = thin_border
        ws.cell(row=row, column=2, value=value).font = normal_font
        # Markeer de bottleneck (Packaging) met een gele achtergrond
        if label == "- Packaging":
            ws.cell(row=row, column=2).fill = bottleneck_fill
        else:
            ws.cell(row=row, column=2).fill = data_fill
        ws.cell(row=row, column=2).border = thin_border

    # Lege rij
    ws.cell(row=9, column=1).fill = subheader_fill
    ws.cell(row=9, column=1).border = thin_border
    ws.cell(row=9, column=2).fill = subheader_fill
    ws.cell(row=9, column=2).border = thin_border

    # Bottleneck Identificatie
    ws.cell(row=10, column=1, value="Bottleneck Identificatie:").font = header_font
    ws.cell(row=10, column=1).fill = subheader_fill
    ws.cell(row=10, column=1).border = thin_border
    ws.cell(row=10, column=2).fill = subheader_fill
    ws.cell(row=10, column=2).border = thin_border

    # Bottleneck identificatie data
    bottleneck_data = [
        ("- Bottleneck proces", "Packaging"),
        ("- Gemiddelde procestijd", 0.50)
    ]

    for i, (label, value) in enumerate(bottleneck_data):
        row = 11 + i
        ws.cell(row=row, column=1, value=label).font = normal_font
        ws.cell(row=row, column=1).border = thin_border
        ws.cell(row=row, column=2, value=value).font = normal_font
        # Markeer de bottleneck rij met een gele achtergrond
        if label == "- Bottleneck proces":
            ws.cell(row=row, column=2).fill = bottleneck_fill
        else:
            ws.cell(row=row, column=2).fill = data_fill
        ws.cell(row=row, column=2).border = thin_border

    # Lege rij
    ws.cell(row=14, column=1).fill = subheader_fill
    ws.cell(row=14, column=1).border = thin_border
    ws.cell(row=14, column=2).fill = subheader_fill
    ws.cell(row=14, column=2).border = thin_border

    # Energieverbruik per Verpakkingsmachine
    ws.cell(row=15, column=1, value="Energieverbruik per Verpakkingsmachine:").font = header_font
    ws.cell(row=15, column=1).fill = subheader_fill
    ws.cell(row=15, column=1).border = thin_border
    ws.cell(row=15, column=2).fill = subheader_fill
    ws.cell(row=15, column=2).border = thin_border

    # Energieverbruik data
    energy_data = [
        ("- Packager 1", 277.56),
        ("- Packager 2", 273.17),
        ("- Packager 3", 275.83),
        ("- Packager 4", 278.37),
        ("- Packager 5", 275.95)
    ]

    for i, (label, value) in enumerate(energy_data):
        row = 16 + i
        ws.cell(row=row, column=1, value=label).font = normal_font
        ws.cell(row=row, column=1).border = thin_border
        ws.cell(row=row, column=2, value=value).font = normal_font
        ws.cell(row=row, column=2).fill = data_fill
        ws.cell(row=row, column=2).border = thin_border

    # Pas kolombreedtes aan
    ws.column_dimensions['A'].width = 40
    ws.column_dimensions['B'].width = 15
    ws.column_dimensions['C'].width = 15

    # 1. Maak een staafdiagram voor procestijden
    process_chart = BarChart()
    process_chart.title = "Procestijden"
    process_chart.y_axis.title = "Tijd (uur)"
    process_chart.x_axis.title = "Proces"
    process_chart.height = 10  # Hoogte van het diagram
    process_chart.width = 15   # Breedte van het diagram

    # Definieer de data voor het staafdiagram direct vanuit de tabel
    # Gebruik de waarden uit kolom B (rij 6-8)
    process_data = Reference(ws, min_col=2, min_row=6, max_row=8, max_col=2)

    # Gebruik de labels uit kolom A (rij 6-8)
    process_cats = Reference(ws, min_col=1, min_row=6, max_row=8)

    # Voeg de data toe aan het staafdiagram
    process_chart.add_data(process_data)
    process_chart.set_categories(process_cats)

    # Voeg data labels toe om de exacte waarden te tonen
    process_chart.dataLabels = DataLabelList()
    process_chart.dataLabels.showVal = True  # Toon de waarden
    process_chart.dataLabels.showCatName = False  # Verberg categorienamen
    process_chart.dataLabels.showSerName = False  # Verberg serienamen
    process_chart.dataLabels.showPercent = False  # Verberg percentages
    process_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels

    # Pas de stijl aan
    process_chart.style = 10  # Kies een stijl

    # Voeg het staafdiagram toe aan het TOC Analyse werkblad naast de tabel
    ws.add_chart(process_chart, "D5")

    # 2. Maak een eenvoudige visualisatie voor bottleneck identificatie
    # We maken een staafdiagram met alleen de bottleneck
    bottleneck_chart = BarChart()
    bottleneck_chart.title = "Bottleneck Identificatie"
    bottleneck_chart.y_axis.title = "Procestijd (uur)"
    bottleneck_chart.x_axis.title = "Proces"
    bottleneck_chart.height = 10  # Hoogte van het diagram
    bottleneck_chart.width = 15   # Breedte van het diagram

    # Definieer de data voor het staafdiagram direct vanuit de tabel
    # We gebruiken de gemiddelde procestijd uit de tabel
    bottleneck_data_ref = Reference(ws, min_col=2, min_row=12, max_row=12, max_col=2)

    # We gebruiken de bottleneck proces naam als categorie
    bottleneck_cats = Reference(ws, min_col=2, min_row=11, max_row=11)

    # Voeg de data toe aan het staafdiagram
    bottleneck_chart.add_data(bottleneck_data_ref)
    bottleneck_chart.set_categories(bottleneck_cats)

    # Voeg data labels toe om de exacte waarden te tonen
    bottleneck_chart.dataLabels = DataLabelList()
    bottleneck_chart.dataLabels.showVal = True  # Toon de waarden
    bottleneck_chart.dataLabels.showCatName = False  # Verberg categorienamen
    bottleneck_chart.dataLabels.showSerName = False  # Verberg serienamen
    bottleneck_chart.dataLabels.showPercent = False  # Verberg percentages
    bottleneck_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels

    # Pas de stijl aan
    bottleneck_chart.style = 11  # Kies een andere stijl

    # Voeg het staafdiagram toe aan het TOC Analyse werkblad naast de tabel
    ws.add_chart(bottleneck_chart, "D10")

    # 3. Maak een staafdiagram voor energieverbruik
    energy_chart = BarChart()
    energy_chart.title = "Energieverbruik per Verpakkingsmachine"
    energy_chart.y_axis.title = "Energieverbruik (kWh)"
    energy_chart.x_axis.title = "Verpakkingsmachine"
    energy_chart.height = 15  # Hoogte van het diagram
    energy_chart.width = 20   # Breedte van het diagram

    # Definieer de data voor het staafdiagram direct vanuit de tabel
    # Gebruik de waarden uit kolom B (rij 16-20)
    energy_data_ref = Reference(ws, min_col=2, min_row=16, max_row=20, max_col=2)

    # Gebruik de labels uit kolom A (rij 16-20)
    energy_cats = Reference(ws, min_col=1, min_row=16, max_row=20)

    # Voeg de data toe aan het staafdiagram
    energy_chart.add_data(energy_data_ref)
    energy_chart.set_categories(energy_cats)

    # Voeg data labels toe om de exacte waarden te tonen
    energy_chart.dataLabels = DataLabelList()
    energy_chart.dataLabels.showVal = True  # Toon de waarden
    energy_chart.dataLabels.showCatName = False  # Verberg categorienamen
    energy_chart.dataLabels.showSerName = False  # Verberg serienamen
    energy_chart.dataLabels.showPercent = False  # Verberg percentages
    energy_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels

    # Pas de stijl aan
    energy_chart.style = 12  # Kies een andere stijl

    # Voeg het staafdiagram toe aan het TOC Analyse werkblad naast de tabel
    ws.add_chart(energy_chart, "D15")

    # Sla het Excel bestand op
    try:
        wb.save(output_file)
        print(f"Excel bestand met visualisaties is opgeslagen als {output_file}")
    except Exception as e:
        print(f"Fout bij opslaan van bestand: {e}")
        # Als er een fout is, probeer op te slaan in een tijdelijke map
        import tempfile
        temp_dir = tempfile.gettempdir()
        temp_file = os.path.join(temp_dir, 'TOC_Analyse_Final.xlsx')
        wb.save(temp_file)
        print(f"Excel bestand met visualisaties is opgeslagen als {temp_file}")

if __name__ == "__main__":
    create_toc_excel()
