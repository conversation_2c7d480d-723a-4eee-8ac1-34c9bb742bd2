# Customer List Screen - EuroCaps Ordering System

## Screen Layout

```
+---------------------------------------------------------------+
| [Logo] EuroCaps Ordering System           [User ▼] [Settings] |
+---------------------------------------------------------------+
| [≡ MENU]  | Customers                       [+ NEW CUSTOMER]  |
|           |                                                    |
| Dashboard | [Search Customers...                      ] [🔍]  |
| Customers |                                                    |
| Products  | FILTERS: [All ▼] [Sort: Name ▼] [Reset Filters]   |
| Orders    |                                                    |
| Reports   | +------------------------------------------------+ |
|           | | Name          | Contact       | Email    | Actions |
| Settings  | |------------------------------------------------| |
| Logout    | | Bean Lovers   | <PERSON>    | john@... | [👁️][📝][🛒] |
|           | | Café Express  | Maria Garcia  | maria@.. | [👁️][📝][🛒] |
|           | | Coffee World  | David Lee     | david@.. | [👁️][📝][🛒] |
|           | | Morning Brew  | Sarah Johnson | sarah@.. | [👁️][📝][🛒] |
|           | | The Daily Cup | Robert Brown  | robert@. | [👁️][📝][🛒] |
|           | | Coffee Corner | Emma Wilson   | emma@... | [👁️][📝][🛒] |
|           | | Espresso Elite| Michael Chen  | michael. | [👁️][📝][🛒] |
|           | | Fresh Grounds | Lisa Taylor   | lisa@... | [👁️][📝][🛒] |
|           | | Java Junction | Thomas White  | thomas@. | [👁️][📝][🛒] |
|           | | Perfect Pour  | Amanda Black  | amanda@. | [👁️][📝][🛒] |
|           | +------------------------------------------------+ |
|           |                                                    |
|           | [◀ Previous]                          [Next ▶]    |
|           | Showing 1-10 of 24 customers                      |
|           |                                                    |
+---------------------------------------------------------------+
```

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Menu sidebar: Dark blue (#3a5a80)
- Background: Light gray (#f5f5f5)
- Table: White (#ffffff)
- Action buttons:
  - View: Blue (#4a6fa5)
  - Edit: Orange (#ff9800)
  - New Order: Green (#4caf50)

### Typography
- Header: Arial, 16pt, Bold, White
- Menu items: Arial, 14pt, White
- Page title: Arial, 18pt, Bold, Dark gray
- Table headers: Arial, 12pt, Bold
- Table content: Arial, 12pt
- Button text: Arial, 14pt, Bold, White

### Components

1. **Header Bar**
   - EuroCaps logo (left-aligned)
   - Application title
   - User profile dropdown (right-aligned)
   - Settings icon (right-aligned)

2. **Navigation Menu**
   - Vertical sidebar with menu items
   - "Customers" highlighted
   - Icons for each menu item

3. **Action Bar**
   - Page title "Customers"
   - "New Customer" button (right-aligned)

4. **Search and Filter Section**
   - Search input with search icon
   - Filter dropdown for customer type
   - Sort dropdown (Name, Recent, etc.)
   - Reset filters button

5. **Customer Table**
   - Sortable columns
   - Alternating row colors for readability
   - Action buttons in last column:
     - View (eye icon)
     - Edit (pencil icon)
     - New Order (cart icon)

6. **Pagination**
   - Previous/Next buttons
   - Page indicator
   - Items per page selector (optional)

## Interactions

1. **Search Functionality**
   - Real-time filtering as user types
   - Search across all customer fields

2. **Filtering and Sorting**
   - Filter dropdown to show specific customer types
   - Sort dropdown to order by different fields
   - Reset button clears all filters and search

3. **Customer Actions**
   - View: Navigate to Customer Details screen
   - Edit: Navigate to Edit Customer screen
   - New Order: Navigate to New Order screen with customer pre-selected

4. **New Customer Button**
   - Navigate to Add Customer screen

5. **Pagination**
   - Previous/Next buttons navigate between pages
   - Disable Previous on first page
   - Disable Next on last page

## Accessibility Considerations
- Clear visual hierarchy
- Consistent navigation patterns
- Icon buttons have text labels on hover
- Sufficient contrast for all text elements

## Notes for Implementation
- Table should be sortable by clicking column headers
- Consider adding export functionality (Excel, CSV)
- For prototype: Use mock data for customer list
- Implement client-side pagination for the prototype
