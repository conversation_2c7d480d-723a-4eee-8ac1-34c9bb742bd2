{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1043{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Arial;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 Aanpassing voor Klantretourpercentage - Beperking tot 100%\b0\fs22\par

\pard\sa200\sl276\slmult1\fs24 Dit document beschrijft de aanpassing die is gemaakt om de Klantretourpercentage-waarden te beperken tot maximaal 100%.\fs22\par

\pard\sa200\sl276\slmult1\b\fs26 1. Geïdentificeerde Probleem\b0\fs22\par
Na de eerdere aanpassing waarbij de Klantretourpercentage-waarden werden omgezet naar percentages zonder decimalen, waren er nog steeds waarden boven de 100% (tot 200%). Dit is onlogisch voor een klantretourpercentage, aangezien percentages normaal gesproken niet boven de 100% uitkomen. Het doel was om alle waarden te beperken tot maximaal 100%.\par

\pard\sa200\sl276\slmult1\b\fs26 2. Aanpassing aan het Script\b0\fs22\par
Er is een speciaal script \i fix_klantretourpercentage_max100.py\i0 gemaakt om de Klantretourpercentage-waarden te beperken tot maximaal 100%:\par

\i import pandas as pd\par
\par
# Laad de dataset\par
df = pd.read_csv('Kikker_cleaned_percentage.csv')\par
\par
# Tel hoeveel waarden boven 100% zijn\par
above_100_count = (df['Klantretourpercentage'] > 100).sum()\par
print(f"\\nAantal waarden boven 100%: \{above_100_count\}")\par
\par
# Beperk alle waarden tot maximaal 100%\par
df.loc[df['Klantretourpercentage'] > 100, 'Klantretourpercentage'] = 100\i0\par

Deze code doet het volgende:\par
\bullet Telt het aantal waarden dat boven 100% ligt\par
\bullet Vervangt alle waarden boven 100% door 100%\par

\pard\sa200\sl276\slmult1\b\fs26 3. Resultaten\b0\fs22\par
Na het uitvoeren van het aangepaste script zijn de Klantretourpercentage-waarden als volgt aangepast:\par

\bullet 3691 waarden boven 100% zijn beperkt tot 100%\par

\b Vóór de aanpassing (voorbeeld van waarden):\b0\par
\bullet 10%\par
\bullet 50%\par
\bullet 100%\par
\bullet 110%\par
\bullet 150%\par
\bullet 200%\par

\b Na de aanpassing (voorbeeld van waarden):\b0\par
\bullet 10%\par
\bullet 50%\par
\bullet 100%\par
\bullet 100% (was 110%)\par
\bullet 100% (was 150%)\par
\bullet 100% (was 200%)\par

\b Statistieken van de aangepaste Klantretourpercentage-kolom:\b0\par
\bullet Minimum: 10%\par
\bullet Maximum: 100%\par
\bullet Gemiddelde: 82.85%\par
\bullet Mediaan: 100%\par
\bullet 25e percentiel: 70%\par
\bullet 75e percentiel: 100%\par
\bullet Standaarddeviatie: 24.30%\par

\b Verdeling van Klantretourpercentage (top 5):\b0\par
\bullet 100%: 4428 waarden (inclusief 3691 waarden die eerder boven 100% waren)\par
\bullet 80%: 687 waarden\par
\bullet 90%: 587 waarden\par
\bullet 60%: 535 waarden\par
\bullet 70%: 486 waarden\par

\pard\sa200\sl276\slmult1\b\fs26 4. Verificatie\b0\fs22\par
De aangepaste Klantretourpercentage-waarden zijn nu veel logischer en realistischer:\par

\bullet \b Geen waarden boven 100%:\b0 Alle waarden zijn nu beperkt tot maximaal 100%, wat logisch is voor percentages.\par
\bullet \b Realistische percentages:\b0 De waarden vertegenwoordigen nu realistische klantretourpercentages.\par
\bullet \b Consistente notatie:\b0 Alle waarden zijn nog steeds gehele getallen zonder decimalen.\par
\bullet \b Logische interpretatie:\b0 De waarden zijn nu gemakkelijker te interpreteren als realistische percentages.\par

\pard\sa200\sl276\slmult1\b\fs26 5. Conclusie\b0\fs22\par
De aanpassing voor de Klantretourpercentage-kolom heeft succesvol alle waarden beperkt tot maximaal 100%. Dit maakt de dataset logischer en realistischer voor analyses en visualisaties.\par

Deze aanpassing draagt bij aan een nog betere datakwaliteit voor de analyse van het productieproces van Americaps koffiecapsules, en zorgt ervoor dat de analyses en conclusies gebaseerd op de Klantretourpercentage-kolom betrouwbaarder zijn.\par

\pard\sa200\sl276\slmult1\i\fs20 Deze aanpassing is gemaakt in aanvulling op de eerder beschreven opschoningsstappen, en is specifiek gericht op het verbeteren van de logica van de Klantretourpercentage-kolom in de dataset.\i0\fs22\par
}
