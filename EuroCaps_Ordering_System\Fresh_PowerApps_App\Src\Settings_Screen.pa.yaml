Settings_Screen As screen:
    Fill: =RGBA(245, 245, 245, 1)
    LoadingSpinnerColor: =RGBA(74, 111, 165, 1)

    HeaderBar As rectangle:
        Fill: =RGBA(74, 111, 165, 1)
        Height: =60
        Width: =Parent.Width
        X: =0
        Y: =0

    HeaderTitle As label:
        Color: =RGBA(255, 255, 255, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =16
        Text: ="EuroCaps Order Management Pro"
        Width: =300
        X: =70
        Y: =10

    BackButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(0, 0, 0, 0)
        Font: =Font.Arial
        Height: =40
        OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)
        Size: =16
        Text: ="← Back"
        Width: =80
        X: =Parent.Width - 100
        Y: =10

    PageTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =18
        Text: ="Settings"
        Width: =200
        X: =50
        Y: =100

    SettingsPanel As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =400
        RadiusBottomLeft: =8
        RadiusBottomRight: =8
        RadiusTopLeft: =8
        RadiusTopRight: =8
        Width: =600
        X: =50
        Y: =160

    UserSettingsTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =16
        Text: ="User Settings"
        Width: =200
        X: =70
        Y: =180

    UsernameLabel As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =25
        Size: =12
        Text: ="Username:"
        Width: =100
        X: =70
        Y: =220

    UsernameDisplay As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =25
        Size: =12
        Text: =varCurrentUser
        Width: =200
        X: =180
        Y: =220

    AppSettingsTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =16
        Text: ="Application Settings"
        Width: =200
        X: =70
        Y: =280

    NotificationsToggle As toggle:
        Default: =true
        Height: =30
        Width: =60
        X: =70
        Y: =320

    NotificationsLabel As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =30
        Size: =12
        Text: ="Enable Notifications"
        Width: =150
        X: =140
        Y: =320

    AboutTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =16
        Text: ="About"
        Width: =200
        X: =70
        Y: =380

    VersionLabel As label:
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =25
        Size: =12
        Text: ="Version 1.0.0"
        Width: =150
        X: =70
        Y: =410

    CopyrightLabel As label:
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =25
        Size: =12
        Text: ="© 2025 EuroCaps"
        Width: =150
        X: =70
        Y: =440

    LogoutButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(244, 67, 54, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =45
        HoverFill: =RGBA(228, 51, 38, 1)
        OnSelect: =Navigate(Login_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="LOGOUT"
        Width: =120
        X: =70
        Y: =500
