# ************************************************************************************************
# Enhanced Product Catalog Screen for EuroCaps Order Management Pro
# Based on detailed mockup specifications
# ************************************************************************************************
Screens:
  Product_Catalog_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(74, 111, 165, 1)
      Fill: =RGBA(245, 245, 245, 1)
    Children:
      # Header Bar (reused from other screens)
      - HeaderBar:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(74, 111, 165, 1)
            Height: =60
            Width: =1366
            X: =0
            Y: =0
            BorderThickness: =0

      # Logo in Header
      - HeaderLogo:
          Control: Image@2.1.0
          Properties:
            Image: ="eurocaps-logo"
            Height: =40
            Width: =40
            X: =20
            Y: =10

      # App Title in Header
      - HeaderTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="EuroCaps Order Management Pro"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =300
            X: =70
            Y: =10

      # Navigation Menu
      - NavigationMenu:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(58, 90, 128, 1)
            Height: =708
            Width: =200
            X: =0
            Y: =60
            BorderThickness: =0

      # Menu Items
      - MenuDashboard:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="≡ Dashboard"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(58, 90, 128, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =60
            Align: =Align.Left
            OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)

      - MenuCustomers:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="👥 Customers"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(58, 90, 128, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =110
            Align: =Align.Left
            OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)

      - MenuProducts:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📦 Products"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(74, 111, 165, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =160
            Align: =Align.Left

      - MenuOrders:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📋 Orders"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(58, 90, 128, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =210
            Align: =Align.Left
            OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade)

      # Main Content Area
      - MainContent:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(245, 245, 245, 1)
            Height: =708
            Width: =1166
            X: =200
            Y: =60
            BorderThickness: =0

      # Page Title
      - PageTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="Products"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =18
            Color: =RGBA(51, 51, 51, 1)
            Height: =40
            Width: =200
            X: =220
            Y: =80

      # Search Bar
      - SearchInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            HintText: ="Search Products..."
            Font: =Font.Arial
            Size: =12
            BorderColor: =RGBA(74, 111, 165, 1)
            Height: =40
            Width: =400
            X: =220
            Y: =140

      - SearchIcon:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="🔍"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(74, 111, 165, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =40
            Width: =40
            X: =630
            Y: =140

      # Filters
      - FiltersLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="FILTERS:"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =80
            X: =220
            Y: =200

      - TypeFilter:
          Control: Classic/Dropdown@2.1.0
          Properties:
            Items: =["All", "Espresso", "Lungo", "Ristretto", "Flavored"]
            DefaultSelectedItems: =["All"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =100
            X: =310
            Y: =195

      - SizeFilter:
          Control: Classic/Dropdown@2.1.0
          Properties:
            Items: =["All", "10", "20", "44"]
            DefaultSelectedItems: =["All"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =80
            X: =420
            Y: =195

      - SortDropdown:
          Control: Classic/Dropdown@2.1.0
          Properties:
            Items: =["Sort: Name", "Sort: Type", "Sort: Size"]
            DefaultSelectedItems: =["Sort: Name"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =120
            X: =510
            Y: =195

      - ResetFiltersButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="Reset"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(74, 111, 165, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(74, 111, 165, 1)
            Height: =35
            Width: =80
            X: =640
            Y: =195

      # Product Grid - Row 1
      - Product1Card:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =180
            Width: =250
            X: =220
            Y: =260
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - Product1Image:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(93, 64, 55, 1)
            Height: =80
            Width: =200
            X: =245
            Y: =280
            RadiusTopLeft: =4
            RadiusTopRight: =4
            RadiusBottomLeft: =4
            RadiusBottomRight: =4

      - Product1Name:
          Control: Label@2.5.1
          Properties:
            Text: ="Espresso"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =200
            X: =245
            Y: =370

      - Product1Type:
          Control: Label@2.5.1
          Properties:
            Text: ="Classic"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(128, 128, 128, 1)
            Height: =20
            Width: =100
            X: =245
            Y: =390

      - Product1Size:
          Control: Label@2.5.1
          Properties:
            Text: ="Size: 10"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(128, 128, 128, 1)
            Height: =20
            Width: =100
            X: =245
            Y: =410

      - Product1AddButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="+ Add"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(76, 175, 80, 1)
            HoverFill: =RGBA(60, 159, 64, 1)
            Height: =30
            Width: =80
            X: =365
            Y: =405

      # Product 2
      - Product2Card:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =180
            Width: =250
            X: =490
            Y: =260
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - Product2Image:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(141, 110, 99, 1)
            Height: =80
            Width: =200
            X: =515
            Y: =280
            RadiusTopLeft: =4
            RadiusTopRight: =4
            RadiusBottomLeft: =4
            RadiusBottomRight: =4

      - Product2Name:
          Control: Label@2.5.1
          Properties:
            Text: ="Lungo"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =200
            X: =515
            Y: =370

      - Product2Type:
          Control: Label@2.5.1
          Properties:
            Text: ="Intense"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(128, 128, 128, 1)
            Height: =20
            Width: =100
            X: =515
            Y: =390

      - Product2Size:
          Control: Label@2.5.1
          Properties:
            Text: ="Size: 20"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(128, 128, 128, 1)
            Height: =20
            Width: =100
            X: =515
            Y: =410

      - Product2AddButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="+ Add"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(76, 175, 80, 1)
            HoverFill: =RGBA(60, 159, 64, 1)
            Height: =30
            Width: =80
            X: =635
            Y: =405

      # Product 3
      - Product3Card:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =180
            Width: =250
            X: =760
            Y: =260
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - Product3Image:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(33, 33, 33, 1)
            Height: =80
            Width: =200
            X: =785
            Y: =280
            RadiusTopLeft: =4
            RadiusTopRight: =4
            RadiusBottomLeft: =4
            RadiusBottomRight: =4

      - Product3Name:
          Control: Label@2.5.1
          Properties:
            Text: ="Ristretto"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =200
            X: =785
            Y: =370

      - Product3Type:
          Control: Label@2.5.1
          Properties:
            Text: ="Strong"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(128, 128, 128, 1)
            Height: =20
            Width: =100
            X: =785
            Y: =390

      - Product3Size:
          Control: Label@2.5.1
          Properties:
            Text: ="Size: 10"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(128, 128, 128, 1)
            Height: =20
            Width: =100
            X: =785
            Y: =410

      - Product3AddButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="+ Add"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(76, 175, 80, 1)
            HoverFill: =RGBA(60, 159, 64, 1)
            Height: =30
            Width: =80
            X: =905
            Y: =405

      # Product 4
      - Product4Card:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =180
            Width: =250
            X: =1030
            Y: =260
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - Product4Image:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 152, 0, 1)
            Height: =80
            Width: =200
            X: =1055
            Y: =280
            RadiusTopLeft: =4
            RadiusTopRight: =4
            RadiusBottomLeft: =4
            RadiusBottomRight: =4

      - Product4Name:
          Control: Label@2.5.1
          Properties:
            Text: ="Vanilla"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =200
            X: =1055
            Y: =370

      - Product4Type:
          Control: Label@2.5.1
          Properties:
            Text: ="Flavored"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(128, 128, 128, 1)
            Height: =20
            Width: =100
            X: =1055
            Y: =390

      - Product4Size:
          Control: Label@2.5.1
          Properties:
            Text: ="Size: 20"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(128, 128, 128, 1)
            Height: =20
            Width: =100
            X: =1055
            Y: =410

      - Product4AddButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="+ Add"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(76, 175, 80, 1)
            HoverFill: =RGBA(60, 159, 64, 1)
            Height: =30
            Width: =80
            X: =1175
            Y: =405

      # Pagination
      - PaginationPrevious:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="◀ Previous"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(74, 111, 165, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(74, 111, 165, 1)
            Height: =35
            Width: =100
            X: =220
            Y: =680

      - PaginationNext:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="Next ▶"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(74, 111, 165, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(74, 111, 165, 1)
            Height: =35
            Width: =100
            X: =1180
            Y: =680

      - PaginationInfo:
          Control: Label@2.5.1
          Properties:
            Text: ="Showing 1-8 of 24 products"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(128, 128, 128, 1)
            Height: =35
            Width: =200
            X: =680
            Y: =680
            Align: =Align.Center
