{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Courier New;}}
{\colortbl ;\red0\green0\blue255;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 PYTHON SYLLABUS UITLEG - DEEL 1\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 1. INLEIDING TOT PYTHON\b0\fs22\par

\pard\sa200\sl276\slmult1\b Wat is Python?\b0\par
Python is een veelzijdige, geïnterpreteerde programmeertaal die bekend staat om zijn leesbaarheid en eenvoud. Het is een taal die zowel voor beginners als gevorderden geschikt is en wordt gebruikt voor webontwikkeling, data-analyse, kunstmatige intelligentie, automatisering en meer.\par

\pard\sa200\sl276\slmult1\b Kenmer<PERSON> van Python:\b0\par
\pard\li360\sa200\sl276\slmult1 - Leesbare syntax met gebruik van indentatie\par
- Dynamisch getypeerd (variabelen hoeven niet vooraf gedeclareerd te worden)\par
- Ondersteunt meerdere programmeerparadigma's (procedureel, object-georiënteerd, functioneel)\par
- Uitgebreide standaardbibliotheek\par
- Grote gemeenschap en veel beschikbare modules\par

\pard\sa200\sl276\slmult1\b Python installeren en uitvoeren\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Python code kan op verschillende manieren worden uitgevoerd:\par
\par
# 1. Interactieve modus (Python shell)\par
# Start Python in de terminal/command prompt en voer commando's direct uit\par
\par
# 2. Script uitvoeren\par
# Maak een .py bestand en voer het uit met: python mijn_script.py\par
\par
# 3. IDE (Integrated Development Environment)\par
# Gebruik een IDE zoals PyCharm, VS Code, of IDLE\par
\par
# Voorbeeld van een eenvoudig Python script:\par
print("Hallo, wereld!")  # Geeft "Hallo, wereld!" weer op het scherm\f0\par

\pard\sa200\sl276\slmult1\b\fs24 2. VARIABELEN EN DATATYPES\b0\fs22\par

\pard\sa200\sl276\slmult1\b Variabelen\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Variabelen toewijzen\par
naam = "Alice"      # String\par
leeftijd = 30       # Integer\par
lengte = 1.75       # Float\par
is_student = True   # Boolean\par
\par
# Meerdere variabelen tegelijk toewijzen\par
a, b, c = 1, 2, 3\par
\par
# Variabelen van waarde wisselen\par
x = 5\par
y = 10\par
x, y = y, x  # Nu is x=10 en y=5\par
\par
# Variabelenamen:\par
# - Moeten beginnen met een letter of underscore\par
# - Kunnen letters, cijfers en underscores bevatten\par
# - Zijn hoofdlettergevoelig (naam ≠ Naam)\par
# - Kunnen geen gereserveerde woorden zijn (if, for, while, etc.)\f0\par

\pard\sa200\sl276\slmult1\b Numerieke datatypes\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Integer (gehele getallen)\par
a = 5\par
b = -10\par
c = 0\par
groot_getal = 1_000_000  # Underscores voor leesbaarheid\par
\par
# Float (kommagetallen)\par
x = 3.14\par
y = -0.001\par
z = 2.5e6  # Wetenschappelijke notatie: 2.5 × 10^6 = 2500000.0\par
\par
# Complex (complexe getallen)\par
c = 2 + 3j  # j is de imaginaire eenheid\par
\par
# Type conversie\par
i = 5\par
f = float(i)  # 5.0\par
\par
f = 3.14\par
i = int(f)    # 3 (decimalen worden afgekapt, niet afgerond)\par
\par
# Rekenkundige operaties\par
som = 5 + 3           # 8\par
verschil = 5 - 3      # 2\par
product = 5 * 3       # 15\par
quotient = 5 / 3      # 1.6666... (float deling)\par
int_quotient = 5 // 3 # 1 (integer deling)\par
rest = 5 % 3          # 2 (modulo/rest)\par
macht = 5 ** 3        # 125 (5 tot de macht 3)\par
\par
# Ingebouwde numerieke functies\par
abs_waarde = abs(-5)   # 5 (absolute waarde)\par
afgerond = round(3.7)  # 4 (afronden)\par
minimum = min(1, 5, 3) # 1 (kleinste waarde)\par
maximum = max(1, 5, 3) # 5 (grootste waarde)\f0\par

\pard\sa200\sl276\slmult1\b Strings (tekst)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Strings aanmaken\par
s1 = 'Enkele aanhalingstekens'\par
s2 = "Dubbele aanhalingstekens"\par
s3 = '''Meerdere\par
regels\par
tekst'''\par
\par
# Escape characters\par
s4 = "Hij zei: \\"Hallo\\""\par
s5 = "Eerste regel\\nTweede regel"  # \\n is newline\par
s6 = "C:\\\\Users\\\\<USER>\\\\ is een backslash\par
\par
# Raw strings (escape characters worden genegeerd)\par
raw_string = r"C:\\Users\\<USER>\par
\par
# String concatenatie (samenvoegen)\par
voornaam = "Jan"\par
achternaam = "Jansen"\par
volledige_naam = voornaam + " " + achternaam  # "Jan Jansen"\par
\par
# String herhaling\par
herhaling = "Ha" * 3  # "HaHaHa"\par
\par
# String indexering (begint bij 0)\par
tekst = "Python"\par
eerste_letter = tekst[0]    # "P"\par
laatste_letter = tekst[-1]  # "n"\par
\par
# String slicing [start:stop:stap]\par
tekst = "Python Programming"\par
deel = tekst[0:6]      # "Python" (karakters 0 t/m 5)\par
deel = tekst[:6]       # "Python" (vanaf begin tot 6)\par
deel = tekst[7:]       # "Programming" (vanaf 7 tot eind)\par
deel = tekst[::2]      # "Pto rgamn" (elke 2e letter)\par
omgekeerd = tekst[::-1]  # "gnimmargorP nohtyP" (omgekeerd)\par
\par
# String methoden\par
tekst = "  Python Programming  "\par
\par
lengte = len(tekst)             # 23 (aantal karakters)\par
hoofdletters = tekst.upper()    # "  PYTHON PROGRAMMING  "\par
kleine_letters = tekst.lower()  # "  python programming  "\par
zonder_spaties = tekst.strip()  # "Python Programming"\par
vervangen = tekst.replace("Python", "Java")  # "  Java Programming  "\par
\par
woorden = "appel,peer,banaan".split(",")  # ["appel", "peer", "banaan"]\par
samengevoegd = "-".join(woorden)  # "appel-peer-banaan"\par
\par
bevat = "Python" in tekst      # True\par
positie = tekst.find("Pro")    # 9 (index waar "Pro" begint)\par
aantal = tekst.count("P")      # 2 (aantal keer dat "P" voorkomt)\par
\par
# String formattering\par
naam = "Alice"\par
leeftijd = 30\par
\par
# f-strings (Python 3.6+)\par
bericht1 = f"Hallo, ik ben \{naam\} en ik ben \{leeftijd\} jaar oud."\par
\par
# .format() methode\par
bericht2 = "Hallo, ik ben \{\} en ik ben \{\} jaar oud.".format(naam, leeftijd)\par
bericht3 = "Hallo, ik ben \{0\} en ik ben \{1\} jaar oud.".format(naam, leeftijd)\par
bericht4 = "Hallo, ik ben \{n\} en ik ben \{l\} jaar oud.".format(n=naam, l=leeftijd)\par
\par
# %-notatie (ouder)\par
bericht5 = "Hallo, ik ben %s en ik ben %d jaar oud." % (naam, leeftijd)\f0\par

\pard\sa200\sl276\slmult1\b Boolean (waar/onwaar)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Boolean waarden\par
waar = True\par
onwaar = False\par
\par
# Vergelijkingsoperatoren geven Boolean terug\par
x = 5\par
y = 10\par
\par
is_gelijk = x == y      # False\par
is_ongelijk = x != y    # True\par
is_groter = x > y       # False\par
is_kleiner = x < y      # True\par
is_groter_gelijk = x >= y  # False\par
is_kleiner_gelijk = x <= y  # True\par
\par
# Logische operatoren\par
a = True\par
b = False\par
\par
en_resultaat = a and b  # False (beide moeten waar zijn)\par
of_resultaat = a or b   # True (minstens één moet waar zijn)\par
niet_resultaat = not a  # False (keert de waarde om)\par
\par
# Kortsluiting (short-circuit evaluation)\par
# 'and' stopt zodra het een False tegenkomt\par
# 'or' stopt zodra het een True tegenkomt\par
\par
# Boolean conversie\par
# Deze waarden worden als False beschouwd:\par
# False, 0, 0.0, "", [], \{\}, set(), None\par
# Alles anders is True\par
\par
print(bool(0))      # False\par
print(bool(1))      # True\par
print(bool(""))     # False\par
print(bool("abc"))  # True\par
print(bool([]))     # False\par
print(bool([1, 2])) # True\par
print(bool(None))   # False\f0\par

\pard\sa200\sl276\slmult1\b None (afwezigheid van waarde)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # None is een speciaal object dat de afwezigheid van een waarde aangeeft\par
x = None\par
\par
# None wordt vaak gebruikt als standaardwaarde voor functieparameters\par
def functie(param=None):\par
    if param is None:\par
        print("Geen parameter opgegeven")\par
    else:\par
        print(f"Parameter: \{param\}")\par
\par
functie()        # "Geen parameter opgegeven"\par
functie("test")  # "Parameter: test"\par
\par
# None controleren\par
# Gebruik 'is' en 'is not' in plaats van == en !=\par
if x is None:\par
    print("x is None")\par
\par
if x is not None:\par
    print("x is niet None")\f0\par

\pard\sa200\sl276\slmult1\b\fs24 3. COLLECTIES (VERZAMELINGEN)\b0\fs22\par

\pard\sa200\sl276\slmult1\b Lijsten (Lists)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Lijsten aanmaken\par
getallen = [1, 2, 3, 4, 5]\par
kleuren = ["rood", "groen", "blauw"]\par
gemengd = [1, "twee", 3.0, [4, 5]]  # Verschillende datatypes\par
lege_lijst = []\par
via_constructor = list("abc")  # ['a', 'b', 'c']\par
\par
# Indexering en slicing (zoals bij strings)\par
eerste = getallen[0]       # 1\par
laatste = getallen[-1]     # 5\par
deel = getallen[1:4]       # [2, 3, 4]\par
\par
# Lijsten wijzigen\par
getallen[0] = 10           # [10, 2, 3, 4, 5]\par
\par
# Lijst methoden\par
getallen.append(6)         # [10, 2, 3, 4, 5, 6] (toevoegen aan einde)\par
getallen.insert(1, 15)     # [10, 15, 2, 3, 4, 5, 6] (invoegen op index)\par
getallen.extend([7, 8])    # [10, 15, 2, 3, 4, 5, 6, 7, 8] (lijst toevoegen)\par
\par
getallen.remove(15)        # [10, 2, 3, 4, 5, 6, 7, 8] (waarde verwijderen)\par
verwijderd = getallen.pop(1)  # verwijderd = 2, getallen = [10, 3, 4, 5, 6, 7, 8]\par
getallen.clear()           # [] (alle elementen verwijderen)\par
\par
# Andere handige methoden\par
kleuren = ["rood", "groen", "blauw", "rood"]\par
index = kleuren.index("groen")  # 1 (eerste voorkomen)\par
aantal = kleuren.count("rood")  # 2 (aantal voorkomens)\par
kleuren.sort()                  # ["blauw", "groen", "rood", "rood"]\par
kleuren.reverse()               # ["rood", "rood", "groen", "blauw"]\par
\par
# Sorteren met key functie\par
woorden = ["appel", "Banaan", "peer", "Kers"]\par
woorden.sort()  # ["Banaan", "Kers", "appel", "peer"] (hoofdletters eerst)\par
woorden.sort(key=str.lower)  # ["appel", "Banaan", "Kers", "peer"] (hoofdletterongevoelig)\par
\par
# Gesorteerde kopie (origineel blijft ongewijzigd)\par
gesorteerd = sorted(woorden)\par
\par
# List comprehension (krachtige manier om lijsten te maken)\par
kwadraten = [x**2 for x in range(5)]  # [0, 1, 4, 9, 16]\par
even = [x for x in range(10) if x % 2 == 0]  # [0, 2, 4, 6, 8]\par
\par
# Geneste list comprehension\par
matrix = [[1, 2, 3], [4, 5, 6], [7, 8, 9]]\par
getransponeerd = [[rij[i] for rij in matrix] for i in range(3)]\par
# [[1, 4, 7], [2, 5, 8], [3, 6, 9]]\par
\par
# Lijsten kopiëren\par
a = [1, 2, 3]\par
b = a  # Referentie naar dezelfde lijst\par
c = a.copy()  # Nieuwe kopie (ondiepe kopie)\par
d = a[:]  # Nieuwe kopie via slicing\par
\par
import copy\par
e = copy.deepcopy(a)  # Diepe kopie (voor geneste lijsten)\f0\par

\pard\sa200\sl276\slmult1\b Tuples (onveranderbare lijsten)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Tuples aanmaken\par
coordinaten = (10, 20)\par
kleuren = ("rood", "groen", "blauw")\par
gemengd = (1, "twee", 3.0)\par
enkele_waarde = (5,)  # Komma nodig voor tuple met één element\par
lege_tuple = ()\par
via_constructor = tuple([1, 2, 3])  # (1, 2, 3)\par
\par
# Indexering en slicing (zoals bij lijsten)\par
x = coordinaten[0]  # 10\par
deel = kleuren[0:2]  # ("rood", "groen")\par
\par
# Tuples zijn onveranderbaar (immutable)\par
# coordinaten[0] = 5  # Dit geeft een TypeError\par
\par
# Tuple uitpakken\par
x, y = coordinaten  # x = 10, y = 20\par
\par
# Meerdere waarden uitpakken\par
a, b, c = kleuren  # a = "rood", b = "groen", c = "blauw"\par
\par
# Met * operator (rest verzamelen)\par
eerste, *rest = kleuren  # eerste = "rood", rest = ["groen", "blauw"]\par
\par
# Tuple methoden (beperkt omdat ze onveranderbaar zijn)\par
index = kleuren.index("groen")  # 1\par
aantal = kleuren.count("rood")  # 1\par
\par
# Tuples worden vaak gebruikt voor functies die meerdere waarden teruggeven\par
def geef_coordinaten():\par
    return (10, 20)\par
\par
x, y = geef_coordinaten()  # x = 10, y = 20\par
\par
# Tuples vs. lijsten\par
# - Tuples zijn onveranderbaar, lijsten zijn veranderbaar\par
# - Tuples zijn meestal sneller dan lijsten\par
# - Tuples kunnen worden gebruikt als dictionary keys, lijsten niet\par
# - Tuples worden gebruikt voor heterogene data, lijsten voor homogene data\f0\par

\pard\sa200\sl276\slmult1\b Dictionaries (woordenboeken)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Dictionaries aanmaken\par
persoon = \{"naam": "Jan", "leeftijd": 25, "stad": "Amsterdam"\}\par
scores = \{"Alice": 95, "Bob": 87, "Charlie": 92\}\par
lege_dict = \{\}\par
via_constructor = dict(naam="Jan", leeftijd=25)  # {"naam": "Jan", "leeftijd": 25}\par
\par
# Toegang tot waarden\par
naam = persoon["naam"]  # "Jan"\par
# naam = persoon["adres"]  # KeyError als key niet bestaat\par
\par
# Veilige toegang met get() (geen error als key niet bestaat)\par
adres = persoon.get("adres")  # None\par
adres = persoon.get("adres", "Onbekend")  # "Onbekend" (standaardwaarde)\par
\par
# Dictionary wijzigen\par
persoon["naam"] = "Piet"  # {"naam": "Piet", "leeftijd": 25, "stad": "Amsterdam"}\par
persoon["adres"] = "Hoofdstraat 1"  # Nieuwe key-value pair toevoegen\par
\par
# Dictionary methoden\par
keys = persoon.keys()    # dict_keys(['naam', 'leeftijd', 'stad', 'adres'])\par
values = persoon.values()  # dict_values(['Piet', 25, 'Amsterdam', 'Hoofdstraat 1'])\par
items = persoon.items()  # dict_items([('naam', 'Piet'), ('leeftijd', 25), ...])\par
\par
# Itereren over een dictionary\par
for key in persoon:  # Itereren over keys\par
    print(key, persoon[key])\par
\par
for key, value in persoon.items():  # Itereren over key-value pairs\par
    print(key, value)\par
\par
# Dictionary methoden\par
verwijderd = persoon.pop("adres")  # Verwijdert en retourneert waarde\par
persoon.update(\{"email": "<EMAIL>", "leeftijd": 26\})  # Update/toevoegen\par
persoon.clear()  # Verwijdert alle items\par
\par
# Dictionary comprehension\par
kwadraten = \{x: x**2 for x in range(5)\}  # \{0: 0, 1: 1, 2: 4, 3: 9, 4: 16\}\par
\par
# Geneste dictionaries\par
studenten = \{\par
    "Alice": \{"leeftijd": 20, "studie": "Informatica", "cijfers": [8, 7, 9]\},\par
    "Bob": \{"leeftijd": 22, "studie": "Wiskunde", "cijfers": [6, 8, 7]\}\par
\}\par
\par
print(studenten["Alice"]["studie"])  # "Informatica"\par
print(studenten["Bob"]["cijfers"][0])  # 6\f0\par

\pard\sa200\sl276\slmult1\b Sets (verzamelingen)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Sets aanmaken (unieke waarden, ongeordend)\par
kleuren = \{"rood", "groen", "blauw", "rood"\}  # {"rood", "groen", "blauw"} (duplicaten worden verwijderd)\par
getallen = \{1, 2, 3, 4, 5\}\par
lege_set = set()  # \{\} maakt een lege dictionary, niet een lege set\par
via_constructor = set([1, 2, 2, 3])  # \{1, 2, 3\}\par
\par
# Set bewerkingen\par
kleuren.add("geel")  # {"rood", "groen", "blauw", "geel"}\par
kleuren.remove("groen")  # {"rood", "blauw", "geel"} (geeft error als element niet bestaat)\par
kleuren.discard("paars")  # geen error als element niet bestaat\par
verwijderd = kleuren.pop()  # verwijdert en retourneert willekeurig element\par
kleuren.clear()  # set() (alle elementen verwijderen)\par
\par
# Set operaties (verzamelingenleer)\par
a = \{1, 2, 3, 4\}\par
b = \{3, 4, 5, 6\}\par
\par
unie = a | b  # \{1, 2, 3, 4, 5, 6\} (elementen in a OF b)\par
doorsnede = a & b  # \{3, 4\} (elementen in a EN b)\par
verschil = a - b  # \{1, 2\} (elementen in a maar NIET in b)\par
sym_verschil = a ^ b  # \{1, 2, 5, 6\} (elementen in a OF b maar NIET in beide)\par
\par
# Set methoden voor bovenstaande operaties\par
unie = a.union(b)\par
doorsnede = a.intersection(b)\par
verschil = a.difference(b)\par
sym_verschil = a.symmetric_difference(b)\par
\par
# Subset en superset\par
c = \{1, 2\}\par
print(c.issubset(a))  # True (alle elementen van c zitten in a)\par
print(a.issuperset(c))  # True (a bevat alle elementen van c)\par
\par
# Testen op lidmaatschap (zeer efficiënt)\par
print(2 in a)  # True\par
print(5 in a)  # False\par
\par
# Sets worden vaak gebruikt voor:\par
# - Verwijderen van duplicaten\par
# - Lidmaatschapstests (is x in verzameling?)\par
# - Wiskundige verzamelingsoperaties\f0\par
}
