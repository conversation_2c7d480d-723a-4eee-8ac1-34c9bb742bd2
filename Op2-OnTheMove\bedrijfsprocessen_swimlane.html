<!DOCTYPE html>
<html>
<head>
    <title>Swimlane Diagram: Orderverwerking Proces</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: white;
        }
        .diagram {
            width: 800px;
            height: 600px;
            margin: 0 auto;
            position: relative;
            border: 1px solid #ccc;
            background-color: white;
        }
        .title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .swimlane {
            position: absolute;
            width: 700px;
            height: 100px;
            border: 1px solid #999;
            left: 50px;
        }
        .lane-title {
            position: absolute;
            width: 100px;
            height: 100%;
            background-color: #f0f0f0;
            border-right: 1px solid #999;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        .lane1 {
            top: 100px;
        }
        .lane2 {
            top: 200px;
        }
        .lane3 {
            top: 300px;
        }
        .lane4 {
            top: 400px;
        }
        .process-box {
            position: absolute;
            width: 100px;
            height: 50px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-size: 12px;
        }
        .sales-box {
            background-color: #dae8fc;
            border: 1px solid #6c8ebf;
        }
        .warehouse-box {
            background-color: #ffe6cc;
            border: 1px solid #d79b00;
        }
        .logistics-box {
            background-color: #d5e8d4;
            border: 1px solid #82b366;
        }
        .finance-box {
            background-color: #f8cecc;
            border: 1px solid #b85450;
        }
        .decision-box {
            width: 80px;
            height: 80px;
            transform: rotate(45deg);
        }
        .decision-text {
            transform: rotate(-45deg);
        }
        .box1 {
            left: 150px;
            top: 125px;
        }
        .box2 {
            left: 300px;
            top: 125px;
        }
        .box3 {
            left: 450px;
            top: 125px;
        }
        .box4 {
            left: 150px;
            top: 225px;
        }
        .box5 {
            left: 300px;
            top: 210px;
        }
        .box6 {
            left: 450px;
            top: 225px;
        }
        .box7 {
            left: 150px;
            top: 325px;
        }
        .box8 {
            left: 300px;
            top: 325px;
        }
        .box9 {
            left: 450px;
            top: 325px;
        }
        .box10 {
            left: 150px;
            top: 425px;
        }
        .box11 {
            left: 300px;
            top: 425px;
        }
        .box12 {
            left: 450px;
            top: 425px;
        }
        .arrow {
            position: absolute;
            background-color: black;
        }
        .horizontal-arrow {
            height: 2px;
        }
        .vertical-arrow {
            width: 2px;
        }
        .arrow:after {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
        }
        .arrow-right:after {
            right: -10px;
            top: -8px;
            border-top: 9px solid transparent;
            border-bottom: 9px solid transparent;
            border-left: 10px solid black;
        }
        .arrow-left:after {
            left: -10px;
            top: -8px;
            border-top: 9px solid transparent;
            border-bottom: 9px solid transparent;
            border-right: 10px solid black;
        }
        .arrow-up:after {
            left: -8px;
            top: -10px;
            border-left: 9px solid transparent;
            border-right: 9px solid transparent;
            border-bottom: 10px solid black;
        }
        .arrow-down:after {
            left: -8px;
            bottom: -10px;
            border-left: 9px solid transparent;
            border-right: 9px solid transparent;
            border-top: 10px solid black;
        }
        .arrow1 {
            width: 50px;
            left: 250px;
            top: 150px;
        }
        .arrow2 {
            width: 50px;
            left: 400px;
            top: 150px;
        }
        .arrow3 {
            height: 75px;
            left: 500px;
            top: 175px;
        }
        .arrow4 {
            width: 50px;
            left: 250px;
            top: 250px;
        }
        .arrow5 {
            width: 50px;
            left: 400px;
            top: 250px;
        }
        .arrow6 {
            height: 50px;
            left: 500px;
            top: 275px;
        }
        .arrow7 {
            width: 50px;
            left: 250px;
            top: 350px;
        }
        .arrow8 {
            width: 50px;
            left: 400px;
            top: 350px;
        }
        .arrow9 {
            height: 50px;
            left: 500px;
            top: 375px;
        }
        .arrow10 {
            width: 50px;
            left: 250px;
            top: 450px;
        }
        .arrow11 {
            width: 50px;
            left: 400px;
            top: 450px;
        }
        .start-end {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #f5f5f5;
            border: 1px solid #666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        .start {
            left: 50px;
            top: 120px;
        }
        .end {
            left: 600px;
            top: 420px;
        }
        .start-arrow {
            width: 40px;
            left: 110px;
            top: 150px;
        }
        .end-arrow {
            width: 40px;
            left: 560px;
            top: 450px;
        }
    </style>
</head>
<body>
    <div class="title">Swimlane Diagram: Orderverwerking Proces</div>
    <div class="diagram">
        <div class="swimlane lane1">
            <div class="lane-title">Verkoop</div>
            <div class="process-box sales-box box1">Ontvang bestelling</div>
            <div class="process-box sales-box box2">Controleer bestelling</div>
            <div class="process-box sales-box box3">Bevestig bestelling</div>
        </div>
        
        <div class="swimlane lane2">
            <div class="lane-title">Magazijn</div>
            <div class="process-box warehouse-box box4">Controleer voorraad</div>
            <div class="process-box warehouse-box box5 decision-box">
                <div class="decision-text">Voorraad beschikbaar?</div>
            </div>
            <div class="process-box warehouse-box box6">Verzamel producten</div>
        </div>
        
        <div class="swimlane lane3">
            <div class="lane-title">Logistiek</div>
            <div class="process-box logistics-box box7">Verpak producten</div>
            <div class="process-box logistics-box box8">Maak verzendlabel</div>
            <div class="process-box logistics-box box9">Verzend pakket</div>
        </div>
        
        <div class="swimlane lane4">
            <div class="lane-title">Financiën</div>
            <div class="process-box finance-box box10">Verwerk betaling</div>
            <div class="process-box finance-box box11">Maak factuur</div>
            <div class="process-box finance-box box12">Boek transactie</div>
        </div>
        
        <div class="start-end start">Start</div>
        <div class="start-end end">Einde</div>
        
        <!-- Horizontal arrows within lanes -->
        <div class="arrow horizontal-arrow arrow1 arrow-right"></div>
        <div class="arrow horizontal-arrow arrow2 arrow-right"></div>
        <div class="arrow horizontal-arrow arrow4 arrow-right"></div>
        <div class="arrow horizontal-arrow arrow5 arrow-right"></div>
        <div class="arrow horizontal-arrow arrow7 arrow-right"></div>
        <div class="arrow horizontal-arrow arrow8 arrow-right"></div>
        <div class="arrow horizontal-arrow arrow10 arrow-right"></div>
        <div class="arrow horizontal-arrow arrow11 arrow-right"></div>
        
        <!-- Vertical arrows between lanes -->
        <div class="arrow vertical-arrow arrow3 arrow-down"></div>
        <div class="arrow vertical-arrow arrow6 arrow-down"></div>
        <div class="arrow vertical-arrow arrow9 arrow-down"></div>
        
        <!-- Start and end arrows -->
        <div class="arrow horizontal-arrow start-arrow arrow-right"></div>
        <div class="arrow horizontal-arrow end-arrow arrow-right"></div>
    </div>
</body>
</html>
