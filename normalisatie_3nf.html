<!DOCTYPE html>
<html>
<head>
    <title>Normalisatie: 3NF</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .diagram {
            width: 800px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: white;
            padding: 20px;
        }
        .table {
            border-collapse: collapse;
            margin: 20px 0;
            width: 100%;
        }
        .table th, .table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #333;
            color: white;
        }
        .pk {
            font-weight: bold;
            text-decoration: underline;
        }
        .fk {
            font-style: italic;
            color: #555;
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .step {
            margin: 30px 0;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .step-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="title">Normalisatie: 3NF (Derde <PERSON>alvorm)</div>
    <div class="subtitle">Geen transitieve afhankelijkheden</div>
    
    <div class="diagram">
        <div class="step">
            <div class="step-title">3NF: Derde Normaalvorm</div>
            <p>Geen transitieve afhankelijkheden (niet-sleutelattributen die afhankelijk zijn van andere niet-sleutelattributen).</p>
            
            <div style="display: flex; flex-wrap: wrap; justify-content: space-between;">
                <div style="width: 30%;">
                    <p><strong>Order tabel:</strong></p>
                    <table class="table">
                        <tr>
                            <th class="pk">OrderID</th>
                            <th class="fk">KlantID</th>
                        </tr>
                        <tr>
                            <td>1001</td>
                            <td>K100</td>
                        </tr>
                        <tr>
                            <td>1002</td>
                            <td>K101</td>
                        </tr>
                    </table>
                </div>
                
                <div style="width: 30%;">
                    <p><strong>Klant tabel:</strong></p>
                    <table class="table">
                        <tr>
                            <th class="pk">KlantID</th>
                            <th>KlantNaam</th>
                            <th>KlantAdres</th>
                        </tr>
                        <tr>
                            <td>K100</td>
                            <td>Jan Jansen</td>
                            <td>Hoofdstraat 1, Amsterdam</td>
                        </tr>
                        <tr>
                            <td>K101</td>
                            <td>Piet Pietersen</td>
                            <td>Kerkstraat 10, Rotterdam</td>
                        </tr>
                    </table>
                </div>
                
                <div style="width: 30%;">
                    <p><strong>Product tabel:</strong></p>
                    <table class="table">
                        <tr>
                            <th class="pk">ProductID</th>
                            <th>ProductNaam</th>
                            <th>Prijs</th>
                        </tr>
                        <tr>
                            <td>P200</td>
                            <td>Laptop</td>
                            <td>899.99</td>
                        </tr>
                        <tr>
                            <td>P201</td>
                            <td>Muis</td>
                            <td>19.99</td>
                        </tr>
                    </table>
                </div>
                
                <div style="width: 100%; margin-top: 20px;">
                    <p><strong>OrderDetail tabel:</strong></p>
                    <table class="table">
                        <tr>
                            <th class="pk fk">OrderID</th>
                            <th class="pk fk">ProductID</th>
                            <th>Aantal</th>
                        </tr>
                        <tr>
                            <td>1001</td>
                            <td>P200</td>
                            <td>1</td>
                        </tr>
                        <tr>
                            <td>1001</td>
                            <td>P201</td>
                            <td>2</td>
                        </tr>
                        <tr>
                            <td>1002</td>
                            <td>P200</td>
                            <td>1</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <p>Veranderingen:</p>
            <ul>
                <li>Transitieve afhankelijkheden verwijderd door het creëren van aparte tabellen voor Klant en Product</li>
                <li>KlantNaam en KlantAdres zijn afhankelijk van KlantID, niet direct van OrderID</li>
                <li>ProductNaam en Prijs zijn afhankelijk van ProductID</li>
                <li>OrderDetail bevat nu alleen nog maar de relatie tussen Order en Product, plus het Aantal</li>
            </ul>
            
            <p>Regels voor 3NF volgens de cursus:</p>
            <ul>
                <li>Volg de regels voor de tweede normaal vorm</li>
                <li>Zoek naar gegevens die op zichzelf weer een entiteit kunnen zijn</li>
                <li>Anders gezegd: zoek naar niet-sleutelattributen die afhankelijk zijn een ander niet-sleutelattribuut</li>
                <li>Maak hiervan een nieuwe entiteit met een identifier</li>
            </ul>
            
            <p>Voordelen van 3NF:</p>
            <ul>
                <li>Minimale redundantie: gegevens worden maar op één plaats opgeslagen</li>
                <li>Vermijdt update-anomalieën: wijzigingen hoeven maar op één plaats te worden doorgevoerd</li>
                <li>Vermijdt verwijder-anomalieën: verwijderen van een order verwijdert niet de klant- of productgegevens</li>
                <li>Vermijdt invoeg-anomalieën: nieuwe producten kunnen worden toegevoegd zonder dat er een order voor bestaat</li>
            </ul>
        </div>
    </div>
</body>
</html>
