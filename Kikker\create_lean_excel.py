from openpyxl import Workbook
from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Reference
from openpyxl.styles import <PERSON>ont, Alignment, Border, Side, PatternFill
from openpyxl.chart.label import DataLabelList
import os

def create_lean_excel():
    # Verwijder het bestaande bestand als het bestaat
    output_file = 'Lean_Analyse.xlsx'
    if os.path.exists(output_file):
        try:
            os.remove(output_file)
            print(f"Bestaand bestand {output_file} verwijderd")
        except:
            print(f"Kon bestaand bestand {output_file} niet verwijderen")
    
    # Maak een Excel workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Lean Analyse"
    
    # Definieer stijlen
    header_font = Font(bold=True, size=12)
    normal_font = Font(size=11)
    
    # Definieer borders
    thin_border = Border(left=Side(style='thin'), 
                         right=Side(style='thin'), 
                         top=Side(style='thin'), 
                         bottom=Side(style='thin'))
    
    # Definieer vulkleuren
    header_fill = PatternFill(start_color="8EAADB", end_color="8EAADB", fill_type="solid")
    subheader_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
    data_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    
    # Maak de tabelheaders
    ws.cell(row=1, column=1, value="Column1").font = header_font
    ws.cell(row=1, column=1).fill = header_fill
    ws.cell(row=1, column=1).alignment = Alignment(horizontal='left')
    ws.cell(row=1, column=1).border = thin_border
    
    ws.cell(row=1, column=2, value="Column2").font = header_font
    ws.cell(row=1, column=2).fill = header_fill
    ws.cell(row=1, column=2).alignment = Alignment(horizontal='left')
    ws.cell(row=1, column=2).border = thin_border
    
    # Titel
    ws.cell(row=2, column=1, value="LEAN ANALYSE").font = header_font
    ws.cell(row=2, column=1).fill = subheader_fill
    ws.cell(row=2, column=1).border = thin_border
    ws.cell(row=2, column=2).fill = subheader_fill
    ws.cell(row=2, column=2).border = thin_border
    
    # Subtitel
    ws.cell(row=3, column=1, value="Verspilling en Efficiëntie").font = Font(italic=True)
    ws.cell(row=3, column=1).fill = subheader_fill
    ws.cell(row=3, column=1).border = thin_border
    ws.cell(row=3, column=2).fill = subheader_fill
    ws.cell(row=3, column=2).border = thin_border
    
    # Lege rij
    ws.cell(row=4, column=1).fill = subheader_fill
    ws.cell(row=4, column=1).border = thin_border
    ws.cell(row=4, column=2).fill = subheader_fill
    ws.cell(row=4, column=2).border = thin_border
    
    # Verspilling Analyse
    ws.cell(row=5, column=1, value="Verspilling Analyse:").font = header_font
    ws.cell(row=5, column=1).fill = subheader_fill
    ws.cell(row=5, column=1).border = thin_border
    ws.cell(row=5, column=2).fill = subheader_fill
    ws.cell(row=5, column=2).border = thin_border
    
    # Verspilling data
    waste_data = [
        ("- Overproductie", 32.5),
        ("- Wachttijd", 25.7),
        ("- Transport", 15.3),
        ("- Overbewerking", 12.8),
        ("- Voorraad", 8.4),
        ("- Beweging", 3.2),
        ("- Defecten", 2.1)
    ]
    
    for i, (label, value) in enumerate(waste_data):
        row = 6 + i
        ws.cell(row=row, column=1, value=label).font = normal_font
        ws.cell(row=row, column=1).border = thin_border
        ws.cell(row=row, column=2, value=value).font = normal_font
        ws.cell(row=row, column=2).fill = data_fill
        ws.cell(row=row, column=2).border = thin_border
    
    # Lege rij
    ws.cell(row=13, column=1).fill = subheader_fill
    ws.cell(row=13, column=1).border = thin_border
    ws.cell(row=13, column=2).fill = subheader_fill
    ws.cell(row=13, column=2).border = thin_border
    
    # Efficiëntie Metrics
    ws.cell(row=14, column=1, value="Efficiëntie Metrics:").font = header_font
    ws.cell(row=14, column=1).fill = subheader_fill
    ws.cell(row=14, column=1).border = thin_border
    ws.cell(row=14, column=2).fill = subheader_fill
    ws.cell(row=14, column=2).border = thin_border
    
    # Efficiëntie data
    efficiency_data = [
        ("- Overall Equipment Effectiveness (OEE)", 67.8),
        ("- Takt Time (min/unit)", 2.5),
        ("- Cycle Time (min/unit)", 3.2),
        ("- Setup Time (min)", 45.0),
        ("- First Pass Yield (%)", 92.3)
    ]
    
    for i, (label, value) in enumerate(efficiency_data):
        row = 15 + i
        ws.cell(row=row, column=1, value=label).font = normal_font
        ws.cell(row=row, column=1).border = thin_border
        ws.cell(row=row, column=2, value=value).font = normal_font
        ws.cell(row=row, column=2).fill = data_fill
        ws.cell(row=row, column=2).border = thin_border
    
    # Lege rij
    ws.cell(row=20, column=1).fill = subheader_fill
    ws.cell(row=20, column=1).border = thin_border
    ws.cell(row=20, column=2).fill = subheader_fill
    ws.cell(row=20, column=2).border = thin_border
    
    # Value Stream Mapping Resultaten
    ws.cell(row=21, column=1, value="Value Stream Mapping Resultaten:").font = header_font
    ws.cell(row=21, column=1).fill = subheader_fill
    ws.cell(row=21, column=1).border = thin_border
    ws.cell(row=21, column=2).fill = subheader_fill
    ws.cell(row=21, column=2).border = thin_border
    
    # VSM data
    vsm_data = [
        ("- Value Added Time (min)", 120.5),
        ("- Non-Value Added Time (min)", 345.2),
        ("- Process Lead Time (min)", 465.7),
        ("- Value Added Ratio (%)", 25.9)
    ]
    
    for i, (label, value) in enumerate(vsm_data):
        row = 22 + i
        ws.cell(row=row, column=1, value=label).font = normal_font
        ws.cell(row=row, column=1).border = thin_border
        ws.cell(row=row, column=2, value=value).font = normal_font
        ws.cell(row=row, column=2).fill = data_fill
        ws.cell(row=row, column=2).border = thin_border
    
    # Pas kolombreedtes aan
    ws.column_dimensions['A'].width = 40
    ws.column_dimensions['B'].width = 15
    ws.column_dimensions['C'].width = 15
    
    # 1. Maak een staafdiagram voor Verspilling Analyse
    waste_chart = BarChart()
    waste_chart.title = "Verspilling Analyse"
    waste_chart.y_axis.title = "Percentage (%)"
    waste_chart.x_axis.title = "Type Verspilling"
    waste_chart.height = 15  # Hoogte van het diagram
    waste_chart.width = 20   # Breedte van het diagram
    
    # Definieer de data voor het staafdiagram direct vanuit de tabel
    waste_data_ref = Reference(ws, min_col=2, min_row=6, max_row=12, max_col=2)
    
    # Gebruik de labels uit kolom A (rij 6-12)
    waste_cats = Reference(ws, min_col=1, min_row=6, max_row=12)
    
    # Voeg de data toe aan het staafdiagram
    waste_chart.add_data(waste_data_ref)
    waste_chart.set_categories(waste_cats)
    
    # Voeg data labels toe om de exacte waarden te tonen
    waste_chart.dataLabels = DataLabelList()
    waste_chart.dataLabels.showVal = True  # Toon de waarden
    waste_chart.dataLabels.showCatName = False  # Verberg categorienamen
    waste_chart.dataLabels.showSerName = False  # Verberg serienamen
    waste_chart.dataLabels.showPercent = False  # Verberg percentages
    waste_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels
    
    # Pas de stijl aan
    waste_chart.style = 10  # Kies een stijl
    
    # Voeg het staafdiagram toe aan het Lean Analyse werkblad naast de tabel
    ws.add_chart(waste_chart, "D5")
    
    # 2. Maak een staafdiagram voor Efficiëntie Metrics
    efficiency_chart = BarChart()
    efficiency_chart.title = "Efficiëntie Metrics"
    efficiency_chart.y_axis.title = "Waarde"
    efficiency_chart.x_axis.title = "Metric"
    efficiency_chart.height = 10  # Hoogte van het diagram
    efficiency_chart.width = 15   # Breedte van het diagram
    
    # Definieer de data voor het staafdiagram direct vanuit de tabel
    efficiency_data_ref = Reference(ws, min_col=2, min_row=15, max_row=19, max_col=2)
    
    # Gebruik de labels uit kolom A (rij 15-19)
    efficiency_cats = Reference(ws, min_col=1, min_row=15, max_row=19)
    
    # Voeg de data toe aan het staafdiagram
    efficiency_chart.add_data(efficiency_data_ref)
    efficiency_chart.set_categories(efficiency_cats)
    
    # Voeg data labels toe om de exacte waarden te tonen
    efficiency_chart.dataLabels = DataLabelList()
    efficiency_chart.dataLabels.showVal = True  # Toon de waarden
    efficiency_chart.dataLabels.showCatName = False  # Verberg categorienamen
    efficiency_chart.dataLabels.showSerName = False  # Verberg serienamen
    efficiency_chart.dataLabels.showPercent = False  # Verberg percentages
    efficiency_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels
    
    # Pas de stijl aan
    efficiency_chart.style = 11  # Kies een andere stijl
    
    # Voeg het staafdiagram toe aan het Lean Analyse werkblad naast de tabel
    ws.add_chart(efficiency_chart, "D14")
    
    # 3. Maak een staafdiagram voor Value Stream Mapping Resultaten
    vsm_chart = BarChart()
    vsm_chart.title = "Value Stream Mapping Resultaten"
    vsm_chart.y_axis.title = "Tijd (min) / Percentage (%)"
    vsm_chart.x_axis.title = "Metric"
    vsm_chart.height = 10  # Hoogte van het diagram
    vsm_chart.width = 15   # Breedte van het diagram
    
    # Definieer de data voor het staafdiagram direct vanuit de tabel
    vsm_data_ref = Reference(ws, min_col=2, min_row=22, max_row=25, max_col=2)
    
    # Gebruik de labels uit kolom A (rij 22-25)
    vsm_cats = Reference(ws, min_col=1, min_row=22, max_row=25)
    
    # Voeg de data toe aan het staafdiagram
    vsm_chart.add_data(vsm_data_ref)
    vsm_chart.set_categories(vsm_cats)
    
    # Voeg data labels toe om de exacte waarden te tonen
    vsm_chart.dataLabels = DataLabelList()
    vsm_chart.dataLabels.showVal = True  # Toon de waarden
    vsm_chart.dataLabels.showCatName = False  # Verberg categorienamen
    vsm_chart.dataLabels.showSerName = False  # Verberg serienamen
    vsm_chart.dataLabels.showPercent = False  # Verberg percentages
    vsm_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels
    
    # Pas de stijl aan
    vsm_chart.style = 12  # Kies een andere stijl
    
    # Voeg het staafdiagram toe aan het Lean Analyse werkblad naast de tabel
    ws.add_chart(vsm_chart, "D21")
    
    # Sla het Excel bestand op
    try:
        wb.save(output_file)
        print(f"Excel bestand met visualisaties is opgeslagen als {output_file}")
    except Exception as e:
        print(f"Fout bij opslaan van bestand: {e}")
        # Als er een fout is, probeer op te slaan in een tijdelijke map
        import tempfile
        temp_dir = tempfile.gettempdir()
        temp_file = os.path.join(temp_dir, 'Lean_Analyse.xlsx')
        wb.save(temp_file)
        print(f"Excel bestand met visualisaties is opgeslagen als {temp_file}")

if __name__ == "__main__":
    create_lean_excel()
