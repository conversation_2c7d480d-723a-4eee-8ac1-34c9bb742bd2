# Conceptueel ERD Voorbeeld (ASCII-visualisatie)

```
+----------+       plaatst       +------------+
|          |-------------------->|            |
|  KLANT   | 1                 N | BESTELLING |
|          |                     |            |
+----------+                     +------------+
                                       |
                                       | bevat
                                       | N:M
                                       v
+----------+     behoort tot     +------------+
|          |<--------------------|            |
| CATEGORIE| 1                 N |  PRODUCT   |
|          |                     |            |
+----------+                     +------------+
```

## Wat is een conceptueel ERD?

Een conceptueel ERD (Entity Relationship Diagram) is een visuele weergave van de belangrijkste entiteiten in een systeem en de relaties tussen deze entiteiten. Het is de meest abstracte vorm van een ERD en bevat:

1. **Entiteiten**: Weergegeven als rechthoeken met de naam van de entiteit (KLANT, BESTELLING, PRODUCT, CATEGORIE)
2. **Relaties**: Weergegeven als lijnen tussen entiteiten met een beschrijving van de relatie (plaatst, bevat, behoort tot)
3. **Cardinaliteit**: Aanduiding van het type relatie (1 = één, N/M = veel)

## Kenmerken van een conceptueel ERD:

- Bevat GEEN attributen (details van entiteiten zoals naam, adres, prijs)
- Toont alleen de hoofdentiteiten en hun relaties
- Is begrijpelijk voor niet-technische stakeholders
- Dient als basis voor het latere logische en fysieke ERD

## Uitleg van het voorbeeld:

- Een KLANT kan meerdere (N) BESTELLINGEN plaatsen, maar elke BESTELLING hoort bij precies één (1) KLANT
- Een BESTELLING kan meerdere (N) PRODUCTEN bevatten, en een PRODUCT kan in meerdere (M) BESTELLINGEN voorkomen (N:M relatie)
- Een PRODUCT behoort tot precies één (1) CATEGORIE, maar een CATEGORIE kan meerdere (N) PRODUCTEN bevatten

Dit is de eerste stap in het databaseontwerpproces, gevolgd door het logische ERD (met attributen) en uiteindelijk het fysieke ERD (met technische details zoals datatypes en sleutels).
