-- EuroCaps Database Creation Script
-- Dit script maakt de database structuur voor EuroCaps op basis van het fysieke ERD
-- en importeert de data uit de CSV-bestanden

-- Database aanmaken
CREATE DATABASE IF NOT EXISTS eurocaps;
USE eurocaps;

-- Tabellen verwijderen als ze al bestaan (in omgekeerde volgorde van afhankelijkheden)
DROP TABLE IF EXISTS Levering_Regel;
DROP TABLE IF EXISTS Levering;
DROP TABLE IF EXISTS Packaging_Product;
DROP TABLE IF EXISTS Packaging;
DROP TABLE IF EXISTS Filling_Product;
DROP TABLE IF EXISTS Filling;
DROP TABLE IF EXISTS Grinding_Product;
DROP TABLE IF EXISTS Grinding;
DROP TABLE IF EXISTS Product;
DROP TABLE IF EXISTS SoortProduct;
DROP TABLE IF EXISTS PartnerContact;
DROP TABLE IF EXISTS Partner;
DROP TABLE IF EXISTS SoortPartner;

-- Tabellen aanmaken

-- SoortPartner tabel
CREATE TABLE SoortPartner (
    SoortPartnerId INT PRIMARY KEY,
    Omschrijving VARCHAR(50) NOT NULL
);

-- Partner tabel
CREATE TABLE Partner (
    PartnerId INT PRIMARY KEY,
    SoortPartnerId INT NOT NULL,
    Bedrijfsnaam VARCHAR(100) NOT NULL,
    Straatnaam VARCHAR(100),
    Huisnummer VARCHAR(10),
    Postcode VARCHAR(10),
    Plaats VARCHAR(50),
    Land VARCHAR(50),
    Email VARCHAR(100),
    Telnr VARCHAR(20),
    FOREIGN KEY (SoortPartnerId) REFERENCES SoortPartner(SoortPartnerId)
);

-- PartnerContact tabel
CREATE TABLE PartnerContact (
    PartnerContactId INT PRIMARY KEY,
    PartnerId INT NOT NULL,
    Voornaam VARCHAR(50) NOT NULL,
    Achternaam VARCHAR(50) NOT NULL,
    Functie VARCHAR(50),
    Email VARCHAR(100),
    Telnr VARCHAR(20),
    FOREIGN KEY (PartnerId) REFERENCES Partner(PartnerId)
);

-- SoortProduct tabel
CREATE TABLE SoortProduct (
    SoortProductId INT PRIMARY KEY,
    Omschrijving VARCHAR(100) NOT NULL,
    Materiaal VARCHAR(50),
    Gewicht DECIMAL(5,2),
    Afmeting VARCHAR(20)
);

-- Product tabel
CREATE TABLE Product (
    ProductId INT PRIMARY KEY,
    SoortProductId INT NOT NULL,
    ProductTHTDatum DATE,
    StatusProduct VARCHAR(20),
    CStatusProduct CHAR(1),
    FStatusProduct CHAR(1),
    PStatusProduct CHAR(1),
    FOREIGN KEY (SoortProductId) REFERENCES SoortProduct(SoortProductId)
);

-- Grinding tabel
CREATE TABLE Grinding (
    GrindingId INT PRIMARY KEY,
    G_DatumTijdStart DATETIME NOT NULL,
    G_DatumTijdEind DATETIME,
    G_Machine VARCHAR(50)
);

-- Grinding_Product koppeltabel
CREATE TABLE Grinding_Product (
    GrindingId INT,
    ProductId INT,
    Aantal INT,
    PRIMARY KEY (GrindingId, ProductId),
    FOREIGN KEY (GrindingId) REFERENCES Grinding(GrindingId),
    FOREIGN KEY (ProductId) REFERENCES Product(ProductId)
);

-- Filling tabel
CREATE TABLE Filling (
    FillingId INT PRIMARY KEY,
    F_DatumTijdStart DATETIME NOT NULL,
    F_DatumTijdEind DATETIME,
    F_Machine VARCHAR(50)
);

-- Filling_Product koppeltabel
CREATE TABLE Filling_Product (
    FillingId INT,
    ProductId INT,
    Aantal INT,
    PRIMARY KEY (FillingId, ProductId),
    FOREIGN KEY (FillingId) REFERENCES Filling(FillingId),
    FOREIGN KEY (ProductId) REFERENCES Product(ProductId)
);

-- Packaging tabel
CREATE TABLE Packaging (
    PackagingId INT PRIMARY KEY,
    P_DatumTijdStart DATETIME NOT NULL,
    P_DatumTijdEind DATETIME,
    P_Machine VARCHAR(50)
);

-- Packaging_Product koppeltabel
CREATE TABLE Packaging_Product (
    PackagingId INT,
    ProductId INT,
    Aantal INT,
    AantalStuksInDoos INT,
    PRIMARY KEY (PackagingId, ProductId),
    FOREIGN KEY (PackagingId) REFERENCES Packaging(PackagingId),
    FOREIGN KEY (ProductId) REFERENCES Product(ProductId)
);

-- Levering tabel
CREATE TABLE Levering (
    LeveringId INT PRIMARY KEY,
    PartnerId INT NOT NULL,
    LeveringDatum DATE NOT NULL,
    VerwachteLeverdatum DATE,
    FOREIGN KEY (PartnerId) REFERENCES Partner(PartnerId)
);

-- Levering_Regel tabel
CREATE TABLE Levering_Regel (
    LeveringId INT,
    ProductId INT,
    Aantal INT,
    PRIMARY KEY (LeveringId, ProductId),
    FOREIGN KEY (LeveringId) REFERENCES Levering(LeveringId),
    FOREIGN KEY (ProductId) REFERENCES Product(ProductId)
);

-- Data importeren uit CSV-bestanden
-- Opmerking: Pas de paden aan naar de locatie van je CSV-bestanden

-- SoortPartner data importeren
LOAD DATA INFILE 'csv_data/soort_partner.csv'
INTO TABLE SoortPartner
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Partner data importeren
LOAD DATA INFILE 'csv_data/partner.csv'
INTO TABLE Partner
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- PartnerContact data importeren
LOAD DATA INFILE 'csv_data/partner_contact.csv'
INTO TABLE PartnerContact
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- SoortProduct data importeren
LOAD DATA INFILE 'csv_data/soort_product.csv'
INTO TABLE SoortProduct
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Product data importeren
LOAD DATA INFILE 'csv_data/product.csv'
INTO TABLE Product
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Grinding data importeren
LOAD DATA INFILE 'csv_data/grinding.csv'
INTO TABLE Grinding
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Grinding_Product data importeren
LOAD DATA INFILE 'csv_data/grinding_product.csv'
INTO TABLE Grinding_Product
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Filling data importeren
LOAD DATA INFILE 'csv_data/filling.csv'
INTO TABLE Filling
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Filling_Product data importeren
LOAD DATA INFILE 'csv_data/filling_product.csv'
INTO TABLE Filling_Product
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Packaging data importeren
LOAD DATA INFILE 'csv_data/packaging.csv'
INTO TABLE Packaging
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Packaging_Product data importeren
LOAD DATA INFILE 'csv_data/packaging_product.csv'
INTO TABLE Packaging_Product
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Levering data importeren
LOAD DATA INFILE 'csv_data/levering.csv'
INTO TABLE Levering
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Levering_Regel data importeren
LOAD DATA INFILE 'csv_data/levering_regel.csv'
INTO TABLE Levering_Regel
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Bevestiging dat alles is geïmporteerd
SELECT 'Database EuroCaps is aangemaakt en gevuld met testdata.' AS Bericht;
