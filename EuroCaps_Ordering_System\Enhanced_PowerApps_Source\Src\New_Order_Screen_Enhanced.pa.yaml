# ************************************************************************************************
# Enhanced New Order Screen for EuroCaps Ordering System
# Based on detailed mockup specifications with complete order creation workflow
# Color Scheme: Header/Sidebar: 2C3E50, Background: 1B3A4B, Panels: A9C6E8, Buttons: F39C12, Text: FFFFFF
# ************************************************************************************************
Screens:
  New_Order_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(243, 156, 18, 1)
      Fill: =RGBA(27, 58, 75, 1)
      OnVisible: |
        =// Initialize order variables
        If(IsBlank(varCurrentOrder), Set(varCurrentOrder, {Items: []}));
        If(IsBlank(varSelectedCustomer), Set(varSelectedCustomer, Blank()));
        Set(varOrderDate, Today());
        Set(varDeliveryDate, Today() + 3);
        Set(varOrderNotes, "");
        // Generate new order number
        Set(varOrderNumber, "ORD-" & Text(Max(colOrders, OrderID) + 1, "0000"));
        // Refresh customer data
        Refresh(colCustomers)
    Children:
      # Header Bar
      - HeaderBar:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(44, 62, 80, 1)
            Height: =60
            Width: =1366
            X: =0
            Y: =0
            BorderThickness: =0

      # Logo in Header
      - HeaderLogo:
          Control: Image@2.1.0
          Properties:
            Image: ="eurocaps-logo"
            Height: =40
            Width: =40
            X: =20
            Y: =10

      # App Title in Header
      - HeaderTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="EuroCaps Order Management Pro"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =300
            X: =70
            Y: =10

      # User Menu
      - UserMenu:
          Control: Classic/Button@2.2.0
          Properties:
            Text: =varUserName & " (" & varUserRole & ") ▼"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =200
            X: =1100
            Y: =10

      # Navigation Menu
      - NavigationMenu:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(44, 62, 80, 1)
            Height: =708
            Width: =200
            X: =0
            Y: =60
            BorderThickness: =0

      # Menu Items
      - MenuDashboard:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="≡ Dashboard"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =60
            Align: =Align.Left
            OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)

      - MenuCustomers:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="👥 Customers"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =110
            Align: =Align.Left
            OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)

      - MenuProducts:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📦 Products"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =160
            Align: =Align.Left
            OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)

      - MenuOrders:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📋 Orders"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =210
            Align: =Align.Left

      # Main Content Area
      - MainContent:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(27, 58, 75, 1)
            Height: =708
            Width: =1166
            X: =200
            Y: =60
            BorderThickness: =0

      # Page Title
      - PageTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="Create New Order"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =18
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =300
            X: =220
            Y: =80

      # Order Number Display
      - OrderNumberLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Order Number: " & varOrderNumber
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(243, 156, 18, 1)
            Height: =30
            Width: =200
            X: =1000
            Y: =85

      # Order Information Panel
      - OrderInfoPanel:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =200
            Width: =500
            X: =220
            Y: =130
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Order Info Title
      - OrderInfoTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="ORDER INFORMATION"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =200
            X: =240
            Y: =145

      # Customer Selection
      - CustomerLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Customer:"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =20
            Width: =80
            X: =240
            Y: =175

      - CustomerDropdown:
          Control: Classic/Dropdown@2.2.0
          Properties:
            Items: =colCustomers
            DisplayFields: =["CustomerName"]
            SearchFields: =["CustomerName", "ContactPerson"]
            DefaultSelectedItems: =If(!IsBlank(varSelectedCustomer), [varSelectedCustomer], [])
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =300
            X: =330
            Y: =170
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varSelectedCustomer, CustomerDropdown.Selected)

      # Order Date
      - OrderDateLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Order Date:"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =20
            Width: =80
            X: =240
            Y: =215

      - OrderDatePicker:
          Control: Classic/DatePicker@2.2.0
          Properties:
            DefaultDate: =varOrderDate
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =150
            X: =330
            Y: =210
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varOrderDate, OrderDatePicker.SelectedDate)

      # Delivery Date
      - DeliveryDateLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Delivery Date:"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =20
            Width: =100
            X: =500
            Y: =215

      - DeliveryDatePicker:
          Control: Classic/DatePicker@2.2.0
          Properties:
            DefaultDate: =varDeliveryDate
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =150
            X: =600
            Y: =210
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varDeliveryDate, DeliveryDatePicker.SelectedDate)

      # Notes
      - NotesLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Notes:"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =20
            Width: =60
            X: =240
            Y: =255

      - NotesInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            Default: =varOrderNotes
            HintText: ="Enter order notes..."
            Mode: =TextMode.MultiLine
            Font: =Font.Arial
            Size: =12
            Height: =60
            Width: =400
            X: =310
            Y: =250
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varOrderNotes, NotesInput.Text)

      # Order Items Panel
      - OrderItemsPanel:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =300
            Width: =500
            X: =750
            Y: =130
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Order Items Title
      - OrderItemsTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="ORDER ITEMS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =200
            X: =770
            Y: =145

      # Add Products Button
      - AddProductsButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="+ Add Products"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =35
            Width: =150
            X: =1070
            Y: =140
            OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)

      # Order Items Gallery
      - OrderItemsGallery:
          Control: Gallery@2.3.0
          Properties:
            Items: =varCurrentOrder.Items
            Height: =200
            Width: =460
            X: =770
            Y: =180
            TemplateSize: =40
            Fill: =RGBA(0, 0, 0, 0)
            BorderThickness: =0
            ShowScrollbar: =true

      # Order Items Gallery Template
      # Item Background
      - ItemBackground:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =35
            Width: =440
            X: =10
            Y: =2
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =1
            RadiusTopLeft: =4
            RadiusTopRight: =4
            RadiusBottomLeft: =4
            RadiusBottomRight: =4

      # Product Name
      - ItemProductName:
          Control: Label@2.5.1
          Properties:
            Text: =ThisItem.ProductName
            Font: =Font.Arial
            Size: =11
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =200
            X: =20
            Y: =7

      # Quantity
      - ItemQuantity:
          Control: Label@2.5.1
          Properties:
            Text: ="Qty: " & ThisItem.Quantity
            Font: =Font.Arial
            Size: =11
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =80
            X: =230
            Y: =7

      # Price
      - ItemPrice:
          Control: Label@2.5.1
          Properties:
            Text: ="€" & Text(ThisItem.Price * ThisItem.Quantity, "0.00")
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =11
            Color: =RGBA(243, 156, 18, 1)
            Height: =25
            Width: =80
            X: =320
            Y: =7

      # Remove Button
      - RemoveItemButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="✕"
            Font: =Font.Arial
            Size: =10
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(220, 53, 69, 1)
            HoverFill: =RGBA(200, 35, 51, 1)
            Height: =25
            Width: =25
            X: =415
            Y: =7
            OnSelect: |
              =Set(varCurrentOrder,
                  {Items: Filter(varCurrentOrder.Items, ProductID <> ThisItem.ProductID)}
              )

      # Order Summary Section
      - OrderSummaryPanel:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =120
            Width: =1030
            X: =220
            Y: =450
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Order Summary Title
      - OrderSummaryTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="ORDER SUMMARY"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =200
            X: =240
            Y: =465

      # Total Items
      - TotalItemsLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Total Items: " & Sum(varCurrentOrder.Items, Quantity)
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =200
            X: =240
            Y: =495

      # Total Amount
      - TotalAmountLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="Total Amount: €" & Text(Sum(varCurrentOrder.Items, Price * Quantity), "0.00")
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(243, 156, 18, 1)
            Height: =25
            Width: =200
            X: =240
            Y: =520

      # Action Buttons Section
      - ActionButtonsPanel:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =80
            Width: =1030
            X: =220
            Y: =590
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Save as Draft Button
      - SaveDraftButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="💾 Save as Draft"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(108, 117, 125, 1)
            HoverFill: =RGBA(90, 98, 104, 1)
            Height: =40
            Width: =150
            X: =240
            Y: =610
            OnSelect: |
              =// Save order as draft
              Patch(colOrders, Defaults(colOrders), {
                  OrderNumber: varOrderNumber,
                  CustomerID: varSelectedCustomer.CustomerID,
                  OrderDate: varOrderDate,
                  DeliveryDate: varDeliveryDate,
                  Status: "draft",
                  Notes: varOrderNotes
              });
              Notify("Order saved as draft", NotificationType.Success);
              Navigate(Dashboard_Screen, ScreenTransition.Fade)

      # Submit Order Button
      - SubmitOrderButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="✅ Submit Order"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =40
            Width: =150
            X: =410
            Y: =610
            DisplayMode: =If(!IsBlank(varSelectedCustomer) && CountRows(varCurrentOrder.Items) > 0, DisplayMode.Edit, DisplayMode.Disabled)
            OnSelect: =Navigate(Order_Confirmation_Screen, ScreenTransition.Fade)

      # Cancel Button
      - CancelButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="❌ Cancel"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(220, 53, 69, 1)
            HoverFill: =RGBA(200, 35, 51, 1)
            Height: =40
            Width: =150
            X: =580
            Y: =610
            OnSelect: |
              =// Clear current order and return to dashboard
              Set(varCurrentOrder, {Items: []});
              Set(varSelectedCustomer, Blank());
              Navigate(Dashboard_Screen, ScreenTransition.Fade)
