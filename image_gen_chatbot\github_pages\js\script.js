// Global function for generating images and responding
window.generateImageAndRespond = async function(prompt, messageElement) {
    console.log('Global generateImageAndRespond called with prompt:', prompt);

    try {
        // Show loading indicator
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'flex';
        }

        // Get current settings
        const resolution = window.userSettings ? window.userSettings.resolution : 'standard';
        const orientation = window.userSettings ? window.userSettings.orientation : 'landscape';

        // Generate a random image URL (simplified for this example)
        let imageUrl;

        // Use cat images for cat-related prompts
        if (prompt.toLowerCase().includes('cat') || prompt.toLowerCase().includes('kitten')) {
            const catImages = [
                'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba',
                'https://images.unsplash.com/photo-1573865526739-10659fec78a5',
                'https://images.unsplash.com/photo-1495360010541-f48722b34f7d',
                'https://images.unsplash.com/photo-1518791841217-8f162f1e1131'
            ];
            const randomIndex = Math.floor(Math.random() * catImages.length);
            imageUrl = catImages[randomIndex];
        }
        // Use beach images for beach-related prompts
        else if (prompt.toLowerCase().includes('beach')) {
            const beachImages = [
                'https://images.unsplash.com/photo-1507525428034-b723cf961d3e',
                'https://images.unsplash.com/photo-1519046904884-53103b34b206',
                'https://images.unsplash.com/photo-1535498730771-e735b998cd64',
                'https://images.unsplash.com/photo-1473496169904-658ba7c44d8a'
            ];
            const randomIndex = Math.floor(Math.random() * beachImages.length);
            imageUrl = beachImages[randomIndex];
        }
        // Use a default image for other prompts
        else {
            imageUrl = 'https://source.unsplash.com/random/800x600/?'+encodeURIComponent(prompt);
        }

        // Add cache-busting parameters
        const timestamp = Date.now();
        imageUrl = `${imageUrl}?t=${timestamp}`;

        // Create the image element
        const img = document.createElement('img');
        img.className = 'generated-image';
        img.alt = `Image of ${prompt}`;

        // Wait for the image to load
        await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = imageUrl;

            // Set a timeout in case the image takes too long to load
            setTimeout(resolve, 5000);
        });

        // Update the message with the image
        if (messageElement) {
            messageElement.innerHTML = `
                <div class="message-content">
                    Here's an image of "${prompt}".
                    <div class="image-container">
                        <img src="${imageUrl}" alt="Image of ${prompt}" class="generated-image">
                        <div class="image-controls">
                            <div class="image-info">Generated image</div>
                            <div class="image-actions">
                                <button class="download-button" onclick="downloadImage('${imageUrl}', '${prompt}')">Download</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Hide loading indicator
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }

        return imageUrl;
    } catch (error) {
        console.error('Error generating image:', error);

        // Update message with error
        if (messageElement) {
            messageElement.innerHTML = `
                <div class="message-content">
                    Sorry, I couldn't generate that image. Error: ${error.message}
                </div>
            `;
        }

        // Hide loading indicator
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }

        return null;
    }
};

// Global function for downloading images
window.downloadImage = function(imageUrl, prompt) {
    console.log('Downloading image:', imageUrl);

    // Create a temporary link element
    const a = document.createElement('a');
    a.href = imageUrl;
    a.download = `image-${prompt.replace(/[^a-z0-9]/gi, '-').toLowerCase()}.jpg`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
};

// Global settings
window.userSettings = {
    resolution: 'standard',
    orientation: 'landscape',
    lastPrompt: ''
};

document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const welcomeContainer = document.getElementById('welcome-container');
    const chatMessages = document.getElementById('chat-messages');
    const chatInput = document.getElementById('message-form'); // Updated to use the form
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');
    const startChatButton = document.getElementById('start-chat-button');
    const loadingIndicator = document.getElementById('loading-indicator');
    const imageResolutionSelect = document.getElementById('image-resolution');
    const imageOrientationSelect = document.getElementById('image-orientation');

    // Image resolution presets
    const resolutionPresets = {
        standard: { width: 1200, height: 800 },
        hd: { width: 1920, height: 1080 },
        ultrawide: { width: 3440, height: 1440 },
        '4k': { width: 3840, height: 2160 }
    };

    // Store user settings - make it globally accessible
    window.userSettings = {
        resolution: 'standard',
        orientation: 'landscape',
        lastPrompt: ''
    };

    // Local reference for convenience
    let userSettings = window.userSettings;

    // Save settings when changed
    imageResolutionSelect.addEventListener('change', function() {
        userSettings.resolution = this.value;
    });

    imageOrientationSelect.addEventListener('change', function() {
        userSettings.orientation = this.value;
    });

    // Start chat when button is clicked - simplified implementation
    console.log('Setting up start chat button click handler');

    // Global function to start chat (can be called from anywhere)
    window.startChatGlobal = function() {
        console.log('Global startChat function called');

        // Save settings
        if (imageResolutionSelect && imageOrientationSelect) {
            window.userSettings.resolution = imageResolutionSelect.value;
            window.userSettings.orientation = imageOrientationSelect.value;
        }

        // Show chat interface
        if (welcomeContainer) welcomeContainer.style.display = 'none';
        if (chatMessages) chatMessages.style.display = 'block';
        if (chatInput) chatInput.style.display = 'flex';

        // Show extra buttons if the function exists
        if (window.showExtraButtons) {
            console.log('Showing extra buttons');
            window.showExtraButtons();
        } else {
            console.log('showExtraButtons function not found');
            // Try to show extra buttons directly
            const extraButtons = document.getElementById('extra-buttons');
            if (extraButtons) {
                extraButtons.style.display = 'block';
            }
        }

        // Focus on input field
        if (userInput) {
            setTimeout(() => {
                userInput.focus();
            }, 100);
        }
    };

    // Add click handler to button if it exists
    if (startChatButton) {
        startChatButton.onclick = function(e) {
            console.log('Start chat button clicked');
            e.preventDefault();
            window.startChatGlobal();
            return false;
        };
    } else {
        console.error('Start chat button not found in the DOM');
    }

    // Function to get dimensions based on settings
    function getImageDimensions() {
        let { width, height } = resolutionPresets[userSettings.resolution];

        // Adjust for orientation
        if (userSettings.orientation === 'portrait') {
            [width, height] = [height, width]; // Swap dimensions
        } else if (userSettings.orientation === 'square') {
            const size = Math.max(width, height);
            width = height = size;
        }

        return { width, height };
    }

    // Function to download an image
    function downloadImage(url, filename) {
        // Create a temporary link
        const a = document.createElement('a');
        a.href = url;
        a.download = filename || 'generated-image.jpg';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    // Function to regenerate an image with the same prompt
    async function regenerateImage(promptText, messageElement) {
        try {
            // Show loading indicator
            loadingIndicator.style.display = 'flex';

            // Add a slight variation to the prompt to ensure we get a different image
            // This helps APIs that might cache results for identical prompts
            const variations = [
                ' different view',
                ' alternative',
                ' another perspective',
                ' variation',
                ' similar',
                ' new angle'
            ];

            // Randomly decide whether to add a variation or use a timestamp
            let modifiedPrompt;
            if (Math.random() > 0.5) {
                // Add a random variation to the prompt
                const randomVariation = variations[Math.floor(Math.random() * variations.length)];
                modifiedPrompt = promptText + randomVariation;
                console.log('Regenerating with modified prompt:', modifiedPrompt);
            } else {
                // Add a timestamp to make the prompt unique
                modifiedPrompt = promptText + ' ' + Date.now();
                console.log('Regenerating with timestamped prompt');
            }

            // Generate a new image with the modified prompt
            const imageUrl = await generateImage(modifiedPrompt);

            // Find the image element in the message
            const imageContainer = messageElement.querySelector('.image-container');
            const oldImage = imageContainer.querySelector('.generated-image');
            const imageControls = imageContainer.querySelector('.image-controls');

            // Create a new image
            const newImage = document.createElement('img');
            newImage.src = imageUrl;
            newImage.alt = 'Generated Image';
            newImage.className = 'generated-image';

            // Handle image loading events
            newImage.onload = function() {
                console.log('Regenerated image loaded successfully');
            };

            newImage.onerror = function() {
                console.error('Failed to load regenerated image');
            };

            // Replace the old image with the new one
            imageContainer.replaceChild(newImage, oldImage);

            // Update the download button
            const downloadButton = imageControls.querySelector('.download-button');
            downloadButton.onclick = function() {
                downloadImage(imageUrl, `image-${promptText.replace(/\s+/g, '-').substring(0, 20)}.jpg`);
            };

            // Update the regenerate button
            const regenerateButton = imageControls.querySelector('.new-image-button');
            regenerateButton.onclick = function() {
                regenerateImage(promptText, messageElement);
            };

        } catch (error) {
            console.error('Error regenerating image:', error);
        } finally {
            loadingIndicator.style.display = 'none';
        }
    }

    // Function to add a message to the chat
    function addMessage(message, isUser, promptText = '') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        // Check if the message contains an image
        if (!isUser && message.includes('[IMAGE_DATA:')) {
            // Extract the text part and image data
            const parts = message.split('[IMAGE_DATA:');
            const textPart = parts[0];
            const imageData = parts[1].replace(']', '');

            // Add the text part
            messageContent.textContent = textPart;

            // Create a container for the image
            const imageContainer = document.createElement('div');
            imageContainer.className = 'image-container';

            // Add a loading indicator for the image
            const imageLoading = document.createElement('div');
            imageLoading.className = 'image-loading';
            imageLoading.innerHTML = 'Loading image...';
            imageContainer.appendChild(imageLoading);

            // Create and add the image
            const image = document.createElement('img');
            image.src = imageData;
            image.alt = 'Generated Image';
            image.className = 'generated-image';

            // Get dimensions for display
            const dimensions = getImageDimensions();

            // Handle image loading events
            image.onload = function() {
                // Remove loading indicator when image loads
                imageLoading.style.display = 'none';
                // Make sure the image is visible
                image.style.display = 'block';

                // Create image controls
                const imageControls = document.createElement('div');
                imageControls.className = 'image-controls';

                // Add image info
                const imageInfo = document.createElement('div');
                imageInfo.className = 'image-info';
                imageInfo.textContent = `${dimensions.width}×${dimensions.height} • ${userSettings.orientation}`;
                imageControls.appendChild(imageInfo);

                // Add image actions
                const imageActions = document.createElement('div');
                imageActions.className = 'image-actions';

                // Add download button
                const downloadButton = document.createElement('button');
                downloadButton.className = 'download-button';
                downloadButton.textContent = 'Download';
                downloadButton.onclick = function() {
                    downloadImage(imageData, `image-${promptText.replace(/\s+/g, '-').substring(0, 20)}.jpg`);
                };
                imageActions.appendChild(downloadButton);

                // Add regenerate button
                const regenerateButton = document.createElement('button');
                regenerateButton.className = 'new-image-button';
                regenerateButton.textContent = 'New Image';
                regenerateButton.onclick = function() {
                    regenerateImage(promptText, messageDiv);
                };
                imageActions.appendChild(regenerateButton);

                // Add actions to controls
                imageControls.appendChild(imageActions);

                // Add controls to container
                imageContainer.appendChild(imageControls);

                // Scroll to show the image
                chatMessages.scrollTop = chatMessages.scrollHeight;

                // Log success for debugging
                console.log('Image loaded successfully:', imageData);
            };

            image.onerror = function() {
                // Show error message if image fails to load
                imageLoading.innerHTML = 'Failed to load image. <a href="' + imageData + '" target="_blank">Click here to open in new tab</a>';
                imageLoading.className = 'image-error';

                // Log error for debugging
                console.error('Failed to load image:', imageData);
            };

            // Add the image to the container
            imageContainer.appendChild(image);

            // Add both to the message
            messageDiv.appendChild(messageContent);
            messageDiv.appendChild(imageContainer);
        } else {
            messageContent.textContent = message;
            messageDiv.appendChild(messageContent);
        }

        chatMessages.appendChild(messageDiv);

        // Scroll to the bottom of the chat
        chatMessages.scrollTop = chatMessages.scrollHeight;

        return messageDiv;
    }

    // Function to improve the prompt for better image results
    function enhancePrompt(prompt) {
        // Convert to lowercase and trim
        let enhancedPrompt = prompt.toLowerCase().trim();

        // Get current orientation to enhance prompt accordingly
        const orientation = userSettings.orientation;

        // Create a mapping of specific subjects to detailed descriptions
        // Enhanced with more detailed descriptions for humans and animals
        const subjectEnhancements = {
            // Nature subjects
            'sunset': 'beautiful sunset with orange and red sky, golden hour',
            'mountain': 'majestic mountains, peaks, scenic landscape, nature',
            'beach': 'tropical beach, clear blue water, sandy shores, ocean waves',
            'forest': 'dense forest, tall trees, woodland, lush greenery',
            'city': 'urban cityscape, skyline, buildings, architecture',
            'water': 'clear water, liquid, flowing water, aquatic',
            'sky': 'blue sky, clouds, atmosphere, heavens',
            'tree': 'tall tree, plant, trunk, branches, leaves',
            'river': 'flowing river, stream, water current, nature',
            'lake': 'calm lake, body of water, reflection, nature scene',
            'ocean': 'vast ocean, sea, waves, marine, deep blue water',
            'snow': 'white snow, winter scene, snowflakes, cold weather',
            'rain': 'rainfall, water droplets, wet weather, precipitation',
            'night': 'dark night, evening scene, stars, moonlight',
            'day': 'daylight, sunny day, bright scene, daytime',
            'flower': 'blooming flower, colorful petals, botanical, garden',
            'garden': 'flowering garden, plants, landscaped area, botanical',
            'park': 'public park, green space, recreational area',
            'desert': 'arid desert, sand dunes, dry landscape, barren',
            'jungle': 'tropical jungle, dense vegetation, rainforest',
            'island': 'isolated island, land surrounded by water, tropical',

            // Human subjects - enhanced with more detail
            'person': 'portrait of a person, human face, natural lighting, professional portrait photography',
            'man': 'portrait of a man, male face, natural expression, professional portrait photography',
            'woman': 'portrait of a woman, female face, natural expression, professional portrait photography',
            'child': 'portrait of a child, young person, innocent expression, professional portrait photography',
            'boy': 'portrait of a boy, young male, natural expression, professional portrait photography',
            'girl': 'portrait of a girl, young female, natural expression, professional portrait photography',
            'people': 'group of people, diverse individuals, social gathering, professional photography',
            'human': 'human portrait, person, individual, professional portrait photography',
            'portrait': 'professional portrait, face close-up, studio lighting, high quality photography',
            'face': 'human face, detailed features, expressive, professional portrait',
            'smile': 'smiling person, happy expression, portrait, positive emotion',
            'family': 'family portrait, group of related people, togetherness, professional photography',
            'couple': 'couple portrait, two people, relationship, professional photography',
            'baby': 'baby portrait, infant, cute, innocent, professional photography',
            'teenager': 'teenage portrait, young person, youth, professional photography',
            'senior': 'senior portrait, elderly person, wisdom, professional photography',

            // Animal subjects - enhanced with more detail
            'animal': 'wildlife photography, animal in natural habitat, detailed, professional nature photography',
            'cat': 'domestic cat, feline pet, cute cat face, professional pet photography',
            'dog': 'domestic dog, canine pet, cute dog portrait, professional pet photography',
            'bird': 'beautiful bird, feathered animal, wildlife photography, natural habitat',
            'fish': 'colorful fish, underwater photography, aquatic animal, marine life',
            'horse': 'majestic horse, equine, strong animal, professional animal photography',
            'lion': 'powerful lion, wild cat, king of the jungle, wildlife photography',
            'tiger': 'striped tiger, wild cat, powerful predator, wildlife photography',
            'elephant': 'large elephant, gentle giant, wildlife photography, natural habitat',
            'giraffe': 'tall giraffe, long neck, spotted pattern, wildlife photography',
            'zebra': 'striped zebra, African wildlife, black and white pattern, natural habitat',
            'bear': 'wild bear, powerful mammal, wildlife photography, natural habitat',
            'wolf': 'wild wolf, canine predator, wildlife photography, natural habitat',
            'fox': 'red fox, cunning animal, wildlife photography, natural habitat',
            'rabbit': 'cute rabbit, bunny, furry animal, wildlife photography',
            'monkey': 'playful monkey, primate, expressive animal, wildlife photography',
            'snake': 'exotic snake, reptile, scaled animal, wildlife photography',
            'turtle': 'shelled turtle, reptile, slow animal, wildlife photography',
            'frog': 'green frog, amphibian, jumping animal, wildlife photography',
            'penguin': 'waddling penguin, arctic bird, black and white, wildlife photography',
            'owl': 'wise owl, nocturnal bird, large eyes, wildlife photography',
            'eagle': 'soaring eagle, bird of prey, powerful wings, wildlife photography',
            'shark': 'powerful shark, ocean predator, underwater photography, marine life',
            'dolphin': 'playful dolphin, marine mammal, underwater photography, ocean life',
            'whale': 'massive whale, marine mammal, underwater photography, ocean giant',
            'butterfly': 'colorful butterfly, delicate wings, insect, macro photography',
            'bee': 'busy bee, pollinator, insect, macro photography',
            'spider': 'detailed spider, arachnid, web spinner, macro photography',
            'kitten': 'adorable kitten, baby cat, playful, cute pet photography',
            'puppy': 'cute puppy, baby dog, playful, adorable pet photography',

            // Other common subjects
            'car': 'automobile, vehicle, modern car, transportation photography',
            'house': 'residential home, building, architecture, property photography',
            'food': 'delicious meal, cuisine, gourmet food, culinary photography',
            'building': 'architectural structure, edifice, construction, architectural photography',
            'road': 'paved road, pathway, street, highway, landscape photography',
            'bridge': 'crossing bridge, architectural structure, span, engineering photography',
            'castle': 'medieval castle, fortress, historical building, architectural photography',
            'church': 'religious building, place of worship, architecture, historical photography',
            'school': 'educational institution, learning center, building, architectural photography',
            'hospital': 'medical facility, healthcare building, institution, architectural photography',
            'restaurant': 'dining establishment, eatery, food service, interior photography',
            'shop': 'retail store, commercial establishment, business, interior photography',
            'market': 'marketplace, vendors, shopping area, commerce, street photography',
            'farm': 'agricultural land, countryside, rural setting, landscape photography',
            'factory': 'industrial facility, manufacturing plant, production, industrial photography',
            'airport': 'aviation facility, terminals, runways, planes, transportation photography',
            'train': 'railway vehicle, locomotive, transportation, vehicle photography',
            'boat': 'watercraft, vessel, sailing, nautical, marine photography',
            'ship': 'large vessel, maritime, ocean liner, naval, marine photography',
            'bicycle': 'bike, cycling, two-wheeled vehicle, transportation photography',
            'motorcycle': 'motorbike, two-wheeled motor vehicle, riding, vehicle photography',
            'truck': 'large vehicle, commercial transport, hauling, vehicle photography',
            'bus': 'public transport vehicle, passenger carrier, transportation photography',
            'helicopter': 'rotorcraft, flying vehicle, aviation, aerial photography',
            'airplane': 'aircraft, flying vehicle, aviation, jet, aerial photography'
        };

        // Check for specific combinations and patterns

        // Human patterns - basic portraits with orientation consideration
        if (enhancedPrompt.match(/portrait\s+of\s+(a|an)?\s+(person|man|woman|child|boy|girl)/i)) {
            if (orientation === 'portrait') {
                return `professional portrait photography, vertical composition, natural lighting, detailed facial features, expressive, high quality portrait, ${enhancedPrompt}, realistic photo`;
            } else if (orientation === 'landscape') {
                return `professional photography, horizontal composition, environmental portrait, natural lighting, ${enhancedPrompt}, realistic photo`;
            } else {
                return `professional portrait photography, natural lighting, detailed facial features, expressive, high quality portrait, ${enhancedPrompt}, realistic photo`;
            }
        }

        if (enhancedPrompt.match(/(happy|smiling|laughing)\s+(person|man|woman|child|boy|girl)/i)) {
            if (orientation === 'portrait') {
                return `happy portrait, vertical composition, joyful expression, natural smile, positive emotion, ${enhancedPrompt}, professional photography, realistic`;
            } else if (orientation === 'landscape') {
                return `happy person, horizontal composition, joyful expression, natural smile, environmental portrait, ${enhancedPrompt}, professional photography, realistic`;
            } else {
                return `happy portrait, joyful expression, natural smile, positive emotion, ${enhancedPrompt}, professional photography, realistic`;
            }
        }

        if (enhancedPrompt.match(/group\s+of\s+people/i)) {
            if (orientation === 'landscape') {
                return `wide group photo, horizontal composition, multiple people, social gathering, diverse individuals, ${enhancedPrompt}, professional photography, realistic`;
            } else if (orientation === 'portrait') {
                return `vertical group portrait, stacked composition, multiple people, social gathering, ${enhancedPrompt}, professional photography, realistic`;
            } else {
                return `group portrait, multiple people, social gathering, diverse individuals, ${enhancedPrompt}, professional photography, realistic`;
            }
        }

        // Beach/swimwear patterns with orientation consideration
        if (enhancedPrompt.match(/beach\s+.*\b(people|person|man|woman|girl|boy|women|men)\b/i) ||
            enhancedPrompt.match(/\b(people|person|man|woman|girl|boy|women|men)\b.*beach/i)) {
            if (orientation === 'landscape') {
                return `wide beach scene, horizontal composition, people enjoying beach day, sunny tropical beach, vacation, ${enhancedPrompt}, professional photography, realistic photo`;
            } else if (orientation === 'portrait') {
                return `vertical beach portrait, person at the beach, sunny tropical setting, vacation, ${enhancedPrompt}, professional photography, realistic photo`;
            } else {
                return `beautiful beach scene, people enjoying beach day, sunny tropical beach, vacation, ${enhancedPrompt}, professional photography, realistic photo`;
            }
        }

        if (enhancedPrompt.match(/\b(swimwear|swimming|sunbathing|bikini|swimsuit)\b.*\b(people|person|man|woman|girl|boy|women|men)\b/i) ||
            enhancedPrompt.match(/\b(people|person|man|woman|girl|boy|women|men)\b.*\b(swimwear|swimming|sunbathing|bikini|swimsuit)\b/i)) {
            if (orientation === 'landscape') {
                return `wide beach scene, horizontal composition, people at beach, summer vacation, tropical paradise, ${enhancedPrompt}, professional photography, realistic photo`;
            } else if (orientation === 'portrait') {
                return `vertical beach portrait, person at the beach, summer vacation, tropical paradise, ${enhancedPrompt}, professional photography, realistic photo`;
            } else {
                return `people at beach, summer vacation, tropical paradise, ${enhancedPrompt}, professional photography, realistic photo`;
            }
        }

        // Complex human patterns with orientation consideration
        if (enhancedPrompt.match(/\b(cute|beautiful|handsome|pretty)\b.*\b(person|man|woman|girl|boy|women|men)\b/i) ||
            enhancedPrompt.match(/\b(person|man|woman|girl|boy|women|men)\b.*\b(cute|beautiful|handsome|pretty)\b/i)) {
            if (orientation === 'portrait') {
                return `vertical portrait, attractive person, professional portrait photography, ${enhancedPrompt}, high quality photography, realistic photo`;
            } else if (orientation === 'landscape') {
                return `horizontal composition, attractive person, environmental portrait, ${enhancedPrompt}, high quality photography, realistic photo`;
            } else {
                return `attractive person, professional portrait, ${enhancedPrompt}, high quality photography, realistic photo`;
            }
        }

        // Animal patterns with orientation consideration
        if (enhancedPrompt.match(/(cute|adorable)\s+(cat|kitten|dog|puppy)/i)) {
            if (orientation === 'portrait') {
                return `vertical pet portrait, adorable pet, cute animal, expressive face, ${enhancedPrompt}, professional pet photography, realistic photo`;
            } else if (orientation === 'landscape') {
                return `horizontal pet portrait, adorable pet in environment, cute animal, ${enhancedPrompt}, professional pet photography, realistic photo`;
            } else {
                return `adorable pet portrait, cute animal, expressive face, ${enhancedPrompt}, professional pet photography, realistic photo`;
            }
        }

        if (enhancedPrompt.match(/wild\s+(lion|tiger|elephant|giraffe|zebra|bear|wolf)/i)) {
            if (orientation === 'landscape') {
                return `wide wildlife scene, horizontal composition, animal in natural habitat, majestic, ${enhancedPrompt}, professional nature photography, realistic photo`;
            } else if (orientation === 'portrait') {
                return `vertical wildlife portrait, close-up animal, majestic, ${enhancedPrompt}, professional nature photography, realistic photo`;
            } else {
                return `wildlife photography, animal in natural habitat, majestic, ${enhancedPrompt}, professional nature photography, realistic photo`;
            }
        }

        if (enhancedPrompt.match(/(dog|cat|horse|bird)\s+(playing|running|sleeping)/i)) {
            if (orientation === 'landscape') {
                return `wide animal scene, horizontal composition, animal behavior, pet in environment, ${enhancedPrompt}, natural setting, professional photography, realistic photo`;
            } else if (orientation === 'portrait') {
                return `vertical animal portrait, close-up pet, animal behavior, ${enhancedPrompt}, professional photography, realistic photo`;
            } else {
                return `animal behavior, pet photography, ${enhancedPrompt}, natural setting, professional photography, realistic photo`;
            }
        }

        // Nature patterns with orientation consideration
        if (enhancedPrompt.includes('sunset') && enhancedPrompt.includes('mountain')) {
            if (orientation === 'landscape') {
                return 'beautiful sunset over mountains with orange and purple sky, wide panoramic landscape, scenic vista, nature photography, realistic, high quality';
            } else if (orientation === 'portrait') {
                return 'beautiful sunset over mountains with orange and purple sky, vertical composition, tall peaks, nature photography, realistic, high quality';
            } else {
                return 'beautiful sunset over mountains with orange and purple sky, scenic landscape, nature photography, realistic, high quality';
            }
        }

        if (enhancedPrompt.includes('beach') && enhancedPrompt.includes('tropical')) {
            if (orientation === 'landscape') {
                return 'tropical beach paradise with palm trees, white sand, clear turquoise water, wide shoreline view, sunny day, vacation destination, realistic photo';
            } else if (orientation === 'portrait') {
                return 'tropical beach paradise with tall palm trees, white sand, clear turquoise water, vertical beach scene, sunny day, vacation destination, realistic photo';
            } else {
                return 'tropical beach paradise with palm trees, white sand, clear turquoise water, sunny day, vacation destination, realistic photo';
            }
        }

        // Build a more specific prompt based on detected keywords
        let detectedKeywords = [];
        let enhancementTerms = [];

        // Check for each subject in our mapping
        for (const [subject, enhancement] of Object.entries(subjectEnhancements)) {
            if (enhancedPrompt.includes(subject)) {
                detectedKeywords.push(subject);
                enhancementTerms.push(enhancement);
            }
        }

        // If we found specific keywords, create an enhanced prompt
        if (detectedKeywords.length > 0) {
            // Start with the original prompt
            let newPrompt = enhancedPrompt;

            // Add the specific enhancements
            newPrompt += ', ' + enhancementTerms.join(', ');

            // Add general quality terms
            newPrompt += ', high quality, professional photography, realistic photo';

            console.log('Enhanced prompt with specific keywords:', detectedKeywords);
            return newPrompt;
        }

        // For any other prompt, add general enhancement terms
        return `${enhancedPrompt}, realistic photo, high quality, professional photography, detailed, clear image`;
    }

    // Function to get a random image from Unsplash based on a prompt
    async function getRandomImageFromUnsplash(prompt) {
        try {
            // Check for specific complex prompts that need special handling
            const lowerPrompt = prompt.toLowerCase();

            // Special case for "girls with bikinis on the beach" type prompts
            if ((lowerPrompt.includes('girl') || lowerPrompt.includes('woman') ||
                 lowerPrompt.includes('women') || lowerPrompt.includes('girls')) &&
                (lowerPrompt.includes('bikini') || lowerPrompt.includes('swimsuit') ||
                 lowerPrompt.includes('swimwear')) &&
                lowerPrompt.includes('beach')) {

                console.log('Using special search terms for beach/bikini prompt in Unsplash');

                // Use very specific search terms for this case
                const specificKeywords = ['woman', 'beach', 'summer'];

                // Get dimensions based on user settings
                const dimensions = getImageDimensions();

                // Format keywords for URL
                const keywordString = specificKeywords.join(',');

                console.log('Using specific Unsplash search terms for beach/bikini:', keywordString);

                // Add a random number to ensure we get different images each time
                const randomSeed = Math.floor(Math.random() * 10000);
                const timestamp = Date.now();

                // Use Unsplash Source API with specific terms
                const imageUrl = `https://source.unsplash.com/${dimensions.width}x${dimensions.height}/?${keywordString}&random=${randomSeed}&t=${timestamp}`;

                // Fetch the image to get the final URL after redirects
                const response = await fetch(imageUrl, {
                    // Add cache control headers to prevent caching
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });

                // Check if we got a valid response
                if (!response.ok) {
                    throw new Error(`Unsplash API error: ${response.status} ${response.statusText}`);
                }

                // Add a cache-busting parameter to prevent browser caching
                const finalUrl = `${response.url}?t=${timestamp}&r=${randomSeed}`;
                console.log('Unsplash image URL for beach/bikini:', finalUrl);

                return finalUrl;
            }

            // For other prompts, use the standard approach
            // Enhance the prompt for better results
            const enhancedPrompt = enhancePrompt(prompt);

            // Extract the most important keywords (max 3-4 keywords for better results)
            const keywords = extractKeywords(enhancedPrompt, 4);

            // Get dimensions based on user settings
            const dimensions = getImageDimensions();

            // Format keywords for URL with randomization
            // Shuffle the keywords order slightly for more variety
            if (keywords.length > 1) {
                // Randomly swap some keywords
                for (let i = 0; i < keywords.length; i++) {
                    const j = Math.floor(Math.random() * keywords.length);
                    [keywords[i], keywords[j]] = [keywords[j], keywords[i]];
                }
            }

            // Add a random qualifier sometimes
            const qualifiers = ['beautiful', 'stunning', 'amazing', 'scenic', 'impressive', 'colorful'];
            if (Math.random() > 0.5) {
                const randomQualifier = qualifiers[Math.floor(Math.random() * qualifiers.length)];
                keywords.unshift(randomQualifier);
            }

            const keywordString = keywords.join(',');

            console.log('Fetching Unsplash image with keywords:', keywordString);
            console.log('Original prompt:', prompt);
            console.log('Enhanced prompt:', enhancedPrompt);
            console.log('Dimensions:', dimensions.width, 'x', dimensions.height);

            // Add a random number to ensure we get different images each time
            const randomSeed = Math.floor(Math.random() * 10000);
            const timestamp = Date.now();

            // Use Unsplash Source API to get a random image related to the prompt
            // This is a free API that doesn't require authentication
            const imageUrl = `https://source.unsplash.com/${dimensions.width}x${dimensions.height}/?${keywordString}&random=${randomSeed}&t=${timestamp}`;

            // Fetch the image to get the final URL after redirects
            const response = await fetch(imageUrl, {
                // Add cache control headers to prevent caching
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            // Check if we got a valid response
            if (!response.ok) {
                throw new Error(`Unsplash API error: ${response.status} ${response.statusText}`);
            }

            // Add a cache-busting parameter to prevent browser caching
            const finalUrl = `${response.url}?t=${timestamp}&r=${randomSeed}`;
            console.log('Unsplash image URL:', finalUrl);

            // Verify that we're not getting a random image (which happens when no results are found)
            if (finalUrl.includes('source.unsplash.com/random')) {
                console.warn('Unsplash returned a random image, which may not match the prompt');
                // Try with fewer keywords
                if (keywords.length > 1) {
                    console.log('Retrying with fewer keywords');
                    return getRandomImageFromUnsplash(keywords[0] + ' ' + keywords[1]);
                }
            }

            return finalUrl;
        } catch (error) {
            console.error('Unsplash API error:', error);
            throw error;
        }
    }

    // Function to extract the most important keywords from a prompt
    function extractKeywords(prompt, maxKeywords = 3) {
        // Remove common words and keep only meaningful keywords
        const stopWords = ['a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'as', 'of', 'from'];

        // Split the prompt into words
        const words = prompt.toLowerCase().split(/\s+/);

        // Filter out stop words and short words
        const keywords = words.filter(word => {
            return word.length > 2 && !stopWords.includes(word) && !/^\d+$/.test(word);
        });

        // Prioritize certain important words
        const priorityWords = ['sunset', 'mountain', 'beach', 'forest', 'city', 'animal', 'cat', 'dog', 'flower', 'car'];

        // Sort keywords by priority and length (longer words often more specific)
        const sortedKeywords = keywords.sort((a, b) => {
            const aPriority = priorityWords.includes(a) ? 1 : 0;
            const bPriority = priorityWords.includes(b) ? 1 : 0;

            if (aPriority !== bPriority) {
                return bPriority - aPriority; // Higher priority first
            }

            return b.length - a.length; // Longer words first
        });

        // Return the top keywords (up to maxKeywords)
        return sortedKeywords.slice(0, maxKeywords);
    }

    // Function to get an image from Picsum (Lorem Picsum) as a fallback
    function getRandomPicsumImage() {
        // Generate a truly random ID with a wider range
        const randomId = Math.floor(Math.random() * 10000) + 1;

        // Add multiple cache-busting parameters
        const timestamp = Date.now();
        const randomParam = Math.floor(Math.random() * 1000000);

        // Get dimensions based on user settings
        const dimensions = getImageDimensions();

        // Randomly adjust dimensions slightly for more variety
        const width = dimensions.width + (Math.random() > 0.5 ? Math.floor(Math.random() * 50) : 0);
        const height = dimensions.height + (Math.random() > 0.5 ? Math.floor(Math.random() * 50) : 0);

        // Randomly choose between seed-based and random images
        let imageUrl;
        if (Math.random() > 0.5) {
            // Use seed-based random image
            imageUrl = `https://picsum.photos/seed/${randomId}/${width}/${height}?t=${timestamp}&r=${randomParam}`;
        } else {
            // Use completely random image
            imageUrl = `https://picsum.photos/${width}/${height}?random=${randomParam}&t=${timestamp}`;
        }

        console.log('Using Picsum fallback image:', imageUrl);

        return imageUrl;
    }

    // Function to get an image from the free Pixabay API
    async function getImageFromPixabay(prompt) {
        try {
            // Pixabay API key (this is a free public API key with limited usage)
            // In a real application, you might want to hide this or use a server-side proxy
            const pixabayApiKey = '**********************************';

            // Check for specific complex prompts that need special handling
            const lowerPrompt = prompt.toLowerCase();

            // Special case for "girls with bikinis on the beach" type prompts
            if ((lowerPrompt.includes('girl') || lowerPrompt.includes('woman') ||
                 lowerPrompt.includes('women') || lowerPrompt.includes('girls')) &&
                (lowerPrompt.includes('bikini') || lowerPrompt.includes('swimsuit') ||
                 lowerPrompt.includes('swimwear')) &&
                lowerPrompt.includes('beach')) {

                console.log('Using special search terms for beach/bikini prompt');

                // Use very specific search terms for this case
                const specificSearchTerm = 'woman+beach';

                // Get dimensions based on user settings
                const dimensions = getImageDimensions();

                // Determine orientation for API request
                let orientation = 'horizontal';
                if (userSettings.orientation === 'portrait') {
                    orientation = 'vertical';
                } else if (userSettings.orientation === 'square') {
                    orientation = 'all';
                }

                console.log('Using specific Pixabay search term for beach/bikini:', specificSearchTerm);

                // Make a targeted API request
                const response = await fetch(
                    `https://pixabay.com/api/?key=${pixabayApiKey}&q=${specificSearchTerm}&image_type=photo&per_page=50&safesearch=true&orientation=${orientation}&min_width=${dimensions.width}&min_height=${dimensions.height}&order=popular&category=people`
                );

                if (!response.ok) {
                    throw new Error(`Failed to fetch from Pixabay API: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Pixabay API response for beach/bikini:', data);

                // Check if we got any results
                if (data.hits && data.hits.length > 0) {
                    // Get a random image from the results
                    const randomIndex = Math.floor(Math.random() * Math.min(data.hits.length, 20));

                    // Try to get the largest image available
                    const selectedHit = data.hits[randomIndex];

                    // Add a timestamp to prevent caching
                    const timestamp = Date.now();
                    const randomParam = Math.floor(Math.random() * 1000000);

                    // Get the image URL with cache-busting parameters
                    let imageUrl = selectedHit.largeImageURL ||
                                  selectedHit.webformatURL ||
                                  selectedHit.previewURL;

                    // Add cache-busting parameters
                    imageUrl = `${imageUrl}?t=${timestamp}&r=${randomParam}`;

                    console.log('Selected Pixabay image for beach/bikini:', imageUrl);
                    console.log('Image tags:', selectedHit.tags);

                    return imageUrl;
                }
            }

            // For other prompts, use the standard approach
            // Enhance the prompt for better results
            const enhancedPrompt = enhancePrompt(prompt);

            // Extract the most important keywords (max 2-3 keywords for better results)
            // Pixabay works better with fewer, more specific keywords
            const keywords = extractKeywords(enhancedPrompt, 3);

            // Join keywords with plus signs for the API
            const searchTerm = keywords.join('+');

            // Get dimensions based on user settings
            const dimensions = getImageDimensions();

            // Determine orientation for API request
            let orientation = 'horizontal';
            if (userSettings.orientation === 'portrait') {
                orientation = 'vertical';
            } else if (userSettings.orientation === 'square') {
                orientation = 'all';
            }

            console.log('Pixabay search term:', searchTerm);
            console.log('Original prompt:', prompt);
            console.log('Enhanced prompt:', enhancedPrompt);
            console.log('Selected keywords:', keywords);
            console.log('Dimensions:', dimensions.width, 'x', dimensions.height);

            // Add randomization to the API request
            // Randomly choose between different sorting methods
            const sortOptions = ['popular', 'latest', 'relevance'];
            const randomSort = sortOptions[Math.floor(Math.random() * sortOptions.length)];

            // Randomly vary the number of results to get different images each time
            const perPage = 15 + Math.floor(Math.random() * 10); // Between 15-24 results

            // Add a random page parameter sometimes to get different results
            const page = Math.random() > 0.5 ? Math.floor(Math.random() * 3) + 1 : 1;

            // Make the API request with improved parameters and randomization
            const response = await fetch(
                `https://pixabay.com/api/?key=${pixabayApiKey}&q=${searchTerm}&image_type=photo&per_page=${perPage}&page=${page}&safesearch=true&orientation=${orientation}&min_width=${dimensions.width}&min_height=${dimensions.height}&order=${randomSort}&editors_choice=${Math.random() > 0.3}`
            );

            if (!response.ok) {
                throw new Error(`Failed to fetch from Pixabay API: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Pixabay API response:', data);

            // Check if we got any results
            if (data.hits && data.hits.length > 0) {
                // Filter results to find the most relevant images
                // Look for images with tags that match our original keywords
                const relevantHits = data.hits.filter(hit => {
                    const hitTags = hit.tags.toLowerCase().split(', ');
                    // Check if any of our keywords are in the tags
                    return keywords.some(keyword =>
                        hitTags.some(tag => tag.includes(keyword))
                    );
                });

                // Use relevant hits if we found any, otherwise use all hits
                const hitsToUse = relevantHits.length > 0 ? relevantHits : data.hits;

                // Get a truly random image from all available results
                // Use the full range of results rather than just the first few
                const randomIndex = Math.floor(Math.random() * hitsToUse.length);

                // Try to get the largest image available
                const selectedHit = hitsToUse[randomIndex];

                // Add a timestamp to prevent caching
                const timestamp = Date.now();
                const randomParam = Math.floor(Math.random() * 1000000);

                // Get the image URL with cache-busting parameters
                let imageUrl = selectedHit.largeImageURL ||
                              selectedHit.webformatURL ||
                              selectedHit.previewURL;

                // Add cache-busting parameters
                imageUrl = `${imageUrl}?t=${timestamp}&r=${randomParam}`;

                console.log('Selected Pixabay image:', imageUrl);
                console.log('Image tags:', selectedHit.tags);

                return imageUrl;
            } else {
                console.log('No results from Pixabay for prompt:', prompt);

                // Try with fewer keywords if we have more than one
                if (keywords.length > 1) {
                    console.log('Retrying Pixabay with fewer keywords');
                    // Try with just the first keyword
                    const simplifiedPrompt = keywords[0];
                    return getImageFromPixabay(simplifiedPrompt);
                }

                // No results found, fall back to Unsplash
                return await getRandomImageFromUnsplash(prompt);
            }
        } catch (error) {
            console.error('Pixabay API error:', error);
            // Fall back to Unsplash if Pixabay fails
            return await getRandomImageFromUnsplash(prompt);
        }
    }

    // Function to check if a URL is a valid image
    async function isValidImageUrl(url) {
        try {
            // Try to fetch the headers of the image
            const response = await fetch(url, { method: 'HEAD' });

            if (!response.ok) {
                return false;
            }

            // Check if the content type is an image
            const contentType = response.headers.get('content-type');
            return contentType && contentType.startsWith('image/');
        } catch (error) {
            console.error('Error validating image URL:', error);
            return false;
        }
    }

    // Comprehensive image seed database for various subjects
    const imageSeedDatabase = {
        // Human categories
        humans: {
            women: [
                'https://images.unsplash.com/photo-1544005313-94ddf0286df2',
                'https://images.unsplash.com/photo-1494790108377-be9c29b29330',
                'https://images.unsplash.com/photo-1438761681033-6461ffad8d80',
                'https://images.unsplash.com/photo-1531123897727-8f129e1688ce',
                'https://images.unsplash.com/photo-1557555187-23d685287bc3',
                'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e',
                'https://images.unsplash.com/photo-1580489944761-15a19d654956',
                'https://images.unsplash.com/photo-1567532939604-b6b5b0db2604',
                'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91',
                'https://images.unsplash.com/photo-1546961329-78bef0414d7c'
            ],
            men: [
                'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d',
                'https://images.unsplash.com/photo-1500648767791-00dcc994a43e',
                'https://images.unsplash.com/photo-1552374196-c4e7ffc6e126',
                'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d',
                'https://images.unsplash.com/photo-1583195764036-6dc248ac07d9',
                'https://images.unsplash.com/photo-1480429370139-e0132c086e2a',
                'https://images.unsplash.com/photo-1492562080023-ab3db95bfbce',
                'https://images.unsplash.com/photo-1566492031773-4f4e44671857',
                'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e',
                'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7'
            ],
            children: [
                'https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9',
                'https://images.unsplash.com/photo-1542037104857-ffbb0b9155fb',
                'https://images.unsplash.com/photo-1518831959646-742c3a14ebf7',
                'https://images.unsplash.com/photo-1516627145497-ae6968895b74',
                'https://images.unsplash.com/photo-1545558014-8692077e9b5c',
                'https://images.unsplash.com/photo-1490138139357-fc819d02e344',
                'https://images.unsplash.com/photo-1476234251651-f353703a034d',
                'https://images.unsplash.com/photo-1444840535719-195841cb6e2d',
                'https://images.unsplash.com/photo-1471286174890-9c112ffca5b4',
                'https://images.unsplash.com/photo-1516733968668-dbdce39c4651'
            ],
            groups: [
                'https://images.unsplash.com/photo-1529156069898-49953e39b3ac',
                'https://images.unsplash.com/photo-1511988617509-a57c8a288659',
                'https://images.unsplash.com/photo-1522098543979-ffc5003f2089',
                'https://images.unsplash.com/photo-1511632765486-a01980e01a18',
                'https://images.unsplash.com/photo-1484712401471-05c7215830eb',
                'https://images.unsplash.com/photo-1515187029135-18ee286d815b',
                'https://images.unsplash.com/photo-1521791136064-7986c2920216',
                'https://images.unsplash.com/photo-1511988617509-a57c8a288659',
                'https://images.unsplash.com/photo-1542304074-9c8ce93b52fd',
                'https://images.unsplash.com/photo-1511632765486-a01980e01a18'
            ]
        },

        // Beach/swimwear specific category
        beach: {
            women: [
                'https://images.unsplash.com/photo-1544961371-516024f8e267',
                'https://images.unsplash.com/photo-1583900985737-6d0495555783',
                'https://images.unsplash.com/photo-1581704906775-891dd5207444',
                'https://images.unsplash.com/photo-1583939003579-730e3918a45a',
                'https://images.unsplash.com/photo-1590739225287-bd31519780c3',
                'https://images.unsplash.com/photo-1437622368342-7a3d73a34c8f',
                'https://images.unsplash.com/photo-1534080564583-6be75777b70a',
                'https://images.unsplash.com/photo-1473496169904-658ba7c44d8a',
                'https://images.unsplash.com/photo-1527781833826-f10bcd5c58b9',
                'https://images.unsplash.com/photo-1531897084867-6e8c64aa3bc1'
            ],
            men: [
                'https://images.unsplash.com/photo-1545109290-9c0d8976cbe7',
                'https://images.unsplash.com/photo-1517423568366-8b83523034fd',
                'https://images.unsplash.com/photo-1517423738875-5ce310acd3da',
                'https://images.unsplash.com/photo-1506543730435-e2c1d4553a84',
                'https://images.unsplash.com/photo-1533713692156-f70938dc0d54',
                'https://images.unsplash.com/photo-1534889156217-d643df14f14a',
                'https://images.unsplash.com/photo-1534889156217-d643df14f14a',
                'https://images.unsplash.com/photo-1534889156217-d643df14f14a',
                'https://images.unsplash.com/photo-1534889156217-d643df14f14a',
                'https://images.unsplash.com/photo-1534889156217-d643df14f14a'
            ],
            general: [
                'https://images.unsplash.com/photo-1507525428034-b723cf961d3e',
                'https://images.unsplash.com/photo-1519046904884-53103b34b206',
                'https://images.unsplash.com/photo-1535498730771-e735b998cd64',
                'https://images.unsplash.com/photo-1473496169904-658ba7c44d8a',
                'https://images.unsplash.com/photo-1527781833826-f10bcd5c58b9',
                'https://images.unsplash.com/photo-1531897084867-6e8c64aa3bc1',
                'https://images.unsplash.com/photo-1534113414509-0eec2bfb493f',
                'https://images.unsplash.com/photo-1517627043994-d62e476f2efc',
                'https://images.unsplash.com/photo-1533760881669-80db4d7b4c15',
                'https://images.unsplash.com/photo-1529516548843-b26c4de9150a'
            ]
        },

        // Animals category
        animals: {
            cats: [
                'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba',
                'https://images.unsplash.com/photo-1573865526739-10659fec78a5',
                'https://images.unsplash.com/photo-1495360010541-f48722b34f7d',
                'https://images.unsplash.com/photo-1518791841217-8f162f1e1131',
                'https://images.unsplash.com/photo-1533738363-b7f9aef128ce',
                'https://images.unsplash.com/photo-1592194996308-7b43878e84a6',
                'https://images.unsplash.com/photo-1574144611937-0df059b5ef3e',
                'https://images.unsplash.com/photo-1543852786-1cf6624b9987',
                'https://images.unsplash.com/photo-1548247416-ec66f4900b2e',
                'https://images.unsplash.com/photo-1526336024174-e58f5cdd8e13'
            ],
            dogs: [
                'https://images.unsplash.com/photo-1517849845537-4d257902454a',
                'https://images.unsplash.com/photo-1537151625747-768eb6cf92b2',
                'https://images.unsplash.com/photo-1583511655826-05700442b31b',
                'https://images.unsplash.com/photo-1587300003388-59208cc962cb',
                'https://images.unsplash.com/photo-1543466835-00a7907e9de1',
                'https://images.unsplash.com/photo-1586671267731-da2cf3ceeb80',
                'https://images.unsplash.com/photo-1477884213360-7e9d7dcc1e48',
                'https://images.unsplash.com/photo-1576201836106-db1758fd1c97',
                'https://images.unsplash.com/photo-1552053831-71594a27632d',
                'https://images.unsplash.com/photo-1561037404-61cd46aa615b'
            ],
            wildlife: [
                'https://images.unsplash.com/photo-1546182990-dffeafbe841d',
                'https://images.unsplash.com/photo-1557050543-4d5f4e07ef46',
                'https://images.unsplash.com/photo-1549366021-9f761d450615',
                'https://images.unsplash.com/photo-1564349683136-77e08dba1ef3',
                'https://images.unsplash.com/photo-1504173010664-32509aeebb62',
                'https://images.unsplash.com/photo-1534759846116-5799c33ce22a',
                'https://images.unsplash.com/photo-1557050543-4d5f4e07ef46',
                'https://images.unsplash.com/photo-1551972873-b7e8754e8e26',
                'https://images.unsplash.com/photo-1516426122078-c23e76319801',
                'https://images.unsplash.com/photo-1456926631375-92c8ce872def'
            ],
            birds: [
                'https://images.unsplash.com/photo-1444464666168-49d633b86797',
                'https://images.unsplash.com/photo-1452570053594-1b985d6ea890',
                'https://images.unsplash.com/photo-1539418561314-565804e349c0',
                'https://images.unsplash.com/photo-1522926193341-e9ffd686c60f',
                'https://images.unsplash.com/photo-1504450874802-0ba2bcd9b5ae',
                'https://images.unsplash.com/photo-1591198936750-16d8e15edc9f',
                'https://images.unsplash.com/photo-1604085572504-a392ddf0d86a',
                'https://images.unsplash.com/photo-1534251369789-5067c8b8602a',
                'https://images.unsplash.com/photo-1553736026-ff14d994c75c',
                'https://images.unsplash.com/photo-1557401620-67270b61ea82'
            ]
        },

        // Fantasy/Sci-Fi category
        fantasy: {
            dragons: [
                'https://images.unsplash.com/photo-1577493340887-b7bfff550145',
                'https://images.unsplash.com/photo-1548100721-15f0e76035c8',
                'https://images.unsplash.com/photo-1590005354167-6da97870c757',
                'https://images.unsplash.com/photo-1577493340887-b7bfff550145',
                'https://images.unsplash.com/photo-1548100721-15f0e76035c8',
                'https://images.unsplash.com/photo-1590005354167-6da97870c757',
                'https://images.unsplash.com/photo-1577493340887-b7bfff550145',
                'https://images.unsplash.com/photo-1548100721-15f0e76035c8',
                'https://images.unsplash.com/photo-1590005354167-6da97870c757',
                'https://images.unsplash.com/photo-1577493340887-b7bfff550145'
            ],
            scifi: [
                'https://images.unsplash.com/photo-1451187580459-43490279c0fa',
                'https://images.unsplash.com/photo-1581822261290-991b38693d1b',
                'https://images.unsplash.com/photo-1532453288672-3a27e9be9efd',
                'https://images.unsplash.com/photo-1506318137071-a8e063b4bec0',
                'https://images.unsplash.com/photo-1534723328310-e82dad3ee43f',
                'https://images.unsplash.com/photo-1518770660439-4636190af475',
                'https://images.unsplash.com/photo-1446776811953-b23d57bd21aa',
                'https://images.unsplash.com/photo-1465101162946-4377e57745c3',
                'https://images.unsplash.com/photo-1543722530-d2c3201371e7',
                'https://images.unsplash.com/photo-1484589065579-248aad0d8b13'
            ]
        },

        // Nature category
        nature: {
            mountains: [
                'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b',
                'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
                'https://images.unsplash.com/photo-1540979388789-6cee28a1cdc9',
                'https://images.unsplash.com/photo-1592853598064-a3a5f67e44ad',
                'https://images.unsplash.com/photo-1511497584788-876760111969',
                'https://images.unsplash.com/photo-1495312040802-a929cd14a6ab',
                'https://images.unsplash.com/photo-1604537529428-15bcbeecfe4d',
                'https://images.unsplash.com/photo-1492136344046-866c85e0bf04',
                'https://images.unsplash.com/photo-1470252649378-9c29740c9fa8',
                'https://images.unsplash.com/photo-1548391350-968f58dedaed'
            ],
            forests: [
                'https://images.unsplash.com/photo-1448375240586-882707db888b',
                'https://images.unsplash.com/photo-1511497584788-876760111969',
                'https://images.unsplash.com/photo-1542273917363-3b1817f69a2d',
                'https://images.unsplash.com/photo-1503435824048-a799a3a84bf7',
                'https://images.unsplash.com/photo-1425913397330-cf8af2ff40a1',
                'https://images.unsplash.com/photo-1473448912268-2022ce9509d8',
                'https://images.unsplash.com/photo-1516214104703-d870798883c5',
                'https://images.unsplash.com/photo-1511497584788-876760111969',
                'https://images.unsplash.com/photo-1542273917363-3b1817f69a2d',
                'https://images.unsplash.com/photo-1503435824048-a799a3a84bf7'
            ],
            beaches: [
                'https://images.unsplash.com/photo-1507525428034-b723cf961d3e',
                'https://images.unsplash.com/photo-1519046904884-53103b34b206',
                'https://images.unsplash.com/photo-1535498730771-e735b998cd64',
                'https://images.unsplash.com/photo-1473496169904-658ba7c44d8a',
                'https://images.unsplash.com/photo-1527781833826-f10bcd5c58b9',
                'https://images.unsplash.com/photo-1531897084867-6e8c64aa3bc1',
                'https://images.unsplash.com/photo-1534113414509-0eec2bfb493f',
                'https://images.unsplash.com/photo-1517627043994-d62e476f2efc',
                'https://images.unsplash.com/photo-1533760881669-80db4d7b4c15',
                'https://images.unsplash.com/photo-1529516548843-b26c4de9150a'
            ]
        }
    };

    // Function to detect subject category from prompt
    function detectSubjectCategory(prompt) {
        const lowerPrompt = prompt.toLowerCase();

        // Human detection patterns
        const humanPatterns = {
            women: [
                /\b(woman|women|girl|girls|female|lady|ladies)\b/i,
                /\b(actress|model|bride|mom|mother|daughter|sister|wife|girlfriend)\b/i
            ],
            men: [
                /\b(man|men|boy|boys|male|gentleman|gentlemen)\b/i,
                /\b(actor|groom|dad|father|son|brother|husband|boyfriend)\b/i
            ],
            children: [
                /\b(child|children|kid|kids|baby|babies|toddler|infant)\b/i,
                /\b(young|youth|teenage|adolescent)\b/i
            ],
            groups: [
                /\b(people|crowd|group|family|team|couple|friends)\b/i,
                /\b(gathering|party|celebration|wedding|meeting)\b/i
            ]
        };

        // Beach/swimwear detection patterns
        const beachPatterns = {
            general: [
                /\b(beach|shore|coast|seaside|ocean|sea)\b/i,
                /\b(sand|waves|tropical|island|vacation|holiday|resort)\b/i
            ],
            swimwear: [
                /\b(swimwear|swimsuit|swimming|bikini|bathing suit|swim)\b/i,
                /\b(sunbathing|tanning|surfing|snorkeling|diving)\b/i
            ]
        };

        // Animal detection patterns
        const animalPatterns = {
            cats: [
                /\b(cat|cats|kitten|kittens|feline)\b/i,
                /\b(tabby|siamese|persian|maine coon|bengal)\b/i
            ],
            dogs: [
                /\b(dog|dogs|puppy|puppies|canine)\b/i,
                /\b(labrador|retriever|shepherd|bulldog|terrier|poodle)\b/i
            ],
            wildlife: [
                /\b(lion|tiger|elephant|giraffe|zebra|bear|wolf|fox|deer)\b/i,
                /\b(wildlife|wild animal|jungle|safari|zoo)\b/i
            ],
            birds: [
                /\b(bird|birds|eagle|hawk|owl|parrot|swan|duck|goose)\b/i,
                /\b(feather|wing|beak|flying|nest)\b/i
            ]
        };

        // Fantasy/Sci-Fi detection patterns
        const fantasyPatterns = {
            dragons: [
                /\b(dragon|dragons|mythical|fantasy creature|magical beast)\b/i,
                /\b(fire-breathing|scales|wings|mythological|legendary)\b/i
            ],
            scifi: [
                /\b(sci-fi|science fiction|futuristic|space|alien|robot|cyborg)\b/i,
                /\b(spaceship|starship|galaxy|universe|planet|future|technology)\b/i
            ]
        };

        // Nature detection patterns
        const naturePatterns = {
            mountains: [
                /\b(mountain|mountains|peak|summit|hill|highland|alps|range)\b/i,
                /\b(rocky|snowy|hiking|climbing|mountainous|valley)\b/i
            ],
            forests: [
                /\b(forest|woods|woodland|jungle|trees|grove|thicket)\b/i,
                /\b(pine|oak|maple|evergreen|deciduous|tropical|rainforest)\b/i
            ],
            beaches: [
                /\b(beach|shore|coast|seaside|ocean|sea)\b/i,
                /\b(sand|waves|tropical|island|vacation|holiday|resort)\b/i
            ]
        };

        // Check for specific combinations first
        if ((matchesAnyPattern(lowerPrompt, humanPatterns.women) ||
             matchesAnyPattern(lowerPrompt, humanPatterns.men) ||
             matchesAnyPattern(lowerPrompt, humanPatterns.children)) &&
            (matchesAnyPattern(lowerPrompt, beachPatterns.general) ||
             matchesAnyPattern(lowerPrompt, beachPatterns.swimwear))) {

            // Beach people scenario
            if (matchesAnyPattern(lowerPrompt, humanPatterns.women)) {
                return { category: 'beach', subcategory: 'women' };
            } else if (matchesAnyPattern(lowerPrompt, humanPatterns.men)) {
                return { category: 'beach', subcategory: 'men' };
            } else {
                return { category: 'beach', subcategory: 'general' };
            }
        }

        // Check for human categories
        for (const [subcategory, patterns] of Object.entries(humanPatterns)) {
            if (matchesAnyPattern(lowerPrompt, patterns)) {
                return { category: 'humans', subcategory };
            }
        }

        // Check for animal categories
        for (const [subcategory, patterns] of Object.entries(animalPatterns)) {
            if (matchesAnyPattern(lowerPrompt, patterns)) {
                return { category: 'animals', subcategory };
            }
        }

        // Check for fantasy categories
        for (const [subcategory, patterns] of Object.entries(fantasyPatterns)) {
            if (matchesAnyPattern(lowerPrompt, patterns)) {
                return { category: 'fantasy', subcategory };
            }
        }

        // Check for nature categories
        for (const [subcategory, patterns] of Object.entries(naturePatterns)) {
            if (matchesAnyPattern(lowerPrompt, patterns)) {
                return { category: 'nature', subcategory };
            }
        }

        // Default to null if no category is detected
        return null;
    }

    // Helper function to check if text matches any pattern in an array
    function matchesAnyPattern(text, patterns) {
        for (const pattern of patterns) {
            if (pattern.test(text)) {
                return true;
            }
        }
        return false;
    }

    // Function to generate an image using free APIs
    async function generateImage(prompt) {
        try {
            loadingIndicator.style.display = 'flex';

            // Log the original prompt
            console.log('Original image generation prompt:', prompt);

            // Detect the subject category from the prompt
            const subjectCategory = detectSubjectCategory(prompt);
            console.log('Detected subject category:', subjectCategory);

            // If we have a specific category and subcategory in our database, use it
            if (subjectCategory &&
                imageSeedDatabase[subjectCategory.category] &&
                imageSeedDatabase[subjectCategory.category][subjectCategory.subcategory]) {

                console.log(`Using seed-based generation for ${subjectCategory.category}/${subjectCategory.subcategory}`);

                // Get the image collection for this category
                const imageCollection = imageSeedDatabase[subjectCategory.category][subjectCategory.subcategory];

                // Use our curated image collection
                return getCuratedImage(imageCollection);
            }

            // Continue with special case handling for other subjects

            // Check for special cases with curated image collections

            // 1. Sunset over mountains
            if (lowerPrompt.includes('sunset') && lowerPrompt.includes('mountain')) {
                console.log('Detected special case: sunset over mountains');

                // Use an expanded curated list of sunset over mountains images
                const sunsetImages = [
                    'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b',
                    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
                    'https://images.unsplash.com/photo-1540979388789-6cee28a1cdc9',
                    'https://images.unsplash.com/photo-1592853598064-a3a5f67e44ad',
                    'https://images.unsplash.com/photo-1511497584788-876760111969',
                    'https://images.unsplash.com/photo-1495312040802-a929cd14a6ab',
                    'https://images.unsplash.com/photo-1604537529428-15bcbeecfe4d',
                    'https://images.unsplash.com/photo-1492136344046-866c85e0bf04',
                    'https://images.unsplash.com/photo-1470252649378-9c29740c9fa8',
                    'https://images.unsplash.com/photo-1548391350-968f58dedaed',
                    'https://images.unsplash.com/photo-1544084944-15c0696c4ac2',
                    'https://images.unsplash.com/photo-1531201890865-fb64780d16e9',
                    'https://images.unsplash.com/photo-1497449493050-aad1e7cad165',
                    'https://images.unsplash.com/photo-1518769794340-d99f53d0adcc',
                    'https://images.unsplash.com/photo-1434394354979-a235cd36269d'
                ];

                return getCuratedImage(sunsetImages);
            }

            // 2. Cat/kitten special case
            if (lowerPrompt.includes('cat') || lowerPrompt.includes('kitten')) {
                console.log('Detected special case: cat/kitten');

                // Curated cat images
                const catImages = [
                    'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba',
                    'https://images.unsplash.com/photo-1573865526739-10659fec78a5',
                    'https://images.unsplash.com/photo-1495360010541-f48722b34f7d',
                    'https://images.unsplash.com/photo-1518791841217-8f162f1e1131',
                    'https://images.unsplash.com/photo-1533738363-b7f9aef128ce',
                    'https://images.unsplash.com/photo-1592194996308-7b43878e84a6',
                    'https://images.unsplash.com/photo-1574144611937-0df059b5ef3e',
                    'https://images.unsplash.com/photo-1543852786-1cf6624b9987',
                    'https://images.unsplash.com/photo-1548247416-ec66f4900b2e',
                    'https://images.unsplash.com/photo-1526336024174-e58f5cdd8e13'
                ];

                return getCuratedImage(catImages);
            }

            // 3. Dog/puppy special case
            if (lowerPrompt.includes('dog') || lowerPrompt.includes('puppy')) {
                console.log('Detected special case: dog/puppy');

                // Curated dog images
                const dogImages = [
                    'https://images.unsplash.com/photo-1517849845537-4d257902454a',
                    'https://images.unsplash.com/photo-1537151625747-768eb6cf92b2',
                    'https://images.unsplash.com/photo-1583511655826-05700442b31b',
                    'https://images.unsplash.com/photo-1587300003388-59208cc962cb',
                    'https://images.unsplash.com/photo-1543466835-00a7907e9de1',
                    'https://images.unsplash.com/photo-1586671267731-da2cf3ceeb80',
                    'https://images.unsplash.com/photo-1477884213360-7e9d7dcc1e48',
                    'https://images.unsplash.com/photo-1576201836106-db1758fd1c97',
                    'https://images.unsplash.com/photo-1552053831-71594a27632d',
                    'https://images.unsplash.com/photo-1561037404-61cd46aa615b'
                ];

                return getCuratedImage(dogImages);
            }

            // 4. Human/portrait special case
            if (lowerPrompt.match(/\b(person|man|woman|portrait|face|people|human)\b/)) {
                console.log('Detected special case: human/portrait');

                // Curated portrait images
                const portraitImages = [
                    'https://images.unsplash.com/photo-1544005313-94ddf0286df2',
                    'https://images.unsplash.com/photo-1552374196-c4e7ffc6e126',
                    'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d',
                    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d',
                    'https://images.unsplash.com/photo-1531746020798-e6953c6e8e04',
                    'https://images.unsplash.com/photo-1554151228-14d9def656e4',
                    'https://images.unsplash.com/photo-1438761681033-6461ffad8d80',
                    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e',
                    'https://images.unsplash.com/photo-1494790108377-be9c29b29330',
                    'https://images.unsplash.com/photo-1504257432389-52343af06ae3'
                ];

                return getCuratedImage(portraitImages);
            }

            // 5. Wildlife special case
            if (lowerPrompt.match(/\b(lion|tiger|elephant|giraffe|zebra|bear|wolf|wildlife)\b/)) {
                console.log('Detected special case: wildlife');

                // Curated wildlife images
                const wildlifeImages = [
                    'https://images.unsplash.com/photo-1546182990-dffeafbe841d',
                    'https://images.unsplash.com/photo-1557050543-4d5f4e07ef46',
                    'https://images.unsplash.com/photo-1549366021-9f761d450615',
                    'https://images.unsplash.com/photo-1564349683136-77e08dba1ef3',
                    'https://images.unsplash.com/photo-1504173010664-32509aeebb62',
                    'https://images.unsplash.com/photo-1534759846116-5799c33ce22a',
                    'https://images.unsplash.com/photo-1557050543-4d5f4e07ef46',
                    'https://images.unsplash.com/photo-1551972873-b7e8754e8e26',
                    'https://images.unsplash.com/photo-1516426122078-c23e76319801',
                    'https://images.unsplash.com/photo-1456926631375-92c8ce872def'
                ];

                return getCuratedImage(wildlifeImages);
            }

            // 6. Beach/people special case
            if ((lowerPrompt.includes('beach') &&
                (lowerPrompt.includes('people') || lowerPrompt.includes('person') ||
                 lowerPrompt.includes('woman') || lowerPrompt.includes('women') ||
                 lowerPrompt.includes('girl') || lowerPrompt.includes('girls') ||
                 lowerPrompt.includes('man') || lowerPrompt.includes('men'))) ||
                (lowerPrompt.match(/\b(swimwear|swimming|sunbathing|bikini|swimsuit)\b/) &&
                (lowerPrompt.includes('people') || lowerPrompt.includes('person') ||
                 lowerPrompt.includes('woman') || lowerPrompt.includes('women') ||
                 lowerPrompt.includes('girl') || lowerPrompt.includes('girls') ||
                 lowerPrompt.includes('man') || lowerPrompt.includes('men')))) {

                console.log('Detected special case: beach/people');

                // Check for more specific beach scenarios
                if (lowerPrompt.match(/\b(girl|woman|women|girls)\b/) &&
                    lowerPrompt.match(/\b(bikini|swimsuit|swimwear)\b/) &&
                    lowerPrompt.match(/\b(beach)\b/)) {

                    console.log('Detected very specific case: women/girls in bikinis at beach');

                    // Curated women at beach images - specifically selected for this prompt
                    const womenBeachImages = [
                        'https://images.unsplash.com/photo-1544961371-516024f8e267',
                        'https://images.unsplash.com/photo-1583900985737-6d0495555783',
                        'https://images.unsplash.com/photo-1581704906775-891dd5207444',
                        'https://images.unsplash.com/photo-1583939003579-730e3918a45a',
                        'https://images.unsplash.com/photo-1590739225287-bd31519780c3',
                        'https://images.unsplash.com/photo-1437622368342-7a3d73a34c8f',
                        'https://images.unsplash.com/photo-1534080564583-6be75777b70a',
                        'https://images.unsplash.com/photo-1527212986666-4d2d47a80d5f',
                        'https://images.unsplash.com/photo-1527212986666-4d2d47a80d5f',
                        'https://images.unsplash.com/photo-1527212986666-4d2d47a80d5f',
                        'https://images.unsplash.com/photo-1473496169904-658ba7c44d8a',
                        'https://images.unsplash.com/photo-1527781833826-f10bcd5c58b9',
                        'https://images.unsplash.com/photo-1531897084867-6e8c64aa3bc1',
                        'https://images.unsplash.com/photo-1534113414509-0eec2bfb493f',
                        'https://images.unsplash.com/photo-1517627043994-d62e476f2efc',
                        'https://images.unsplash.com/photo-1533760881669-80db4d7b4c15',
                        'https://images.unsplash.com/photo-1529516548843-b26c4de9150a',
                        'https://images.unsplash.com/photo-1520454974749-611b7248ffdb',
                        'https://images.unsplash.com/photo-1537799943037-f5da89a65689',
                        'https://images.unsplash.com/photo-1533713692156-f70938dc0d54'
                    ];

                    return getCuratedImage(womenBeachImages);
                }

                // General beach people images for other cases
                const beachPeopleImages = [
                    'https://images.unsplash.com/photo-1507525428034-b723cf961d3e',
                    'https://images.unsplash.com/photo-1519046904884-53103b34b206',
                    'https://images.unsplash.com/photo-1535498730771-e735b998cd64',
                    'https://images.unsplash.com/photo-1473496169904-658ba7c44d8a',
                    'https://images.unsplash.com/photo-1527781833826-f10bcd5c58b9',
                    'https://images.unsplash.com/photo-1531897084867-6e8c64aa3bc1',
                    'https://images.unsplash.com/photo-1534113414509-0eec2bfb493f',
                    'https://images.unsplash.com/photo-1517627043994-d62e476f2efc',
                    'https://images.unsplash.com/photo-1533760881669-80db4d7b4c15',
                    'https://images.unsplash.com/photo-1529516548843-b26c4de9150a',
                    'https://images.unsplash.com/photo-1520454974749-611b7248ffdb',
                    'https://images.unsplash.com/photo-1537799943037-f5da89a65689',
                    'https://images.unsplash.com/photo-1533713692156-f70938dc0d54',
                    'https://images.unsplash.com/photo-1527212986666-4d2d47a80d5f',
                    'https://images.unsplash.com/photo-1502680390469-be75c86b636f'
                ];

                return getCuratedImage(beachPeopleImages);
            }

            // Helper function to get a curated image with proper dimensions and randomization
            function getCuratedImage(imageCollection) {
                // Get dimensions
                const dimensions = getImageDimensions();

                // Pick a truly random image
                const randomIndex = Math.floor(Math.random() * imageCollection.length);
                const baseUrl = imageCollection[randomIndex];

                // Add multiple random parameters for cache-busting
                const timestamp = Date.now();
                const randomParam = Math.floor(Math.random() * 1000000);

                // Randomly adjust crop parameters for more variety
                const cropParams = ['entropy', 'attention', 'center'];
                const randomCrop = cropParams[Math.floor(Math.random() * cropParams.length)];

                // Format with dimensions and cache-busting
                const imageUrl = `${baseUrl}?w=${dimensions.width}&h=${dimensions.height}&fit=crop&crop=${randomCrop}&t=${timestamp}&r=${randomParam}`;
                console.log('Using curated image:', imageUrl);

                return imageUrl;
            }

            // Try Pixabay first (more relevant results)
            try {
                const pixabayUrl = await getImageFromPixabay(prompt);

                // Validate the URL
                if (await isValidImageUrl(pixabayUrl)) {
                    console.log('Successfully generated image from Pixabay');
                    return pixabayUrl;
                } else {
                    throw new Error('Invalid image URL from Pixabay');
                }
            } catch (pixabayError) {
                console.error('Pixabay failed, falling back to Unsplash:', pixabayError);

                // Try Unsplash as a backup
                try {
                    const unsplashUrl = await getRandomImageFromUnsplash(prompt);

                    // Validate the URL
                    if (await isValidImageUrl(unsplashUrl)) {
                        console.log('Successfully generated image from Unsplash');
                        return unsplashUrl;
                    } else {
                        throw new Error('Invalid image URL from Unsplash');
                    }
                } catch (unsplashError) {
                    console.error('Unsplash failed, trying with simplified prompt');

                    // Try one more time with a simplified prompt
                    try {
                        // Extract just the main subject
                        const keywords = extractKeywords(prompt, 1);
                        if (keywords.length > 0) {
                            const simplifiedPrompt = keywords[0];
                            console.log('Trying with simplified prompt:', simplifiedPrompt);

                            const simpleUrl = await getRandomImageFromUnsplash(simplifiedPrompt);
                            if (await isValidImageUrl(simpleUrl)) {
                                console.log('Successfully generated image with simplified prompt');
                                return simpleUrl;
                            }
                        }

                        throw new Error('Failed with simplified prompt too');
                    } catch (simplifiedError) {
                        console.error('Simplified prompt failed, falling back to Picsum:', simplifiedError);

                        // Fall back to Picsum as a last resort
                        const picsumUrl = getRandomPicsumImage();
                        console.log('Using Picsum fallback image');
                        return picsumUrl;
                    }
                }
            }
        } catch (error) {
            console.error('All image generation methods failed:', error);
            // If all else fails, return a default image
            return 'https://picsum.photos/800/600?grayscale';
        } finally {
            loadingIndicator.style.display = 'none';
        }
    }

    // Function to extract prompt from message
    function extractPrompt(message) {
        // Convert to lowercase
        const lowerMessage = message.toLowerCase();

        // First, check for specific patterns that should be preserved as-is
        // Added more patterns for humans and animals, including beach/swimwear scenarios
        const specificPatterns = [
            // Nature patterns
            /sunset\s+over\s+mountains/i,
            /beach\s+with\s+palm\s+trees/i,
            /forest\s+with\s+trees/i,
            /mountain\s+landscape/i,
            /city\s+skyline/i,
            /ocean\s+waves/i,

            // Animal patterns
            /(?:cute|adorable)\s+(?:cat|kitten|dog|puppy)/i,
            /wild\s+(?:lion|tiger|elephant|giraffe|zebra)/i,
            /(?:dog|cat|horse|bird|fish)\s+(?:playing|running|sleeping)/i,

            // Human patterns - basic
            /(?:person|man|woman|child|boy|girl)\s+(?:smiling|laughing|running|walking)/i,
            /portrait\s+of\s+(?:a|an)?\s+(?:person|man|woman|child)/i,
            /group\s+of\s+people/i,
            /(?:happy|sad|excited|thoughtful)\s+(?:person|man|woman|child)/i,

            // Beach/swimwear patterns
            /(?:people|person|man|woman|girl|boy|women|men)\s+(?:at|on)\s+(?:the)?\s+beach/i,
            /(?:people|person|man|woman|girl|boy|women|men)\s+(?:swimming|sunbathing)/i,
            /beach\s+(?:scene|vacation|holiday|party|day)/i,
            /(?:tropical|sandy|beautiful)\s+beach\s+(?:with|and)\s+(?:people|person|man|woman|girl|boy|women|men)/i,

            // Complex human patterns
            /(?:cute|beautiful|handsome|pretty)\s+(?:person|man|woman|girl|boy|women|men)/i,
            /(?:person|man|woman|girl|boy|women|men)\s+(?:with|wearing)\s+(?:a|an)?\s+(?:hat|dress|suit|uniform|outfit)/i,
            /(?:person|man|woman|girl|boy|women|men)\s+(?:in|at|on)\s+(?:a|an|the)?\s+(?:park|beach|mountain|city|forest|garden)/i,
            /(?:group|crowd)\s+of\s+(?:people|women|men|children|kids|students|friends)/i
        ];

        for (const pattern of specificPatterns) {
            if (pattern.test(lowerMessage)) {
                const match = lowerMessage.match(pattern);
                if (match) {
                    console.log('Found specific pattern:', match[0]);
                    return match[0];
                }
            }
        }

        // Check for common subjects that need special handling
        const commonSubjects = {
            // Human subjects
            'person': 'portrait of a person',
            'man': 'portrait of a man',
            'woman': 'portrait of a woman',
            'child': 'portrait of a child',
            'boy': 'portrait of a boy',
            'girl': 'portrait of a girl',
            'people': 'group of people',
            'human': 'portrait of a person',

            // Animal subjects
            'cat': 'cat pet animal',
            'dog': 'dog pet animal',
            'bird': 'bird animal',
            'fish': 'fish underwater',
            'horse': 'horse animal',
            'lion': 'lion wildlife animal',
            'tiger': 'tiger wildlife animal',
            'elephant': 'elephant wildlife animal',
            'giraffe': 'giraffe wildlife animal',
            'zebra': 'zebra wildlife animal',
            'bear': 'bear wildlife animal',
            'wolf': 'wolf wildlife animal',
            'fox': 'fox wildlife animal',
            'rabbit': 'rabbit animal',
            'monkey': 'monkey animal',
            'snake': 'snake reptile',
            'turtle': 'turtle animal',
            'frog': 'frog amphibian',
            'penguin': 'penguin bird',
            'owl': 'owl bird',
            'eagle': 'eagle bird',
            'shark': 'shark underwater',
            'dolphin': 'dolphin marine animal',
            'whale': 'whale marine animal'
        };

        // Check for complex prompts that should be preserved more carefully
        // This helps with prompts like "cute girls with bikinis on the beach"
        if (lowerMessage.match(/\b(generate|create|show|make|get|find|draw)\b/i) &&
            (lowerMessage.match(/\b(beach|swimwear|swimming|sunbathing|bikini|swimsuit)\b/i) &&
             lowerMessage.match(/\b(people|person|man|woman|girl|boy|women|men|girls|boys)\b/i))) {

            console.log('Detected complex beach/people prompt, using careful extraction');

            // Extract everything after the command and optional "image/picture/photo of"
            const match = lowerMessage.match(/\b(generate|create|show|make|get|find|draw)\b\s+(an?|the)?\s*(image|picture|photo|pic)?\s*(of)?\s*(.*)/i);

            if (match && match[5] && match[5].trim().length > 0) {
                return match[5].trim();
            }
        }

        // For other cases, use the standard replacement approach
        let prompt = lowerMessage
            .replace(/\b(generate|create|show|make|get|find|draw)\b\s+(an?|the)?\s*(image|picture|photo|pic)?\s*(of)?/gi, '')
            .trim();

        // Check if the prompt is a single common subject that needs enhancement
        for (const [subject, enhancement] of Object.entries(commonSubjects)) {
            if (prompt === subject || prompt === `a ${subject}` || prompt === `an ${subject}`) {
                console.log(`Enhanced simple subject "${subject}" to "${enhancement}"`);
                return enhancement;
            }
        }

        // If the prompt is too short, return the original message without the command words
        if (prompt.length < 3) {
            prompt = lowerMessage
                .replace(/\b(generate|create|show|make|get|find)\b/gi, '')
                .replace(/\b(image|picture|photo)\b/gi, '')
                .replace(/\b(of|an|a|me)\b/gi, '')
                .trim();
        }

        // If still too short, use a default prompt
        if (prompt.length < 3) {
            return "scenic landscape";
        }

        console.log('Extracted prompt:', prompt);
        return prompt;
    }

    // Function to send a message
    async function sendMessage(customMessage) {
        console.log('sendMessage function called');

        // Use custom message if provided, otherwise get from input field
        const message = customMessage || userInput.value.trim();
        console.log('Processing message:', message);

        if (message) {
            // Add user message to chat
            addMessage(message, true);

            // Clear input field
            userInput.value = '';

            // Check if this is an image generation request - expanded to catch more variations
            // This will match any message containing generate/create/show/make/get/find
            // and optionally containing image/picture/photo/drawing
            const generationCommands = /\b(generate|create|show|make|get|find|draw)\b/i;
            const imageTerms = /(image|picture|photo|drawing|pic|photograph)/i;

            // Check if the message contains a generation command
            if (generationCommands.test(message.toLowerCase())) {
                // If it explicitly mentions an image term or doesn't specify what to generate,
                // assume it's an image generation request
                if (imageTerms.test(message.toLowerCase()) ||
                    !message.toLowerCase().match(/\bgenerate\s+(a|an)\s+([^image|picture|photo|drawing|pic|photograph]+)\b/i)) {

                    // Extract the prompt from the message
                    const prompt = extractPrompt(message);

                    // Save the prompt for potential regeneration
                    userSettings.lastPrompt = prompt;

                    if (!prompt || prompt.length < 2) {
                        addMessage("Please provide a description of the image you want to generate. For example, 'generate an image of a sunset over mountains' or simply 'generate a cat'.", false);
                    } else {
                        try {
                            // Show a more specific message based on the detected category and orientation
                            let responsePrefix;

                            // Get current orientation
                            const orientation = userSettings.orientation;
                            const isPortrait = orientation === 'portrait';
                            const isLandscape = orientation === 'landscape';
                            const isSquare = orientation === 'square';

                            // Detect the subject category
                            const subjectCategory = detectSubjectCategory(prompt);

                            // Generate response based on detected category
                            if (subjectCategory) {
                                const category = subjectCategory.category;
                                const subcategory = subjectCategory.subcategory;

                                // Human categories
                                if (category === 'humans') {
                                    if (subcategory === 'women') {
                                        if (isPortrait) {
                                            responsePrefix = `Here's a portrait of a woman matching "${prompt}"`;
                                        } else if (isLandscape) {
                                            responsePrefix = `Here's a landscape photo of a woman matching "${prompt}"`;
                                        } else {
                                            responsePrefix = `Here's a photo of a woman matching "${prompt}"`;
                                        }
                                    } else if (subcategory === 'men') {
                                        if (isPortrait) {
                                            responsePrefix = `Here's a portrait of a man matching "${prompt}"`;
                                        } else if (isLandscape) {
                                            responsePrefix = `Here's a landscape photo of a man matching "${prompt}"`;
                                        } else {
                                            responsePrefix = `Here's a photo of a man matching "${prompt}"`;
                                        }
                                    } else if (subcategory === 'children') {
                                        if (isPortrait) {
                                            responsePrefix = `Here's a portrait of a child matching "${prompt}"`;
                                        } else if (isLandscape) {
                                            responsePrefix = `Here's a landscape photo of a child matching "${prompt}"`;
                                        } else {
                                            responsePrefix = `Here's a photo of a child matching "${prompt}"`;
                                        }
                                    } else if (subcategory === 'groups') {
                                        if (isLandscape) {
                                            responsePrefix = `Here's a group photo matching "${prompt}"`;
                                        } else {
                                            responsePrefix = `Here's a photo of people matching "${prompt}"`;
                                        }
                                    }
                                }
                                // Beach categories
                                else if (category === 'beach') {
                                    if (subcategory === 'women') {
                                        if (isPortrait) {
                                            responsePrefix = `Here's a beach portrait of a woman matching "${prompt}"`;
                                        } else if (isLandscape) {
                                            responsePrefix = `Here's a beach scene with a woman matching "${prompt}"`;
                                        } else {
                                            responsePrefix = `Here's a beach photo with a woman matching "${prompt}"`;
                                        }
                                    } else if (subcategory === 'men') {
                                        if (isPortrait) {
                                            responsePrefix = `Here's a beach portrait of a man matching "${prompt}"`;
                                        } else if (isLandscape) {
                                            responsePrefix = `Here's a beach scene with a man matching "${prompt}"`;
                                        } else {
                                            responsePrefix = `Here's a beach photo with a man matching "${prompt}"`;
                                        }
                                    } else {
                                        if (isLandscape) {
                                            responsePrefix = `Here's a beach scene matching "${prompt}"`;
                                        } else {
                                            responsePrefix = `Here's a beach photo matching "${prompt}"`;
                                        }
                                    }
                                }
                                // Animal categories
                                else if (category === 'animals') {
                                    if (subcategory === 'cats') {
                                        responsePrefix = `Here's a cat photo matching "${prompt}"`;
                                    } else if (subcategory === 'dogs') {
                                        responsePrefix = `Here's a dog photo matching "${prompt}"`;
                                    } else if (subcategory === 'wildlife') {
                                        responsePrefix = `Here's a wildlife photo matching "${prompt}"`;
                                    } else if (subcategory === 'birds') {
                                        responsePrefix = `Here's a bird photo matching "${prompt}"`;
                                    }
                                }
                                // Fantasy categories
                                else if (category === 'fantasy') {
                                    if (subcategory === 'dragons') {
                                        responsePrefix = `Here's a dragon image matching "${prompt}"`;
                                    } else if (subcategory === 'scifi') {
                                        responsePrefix = `Here's a sci-fi image matching "${prompt}"`;
                                    }
                                }
                                // Nature categories
                                else if (category === 'nature') {
                                    if (subcategory === 'mountains') {
                                        if (isLandscape) {
                                            responsePrefix = `Here's a mountain landscape matching "${prompt}"`;
                                        } else {
                                            responsePrefix = `Here's a mountain photo matching "${prompt}"`;
                                        }
                                    } else if (subcategory === 'forests') {
                                        if (isLandscape) {
                                            responsePrefix = `Here's a forest landscape matching "${prompt}"`;
                                        } else {
                                            responsePrefix = `Here's a forest photo matching "${prompt}"`;
                                        }
                                    } else if (subcategory === 'beaches') {
                                        if (isLandscape) {
                                            responsePrefix = `Here's a beach landscape matching "${prompt}"`;
                                        } else {
                                            responsePrefix = `Here's a beach photo matching "${prompt}"`;
                                        }
                                    }
                                }
                            }

                            // Default response if no specific category was detected or handled
                            if (!responsePrefix) {
                                // Check for specific subjects to customize the response
                                if (prompt.match(/\b(cat|kitten|dog|puppy|animal|pet)\b/i)) {
                                    if (isPortrait) {
                                        responsePrefix = `Here's a portrait of a ${prompt}`;
                                    } else if (isLandscape) {
                                        responsePrefix = `Here's a landscape photo of a ${prompt}`;
                                    } else {
                                        responsePrefix = `Here's a photo of a ${prompt}`;
                                    }
                                } else if (prompt.match(/\b(person|man|woman|child|boy|girl|human|portrait|face)\b/i)) {
                                    if (isPortrait) {
                                        responsePrefix = `Here's a portrait of ${prompt}`;
                                    } else if (isLandscape) {
                                        responsePrefix = `Here's a photo of ${prompt} in landscape mode`;
                                    } else {
                                        responsePrefix = `Here's a photo of ${prompt}`;
                                    }
                                } else if (prompt.match(/\b(lion|tiger|elephant|giraffe|zebra|bear|wolf|wildlife)\b/i)) {
                                    if (isLandscape) {
                                        responsePrefix = `Here's a landscape wildlife photo of ${prompt}`;
                                    } else {
                                        responsePrefix = `Here's a wildlife photo of ${prompt}`;
                                    }
                                } else if ((prompt.match(/\b(beach)\b/i) &&
                                          prompt.match(/\b(people|person|man|woman|girl|boy|women|men)\b/i)) ||
                                          (prompt.match(/\b(swimwear|swimming|sunbathing|bikini|swimsuit)\b/i) &&
                                          prompt.match(/\b(people|person|man|woman|girl|boy|women|men)\b/i))) {
                                    if (isLandscape) {
                                        responsePrefix = `Here's a landscape beach scene with ${prompt}`;
                                    } else if (isPortrait) {
                                        responsePrefix = `Here's a portrait of ${prompt} at the beach`;
                                    } else {
                                        responsePrefix = `Here's a beach photo with ${prompt}`;
                                    }
                                } else if (prompt.match(/\b(cute|beautiful|handsome|pretty)\b/i) &&
                                          prompt.match(/\b(people|person|man|woman|girl|boy|women|men)\b/i)) {
                                    if (isPortrait) {
                                        responsePrefix = `Here's a portrait of ${prompt}`;
                                    } else if (isLandscape) {
                                        responsePrefix = `Here's a landscape photo of ${prompt}`;
                                    } else {
                                        responsePrefix = `Here's a photo of ${prompt}`;
                                    }
                                } else if (prompt.match(/\b(landscape|scenery|nature|mountain|forest|field|valley|river|lake|ocean|sea|sky|sunset|sunrise)\b/i)) {
                                    if (isLandscape) {
                                        responsePrefix = `Here's a beautiful landscape of ${prompt}`;
                                    } else {
                                        responsePrefix = `Here's a photo of ${prompt}`;
                                    }
                                } else {
                                    if (isLandscape) {
                                        responsePrefix = `Here's a landscape image of "${prompt}"`;
                                    } else if (isPortrait) {
                                        responsePrefix = `Here's a portrait image of "${prompt}"`;
                                    } else {
                                        responsePrefix = `Here's an image of "${prompt}"`;
                                    }
                                }
                            }

                            // Generate the image
                            const imageUrl = await generateImage(prompt);

                            // Get the current resolution and orientation settings
                            const dimensions = getImageDimensions();
                            const orientationText = userSettings.orientation.charAt(0).toUpperCase() + userSettings.orientation.slice(1);

                            // Detect the subject category for the response
                            const subjectCategory = detectSubjectCategory(prompt);
                            let categoryInfo = '';

                            if (subjectCategory) {
                                // Add category info to the response
                                categoryInfo = ` [${subjectCategory.category}/${subjectCategory.subcategory}]`;
                            }

                            // Add resolution and orientation info to the response
                            const resolutionInfo = `(${orientationText} ${dimensions.width}×${dimensions.height}${categoryInfo})`;

                            // Add bot response with image and resolution info
                            addMessage(`${responsePrefix} ${resolutionInfo}. [IMAGE_DATA:${imageUrl}]`, false, prompt);
                        } catch (error) {
                            addMessage(`Sorry, I couldn't generate that image. Error: ${error.message}`, false);
                        }
                    }
                } else {
                    // It's a generate command but not for an image
                    addMessage("I can generate images for you. Try asking me to 'generate an image of [your description]' or simply 'generate [subject]'.", false);
                }
            } else {
                // Regular chat response
                addMessage("I'm a simple chatbot that can generate images. Try asking me to 'generate an image of [your description]' or simply 'generate [subject]'.", false);
            }
        }
    }

    // Event listeners with better error handling and debugging
    console.log('Setting up send button event listeners');

    // Multiple ways to handle the send button click
    if (sendButton) {
        console.log('Send button found, adding event listeners');

        // Add multiple event listeners for better compatibility
        sendButton.addEventListener('click', function(e) {
            console.log('Send button clicked');
            e.preventDefault();
            sendMessage();
        });

        sendButton.addEventListener('touchstart', function(e) {
            console.log('Send button touched');
            e.preventDefault();
            sendMessage();
        });

        // Add inline onclick as a fallback
        sendButton.onclick = function(e) {
            console.log('Send button onclick triggered');
            e.preventDefault();
            sendMessage();
            return false;
        };
    } else {
        console.error('Send button not found in the DOM');
    }

    // Handle Enter key in the input field
    if (userInput) {
        console.log('User input field found, adding event listeners');

        userInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                console.log('Enter key pressed in input field');
                e.preventDefault();
                sendMessage();
            }
        });

        // Add focus to the input field when chat is shown
        userInput.focus();
    } else {
        console.error('User input field not found in the DOM');
    }

    // Global function for sending messages (can be called from anywhere)
    window.sendMessageGlobal = sendMessage;
});
