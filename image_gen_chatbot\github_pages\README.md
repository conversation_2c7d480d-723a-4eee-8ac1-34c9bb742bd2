# AI Image Generator Chatbot (GitHub Pages Version)

A static web application that can generate images based on text prompts using free public APIs. This version is designed to be hosted on GitHub Pages and requires no API keys or authentication.

## Features

- Chat interface for interacting with the AI
- Image generation using free public APIs
- Completely client-side (no server required)
- No API keys or authentication needed
- Multiple fallback options for reliable image retrieval

## How to Use

1. Visit the hosted version at [your-github-username.github.io/image-gen-chatbot](https://your-github-username.github.io/image-gen-chatbot)
2. Click "Start Chatting" to begin
3. Type a message to interact with the chatbot
4. To generate an image, type something like "Generate an image of a sunset over mountains"

## Free APIs Used

### Pixabay API
- Primary image source
- Provides relevant, high-quality images based on search terms
- Free to use with attribution

### Unsplash Source API
- Secondary image source (fallback)
- Provides random high-quality images related to keywords
- No authentication required

### Lorem Picsum
- Final fallback option
- Provides random placeholder images
- Always available even when other APIs fail

## Hosting on Your Own GitHub Pages

1. Fork this repository
2. Go to the repository settings
3. Navigate to the "Pages" section
4. Select the main branch as the source
5. Your site will be published at `https://[your-username].github.io/[repository-name]/`

## Privacy & Security

- All API calls are made directly from your browser to the respective API services
- Your API keys are stored locally in your browser's localStorage and are never sent to our servers
- No data is collected or stored by this application

## Limitations

- The application requires an internet connection to generate images
- Image generation may take some time depending on the API and complexity of the prompt
- API usage may incur costs depending on your subscription plan with the service providers

## License

This project is open source and available under the MIT License.
