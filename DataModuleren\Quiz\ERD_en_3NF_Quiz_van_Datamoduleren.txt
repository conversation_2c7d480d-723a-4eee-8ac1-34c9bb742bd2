# Quiz over Entity Relationship Diagrams (ERD) en Derde Normaalvorm (3NF)
# Gebaseerd op "IT Datamodelleren 2.7.pdf"

## Hoofdstuk 1: Basisconcepten ERD

1. Wat zijn de drie hoofdcomponenten van een Entity Relationship Diagram?
   a) Entiteiten, attributen en relaties
   b) Tabellen, rijen en kolommen
   c) Databases, schema's en views
   d) Primaire sleutels, vreemde sleutels en indexen

2. Welk symbool wordt meestal gebruikt om een entiteit weer te geven in een ERD?
   a) Ovaal
   b) Rechthoek
   c) Ruit
   d) Cirkel

3. Wat is een veel-op-veel (many-to-many of N:M) relatie in een ERD?
   a) Een relatie waarbij één record in tabel A gerelateerd is aan <PERSON><PERSON> record in tabel B
   b) Een relatie waarbij één record in tabel A gerelateerd is aan meerdere records in tabel B
   c) Een relatie waarbij meerdere records in tabel A gerelateerd zijn aan meerdere records in tabel B
   d) Een relatie waarbij geen records in tabel A gerelateerd zijn aan records in tabel B

4. In het voorbeeld uit de cursus: "Een Superheld behoort tot 0 of meerdere groepen, Een Groep kan 1 of meerdere superhelden bevatten", wat voor soort relatie is dit?
   a) Eén-op-één (1:1)
   b) Eén-op-veel (1:N)
   c) Veel-op-veel (N:M)
   d) Nul-op-veel (0:N)

5. Waarom is de volgende tabel niet correct voor het opslaan van de relatie tussen superhelden en groepen?
   | SuperheldID | Naam | GroepID | GroepNaam |
   |-------------|------|---------|-----------|
   | 1 | Thor | 1 | Avengers |
   | 1 | Thor | 2 | Guardians of the Galaxy |
   | 2 | Iron Man | 1 | Avengers |
   | 3 | Gamora | 2 | Guardians of the Galaxy |
   
   a) Omdat de primaire sleutel niet uniek is
   b) Omdat er geen vreemde sleutel is
   c) Omdat de tabel niet in 3NF is
   d) Omdat er geen datum is opgenomen

## Hoofdstuk 2: Normalisatie

6. Wat is de definitie van de nulde normaalvorm (0NF)?
   a) Een tabel waarin alle attributen atomair zijn
   b) Een tabel waarin geen partiële afhankelijkheden zijn
   c) Een tabel waarin geen transitieve afhankelijkheden zijn
   d) Gegevens die op geen enkele manier genormaliseerd zijn

7. Welke van de volgende handelingen moet je NIET uitvoeren om tot de eerste normaalvorm (1NF) te komen?
   a) Zorgen dat een attribuut geen dubbele betekenis heeft
   b) Attributen die berekend kunnen worden uit andere attributen weglaten
   c) De identifier bepalen (attributen die de entiteit uniek maken)
   d) Zoeken naar niet-sleutelattributen die afhankelijk zijn van een ander niet-sleutelattribuut

8. Wat is een voorwaarde voor de tweede normaalvorm (2NF)?
   a) De tabel moet in 1NF zijn
   b) De tabel moet in 3NF zijn
   c) Er mogen geen transitieve afhankelijkheden zijn
   d) Alle attributen moeten deel uitmaken van de primaire sleutel

9. Wat moet je doen om een tabel naar de derde normaalvorm (3NF) te brengen?
   a) Repeterende groepen verwijderen
   b) Zorgen dat alle attributen atomair zijn
   c) Zoeken naar niet-sleutelattributen die afhankelijk zijn van een ander niet-sleutelattribuut
   d) Zorgen dat alle attributen direct afhankelijk zijn van de primaire sleutel

10. Welke van de volgende vragen moet je NIET stellen bij het normaliseren van een tabel volgens de cursus?
    a) Zie je meerdere entiteiten?
    b) Zijn er herhalende gegevens?
    c) Zijn er meerdere waardes in 1 kolom?
    d) Is de tabel in Boyce-Codd normaalvorm?

## Hoofdstuk 3: Conceptueel, Logisch en Fysiek ERD

11. Wat is het verschil tussen een conceptueel ERD en een logisch ERD?
    a) Een conceptueel ERD bevat meer technische details dan een logisch ERD
    b) Een logisch ERD bevat meer technische details dan een conceptueel ERD
    c) Een conceptueel ERD is specifiek voor één databasemanagementsysteem
    d) Er is geen verschil tussen deze twee types

12. Wat is een kenmerk van een fysiek ERD?
    a) Het bevat alleen entiteiten en relaties, geen attributen
    b) Het bevat vreemde sleutels (FK) om relaties te implementeren
    c) Het is onafhankelijk van het gekozen databasemanagementsysteem
    d) Het bevat geen informatie over kardinaliteit

13. Welke van de volgende elementen zou je NIET vinden in een conceptueel ERD?
    a) Entiteiten
    b) Relaties
    c) Vreemde sleutels
    d) Kardinaliteit

14. Hoe implementeer je een veel-op-veel (N:M) relatie in een fysiek ERD?
    a) Door een directe relatie tussen de twee entiteiten
    b) Door een associatieve entiteit (junction table) te creëren
    c) Door een vreemde sleutel toe te voegen aan beide entiteiten
    d) Door de relatie te negeren

15. In een situatie waar "Een Manager kan veel andere verkopers managen. Een verkoper wordt beheerd door slechts één manager", wat voor soort relatie is dit?
    a) Eén-op-één (1:1)
    b) Eén-op-veel (1:N)
    c) Veel-op-veel (N:M)
    d) Recursieve relatie

## Hoofdstuk 4: Praktische Toepassingen

16. Je hebt een tabel BESTELLING(OrderID, KlantID, KlantNaam, KlantAdres, ProductID, ProductNaam, Aantal, Prijs). Welke stap moet je als eerste nemen om deze tabel te normaliseren?
    a) Zoeken naar transitieve afhankelijkheden
    b) Controleren of er meerdere entiteiten zijn
    c) De primaire sleutel bepalen
    d) Berekende velden verwijderen

17. Gegeven de volgende functionele afhankelijkheden in een tabel:
    - OrderID → KlantID, Datum
    - KlantID → KlantNaam, KlantAdres
    - ProductID → ProductNaam, Prijs
    Waarom is deze tabel niet in 3NF?
    a) Omdat er geen primaire sleutel is
    b) Omdat er partiële afhankelijkheden zijn
    c) Omdat er transitieve afhankelijkheden zijn
    d) Omdat er repeterende groepen zijn

18. Hoe zou je een tabel MEDEWERKER(MedewerkerID, Naam, AfdelingID, AfdelingNaam, LocatieID, LocatieAdres) normaliseren naar 3NF?
    a) Eén tabel is voldoende omdat alle attributen afhankelijk zijn van MedewerkerID
    b) Twee tabellen: MEDEWERKER(MedewerkerID, Naam, AfdelingID) en AFDELING(AfdelingID, AfdelingNaam, LocatieID, LocatieAdres)
    c) Drie tabellen: MEDEWERKER(MedewerkerID, Naam, AfdelingID), AFDELING(AfdelingID, AfdelingNaam, LocatieID) en LOCATIE(LocatieID, LocatieAdres)
    d) Vier tabellen zijn nodig om 3NF te bereiken

19. Welke tool wordt in de cursus aanbevolen voor het maken van ERD-diagrammen?
    a) Microsoft Visio
    b) draw.io
    c) MySQL Workbench
    d) ERDPlus

20. Wat is een belangrijke stap bij het maken van een fysiek ERD op basis van een logisch ERD?
    a) Het verwijderen van alle attributen
    b) Het toevoegen van vreemde sleutels om relaties te implementeren
    c) Het verwijderen van alle relaties
    d) Het veranderen van alle kardinaliteiten

## Hoofdstuk 5: Geavanceerde Concepten

21. Wat is een recursieve relatie in een ERD?
    a) Een relatie tussen twee verschillende entiteiten
    b) Een relatie van een entiteit met zichzelf
    c) Een relatie die niet kan worden geïmplementeerd
    d) Een relatie die alleen in conceptuele ERD's voorkomt

22. In het voorbeeld van de cursus: "Een Manager kan veel andere verkopers managen. Een verkoper wordt beheerd door slechts één manager", wat voor soort relatie is dit?
    a) Een standaard één-op-veel relatie
    b) Een veel-op-veel relatie
    c) Een recursieve relatie
    d) Een zwakke entiteit relatie

23. Wat is een zwakke entiteit in een ERD?
    a) Een entiteit die niet vaak wordt gebruikt
    b) Een entiteit die afhankelijk is van een andere entiteit voor zijn bestaan
    c) Een entiteit met weinig attributen
    d) Een entiteit zonder relaties

24. Wat is het doel van normalisatie in databaseontwerp?
    a) Het versnellen van database queries
    b) Het verminderen van redundantie en het voorkomen van anomalieën
    c) Het vereenvoudigen van het databaseschema
    d) Het verminderen van het aantal tabellen

25. Welke stap in het normalisatieproces is het belangrijkst volgens de cursus?
    a) Het identificeren van de primaire sleutel
    b) Het verwijderen van transitieve afhankelijkheden
    c) Het identificeren van meerdere entiteiten
    d) Het verwijderen van berekende velden

## Antwoorden

### Hoofdstuk 1: Basisconcepten ERD
1. a) Entiteiten, attributen en relaties
2. b) Rechthoek
3. c) Een relatie waarbij meerdere records in tabel A gerelateerd zijn aan meerdere records in tabel B
4. c) Veel-op-veel (N:M)
5. a) Omdat de primaire sleutel niet uniek is

### Hoofdstuk 2: Normalisatie
6. d) Gegevens die op geen enkele manier genormaliseerd zijn
7. d) Zoeken naar niet-sleutelattributen die afhankelijk zijn van een ander niet-sleutelattribuut
8. a) De tabel moet in 1NF zijn
9. c) Zoeken naar niet-sleutelattributen die afhankelijk zijn van een ander niet-sleutelattribuut
10. d) Is de tabel in Boyce-Codd normaalvorm?

### Hoofdstuk 3: Conceptueel, Logisch en Fysiek ERD
11. b) Een logisch ERD bevat meer technische details dan een conceptueel ERD
12. b) Het bevat vreemde sleutels (FK) om relaties te implementeren
13. c) Vreemde sleutels
14. b) Door een associatieve entiteit (junction table) te creëren
15. b) Eén-op-veel (1:N)

### Hoofdstuk 4: Praktische Toepassingen
16. b) Controleren of er meerdere entiteiten zijn
17. c) Omdat er transitieve afhankelijkheden zijn
18. c) Drie tabellen: MEDEWERKER(MedewerkerID, Naam, AfdelingID), AFDELING(AfdelingID, AfdelingNaam, LocatieID) en LOCATIE(LocatieID, LocatieAdres)
19. b) draw.io
20. b) Het toevoegen van vreemde sleutels om relaties te implementeren

### Hoofdstuk 5: Geavanceerde Concepten
21. b) Een relatie van een entiteit met zichzelf
22. c) Een recursieve relatie
23. b) Een entiteit die afhankelijk is van een andere entiteit voor zijn bestaan
24. b) Het verminderen van redundantie en het voorkomen van anomalieën
25. c) Het identificeren van meerdere entiteiten
