# Reflectie op het ERD-ontwerp voor Euro Caps

Dit document bevat een reflectie op het ontwerp van het Entity-Relationship Diagram (ERD) voor Euro Caps, waarin wordt uitgelegd hoe de bedrijfsprocessen zijn doorvertaald in de verschillende ERD-niveaus (conceptueel, logisch, fysiek), welke ontwerpkeuzes zijn gemaakt en waarom, en welke lessen en inzichten zijn opgedaan voor zowel datakwaliteit als bedrijfsvoering.

## Doorvertaling van bedrijfsprocessen in de ERD-niveaus

### Van bedrijfsprocessen naar conceptueel ERD

Het conceptuele ERD vormt de basis van het databaseontwerp en is direct afgeleid van de kernprocessen van Euro Caps:

1. **Grinding (Malen)**: In het conceptuele ERD is dit vertaald naar entiteiten zoals 'Grondstof' (voor koffiebonen), 'Productiestap' (voor het maalproces) en 'Kwaliteitscontrole' (voor de kwaliteitsmetingen tijdens het malen).

2. **Filling (Vullen)**: Dit proces is vertaald naar entiteiten zoals 'Productiestap' (voor het vulproces), 'Machine' (voor de vulmachines) en 'Kwaliteitscontrole' (voor gewichtscontroles en vulniveaumetingen).

3. **Packing (Verpakken)**: Dit is vertaald naar entiteiten zoals 'Productiestap' (voor het verpakkingsproces), 'Product' (voor de verschillende verpakkingsformaten) en 'Kwaliteitscontrole' (voor verpakkingscontroles).

4. **Supply Chain**: De volledige supply chain is vertaald naar entiteiten zoals 'Leverancier', 'Grondstof', 'Productiebatch', 'Product', 'Bestelling' en 'Klant'.

Het conceptuele ERD legt de nadruk op de belangrijkste entiteiten en hun onderlinge relaties, zonder in detail te treden over attributen of technische implementatie. Het biedt een helikopterview van het systeem die begrijpelijk is voor zowel technische als niet-technische stakeholders.

### Van conceptueel naar logisch ERD

In de overgang naar het logische ERD zijn de volgende stappen genomen:

1. **Toevoegen van attributen**: Elke entiteit is verrijkt met specifieke attributen die de eigenschappen van die entiteit beschrijven. Bijvoorbeeld, 'Klant' heeft attributen zoals Klant_ID, Naam, E-mailadres en Telefoonnummer.

2. **Specificeren van primaire sleutels**: Voor elke entiteit is een primaire sleutel gedefinieerd, zoals Klant_ID voor 'Klant' en Product_ID voor 'Product'.

3. **Normalisatie**: De datastructuur is genormaliseerd om redundantie te verminderen en data-integriteit te waarborgen. Dit heeft geleid tot de creatie van tussentabellen zoals 'OrderItems' voor de many-to-many relatie tussen 'Bestelling' en 'Product'.

4. **Definiëren van relaties met cardinaliteit**: De relaties tussen entiteiten zijn gespecificeerd met cardinaliteit (1:n, n:m), zoals de 1:n relatie tussen 'Klant' en 'Bestelling' en de n:m relatie tussen 'Bestelling' en 'Product'.

5. **Verfijnen van het model**: Het model is stapsgewijs verfijnd om alle bedrijfsprocessen adequaat te ondersteunen, met speciale aandacht voor de drie hoofdprocessen (malen, vullen, verpakken) en de kwaliteitscontroles.

Het logische ERD biedt een gedetailleerd overzicht van de datastructuur, maar is nog steeds databaseonafhankelijk.

### Van logisch naar fysiek ERD

In de overgang naar het fysieke ERD zijn de volgende stappen genomen:

1. **Specificeren van datatypen**: Voor elk attribuut is een specifiek datatype gedefinieerd, zoals INT voor ID's, VARCHAR voor tekstvelden, DECIMAL voor numerieke waarden met precisie, en ENUM voor velden met een beperkte set waarden.

2. **Toevoegen van constraints**: Er zijn verschillende constraints toegevoegd, zoals NOT NULL voor verplichte velden, UNIQUE voor unieke waarden, DEFAULT voor standaardwaarden, en CHECK constraints voor validatie.

3. **Implementeren van vreemde sleutels**: De relaties zijn geïmplementeerd met vreemde sleutels die verwijzen naar de primaire sleutels van gerelateerde tabellen.

4. **Optimalisatie voor prestaties**: Het model is geoptimaliseerd voor prestaties door het toevoegen van indexen op veelgebruikte zoekvelden en vreemde sleutels.

5. **Toevoegen van database-specifieke elementen**: Er zijn database-specifieke elementen toegevoegd zoals auto-increment voor primaire sleutels, timestamps voor audit-doeleinden, en views voor veelgebruikte rapportages.

Het fysieke ERD is een complete blauwdruk voor de implementatie van de database en bevat alle technische details die nodig zijn voor de creatie van de database.

## Ontwerpkeuzes en motivatie

### 1. Keuze voor genormaliseerd ontwerp

Er is gekozen voor een volledig genormaliseerd ontwerp (tot en met 3NF) om redundantie te minimaliseren en data-integriteit te maximaliseren. Dit is cruciaal voor een productieomgeving waar nauwkeurigheid en betrouwbaarheid van gegevens essentieel zijn.

**Motivatie**: In een productieomgeving zoals die van Euro Caps is het essentieel dat gegevens consistent en betrouwbaar zijn. Een genormaliseerd ontwerp voorkomt anomalieën bij het invoeren, wijzigen en verwijderen van gegevens, wat de datakwaliteit ten goede komt.

### 2. Gebruik van ENUM types

Voor velden met een beperkte set waarden, zoals MaterialType, ProductType, StepType en Status, is gekozen voor ENUM types in plaats van losse referentietabellen.

**Motivatie**: ENUM types bieden ingebouwde validatie en zijn efficiënter in termen van opslag en prestaties dan aparte referentietabellen. Voor waarden die zelden of nooit veranderen, zoals de typen productiestappen (malen, vullen, verpakken), bieden ENUM types een goede balans tussen flexibiliteit en eenvoud.

### 3. Implementatie van audit trails

Alle tabellen bevatten CreatedAt en UpdatedAt velden voor audit-doeleinden, met automatische updates.

**Motivatie**: In een productieomgeving is het belangrijk om te kunnen traceren wanneer gegevens zijn aangemaakt of gewijzigd, zowel voor kwaliteitscontrole als voor compliance-doeleinden. Automatische timestamps verminderen de kans op menselijke fouten en zorgen ervoor dat deze informatie altijd wordt vastgelegd.

### 4. Gedetailleerde kwaliteitscontrole

Er is een aparte tabel (QualityChecks) voor kwaliteitscontroles, gekoppeld aan specifieke productiestappen, met gedetailleerde informatie over het type controle, de gemeten waarde, en de drempelwaarden.

**Motivatie**: Kwaliteitscontrole is een kritisch aspect van het productieproces van Euro Caps. Door gedetailleerde informatie vast te leggen over elke kwaliteitscontrole, kunnen trends worden geïdentificeerd, problemen worden opgespoord, en de kwaliteit worden verbeterd.

### 5. Flexibele materiaalregistratie

De MaterialUsage tabel koppelt materialen aan productiebatches, waardoor flexibel kan worden bijgehouden welke materialen in welke hoeveelheden zijn gebruikt voor welke batch.

**Motivatie**: Deze aanpak ondersteunt traceerbaarheid van materialen door de productieketen, wat essentieel is voor kwaliteitscontrole en recall-procedures. Het maakt ook nauwkeurige kostenberekening en voorraadbeheersing mogelijk.

### 6. Geïntegreerde views voor rapportage

Er zijn database views toegevoegd voor veelgebruikte rapportages, zoals ProductionEfficiency, QualityControlStats, MaterialConsumption en OrderFulfillment.

**Motivatie**: Views vereenvoudigen complexe queries en verbeteren de prestaties voor veelgebruikte rapportages. Ze bieden ook een abstractielaag die de onderliggende datastructuur verbergt voor eindgebruikers, wat de gebruiksvriendelijkheid verhoogt.

## Lessen en inzichten

### Inzichten voor datakwaliteit

1. **Belang van constraints**: Het implementeren van constraints zoals NOT NULL, UNIQUE en CHECK is essentieel voor het waarborgen van datakwaliteit. Deze constraints fungeren als een eerste verdedigingslinie tegen ongeldige gegevens.

2. **Waarde van normalisatie**: Een genormaliseerd ontwerp vermindert redundantie en verhoogt de consistentie van gegevens, wat de datakwaliteit ten goede komt. Het maakt het ook eenvoudiger om gegevens bij te werken zonder anomalieën te introduceren.

3. **Balans tussen flexibiliteit en structuur**: Het ontwerp moet flexibel genoeg zijn om toekomstige veranderingen te accommoderen, maar ook gestructureerd genoeg om datakwaliteit te waarborgen. ENUM types bieden bijvoorbeeld structuur, terwijl generieke relaties flexibiliteit bieden.

4. **Belang van audit trails**: Het vastleggen van wanneer en door wie gegevens zijn aangemaakt of gewijzigd is cruciaal voor het traceren van problemen en het waarborgen van accountability.

### Inzichten voor bedrijfsvoering

1. **Ondersteuning voor kernprocessen**: Het databaseontwerp moet direct aansluiten bij de kernprocessen van het bedrijf. Voor Euro Caps zijn dit de processen van malen, vullen en verpakken, evenals kwaliteitscontrole en supply chain management.

2. **Balans tussen operationele en analytische behoeften**: Het ontwerp moet zowel operationele transacties (zoals het registreren van productiestappen) als analytische behoeften (zoals het analyseren van productie-efficiëntie) ondersteunen.

3. **Integratie van kwaliteitscontrole**: Kwaliteitscontrole is geen losstaand proces, maar moet geïntegreerd zijn in alle fasen van het productieproces. Dit is gereflecteerd in het ontwerp door de directe koppeling tussen productiestappen en kwaliteitscontroles.

4. **Traceerbaarheid door de keten**: Het ontwerp ondersteunt traceerbaarheid van materialen en producten door de hele keten, van leverancier tot klant. Dit is essentieel voor kwaliteitscontrole, recall-procedures en compliance.

5. **Ondersteuning voor continue verbetering**: Door gedetailleerde gegevens vast te leggen over productie, kwaliteit en materiaalgebruik, biedt het ontwerp een solide basis voor analyse en continue verbetering van processen.

## Conclusie

Het ERD-ontwerp voor Euro Caps is stapsgewijs ontwikkeld van conceptueel naar logisch naar fysiek, met een sterke focus op het ondersteunen van de kernprocessen van het bedrijf en het waarborgen van datakwaliteit. De gemaakte ontwerpkeuzes zijn gemotiveerd door de specifieke behoeften van een productieomgeving, met speciale aandacht voor kwaliteitscontrole, traceerbaarheid en rapportage.

Het resulterende ontwerp biedt een solide basis voor de implementatie van een database die zowel operationele als analytische behoeften ondersteunt, en die kan meegroeien met het bedrijf. De opgedane inzichten over datakwaliteit en bedrijfsvoering kunnen worden toegepast in toekomstige projecten en kunnen bijdragen aan de continue verbetering van processen binnen Euro Caps.
