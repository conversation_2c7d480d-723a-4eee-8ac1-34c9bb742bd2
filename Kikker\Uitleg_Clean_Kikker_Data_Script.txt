Uitleg van het Python Script: clean_kikker_data.py
=============================================

<PERSON><PERSON> van het Script
------------------
Het script 'clean_kikker_data.py' is ontwikkeld om ruwe data uit het bestand 'Kikker.csv' te laden, op te schonen en te transformeren naar een bruikbaar formaat voor verdere analyse. Het script voert verschillende opschoonacties uit op verschillende soorten data (percentages, numerieke waarden, categorische waarden en datums) en slaat het resultaat op in een nieuw CSV-bestand.

Gebruikte Modules en Waarom
--------------------------
1. pandas (geïmporteerd als pd):
   - Pandas is een krachtige Python-bibliotheek voor data-analyse en -manipulatie.
   - Het biedt datastructuren zoals DataFrame die ideaal zijn voor het werken met tabelgegevens.
   - Gebruikt voor:
     * Het laden van CSV-bestanden (pd.read_csv)
     * Het transformeren van data (string naar numeriek, datum conversies)
     * Het filteren en manipuleren van data
     * Het opslaan van opgeschoonde data naar CSV
   - Pandas is essentieel voor dit script omdat het efficiënte methoden biedt voor datamanipulatie en -transformatie.

2. numpy (geïmporteerd als np):
   - NumPy is een fundamentele bibliotheek voor wetenschappelijke berekeningen in Python.
   - Het biedt ondersteuning voor arrays en matrices, plus wiskundige functies.
   - Gebruikt voor:
     * Het vervangen van ongeldige waarden door NaN (Not a Number)
     * Efficiënte numerieke operaties
   - NumPy werkt nauw samen met pandas en biedt de onderliggende functionaliteit voor veel pandas-operaties.

Hoofdfuncties en Hun Doel
------------------------
1. load_data():
   - Probeert het Kikker.csv bestand te laden vanuit verschillende mogelijke locaties.
   - Geeft informatie over het aantal rijen en kolommen in de dataset.
   - Essentieel voor het flexibel laden van data, ongeacht de exacte bestandslocatie.

2. clean_percentages(df):
   - Schoont kolommen op die percentages bevatten (Klantretourpercentage, Defectpercentage, Benuttingsgraad).
   - Verwijdert %-tekens en converteert waarden naar decimale getallen.
   - Filtert negatieve of onrealistische waarden uit de Benuttingsgraad kolom.
   - Belangrijk voor het standaardiseren van percentagewaarden voor analyse.

3. clean_numeric_values(df):
   - Schoont numerieke kolommen op zoals Gewichtscontrole, Energieverbruik, Cost, Cyclustijd, ProcessTime en Voorraadniveaus.
   - Verwijdert eenheden (kWh, euros, units) en extraheert numerieke waarden.
   - Vervangt onrealistische waarden door NaN.
   - Cruciaal voor het verkrijgen van zuivere numerieke data voor berekeningen.

4. clean_categorical_values(df):
   - Standaardiseert categorische waarden in kolommen zoals Panel Test en PackagingApparaat.
   - Zorgt voor consistente categorieën door verschillende schrijfwijzen te mappen naar standaardwaarden.
   - Belangrijk voor correcte groepering en analyse van categorische data.

5. clean_dates(df):
   - Converteert datumkolommen naar het juiste datetime-formaat.
   - Maakt tijdreeksanalyse en datumgebaseerde filtering mogelijk.

6. clean_kikker_data():
   - Hoofdfunctie die alle bovenstaande functies in de juiste volgorde aanroept.
   - Slaat de opgeschoonde data op in een nieuw CSV-bestand.
   - Toont een samenvatting van de opgeschoonde data en statistieken voor belangrijke kolommen.
   - Organiseert het hele opschoonproces in een logische workflow.

Dataopschoning en -transformatie
-------------------------------
Het script voert verschillende soorten dataopschoning uit:

1. Verwijderen van niet-numerieke tekens (%, kWh, euros, units) uit waarden.
2. Converteren van strings naar het juiste datatype (float, datetime).
3. Standaardiseren van categorische waarden.
4. Filteren van onrealistische of ongeldige waarden.
5. Vervangen van ontbrekende of ongeldige waarden door NaN.

Deze opschoningsacties zijn essentieel voor het verkrijgen van betrouwbare en consistente data voor verdere analyse.

Statistieken en Rapportage
-------------------------
Het script genereert ook een samenvatting van de opgeschoonde data, inclusief:
- Aantal rijen en kolommen
- Aantal ontbrekende waarden
- Statistieken (gemiddelde, mediaan, min, max) voor belangrijke kolommen gerelateerd aan kwaliteitsmanagement:
  * Six Sigma: Defectpercentage
  * Lean: Cyclustijd
  * TOC: Benuttingsgraad
  * Kaizen: Klanttevredenheid en Klantretourpercentage

Deze statistieken geven een snel overzicht van de kwaliteit en kenmerken van de data na opschoning.

Conclusie
--------
Het script 'clean_kikker_data.py' is een essentieel onderdeel van de data-analysepijplijn voor het Americaps project. Het zorgt ervoor dat de ruwe data uit Kikker.csv wordt getransformeerd naar een schone, consistente en bruikbare dataset voor verdere analyse met kwaliteitsmanagementmethoden zoals Six Sigma, Lean, TOC en Kaizen.

De combinatie van pandas en numpy biedt een krachtige en efficiënte manier om deze datatransformaties uit te voeren, terwijl de modulaire opbouw van het script zorgt voor een georganiseerde en onderhoudbare codebase.
