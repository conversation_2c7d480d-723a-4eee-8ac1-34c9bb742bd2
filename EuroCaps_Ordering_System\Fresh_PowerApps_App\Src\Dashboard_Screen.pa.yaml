Dashboard_Screen As screen:
    Fill: =RGBA(245, 245, 245, 1)
    LoadingSpinnerColor: =RGBA(74, 111, 165, 1)

    HeaderBar As rectangle:
        Fill: =RGBA(74, 111, 165, 1)
        Height: =60
        Width: =Parent.Width
        X: =0
        Y: =0

    HeaderLogo As image:
        Height: =40
        Image: ="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ3aGl0ZSIvPgo8dGV4dCB4PSIyMCIgeT0iMjUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI4IiBmaWxsPSIjNGE2ZmE1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5FQzwvdGV4dD4KPC9zdmc+"
        Width: =40
        X: =20
        Y: =10

    HeaderTitle As label:
        Color: =RGBA(255, 255, 255, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =16
        Text: ="EuroCaps Order Management Pro"
        Width: =300
        X: =70
        Y: =10

    UserMenu As button:
        BorderColor: =RGBA(255, 255, 255, 1)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(0, 0, 0, 0)
        Font: =Font.Arial
        Height: =40
        Size: =14
        Text: ="User ▼"
        Width: =100
        X: =Parent.Width - 160
        Y: =10

    SettingsIcon As button:
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(0, 0, 0, 0)
        Font: =Font.Arial
        Height: =40
        OnSelect: =Navigate(Settings_Screen, ScreenTransition.Fade)
        Size: =16
        Text: ="⚙"
        Width: =40
        X: =Parent.Width - 56
        Y: =10

    NavigationMenu As rectangle:
        Fill: =RGBA(58, 90, 128, 1)
        Height: =Parent.Height - 60
        Width: =200
        X: =0
        Y: =60

    MenuDashboard As button:
        Align: =Align.Left
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        Height: =50
        Size: =14
        Text: ="≡ Dashboard"
        Width: =200
        X: =0
        Y: =60

    MenuCustomers As button:
        Align: =Align.Left
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(58, 90, 128, 1)
        Font: =Font.Arial
        Height: =50
        OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="👥 Customers"
        Width: =200
        X: =0
        Y: =110

    MenuProducts As button:
        Align: =Align.Left
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(58, 90, 128, 1)
        Font: =Font.Arial
        Height: =50
        OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="📦 Products"
        Width: =200
        X: =0
        Y: =160

    MenuOrders As button:
        Align: =Align.Left
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(58, 90, 128, 1)
        Font: =Font.Arial
        Height: =50
        OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="📋 Orders"
        Width: =200
        X: =0
        Y: =210

    PageTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =18
        Text: ="Dashboard"
        Width: =200
        X: =220
        Y: =80

    NewOrdersCard As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =120
        RadiusBottomLeft: =8
        RadiusBottomRight: =8
        RadiusTopLeft: =8
        RadiusTopRight: =8
        Width: =250
        X: =220
        Y: =140

    NewOrdersTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =14
        Text: ="NEW ORDERS"
        Width: =200
        X: =245
        Y: =155

    NewOrdersCount As label:
        Align: =Align.Center
        Color: =RGBA(255, 152, 0, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =24
        Text: =CountRows(Filter(colOrders, Status = "New"))
        Width: =100
        X: =320
        Y: =190

    ProcessingOrdersCard As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =120
        RadiusBottomLeft: =8
        RadiusBottomRight: =8
        RadiusTopLeft: =8
        RadiusTopRight: =8
        Width: =250
        X: =490
        Y: =140

    ProcessingOrdersTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =14
        Text: ="PROCESSING ORDERS"
        Width: =200
        X: =515
        Y: =155

    ProcessingOrdersCount As label:
        Align: =Align.Center
        Color: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =24
        Text: =CountRows(Filter(colOrders, Status = "Processing"))
        Width: =100
        X: =590
        Y: =190

    ShippedOrdersCard As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =120
        RadiusBottomLeft: =8
        RadiusBottomRight: =8
        RadiusTopLeft: =8
        RadiusTopRight: =8
        Width: =250
        X: =220
        Y: =280

    ShippedOrdersTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =14
        Text: ="SHIPPED ORDERS"
        Width: =200
        X: =245
        Y: =295

    ShippedOrdersCount As label:
        Align: =Align.Center
        Color: =RGBA(156, 39, 176, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =24
        Text: =CountRows(Filter(colOrders, Status = "Shipped"))
        Width: =100
        X: =320
        Y: =330

    DeliveredOrdersCard As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =120
        RadiusBottomLeft: =8
        RadiusBottomRight: =8
        RadiusTopLeft: =8
        RadiusTopRight: =8
        Width: =250
        X: =490
        Y: =280

    DeliveredOrdersTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =14
        Text: ="DELIVERED ORDERS"
        Width: =200
        X: =515
        Y: =295

    DeliveredOrdersCount As label:
        Align: =Align.Center
        Color: =RGBA(76, 175, 80, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =24
        Text: =CountRows(Filter(colOrders, Status = "Delivered"))
        Width: =100
        X: =590
        Y: =330

    RecentOrdersTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =16
        Text: ="RECENT ORDERS"
        Width: =200
        X: =220
        Y: =430

    RecentOrdersTable As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =150
        Width: =520
        X: =220
        Y: =460

    RecentOrdersHeader As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =25
        Size: =12
        Text: ="Order #     Customer          Date         Status"
        Width: =500
        X: =230
        Y: =470

    RecentOrder1 As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =25
        Size: =12
        Text: ="ORD-1089   Coffee World      15/05/25     New"
        Width: =500
        X: =230
        Y: =495

    RecentOrder2 As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =25
        Size: =12
        Text: ="ORD-1088   Bean Lovers       14/05/25     New"
        Width: =500
        X: =230
        Y: =520

    RecentOrder3 As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =25
        Size: =12
        Text: ="ORD-1087   Café Express      14/05/25     Processing"
        Width: =500
        X: =230
        Y: =545

    QuickActionsTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =16
        Text: ="QUICK ACTIONS"
        Width: =200
        X: =220
        Y: =630

    NewOrderButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(76, 175, 80, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        HoverFill: =RGBA(60, 159, 64, 1)
        OnSelect: =Navigate(New_Order_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="+ NEW ORDER"
        Width: =150
        X: =220
        Y: =670

    ViewCustomersButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        HoverFill: =RGBA(58, 95, 149, 1)
        OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="VIEW CUSTOMERS"
        Width: =150
        X: =390
        Y: =670

    ViewProductsButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        HoverFill: =RGBA(58, 95, 149, 1)
        OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="VIEW PRODUCTS"
        Width: =150
        X: =560
        Y: =670
