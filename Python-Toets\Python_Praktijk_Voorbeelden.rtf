{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Courier New;}}
{\colortbl ;\red0\green0\blue255;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 PYTHON PRAKTIJK VOORBEELDEN\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 1. MATH MODULE IN PRAKTIJK\b0\fs22\par
\pard\li360\sa200\sl276\slmult1\f1 # Voorbeeld: Cirkel berekeningen\par
import math\par
\par
straal = 5\par
\par
# Omtrek berekenen\par
omtrek = 2 * math.pi * straal\par
print(f"Omtrek: \{omtrek:.2f\}")  # Omtrek: 31.42\par
\par
# Oppervlakte berekenen\par
oppervlakte = math.pi * straal**2\par
print(f"Oppervlakte: \{oppervlakte:.2f\}")  # Oppervlakte: 78.54\par
\par
# Voorbeeld: Afstand tussen twee punten\par
def afstand(x1, y1, x2, y2):\par
    return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)\par
\par
print(afstand(0, 0, 3, 4))  # 5.0\f0\par

\pard\sa200\sl276\slmult1\b\fs24 2. RANDOM MODULE IN PRAKTIJK\b0\fs22\par
\pard\li360\sa200\sl276\slmult1\f1 # Voorbeeld: Dobbelsteen simulator\par
import random\par
\par
def gooi_dobbelsteen(aantal=1):\par
    return [random.randint(1, 6) for _ in range(aantal)]\par
\par
print(gooi_dobbelsteen())      # bijv. [4]\par
print(gooi_dobbelsteen(3))     # bijv. [2, 5, 1]\par
\par
# Voorbeeld: Kaarten schudden\par
kaarten = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'B', 'V', 'H', 'A']\par
kleuren = ['Harten', 'Ruiten', 'Klaveren', 'Schoppen']\par
\par
deck = [f"\{kleur\} \{kaart\}" for kleur in kleuren for kaart in kaarten]\par
random.shuffle(deck)\par
\par
# Trek 5 kaarten\par
hand = [deck.pop() for _ in range(5)]\par
print(hand)  # bijv. ['Klaveren 5', 'Harten A', 'Schoppen 7', 'Ruiten B', 'Harten 3']\f0\par

\pard\sa200\sl276\slmult1\b\fs24 3. DATETIME MODULE IN PRAKTIJK\b0\fs22\par
\pard\li360\sa200\sl276\slmult1\f1 # Voorbeeld: Leeftijd berekenen\par
from datetime import datetime, timedelta\par
\par
def bereken_leeftijd(geboortedatum):\par
    vandaag = datetime.now()\par
    leeftijd = vandaag.year - geboortedatum.year\par
    \par
    # Corrigeer als verjaardag nog niet geweest is dit jaar\par
    if (vandaag.month, vandaag.day) < (geboortedatum.month, geboortedatum.day):\par
        leeftijd -= 1\par
        \par
    return leeftijd\par
\par
geboortedatum = datetime(1990, 5, 15)\par
print(f"Leeftijd: \{bereken_leeftijd(geboortedatum)\} jaar")\par
\par
# Voorbeeld: Werkdagen berekenen\par
def werkdagen_tussen(start_datum, eind_datum):\par
    dagen = 0\par
    huidige_datum = start_datum\par
    \par
    while huidige_datum <= eind_datum:\par
        # 0 = maandag, 6 = zondag\par
        if huidige_datum.weekday() < 5:  # 0-4 zijn werkdagen\par
            dagen += 1\par
        huidige_datum += timedelta(days=1)\par
        \par
    return dagen\par
\par
start = datetime(2023, 1, 1)\par
eind = datetime(2023, 1, 31)\par
print(f"Werkdagen: \{werkdagen_tussen(start, eind)\}")  # 22 werkdagen\f0\par

\pard\sa200\sl276\slmult1\b\fs24 4. OS MODULE IN PRAKTIJK\b0\fs22\par
\pard\li360\sa200\sl276\slmult1\f1 # Voorbeeld: Bestandsgrootte controleren\par
import os\par
\par
def toon_grote_bestanden(map_pad, min_grootte_mb=1):\par
    min_grootte_bytes = min_grootte_mb * 1024 * 1024\par
    grote_bestanden = []\par
    \par
    for bestand in os.listdir(map_pad):\par
        bestand_pad = os.path.join(map_pad, bestand)\par
        if os.path.isfile(bestand_pad):\par
            grootte = os.path.getsize(bestand_pad)\par
            if grootte >= min_grootte_bytes:\par
                grote_bestanden.append((bestand, grootte / (1024 * 1024)))\par
    \par
    return grote_bestanden\par
\par
# Voorbeeld aanroep (niet uitvoeren in deze context)\par
# grote_bestanden = toon_grote_bestanden("C:/Downloads", 10)\par
# for naam, grootte in grote_bestanden:\par
#     print(f"\{naam\}: \{grootte:.2f\} MB")\par
\par
# Voorbeeld: Mapstructuur maken\par
def maak_project_structuur(basis_map):\par
    mappen = ["src", "docs", "tests", "data"]\par
    \par
    if not os.path.exists(basis_map):\par
        os.makedirs(basis_map)\par
        \par
    for map in mappen:\par
        pad = os.path.join(basis_map, map)\par
        if not os.path.exists(pad):\par
            os.makedirs(pad)\par
            print(f"Map aangemaakt: \{pad\}")\par
\par
# Voorbeeld aanroep (niet uitvoeren in deze context)\par
# maak_project_structuur("mijn_project")\f0\par

\pard\sa200\sl276\slmult1\b\fs24 5. JSON MODULE IN PRAKTIJK\b0\fs22\par
\pard\li360\sa200\sl276\slmult1\f1 # Voorbeeld: Configuratie opslaan en laden\par
import json\par
\par
# Configuratie aanmaken\par
config = \{\par
    "app_naam": "MijnApp",\par
    "versie": "1.0.0",\par
    "instellingen": \{\par
        "dark_mode": True,\par
        "taal": "nl",\par
        "notificaties": True\par
    \},\par
    "gebruikers": [\par
        \{"id": 1, "naam": "Alice", "admin": True\},\par
        \{"id": 2, "naam": "Bob", "admin": False\}\par
    ]\par
\}\par
\par
# Configuratie opslaan\par
def sla_config_op(config, bestandsnaam):\par
    with open(bestandsnaam, 'w') as f:\par
        json.dump(config, f, indent=4)\par
    print(f"Configuratie opgeslagen in \{bestandsnaam\}")\par
\par
# Configuratie laden\par
def laad_config(bestandsnaam):\par
    try:\par
        with open(bestandsnaam, 'r') as f:\par
            return json.load(f)\par
    except FileNotFoundError:\par
        print(f"Configuratiebestand \{bestandsnaam\} niet gevonden")\par
        return None\par
\par
# Voorbeeld gebruik\par
# sla_config_op(config, "config.json")\par
# geladen_config = laad_config("config.json")\par
# print(geladen_config["app_naam"])  # MijnApp\par
# print(geladen_config["instellingen"]["taal"])  # nl\f0\par

\pard\sa200\sl276\slmult1\b\fs24 6. CSV MODULE IN PRAKTIJK\b0\fs22\par
\pard\li360\sa200\sl276\slmult1\f1 # Voorbeeld: Verkoopcijfers analyseren\par
import csv\par
\par
# Voorbeeld CSV data:\par
# datum,product,aantal,prijs\par
# 2023-01-15,Laptop,3,899.99\par
# 2023-01-15,Muis,10,24.99\par
# 2023-01-16,Toetsenbord,5,49.99\par
# 2023-01-17,Monitor,2,249.99\par
# 2023-01-17,Laptop,1,899.99\par
\par
def analyseer_verkopen(csv_bestand):\par
    verkopen_per_product = \{\}\par
    totale_omzet = 0\par
    \par
    with open(csv_bestand, 'r', newline='') as f:\par
        reader = csv.DictReader(f)\par
        \par
        for rij in reader:\par
            product = rij['product']\par
            aantal = int(rij['aantal'])\par
            prijs = float(rij['prijs'])\par
            omzet = aantal * prijs\par
            \par
            if product in verkopen_per_product:\par
                verkopen_per_product[product]['aantal'] += aantal\par
                verkopen_per_product[product]['omzet'] += omzet\par
            else:\par
                verkopen_per_product[product] = \{\par
                    'aantal': aantal,\par
                    'omzet': omzet\par
                \}\par
            \par
            totale_omzet += omzet\par
    \par
    # Resultaten tonen\par
    print(f"Totale omzet: €\{totale_omzet:.2f\}")\par
    print("\\nVerkopen per product:")\par
    for product, data in verkopen_per_product.items():\par
        print(f"\{product\}: \{data['aantal'\]} stuks, €\{data['omzet']:.2f\} omzet")\par
\par
# Voorbeeld aanroep (niet uitvoeren in deze context)\par
# analyseer_verkopen("verkopen.csv")\f0\par

\pard\sa200\sl276\slmult1\b\fs24 7. REQUESTS MODULE IN PRAKTIJK\b0\fs22\par
\pard\li360\sa200\sl276\slmult1\f1 # Voorbeeld: Weer API gebruiken\par
# Eerst installeren: pip install requests\par
import requests\par
\par
def haal_weer_op(stad):\par
    # Gebruik OpenWeatherMap API (gratis API key nodig)\par
    api_key = "jouw_api_key_hier"  # Vervang door echte API key\par
    url = f"https://api.openweathermap.org/data/2.5/weather?q=\{stad\}&appid=\{api_key\}&units=metric&lang=nl"\par
    \par
    try:\par
        response = requests.get(url)\par
        response.raise_for_status()  # Raise exception voor HTTP errors\par
        \par
        weer_data = response.json()\par
        \par
        # Relevante informatie extraheren\par
        temperatuur = weer_data["main"]["temp"]\par
        luchtvochtigheid = weer_data["main"]["humidity"]\par
        beschrijving = weer_data["weather"][0]["description"]\par
        \par
        print(f"Weer in \{stad\}:")\par
        print(f"Temperatuur: \{temperatuur\}°C")\par
        print(f"Luchtvochtigheid: \{luchtvochtigheid\}%")\par
        print(f"Beschrijving: \{beschrijving\}")\par
        \par
    except requests.exceptions.RequestException as e:\par
        print(f"Fout bij ophalen weerdata: \{e\}")\par
\par
# Voorbeeld aanroep (niet uitvoeren zonder API key)\par
# haal_weer_op("Amsterdam")\f0\par

\pard\sa200\sl276\slmult1\b\fs24 8. COLLECTIONS MODULE IN PRAKTIJK\b0\fs22\par
\pard\li360\sa200\sl276\slmult1\f1 # Voorbeeld: Woordfrequentie tellen\par
from collections import Counter\par
\par
def tel_woorden(tekst):\par
    # Tekst opschonen en splitsen in woorden\par
    woorden = tekst.lower().replace('.', '').replace(',', '').split()\par
    \par
    # Frequentie tellen\par
    frequentie = Counter(woorden)\par
    \par
    # Top 5 meest voorkomende woorden\par
    print("Top 5 meest voorkomende woorden:")\par
    for woord, aantal in frequentie.most_common(5):\par
        print(f"\{woord\}: \{aantal\} keer")\par
\par
voorbeeld_tekst = """\par
Python is een veelzijdige programmeertaal. Python is eenvoudig te leren\par
en krachtig om mee te werken. Python heeft een elegante syntax en is\par
daarom een uitstekende taal om programmeren mee te leren.\par
"""\par
\par
tel_woorden(voorbeeld_tekst)\par
# Uitvoer:\par
# Top 5 meest voorkomende woorden:\par
# python: 3 keer\par
# is: 3 keer\par
# een: 3 keer\par
# te: 2 keer\par
# mee: 2 keer\par
\par
# Voorbeeld: defaultdict gebruiken\par
from collections import defaultdict\par
\par
def groepeer_op_lengte(woorden):\par
    groepen = defaultdict(list)\par
    \par
    for woord in woorden:\par
        groepen[len(woord)].append(woord)\par
    \par
    return groepen\par
\par
woorden_lijst = ["appel", "peer", "banaan", "kers", "druif", "framboos"]\par
gegroepeerd = groepeer_op_lengte(woorden_lijst)\par
\par
for lengte, woorden in sorted(gegroepeerd.items()):\par
    print(f"Woorden met \{lengte\} letters: \{woorden\}")\par
# Uitvoer:\par
# Woorden met 4 letters: ['peer', 'kers']\par
# Woorden met 5 letters: ['appel', 'druif']\par
# Woorden met 6 letters: ['banaan']\par
# Woorden met 8 letters: ['framboos']\f0\par

\pard\sa200\sl276\slmult1\b\fs24 9. FUNCTIES IN PRAKTIJK\b0\fs22\par
\pard\li360\sa200\sl276\slmult1\f1 # Voorbeeld: Functie voor BTW berekening\par
def bereken_btw(bedrag, btw_percentage=21):\par
    """Berekent het BTW-bedrag en totaalbedrag inclusief BTW.\par
    \par
    Args:\par
        bedrag: Het bedrag exclusief BTW\par
        btw_percentage: Het BTW-percentage (standaard 21%)\par
        \par
    Returns:\par
        tuple: (btw_bedrag, totaal_bedrag)\par
    """\par
    btw_bedrag = bedrag * (btw_percentage / 100)\par
    totaal_bedrag = bedrag + btw_bedrag\par
    return btw_bedrag, totaal_bedrag\par
\par
# Functie gebruiken\par
prijs = 100\par
btw, totaal = bereken_btw(prijs)\par
print(f"Prijs: €\{prijs:.2f\}")\par
print(f"BTW (21%): €\{btw:.2f\}")\par
print(f"Totaal: €\{totaal:.2f\}")\par
\par
# Met ander BTW-percentage\par
btw, totaal = bereken_btw(prijs, 9)\par
print(f"\\nPrijs: €\{prijs:.2f\}")\par
print(f"BTW (9%): €\{btw:.2f\}")\par
print(f"Totaal: €\{totaal:.2f\}")\par
\par
# Voorbeeld: Functie met *args en **kwargs\par
def maak_bestelling(*items, **klant_info):\par
    """Maakt een bestelling aan met variabel aantal items en klantinfo."""\par
    print(f"Nieuwe bestelling voor \{klant_info.get('naam', 'Onbekende klant'\)}")\par
    \par
    if 'adres' in klant_info:\par
        print(f"Bezorgen op: \{klant_info['adres']\}")\par
    \par
    print("\\nBestelde items:")\par
    for i, item in enumerate(items, 1):\par
        print(f"\{i}. \{item\}")\par
\par
# Functie gebruiken\par
maak_bestelling(\par
    "Pizza Margherita", \par
    "Cola 1L", \par
    "Tiramisu",\par
    naam="Jan Jansen",\par
    adres="Hoofdstraat 123, Amsterdam",\par
    telefoon="06-********"\par
)\f0\par

\pard\sa200\sl276\slmult1\b\fs24 10. KLASSEN IN PRAKTIJK\b0\fs22\par
\pard\li360\sa200\sl276\slmult1\f1 # Voorbeeld: Bankrekening klasse\par
class Bankrekening:\par
    def __init__(self, rekeningnummer, eigenaar, saldo=0):\par
        self.rekeningnummer = rekeningnummer\par
        self.eigenaar = eigenaar\par
        self._saldo = saldo  # Protected attribuut\par
    \par
    @property\par
    def saldo(self):\par
        return self._saldo\par
    \par
    def stort(self, bedrag):\par
        if bedrag > 0:\par
            self._saldo += bedrag\par
            print(f"€\{bedrag:.2f\} gestort. Nieuw saldo: €\{self._saldo:.2f\}")\par
        else:\par
            print("Bedrag moet positief zijn")\par
    \par
    def neem_op(self, bedrag):\par
        if 0 < bedrag <= self._saldo:\par
            self._saldo -= bedrag\par
            print(f"€\{bedrag:.2f\} opgenomen. Nieuw saldo: €\{self._saldo:.2f\}")\par
        else:\par
            print("Ongeldig bedrag of onvoldoende saldo")\par
    \par
    def __str__(self):\par
        return f"Bankrekening \{self.rekeningnummer\} van \{self.eigenaar\}, saldo: €\{self._saldo:.2f\}"\par
\par
# Spaarrekening subklasse\par
class Spaarrekening(Bankrekening):\par
    def __init__(self, rekeningnummer, eigenaar, saldo=0, rente_percentage=2.5):\par
        super().__init__(rekeningnummer, eigenaar, saldo)\par
        self.rente_percentage = rente_percentage\par
    \par
    def rente_bijschrijven(self):\par
        rente = self._saldo * (self.rente_percentage / 100)\par
        self._saldo += rente\par
        print(f"€\{rente:.2f\} rente bijgeschreven. Nieuw saldo: €\{self._saldo:.2f\}")\par
\par
# Klassen gebruiken\par
rekening = Bankrekening("NL01BANK0********9", "Alice Johnson", 1000)\par
print(rekening)  # Bankrekening NL01BANK0********9 van Alice Johnson, saldo: €1000.00\par
\par
rekening.stort(500)\par
rekening.neem_op(200)\par
print(f"Huidig saldo: €\{rekening.saldo:.2f\}")\par
\par
print("\\nSpaarrekening aanmaken:")\par
spaarrekening = Spaarrekening("NL02BANK9876543210", "Bob Smith", 2000)\par
print(spaarrekening)\par
spaarrekening.rente_bijschrijven()\f0\par

\pard\sa200\sl276\slmult1\b\fs24 11. BESTANDSVERWERKING IN PRAKTIJK\b0\fs22\par
\pard\li360\sa200\sl276\slmult1\f1 # Voorbeeld: Logbestand analyseren\par
def analyseer_logbestand(bestandsnaam):\par
    """Analyseert een logbestand en telt foutmeldingen per type."""\par
    fout_teller = \{\}\par
    regel_teller = 0\par
    \par
    try:\par
        with open(bestandsnaam, 'r') as f:\par
            for regel in f:\par
                regel_teller += 1\par
                \par
                # Zoek naar foutmeldingen (voorbeeld formaat: [ERROR] Foutmelding)\par
                if "[ERROR]" in regel:\par
                    # Extract fouttype (alles na [ERROR] tot einde regel of :)\par
                    fout_start = regel.find("[ERROR]") + 8\par
                    fout_eind = regel.find(":", fout_start)\par
                    if fout_eind == -1:  # Geen : gevonden\par
                        fout_eind = len(regel)\par
                    \par
                    fout_type = regel[fout_start:fout_eind].strip()\par
                    \par
                    if fout_type in fout_teller:\par
                        fout_teller[fout_type] += 1\par
                    else:\par
                        fout_teller[fout_type] = 1\par
        \par
        # Resultaten tonen\par
        print(f"Logbestand \{bestandsnaam\} geanalyseerd")\par
        print(f"Totaal aantal regels: \{regel_teller\}")\par
        print(f"Aantal foutmeldingen: \{sum(fout_teller.values())\}")\par
        \par
        if fout_teller:\par
            print("\\nFouten per type:")\par
            for fout_type, aantal in sorted(fout_teller.items(), key=lambda x: x[1], reverse=True):\par
                print(f"- \{fout_type\}: \{aantal\} keer")\par
    \par
    except FileNotFoundError:\par
        print(f"Bestand \{bestandsnaam\} niet gevonden")\par
    except Exception as e:\par
        print(f"Fout bij analyseren: \{e\}")\par
\par
# Voorbeeld aanroep (niet uitvoeren in deze context)\par
# analyseer_logbestand("server.log")\f0\par

\pard\sa200\sl276\slmult1\b\fs24 12. REGULIERE EXPRESSIES IN PRAKTIJK\b0\fs22\par
\pard\li360\sa200\sl276\slmult1\f1 # Voorbeeld: E-mailadressen valideren en extraheren\par
import re\par
\par
def valideer_email(email):\par
    """Controleert of een e-mailadres geldig is."""\par
    # Basis patroon voor e-mailadres validatie\par
    patroon = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]\{2,\}$'\par
    \par
    if re.match(patroon, email):\par
        return True\par
    return False\par
\par
def extract_emails(tekst):\par
    """Extraheert alle e-mailadressen uit een tekst."""\par
    patroon = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]\{2,\}'\par
    return re.findall(patroon, tekst)\par
\par
# Validatie testen\par
emails = [\par
    "<EMAIL>",\par
    "invalid@email",\par
    "<EMAIL>",\par
    "@geen-gebruiker.com",\par
    "spaties <EMAIL>"\par
]\par
\par
print("E-mail validatie:")\par
for email in emails:\par
    resultaat = "geldig" if valideer_email(email) else "ongeldig"\par
    print(f"\{email\}: \{resultaat\}")\par
\par
# E-mails extraheren\par
voorbeeld_tekst = """\par
Contactpersonen:\par
- Alice Johnson (<EMAIL>)\par
- Bob Smith <<EMAIL>>\par
- <EMAIL>\par
Stuur ook een <NAME_EMAIL>\par
"""\par
\par
gevonden_emails = extract_emails(voorbeeld_tekst)\par
print("\\nGevonden e-mailadressen:")\par
for email in gevonden_emails:\par
    print(f"- \{email\}")\f0\par
}
