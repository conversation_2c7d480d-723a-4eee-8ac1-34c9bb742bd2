New_Order_Screen As screen:
    Fill: =RGBA(245, 245, 245, 1)
    LoadingSpinnerColor: =RGBA(74, 111, 165, 1)

    HeaderBar As rectangle:
        Fill: =RGBA(74, 111, 165, 1)
        Height: =60
        Width: =Parent.Width
        X: =0
        Y: =0

    HeaderTitle As label:
        Color: =RGBA(255, 255, 255, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =16
        Text: ="EuroCaps Order Management Pro"
        Width: =300
        X: =70
        Y: =10

    NavigationMenu As rectangle:
        Fill: =RGBA(58, 90, 128, 1)
        Height: =Parent.Height - 60
        Width: =200
        X: =0
        Y: =60

    MenuDashboard As button:
        Align: =Align.Left
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(58, 90, 128, 1)
        Font: =Font.Arial
        Height: =50
        OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="≡ Dashboard"
        Width: =200
        X: =0
        Y: =60

    PageTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =18
        Text: ="New Order"
        Width: =200
        X: =220
        Y: =80

    OrderInfoTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =16
        Text: ="ORDER INFORMATION"
        Width: =300
        X: =220
        Y: =130

    OrderInfoPanel As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =200
        RadiusBottomLeft: =8
        RadiusBottomRight: =8
        RadiusTopLeft: =8
        RadiusTopRight: =8
        Width: =600
        X: =220
        Y: =160

    CustomerLabel As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =25
        Size: =12
        Text: ="Customer:"
        Width: =100
        X: =240
        Y: =180

    CustomerDropdown As dropdown:
        Font: =Font.Arial
        Height: =35
        Items: =colCustomers
        Size: =12
        Width: =300
        X: =350
        Y: =175

    OrderDateLabel As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =25
        Size: =12
        Text: ="Order Date:"
        Width: =100
        X: =240
        Y: =220

    OrderDatePicker As datePicker:
        DefaultDate: =Today()
        Font: =Font.Arial
        Height: =35
        Size: =12
        Width: =150
        X: =350
        Y: =215

    DeliveryDateLabel As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =25
        Size: =12
        Text: ="Delivery Date:"
        Width: =120
        X: =240
        Y: =260

    DeliveryDatePicker As datePicker:
        DefaultDate: =DateAdd(Today(), 7, Days)
        Font: =Font.Arial
        Height: =35
        Size: =12
        Width: =150
        X: =370
        Y: =255

    NotesLabel As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =25
        Size: =12
        Text: ="Notes:"
        Width: =100
        X: =240
        Y: =300

    NotesInput As text:
        Font: =Font.Arial
        Height: =40
        HintText: ="Enter special instructions..."
        Mode: =TextMode.MultiLine
        Size: =12
        Width: =400
        X: =240
        Y: =320

    OrderItemsTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =16
        Text: ="ORDER ITEMS"
        Width: =300
        X: =220
        Y: =390

    OrderItemsPanel As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =120
        RadiusBottomLeft: =8
        RadiusBottomRight: =8
        RadiusTopLeft: =8
        RadiusTopRight: =8
        Width: =600
        X: =220
        Y: =420

    NoItemsMessage As label:
        Align: =Align.Center
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =30
        Size: =14
        Text: ="No items added yet."
        Width: =400
        X: =320
        Y: =460

    AddProductsButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(76, 175, 80, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        HoverFill: =RGBA(60, 159, 64, 1)
        OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="+ ADD PRODUCTS"
        Width: =150
        X: =420
        Y: =490

    OrderSummaryTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =16
        Text: ="ORDER SUMMARY"
        Width: =300
        X: =220
        Y: =560

    OrderSummaryPanel As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =80
        RadiusBottomLeft: =8
        RadiusBottomRight: =8
        RadiusTopLeft: =8
        RadiusTopRight: =8
        Width: =600
        X: =220
        Y: =590

    TotalItemsLabel As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =25
        Size: =12
        Text: ="Total Items: 0"
        Width: =150
        X: =240
        Y: =610

    TotalQuantityLabel As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =25
        Size: =12
        Text: ="Total Quantity: 0"
        Width: =150
        X: =500
        Y: =610

    SaveDraftButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =45
        HoverFill: =RGBA(58, 95, 149, 1)
        Size: =14
        Text: ="SAVE AS DRAFT"
        Width: =150
        X: =220
        Y: =700

    SubmitOrderButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(76, 175, 80, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =45
        HoverFill: =RGBA(60, 159, 64, 1)
        Size: =14
        Text: ="SUBMIT ORDER"
        Width: =150
        X: =390
        Y: =700

    CancelButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(158, 158, 158, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =45
        HoverFill: =RGBA(142, 142, 142, 1)
        OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="CANCEL"
        Width: =100
        X: =560
        Y: =700
