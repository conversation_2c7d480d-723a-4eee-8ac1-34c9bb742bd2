<!DOCTYPE html>
<html>
<head>
    <title>Normalisatie: 0NF naar 1NF</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .diagram {
            width: 800px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: white;
            padding: 20px;
        }
        .table {
            border-collapse: collapse;
            margin: 20px 0;
            width: 100%;
        }
        .table th, .table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #333;
            color: white;
        }
        .pk {
            font-weight: bold;
            text-decoration: underline;
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .step {
            margin: 30px 0;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .step-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }
        .arrow {
            text-align: center;
            font-size: 24px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="title">Normalisatie: 0NF naar 1NF</div>
    <div class="subtitle">Eerste stap in het normalisatieproces</div>
    
    <div class="diagram">
        <div class="step">
            <div class="step-title">0NF: Niet-genormaliseerde tabel</div>
            <p>Gegevens zijn op geen enkele manier genormaliseerd.</p>
            <table class="table">
                <tr>
                    <th>OrderID</th>
                    <th>KlantID</th>
                    <th>KlantNaam</th>
                    <th>KlantAdres</th>
                    <th>ProductID</th>
                    <th>ProductNaam</th>
                    <th>Prijs</th>
                    <th>Aantal</th>
                    <th>Subtotaal</th>
                </tr>
                <tr>
                    <td>1001</td>
                    <td>K100</td>
                    <td>Jan Jansen</td>
                    <td>Hoofdstraat 1, Amsterdam</td>
                    <td>P200</td>
                    <td>Laptop</td>
                    <td>899.99</td>
                    <td>1</td>
                    <td>899.99</td>
                </tr>
                <tr>
                    <td>1001</td>
                    <td>K100</td>
                    <td>Jan Jansen</td>
                    <td>Hoofdstraat 1, Amsterdam</td>
                    <td>P201</td>
                    <td>Muis</td>
                    <td>19.99</td>
                    <td>2</td>
                    <td>39.98</td>
                </tr>
                <tr>
                    <td>1002</td>
                    <td>K101</td>
                    <td>Piet Pietersen</td>
                    <td>Kerkstraat 10, Rotterdam</td>
                    <td>P200</td>
                    <td>Laptop</td>
                    <td>899.99</td>
                    <td>1</td>
                    <td>899.99</td>
                </tr>
            </table>
            <p>Problemen:</p>
            <ul>
                <li>Redundantie: klantgegevens worden herhaald voor elke orderregel</li>
                <li>Berekende velden: Subtotaal kan worden berekend uit Prijs en Aantal</li>
                <li>Geen duidelijke primaire sleutel: OrderID is niet uniek</li>
            </ul>
        </div>
        
        <div class="arrow">↓</div>
        
        <div class="step">
            <div class="step-title">1NF: Eerste Normaalvorm</div>
            <p>Alle attributen zijn atomair, geen repeterende groepen, primaire sleutel geïdentificeerd.</p>
            <table class="table">
                <tr>
                    <th class="pk">OrderID</th>
                    <th class="pk">ProductID</th>
                    <th>KlantID</th>
                    <th>KlantNaam</th>
                    <th>KlantAdres</th>
                    <th>ProductNaam</th>
                    <th>Prijs</th>
                    <th>Aantal</th>
                </tr>
                <tr>
                    <td>1001</td>
                    <td>P200</td>
                    <td>K100</td>
                    <td>Jan Jansen</td>
                    <td>Hoofdstraat 1, Amsterdam</td>
                    <td>Laptop</td>
                    <td>899.99</td>
                    <td>1</td>
                </tr>
                <tr>
                    <td>1001</td>
                    <td>P201</td>
                    <td>K100</td>
                    <td>Jan Jansen</td>
                    <td>Hoofdstraat 1, Amsterdam</td>
                    <td>Muis</td>
                    <td>19.99</td>
                    <td>2</td>
                </tr>
                <tr>
                    <td>1002</td>
                    <td>P200</td>
                    <td>K101</td>
                    <td>Piet Pietersen</td>
                    <td>Kerkstraat 10, Rotterdam</td>
                    <td>Laptop</td>
                    <td>899.99</td>
                    <td>1</td>
                </tr>
            </table>
            <p>Veranderingen:</p>
            <ul>
                <li>Berekende velden (Subtotaal) verwijderd</li>
                <li>Samengestelde primaire sleutel (OrderID, ProductID) gedefinieerd</li>
                <li>Alle attributen zijn atomair (ondeelbaar)</li>
            </ul>
            <p>Regels voor 1NF volgens de cursus:</p>
            <ul>
                <li>Zorg dat een attribuut geen dubbele betekenis heeft</li>
                <li>Laat de attributen die berekend kunnen worden uit andere attributen weg</li>
                <li>Bepaal de identifier (de attributen die de entiteit uniek maken)</li>
                <li>Verwijder repeterende groepen en plaats deze in een nieuwe entiteit</li>
            </ul>
        </div>
    </div>
</body>
</html>
