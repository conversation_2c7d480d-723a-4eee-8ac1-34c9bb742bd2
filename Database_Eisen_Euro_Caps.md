# 4.1 Database Eisen

## Analyse van de Informatiebehoefte vanuit de Processen

De informatiebehoeften voor Euro Caps' database zijn direct afgeleid van de kernprocessen binnen de koffiecapsuleproductie. Een grondige analyse van deze processen onthult specifieke databehoefte voor elke fase van de productieketen. De database moet niet alleen de operationele aspecten van het productieproces ondersteunen, maar ook strategische besluitvorming faciliteren door middel van uitgebreide rapportage- en analysemogelijkheden.

Bij het ontwerpen van een database voor Euro Caps is het essentieel om rekening te houden met de unieke kenmerken van de koffiecapsuleproductie. De hoge kwaliteitseisen, de noodzaak voor nauwkeurige dosering, de complexe supply chain en de strikte traceerbaarheidsvereisten stellen specifieke eisen aan de databasestructuur. Daarnaast moet de database schaalbaar zijn om toekomstige groei te accommoderen en flexibel genoeg om aan veranderende marktomstandigheden en regelgeving te kunnen voldoen.

De database moet ook integratiemogelijkheden bieden met andere systemen binnen Euro Caps, zoals ERP-systemen, kwaliteitsmanagementsystemen en logistieke planningssoftware. Dit vereist goed gedefinieerde interfaces en datastructuren die compatibel zijn met industriestandaarden. Bovendien moet de database voldoen aan de hoogste normen voor gegevensbeveiliging en privacy, gezien de gevoelige aard van sommige bedrijfsgegevens.

### Informatiebehoefte vanuit het Maalproces

Het maalproces, waarbij 500kg Big Bags koffiebonen worden verwerkt, vereist nauwkeurige registratie van diverse gegevens. Voor elke batch koffiebonen is traceerbaarheid essentieel met informatie over herkomst, leverancier, ontvangstdatum en kwaliteitskenmerken. Specifiek voor de koffiebonen moeten gegevens worden vastgelegd over de variëteit (Arabica, Robusta of mengsels), het land en de regio van herkomst, de oogstdatum, de roostergraad en eventuele certificeringen (zoals Rainforest Alliance of Fair Trade). Deze gedetailleerde informatie is niet alleen belangrijk voor kwaliteitscontrole, maar ook voor marketing en productspecificaties.

De procesparameters zoals maalgraad (fijn, medium of grof), temperatuur tijdens het malen en aroma-eigenschappen moeten worden vastgelegd om consistentie te waarborgen. Deze parameters hebben directe invloed op de smaakbeleving van het eindproduct en moeten daarom nauwkeurig worden gecontroleerd en gedocumenteerd. De database moet ook historische gegevens over deze parameters bevatten om trendanalyses mogelijk te maken en procesoptimalisaties te ondersteunen.

Resultaten van continue kwaliteitscontroles dienen te worden opgeslagen voor analyse en traceerbaarheid. Dit omvat sensorische tests (geur, kleur), fysieke metingen (deeltjesgrootte, vochtigheid) en chemische analyses (cafeïnegehalte, zuurgraad). Voor elke test moeten de meetwaarden, acceptatiecriteria, testmethode en testfrequentie worden vastgelegd. Afwijkingen moeten worden gedocumenteerd met oorzaakanalyse en correctieve maatregelen.

Daarnaast is informatie over gebruikte maalmachines noodzakelijk, inclusief onderhoudsstatus, operationele parameters en prestatie-indicatoren zoals energieverbruik, verwerkingssnelheid en stilstandtijd. De database moet ook onderhoudschema's, reserveonderdeleninventaris en technische specificaties bevatten om preventief onderhoud te faciliteren en machinestilstand te minimaliseren.

Tot slot vereist het proces registratie van betrokken medewerkers voor verantwoordelijkheid en training. Dit omvat niet alleen basisgegevens zoals naam en functie, maar ook specifieke kwalificaties, trainingsgeschiedenis, certificeringen en autorisatieniveaus. Deze informatie is cruciaal voor compliance met kwaliteitsmanagementsystemen zoals ISO 9001 en voor het waarborgen dat alleen gekwalificeerd personeel kritische processen uitvoert.

### Informatiebehoefte vanuit het Vulproces

Het vulproces, waarbij capsules worden gevuld met 4,0-6,0g gemalen koffie en worden afgesloten met aluminiumfolie, brengt eigen informatiebehoeften met zich mee. Precieze registratie van de hoeveelheid koffie per capsule, met tolerantiegrenzen, is cruciaal voor productkwaliteit en kostenbeheersing. De database moet niet alleen de streefgewichten vastleggen, maar ook de toegestane afwijkingen (bijvoorbeeld ±0,2g) en de werkelijke meetwaarden van steekproeven tijdens de productie. Deze gegevens zijn essentieel voor statistische procescontrole (SPC) en voor het identificeren van trends die kunnen wijzen op kalibratieproblemen of slijtage van doseerapparatuur.

Het proces vereist nauwkeurige tracking van gebruikte capsules en aluminiumfolie, inclusief batchnummers, leveranciers, productiedatum en kwaliteitsspecificaties voor traceerbaarheid. Voor de capsules moeten gegevens worden vastgelegd over materiaaltype (bijvoorbeeld aluminium of biologisch afbreekbaar), kleur, afmetingen en compatibiliteit met verschillende koffiemachines. Voor de aluminiumfolie zijn gegevens nodig over dikte, samenstelling, sealingeigenschappen en bedrukking. Deze gedetailleerde materiaalspecificaties zijn niet alleen belangrijk voor kwaliteitscontrole, maar ook voor productinnovatie en duurzaamheidsrapportage.

Gegevens over gewichtscontroles en vulniveaucontroles, met classificatie (OK/NOK), moeten worden vastgelegd met tijdstempel, operator-ID en exacte meetwaarden. De database moet ook de instellingen van de meetapparatuur, kalibratiestatus en meetonzekerheid documenteren om de betrouwbaarheid van de kwaliteitsgegevens te waarborgen. Voor NOK-producten moet de database de specifieke afwijking, de ernst van het probleem en de genomen maatregelen (herbewerking, afkeuring, aanpassing van procesparameters) registreren.

Productiestatistieken zoals aantallen geproduceerde capsules, verwerkingssnelheid, uitvalpercentages, machinerendement (OEE - Overall Equipment Effectiveness) en doorlooptijden zijn essentieel voor procesoptimalisatie en capaciteitsplanning. De database moet deze gegevens kunnen aggregeren per tijdsperiode, producttype, ploeg en machine om vergelijkende analyses mogelijk te maken en prestatiedoelen te kunnen stellen en monitoren.

Informatie over vulmachines en sealingapparatuur, inclusief instellingen (temperatuur, druk, snelheid), prestaties en storingen, completeert de databehoefte voor deze fase. De database moet ook onderhoudgeschiedenis, reserveonderdelen, technische documentatie en contactgegevens van leveranciers bevatten om snelle probleemoplossing te faciliteren. Daarnaast zijn gegevens nodig over energieverbruik, persluchtverbruik en andere hulpstoffen om de operationele kosten te kunnen analyseren en duurzaamheidsdoelstellingen te monitoren.

### Informatiebehoefte vanuit het Verpakkingsproces

Bij het verpakkingsproces, waarbij capsules in consumentenverpakkingen worden verpakt, is gedetailleerde informatie over verpakkingsformaten (10, 20, of 44 capsules), verpakkingsontwerp, materiaalgebruik en productspecificaties van cruciaal belang. De database moet alle verpakkingsvarianten kunnen beheren, inclusief reguliere verpakkingen, seizoensgebonden edities, promotieverpakkingen en klantspecifieke verpakkingen. Voor elk verpakkingstype moeten gegevens worden vastgelegd over afmetingen, gewicht, barcodeinformatie, houdbaarheid, opslagcondities en marketinginformatie zoals productbeschrijvingen in verschillende talen en afbeeldingen voor e-commerce platforms.

Een nauwkeurige koppeling tussen productbatches en verpakkingsbatches is noodzakelijk voor traceerbaarheid in geval van kwaliteitsproblemen of terugroepacties. De database moet kunnen vastleggen welke specifieke capsules (met hun eigen productie- en kwaliteitsgegevens) in welke specifieke verpakkingen zijn terechtgekomen, inclusief tijdstempel, verpakkingslijn en operator. Deze gedetailleerde traceerbaarheid is niet alleen belangrijk voor kwaliteitsborging, maar ook voor compliance met voedselveiligheidsregelgeving zoals HACCP en voor het minimaliseren van de impact van eventuele terugroepacties.

Registratie van batchinformatie op verpakkingen, zoals productiedatum, houdbaarheidsdatum, batchnummer en productiefaciliteit, zorgt voor effectieve tracking door de gehele supply chain. De database moet de generatie van deze informatie ondersteunen en de correcte toepassing ervan op de verpakking valideren via geautomatiseerde controlesystemen zoals vision inspection. Daarnaast moet de database informatie bevatten over verpakkingsmateriaal, drukinkt en lijm, inclusief certificeringen voor voedselveiligheid en duurzaamheid (zoals FSC-certificering voor karton of composteerbare materialen).

Gegevens over palletopbouw, aantallen dozen per pallet, palletafmetingen, gewicht, stapelpatronen en opslaglocaties ondersteunen de logistieke processen en optimaliseren de benutting van opslagruimte en transportcapaciteit. De database moet ook informatie bevatten over verzendlabels, transportdocumenten, douanedocumentatie voor export en specifieke klanteneisen voor levering en palletisering. Deze gedetailleerde logistieke informatie is essentieel voor efficiënte orderverwerking, voorraadbeheersing en distributieplanningen.

De resultaten van finale kwaliteitsinspecties voordat producten worden vrijgegeven vormen het sluitstuk van de informatieketen in deze fase. Deze inspecties omvatten controles op verpakkingsintegriteit, etikettering, leesbaarheid van batchinformatie, verpakkingsgewicht en visuele aspecten. De database moet de inspectieresultaten, eventuele afwijkingen, correctieve acties en goedkeuringen door kwaliteitsverantwoordelijken vastleggen. Daarnaast moet de database ook informatie bevatten over bewaarmonsters die worden genomen voor latere referentie en kwaliteitsverificatie gedurende de houdbaarheidsperiode van het product.

### Informatiebehoefte vanuit Kwaliteitscontrole

Het kwaliteitscontrolesysteem van Euro Caps, dat producten classificeert als OK of NOK, vraagt om een uitgebreide en gestructureerde informatievoorziening die alle aspecten van kwaliteitsmanagement ondersteunt. De definitie van kwaliteitscriteria voor elke productiefase vormt de basis van het systeem en moet in de database worden vastgelegd met duidelijke specificaties, tolerantiegrenzen, meetmethoden en beslissingscriteria. Voor elke kwaliteitsparameter moeten de minimale, maximale en streefwaarden worden gedefinieerd, evenals de frequentie van controles en de verantwoordelijke functies. Deze kwaliteitscriteria moeten regelmatig worden geëvalueerd en bijgewerkt op basis van klantfeedback, markttrends en interne verbeterinitiatieven.

Gedetailleerde registratie van alle kwaliteitscontroles en hun uitkomsten is essentieel voor kwaliteitsborging en traceerbaarheid. De database moet voor elke kwaliteitscontrole vastleggen wanneer deze is uitgevoerd, door wie, met welke meetapparatuur, volgens welke procedure, en met welk resultaat. Voor kwantitatieve metingen moeten de exacte meetwaarden worden opgeslagen, niet alleen de OK/NOK-classificatie, om trendanalyse mogelijk te maken. De database moet ook de kalibratiestatus van meetapparatuur bijhouden en waarschuwen wanneer kalibratie nodig is om de betrouwbaarheid van metingen te waarborgen.

Documentatie van NOK-producten, inclusief aard, ernst en frequentie van afwijkingen, ondersteunt het verbeterproces en helpt bij het identificeren van structurele kwaliteitsproblemen. De database moet een gedetailleerde categorisering van afwijkingen mogelijk maken (bijvoorbeeld: ondergewicht, sealingproblemen, verpakkingsdefecten, smaakafwijkingen) en deze kunnen correleren met procesparameters, materiaalpartijen, machines, operators en tijdstippen om oorzaakanalyse te faciliteren. Voor elke NOK-classificatie moet worden vastgelegd of het product is herbewerkt, gedeclasseerd, of afgekeurd, inclusief de bijbehorende kosten en impact op productiecapaciteit.

Registratie van maatregelen genomen bij kwaliteitsproblemen zorgt voor continue verbetering en voorkomt herhaling van problemen. De database moet correctieve en preventieve acties (CAPA) kunnen beheren, inclusief probleembeschrijving, oorzaakanalyse, actieplan, verantwoordelijken, deadlines, implementatiestatus en effectiviteitsbeoordeling. Deze CAPA-module moet geïntegreerd zijn met andere kwaliteitssystemen zoals klachtenafhandeling, interne audits en leveranciersbeoordeling om een holistische benadering van kwaliteitsmanagement te ondersteunen.

Historische gegevens voor analyse van kwaliteitspatronen over tijd maken proactief kwaliteitsmanagement mogelijk en ondersteunen continue verbetering. De database moet geavanceerde analytische mogelijkheden bieden, zoals statistische procescontrole (SPC), trendanalyse, Pareto-analyses van defecten, en correlatiestudies tussen kwaliteitsparameters en procesomstandigheden. Deze analyses moeten worden ondersteund door visualisatietools zoals controlekaarten, trendgrafieken en dashboards die real-time inzicht geven in de kwaliteitsprestaties. Daarnaast moet de database ook benchmarking ondersteunen, waarbij kwaliteitsprestaties kunnen worden vergeleken tussen productielijnen, ploegen, producten en tijdsperioden om best practices te identificeren en te implementeren.

### Informatiebehoefte vanuit Logistiek en Voorraad

Voor effectieve materiaalstroom en voorraadbeheersing zijn real-time en nauwkeurige gegevens over beschikbare grondstoffen, verpakkingsmaterialen en eindproducten onmisbaar in een productieomgeving zoals die van Euro Caps. De database moet gedetailleerde voorraadgegevens bijhouden voor alle materialen, inclusief actuele voorraadniveaus, minimale voorraadniveaus, optimale bestelhoeveelheden, voorraadlocaties, houdbaarheidsinformatie en waardeberekeningen. Voor grondstoffen zoals koffiebonen, die onderhevig zijn aan prijsschommelingen, moet de database ook historische prijsinformatie en prijstrends kunnen vastleggen om kostenanalyses en inkoopstrategieën te ondersteunen. Daarnaast moet het systeem voorraadrotatie volgens het FIFO-principe (First In, First Out) faciliteren, wat vooral belangrijk is voor materialen met beperkte houdbaarheid.

Informatie over inkomende bestellingen van klanten en uitgaande bestellingen naar leveranciers drijft de productieplanning en moet daarom naadloos geïntegreerd zijn in de database. Voor klantenorders moet het systeem orderdetails vastleggen zoals producten, hoeveelheden, gewenste leverdatum, speciale verpakkingseisen, prijsafspraken en leveringsvoorwaarden. De database moet ook orderstatus, orderhistorie en klantspecifieke afspraken bijhouden om consistente dienstverlening te waarborgen. Voor leveranciersbestellingen moet het systeem inkooporders, leveringstermijnen, prijsafspraken, kwaliteitseisen en ontvangstbevestigingen beheren. Een geavanceerd systeem zou ook automatische besteladviezen moeten genereren op basis van voorraadniveaus, productieplanningen en levertijden van leveranciers.

Contactinformatie, leveringsvoorwaarden, kwaliteitscertificeringen, capaciteiten en prestatiemetrieken van leveranciers ondersteunen het strategische leveranciersmanagement. De database moet leveranciersevaluaties faciliteren op basis van criteria zoals leverbetrouwbaarheid, kwaliteit, prijsstelling, flexibiliteit en innovatievermogen. Deze evaluaties helpen bij het identificeren van voorkeursleveranciers en het verbeteren van de supply chain. Voor kritische materialen moet de database ook alternatieve leveranciers kunnen identificeren om continuïteitsrisico's te beheersen. Daarnaast moet het systeem contractbeheer ondersteunen, inclusief contractvoorwaarden, looptijden, prijsafspraken en prestatieafspraken.

Gegevens over klanten, hun bestellingen, betaalgedrag, groeipatronen en leveringsvoorkeuren faciliteren klantgerichte dienstverlening en strategisch accountmanagement. De database moet klantsegmentatie ondersteunen op basis van criteria zoals omzet, winstgevendheid, groeipotentieel en strategisch belang. Voor belangrijke klanten moet het systeem specifieke serviceafspraken, prijsafspraken en contactpersonen kunnen vastleggen. Klantfeedback en klachtenregistratie moeten worden geïntegreerd om kwaliteitsverbetering te ondersteunen en klanttevredenheid te monitoren. Daarnaast moet de database ook marketinginformatie bevatten zoals doelmarkten, concurrentieanalyse en markttrends om productontwikkeling en verkoopstrategieën te ondersteunen.

Informatie over verzending, transporteurs, routes, leveringstijden, transportkosten en leveringsstatussen rondt het logistieke informatiebeeld af. De database moet verschillende transportmodaliteiten kunnen beheren (weg, zee, lucht) met hun specifieke kenmerken, documentatie-eisen en kostenstructuren. Voor internationale zendingen moet het systeem douanedocumentatie, invoerrechten en compliance-eisen kunnen beheren. Tracking & tracing-informatie moet worden vastgelegd om real-time inzicht te bieden in de status van zendingen en om leverbetrouwbaarheid te kunnen meten. Daarnaast moet de database ook retourlogistiek ondersteunen, inclusief retourautorisaties, inspectie van geretourneerde goederen en verwerking van creditnota's. Een geavanceerd systeem zou ook routeoptimalisatie en laadruimte-optimalisatie moeten ondersteunen om transportkosten te minimaliseren en de ecologische voetafdruk te verkleinen.

## Kritische Beoordeling van het Aangeleverde ERD

Het aangeleverde ERD voor Euro Caps biedt een solide basis, maar vereist enkele aanpassingen om volledig aan te sluiten bij de specifieke bedrijfsprocessen en informatiebehoeften.

### Sterke Punten van het Huidige ERD

Het ERD bevat de kernentiteiten voor het vastleggen van productiegegevens, zoals Materials, ProductionBatches, ProductionSteps en QualityChecks, wat een goede basisstructuur voor productieprocessen biedt. De QualityChecks-entiteit maakt gedetailleerde registratie van kwaliteitscontroles mogelijk, inclusief verschillende types controles en resultaten, wat essentieel is voor Euro Caps' kwaliteitsgerichte aanpak. Het model omvat zowel leveranciers (Suppliers) als klanten (Customers), wat een end-to-end zicht op de supply chain mogelijk maakt en de volledige waardeketen ondersteunt. De entiteiten Employees en Machines maken het mogelijk om verantwoordelijkheden en apparatuurgebruik te traceren, wat belangrijk is voor zowel operationele efficiëntie als kwaliteitsborging.

### Noodzakelijke Aanpassingen en Uitbreidingen

Ondanks de sterke basis zijn er verschillende aanpassingen nodig om het ERD optimaal te laten aansluiten bij Euro Caps' specifieke processen. De huidige MaterialType ENUM is te beperkt en moet worden uitgebreid om alle specifieke materiaalsoorten te omvatten. Daarnaast is het toevoegen van attributen voor kwaliteitskenmerken van koffiebonen (herkomst, roostergraad, etc.) noodzakelijk voor kwaliteitsborging. Voor de kwaliteitscontrolegegevens is een relatie tussen QualityChecks en correctieve acties gewenst, evenals een gedetailleerder classificatiesysteem dan alleen OK/NOK. Attributen voor trendanalyse en statistische procescontrole zullen de kwaliteitsmonitoring versterken.

De productiestappenregistratie kan worden verbeterd door gedetailleerdere procesparameters voor elke productiestap toe te voegen en een hiërarchische structuur voor productiestappen (hoofdstappen en substappen) te implementeren. Attributen voor efficiëntiemeting en optimalisatie zullen bijdragen aan procesverbetering. De orderverwerking vraagt om uitbreiding met orderprioriteiten en leveringstermijnen, een koppeling tussen orders en productieplanning, en attributen voor klanttevredenheid en leveringsprestaties.

Voor effectief voorraadbeheer is het implementeren van locatiegegevens voor opslag, voorraadtransacties voor nauwkeurigere tracking, en minimale voorraadniveaus met automatische bestelwaarschuwingen essentieel. De traceerbaarheid kan worden verbeterd door een end-to-end traceerbaarheidssysteem van grondstof tot eindproduct te implementeren, batchcodering die consistent is door alle productiefasen toe te voegen, en een systeem voor het traceren van NOK-producten en correctieve acties te ontwikkelen. Tot slot is het toevoegen van rapportage- en analysefunctionaliteit gewenst, met KPI-definities en -berekeningen, geaggregeerde views voor managementrapportage, en historische gegevensopslag voor trendanalyse.

### Voorgestelde ERD-Structuur

Op basis van bovenstaande analyse wordt een aangepaste ERD-structuur voorgesteld die beter aansluit bij de specifieke behoeften van Euro Caps. Voor de supply chain worden de Suppliers-entiteiten uitgebreid met prestatiemetrieken, Materials verfijnd met gedetailleerde categorieën en kwaliteitskenmerken, en een nieuwe Inventory-entiteit toegevoegd voor nauwkeurige voorraadregistratie. Customers worden uitgebreid met voorkeuren en historiek, terwijl Orders worden verfijnd met prioriteiten en planning.

Voor het productieproces worden nieuwe ProductionPlans-entiteiten geïntroduceerd voor productieplanning, ProductionBatches verfijnd met traceerbaarheidskenmerken, ProductionSteps uitgebreid met gedetailleerde procesparameters, en Products verfijnd met productspecificaties. Het kwaliteitscontroledomein wordt versterkt met nieuwe QualityParameters-entiteiten voor definitie van kwaliteitscriteria, uitgebreide QualityChecks met gedetailleerde testresultaten, nieuwe QualityIssues-entiteiten voor registratie van afwijkingen, en CorrectiveActions voor opvolging van kwaliteitsproblemen.

De operationele kant wordt ondersteund door uitgebreide Employees-entiteiten met vaardigheden en certificeringen, Machines met onderhoudsprogramma's, nieuwe Maintenance-entiteiten voor registratie van machineonderhoud, en Shifts voor planning van personeel. Deze aangepaste ERD-structuur zal Euro Caps in staat stellen om alle aspecten van hun productieproces nauwkeurig te registreren, te analyseren en te optimaliseren, met bijzondere aandacht voor kwaliteitscontrole, traceerbaarheid en efficiëntie.

## Conclusie

De informatiebehoeften van Euro Caps vereisen een uitgebreide en gedetailleerde databasestructuur die alle aspecten van het productieproces ondersteunt. Het aangeleverde ERD biedt een goede basis, maar moet worden aangepast en uitgebreid om volledig aan deze behoeften te voldoen. De voorgestelde aanpassingen zullen resulteren in een database die niet alleen operationele gegevens vastlegt, maar ook waardevolle inzichten biedt voor kwaliteitsverbetering, efficiëntieverhoging en strategische besluitvorming.
