from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

def add_heading(doc, text, level):
    doc.add_heading(text, level=level)

def add_paragraph(doc, text, italic=False, bold=False, align=None):
    p = doc.add_paragraph()
    run = p.add_run(text)
    run.italic = italic
    run.bold = bold
    if align:
        p.alignment = align

def insert_placeholder(doc, text):
    doc.add_paragraph(f"[VOEG HIER IN: {text.upper()}]")

def create_structure():
    doc = Document()

    # VOORWERK
    add_heading(doc, "Titelpagina", 1)
    insert_placeholder(doc, "Titel, naam, studentnummer, opleiding, datum, begeleiders")

    add_heading(doc, "Managementsamenvatting", 1)
    insert_placeholder(doc, "Samenvatting van pro<PERSON>em, aanpak, bevindingen, aanbevelingen")

    add_heading(doc, "Voorwoord", 1)
    insert_placeholder(doc, "Persoonlijk woord / dankwoord")

    add_heading(doc, "Inhoudsopgave", 1)
    insert_placeholder(doc, "Automatisch gegenereerd bij export")

    # HOOFDRAPPORT
    add_heading(doc, "1. Inleiding", 1)
    add_paragraph(doc, "1.1 Aanleiding")
    add_paragraph(doc, "1.2 Probleemstelling / Doelstelling")
    add_paragraph(doc, "1.3 Hoofdvraag en deelvragen")
    add_paragraph(doc, "1.4 Afbakening / Scope")
    add_paragraph(doc, "1.5 Leeswijzer")

    add_heading(doc, "2. Analyse Huidige Situatie (As-Is)", 1)
    add_paragraph(doc, "2.1 Bedrijfsprofiel Euro Caps")
    add_paragraph(doc, "2.2 Huidige Bedrijfsprocessen")
    insert_placeholder(doc, "Visuele weergave primair proces")
    insert_placeholder(doc, "Swimlane productieproces")
    insert_placeholder(doc, "Swimlane logistiek proces")
    add_paragraph(doc, "2.3 Huidig IT-Landschap")
    add_paragraph(doc, "2.4 Geïdentificeerde Knelpunten")

    add_heading(doc, "3. Kwaliteitsmanagement Analyse", 1)
    add_paragraph(doc, "3.1 Verkenning Kwaliteitsmanagementmethodes")
    insert_placeholder(doc, "Beslissingsmatrix met methodes")
    add_paragraph(doc, "3.2 Keuze Methode voor Euro Caps")
    add_paragraph(doc, "3.3 Toepassing Gekozen Methode")
    insert_placeholder(doc, "Visuele QM-methode toepassing")

    add_heading(doc, "4. IT Oplossing: Database Ontwerp en Realisatie", 1)
    add_paragraph(doc, "4.1 Database Eisen")
    add_paragraph(doc, "4.2 Database Ontwerp")
    insert_placeholder(doc, "Conceptueel ERD")
    insert_placeholder(doc, "Logisch ERD")
    insert_placeholder(doc, "Fysiek ERD + toelichting")
    add_paragraph(doc, "4.3 Database Implementatie")
    insert_placeholder(doc, "MySQL structuur, constraints")
    insert_placeholder(doc, "Python script + CSV structuur")
    add_paragraph(doc, "4.4 Data Analyse en Inzichten")
    insert_placeholder(doc, "SQL Queries + Resultaten")

    add_heading(doc, "5. Optimalisaties en Aanbevelingen (To-Be)", 1)
    add_paragraph(doc, "5.1 Procesoptimalisaties")
    add_paragraph(doc, "5.2 IT-Aanbevelingen")
    add_paragraph(doc, "5.3 Verbetering Ketenintegratie")
    add_paragraph(doc, "5.4 Implementatie Overwegingen (optioneel)")

    add_heading(doc, "6. Conclusie", 1)
    add_paragraph(doc, "Samenvatting bevindingen per deelvraag")
    add_paragraph(doc, "Beantwoording hoofdvraag")
    add_paragraph(doc, "Herhaling aanbevelingen")

    add_heading(doc, "7. Reflectie", 1)
    insert_placeholder(doc, "Reflectie op ontwerp, uitvoering, uitdagingen, lessen")

    # AFSLUITING
    add_heading(doc, "Literatuurlijst", 1)
    insert_placeholder(doc, "Bronnenlijst volgens APA 7e")

    add_heading(doc, "Bijlagen", 1)
    insert_placeholder(doc, "Swimlanes, ERD's, QM-schema's")
    insert_placeholder(doc, "MySQL export (DDL + data)")
    insert_placeholder(doc, "Python script + CSV bestanden")
    insert_placeholder(doc, "Volledige SQL-queries + resultaten")
    insert_placeholder(doc, "Overige documenten")

    # Opslaan
    doc.save("From_Beans_to_Bytes_Euro_Caps_Structuur.docx")
    print("✅ Document gegenereerd: From_Beans_to_Bytes_Euro_Caps_Structuur.docx")

if __name__ == "__main__":
    create_structure()
