<!DOCTYPE html>
<html>
<head>
    <title>Logisch ERD</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .diagram {
            width: 800px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: white;
            padding: 20px;
        }
        .entity {
            border: 2px solid #333;
            background-color: #f8f8f8;
            border-radius: 5px;
            text-align: center;
            margin: 10px;
            display: inline-block;
            vertical-align: top;
        }
        .entity-header {
            background-color: #333;
            color: white;
            padding: 8px;
            font-weight: bold;
        }
        .entity-body {
            padding: 10px;
        }
        .attribute {
            text-align: left;
            margin: 5px;
            padding: 3px;
        }
        .pk {
            font-weight: bold;
            text-decoration: underline;
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .erd-type {
            margin: 30px 0;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .erd-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }
        .arrow {
            display: inline-block;
            margin: 0 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="title">Logisch ERD</div>
    <div class="subtitle">Middenniveau van abstractie, voegt attributen en sleutels toe</div>
    
    <div class="diagram">
        <div class="erd-type">
            <div class="erd-title">Logisch ERD</div>
            <p>Middenniveau van abstractie, voegt attributen en sleutels toe.</p>
            
            <div style="text-align: center;">
                <div class="entity" style="width: 180px;">
                    <div class="entity-header">Klant</div>
                    <div class="entity-body">
                        <div class="attribute pk">KlantID</div>
                        <div class="attribute">Naam</div>
                        <div class="attribute">Adres</div>
                        <div class="attribute">Telefoon</div>
                        <div class="attribute">Email</div>
                    </div>
                </div>
                
                <div class="arrow">1 —— N</div>
                
                <div class="entity" style="width: 180px;">
                    <div class="entity-header">Order</div>
                    <div class="entity-body">
                        <div class="attribute pk">OrderID</div>
                        <div class="attribute">Datum</div>
                        <div class="attribute">Totaal</div>
                        <div class="attribute">Status</div>
                    </div>
                </div>
                
                <div class="arrow">1 —— N</div>
                
                <div class="entity" style="width: 180px;">
                    <div class="entity-header">OrderDetail</div>
                    <div class="entity-body">
                        <div class="attribute pk">OrderID</div>
                        <div class="attribute pk">ProductID</div>
                        <div class="attribute">Aantal</div>
                        <div class="attribute">Prijs</div>
                    </div>
                </div>
                
                <div class="arrow">N —— 1</div>
                
                <div class="entity" style="width: 180px;">
                    <div class="entity-header">Product</div>
                    <div class="entity-body">
                        <div class="attribute pk">ProductID</div>
                        <div class="attribute">Naam</div>
                        <div class="attribute">Beschrijving</div>
                        <div class="attribute">Prijs</div>
                        <div class="attribute">Voorraad</div>
                    </div>
                </div>
            </div>
            
            <p>Kenmerken:</p>
            <ul>
                <li>Toont alle attributen van entiteiten</li>
                <li>Identificeert primaire sleutels</li>
                <li>Toont kardinaliteit van relaties duidelijk</li>
                <li>Veel-op-veel relaties worden opgesplitst in associatieve entiteiten (OrderDetail)</li>
                <li>Nog steeds onafhankelijk van specifieke databasetechnologie</li>
                <li>Vreemde sleutels worden niet expliciet getoond, maar zijn impliciet aanwezig in de relaties</li>
            </ul>
            
            <p>Doel:</p>
            <ul>
                <li>Het specificeren van alle attributen van entiteiten</li>
                <li>Het identificeren van primaire sleutels</li>
                <li>Het omzetten van veel-op-veel relaties naar één-op-veel relaties via associatieve entiteiten</li>
                <li>Basis voor het fysieke ERD</li>
            </ul>
        </div>
    </div>
</body>
</html>
