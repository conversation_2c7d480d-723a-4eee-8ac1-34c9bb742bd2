# ************************************************************************************************
# Enhanced EuroCaps Ordering System with Integrated Raw Materials, Suppliers & Stakeholders
# Includes complete supply chain management functionality
# Color Scheme: Header: 2C3E50, Background: 1B3A4B, Cards: A9C6E8, Buttons: F39C12, Text: FFFFFF
# ************************************************************************************************

App:
  Properties:
    Theme: =PowerAppsTheme
    OnStart: |
      =// Initialize all global variables
      Set(varUserRole, "");
      Set(varUserName, "");
      Set(varRememberMe, false);
      Set(varCurrentOrder, {Items: []});
      Set(varSelectedCustomer, Blank());
      Set(varSelectedSupplier, Blank());
      Set(varSelectedMaterial, Blank());
      
      // Initialize enhanced data collections with supply chain integration
      
      // Raw Materials Database (from Grondstoffen_Eurocaps.xlsx)
      ClearCollect(colRawMaterials,
          {MaterialID: 1, MaterialName: "Arabica Coffee Beans - Premium", MaterialType: "Coffee", SupplierID: 1, StockLevel: 500, UnitCost: 12.50, ReorderPoint: 100, Unit: "kg", QualityGrade: "A", LastUpdated: Today()},
          {MaterialID: 2, MaterialName: "Robusta Coffee Beans - Strong", MaterialType: "Coffee", SupplierID: 1, StockLevel: 300, UnitCost: 10.75, ReorderPoint: 75, Unit: "kg", QualityGrade: "A", LastUpdated: Today()},
          {MaterialID: 3, MaterialName: "Aluminum Capsules - Standard", MaterialType: "Packaging", SupplierID: 2, StockLevel: 10000, UnitCost: 0.15, ReorderPoint: 2000, Unit: "pieces", QualityGrade: "A", LastUpdated: Today()},
          {MaterialID: 4, MaterialName: "Aluminum Capsules - Premium", MaterialType: "Packaging", SupplierID: 2, StockLevel: 5000, UnitCost: 0.18, ReorderPoint: 1000, Unit: "pieces", QualityGrade: "A+", LastUpdated: Today()},
          {MaterialID: 5, MaterialName: "Cardboard Boxes - 10 pack", MaterialType: "Packaging", SupplierID: 3, StockLevel: 1500, UnitCost: 0.75, ReorderPoint: 300, Unit: "pieces", QualityGrade: "B", LastUpdated: Today()},
          {MaterialID: 6, MaterialName: "Cardboard Boxes - 20 pack", MaterialType: "Packaging", SupplierID: 3, StockLevel: 800, UnitCost: 1.25, ReorderPoint: 150, Unit: "pieces", QualityGrade: "B", LastUpdated: Today()},
          {MaterialID: 7, MaterialName: "Vanilla Flavoring", MaterialType: "Flavoring", SupplierID: 4, StockLevel: 50, UnitCost: 25.00, ReorderPoint: 10, Unit: "liters", QualityGrade: "A+", LastUpdated: Today()},
          {MaterialID: 8, MaterialName: "Caramel Flavoring", MaterialType: "Flavoring", SupplierID: 4, StockLevel: 30, UnitCost: 28.00, ReorderPoint: 8, Unit: "liters", QualityGrade: "A", LastUpdated: Today()}
      );
      
      // Suppliers Database (from Leveranciers_Eurocaps_Grondstoffen.xlsx)
      ClearCollect(colSuppliers,
          {SupplierID: 1, SupplierName: "Premium Coffee Co.", ContactPerson: "Maria Santos", Email: "<EMAIL>", Phone: "+31 20 555 0101", Address: "Koffiestraat 15, 1012 Amsterdam", Country: "Netherlands", PaymentTerms: "30 days", DeliveryTerms: "FOB", Rating: 4.8, Status: "Active", LastOrder: Today()-5},
          {SupplierID: 2, SupplierName: "Aluminum Solutions BV", ContactPerson: "Jan de Vries", Email: "<EMAIL>", Phone: "+31 30 555 0202", Address: "Industrieweg 42, 3542 Utrecht", Country: "Netherlands", PaymentTerms: "45 days", DeliveryTerms: "DDP", Rating: 4.6, Status: "Active", LastOrder: Today()-3},
          {SupplierID: 3, SupplierName: "PackTech Europe", ContactPerson: "Sophie Mueller", Email: "<EMAIL>", Phone: "+49 30 555 0303", Address: "Verpackungsallee 8, 10115 Berlin", Country: "Germany", PaymentTerms: "30 days", DeliveryTerms: "FOB", Rating: 4.2, Status: "Active", LastOrder: Today()-7},
          {SupplierID: 4, SupplierName: "FlavorMasters International", ContactPerson: "Pierre Dubois", Email: "<EMAIL>", Phone: "+33 1 555 0404", Address: "Rue des Arômes 25, 75001 Paris", Country: "France", PaymentTerms: "60 days", DeliveryTerms: "EXW", Rating: 4.9, Status: "Active", LastOrder: Today()-2}
      );
      
      // Stakeholders Database (from Eurocaps_Stakeholders.xlsx)
      ClearCollect(colStakeholders,
          {StakeholderID: 1, Name: "Coffee World", Type: "Customer", ContactPerson: "David Lee", Email: "<EMAIL>", Phone: "+31 20 123 4567", Department: "Procurement", AccessLevel: "Standard", Status: "Active", LastContact: Today()-1},
          {StakeholderID: 2, Name: "Bean Lovers", Type: "Customer", ContactPerson: "John Smith", Email: "<EMAIL>", Phone: "+31 30 234 5678", Department: "Purchasing", AccessLevel: "Standard", Status: "Active", LastContact: Today()-3},
          {StakeholderID: 3, Name: "Premium Coffee Co.", Type: "Supplier", ContactPerson: "Maria Santos", Email: "<EMAIL>", Phone: "+31 20 555 0101", Department: "Sales", AccessLevel: "Supplier", Status: "Active", LastContact: Today()-5},
          {StakeholderID: 4, Name: "EuroCaps Management", Type: "Internal", ContactPerson: "Director", Email: "<EMAIL>", Phone: "+31 20 999 0001", Department: "Management", AccessLevel: "Admin", Status: "Active", LastContact: Today()},
          {StakeholderID: 5, Name: "EuroCaps Production", Type: "Internal", ContactPerson: "Production Manager", Email: "<EMAIL>", Phone: "+31 20 999 0002", Department: "Production", AccessLevel: "Manager", Status: "Active", LastContact: Today()},
          {StakeholderID: 6, Name: "EuroCaps Quality Control", Type: "Internal", ContactPerson: "QC Manager", Email: "<EMAIL>", Phone: "+31 20 999 0003", Department: "Quality", AccessLevel: "Manager", Status: "Active", LastContact: Today()}
      );
      
      // Enhanced Products with Raw Material Requirements
      ClearCollect(colProducts,
          {ProductID: 1, ProductName: "Espresso Classic", ProductType: "espresso", PackageSize: 10, Description: "Traditional Italian-style espresso", Price: 4.99, Status: "Active"},
          {ProductID: 2, ProductName: "Lungo Intense", ProductType: "lungo", PackageSize: 20, Description: "Rich and intense lungo", Price: 8.99, Status: "Active"},
          {ProductID: 3, ProductName: "Ristretto Strong", ProductType: "ristretto", PackageSize: 10, Description: "Extra strong ristretto", Price: 5.49, Status: "Active"},
          {ProductID: 4, ProductName: "Vanilla Flavored", ProductType: "flavored", PackageSize: 20, Description: "Smooth vanilla flavored coffee", Price: 9.99, Status: "Active"},
          {ProductID: 5, ProductName: "Caramel Delight", ProductType: "flavored", PackageSize: 20, Description: "Rich caramel flavored coffee", Price: 10.49, Status: "Active"}
      );
      
      // Product-Material Requirements (Bill of Materials)
      ClearCollect(colProductMaterials,
          // Espresso Classic (10 capsules)
          {ProductID: 1, MaterialID: 1, QuantityRequired: 75, Unit: "g"},   // 7.5g coffee per capsule
          {ProductID: 1, MaterialID: 3, QuantityRequired: 10, Unit: "pieces"}, // 10 standard capsules
          {ProductID: 1, MaterialID: 5, QuantityRequired: 1, Unit: "pieces"},  // 1 box for 10 pack
          
          // Lungo Intense (20 capsules)
          {ProductID: 2, MaterialID: 1, QuantityRequired: 180, Unit: "g"},  // 9g coffee per capsule
          {ProductID: 2, MaterialID: 4, QuantityRequired: 20, Unit: "pieces"}, // 20 premium capsules
          {ProductID: 2, MaterialID: 6, QuantityRequired: 1, Unit: "pieces"},  // 1 box for 20 pack
          
          // Ristretto Strong (10 capsules)
          {ProductID: 3, MaterialID: 2, QuantityRequired: 60, Unit: "g"},   // 6g robusta per capsule
          {ProductID: 3, MaterialID: 3, QuantityRequired: 10, Unit: "pieces"}, // 10 standard capsules
          {ProductID: 3, MaterialID: 5, QuantityRequired: 1, Unit: "pieces"},  // 1 box for 10 pack
          
          // Vanilla Flavored (20 capsules)
          {ProductID: 4, MaterialID: 1, QuantityRequired: 140, Unit: "g"},  // 7g coffee per capsule
          {ProductID: 4, MaterialID: 7, QuantityRequired: 20, Unit: "ml"},  // 1ml vanilla per capsule
          {ProductID: 4, MaterialID: 4, QuantityRequired: 20, Unit: "pieces"}, // 20 premium capsules
          {ProductID: 4, MaterialID: 6, QuantityRequired: 1, Unit: "pieces"},  // 1 box for 20 pack
          
          // Caramel Delight (20 capsules)
          {ProductID: 5, MaterialID: 1, QuantityRequired: 140, Unit: "g"},  // 7g coffee per capsule
          {ProductID: 5, MaterialID: 8, QuantityRequired: 20, Unit: "ml"},  // 1ml caramel per capsule
          {ProductID: 5, MaterialID: 4, QuantityRequired: 20, Unit: "pieces"}, // 20 premium capsules
          {ProductID: 5, MaterialID: 6, QuantityRequired: 1, Unit: "pieces"}   // 1 box for 20 pack
      );
      
      // Enhanced Customers (now linked to Stakeholders)
      ClearCollect(colCustomers,
          {CustomerID: 1, CustomerName: "Coffee World", ContactPerson: "David Lee", Email: "<EMAIL>", Phone: "+31 20 123 4567", Address: "Koffieweg 10, Amsterdam", Status: "Active", StakeholderID: 1, CreditLimit: 10000, PaymentTerms: "30 days"},
          {CustomerID: 2, CustomerName: "Bean Lovers", ContactPerson: "John Smith", Email: "<EMAIL>", Phone: "+31 30 234 5678", Address: "Bonenstraat 25, Utrecht", Status: "Active", StakeholderID: 2, CreditLimit: 15000, PaymentTerms: "45 days"},
          {CustomerID: 3, CustomerName: "Café Express", ContactPerson: "Maria Garcia", Email: "<EMAIL>", Phone: "+31 10 345 6789", Address: "Espressolaan 5, Rotterdam", Status: "Active", StakeholderID: 0, CreditLimit: 8000, PaymentTerms: "30 days"}
      );
      
      // Enhanced Orders with Material Impact Tracking
      ClearCollect(colOrders,
          {OrderID: 1, OrderNumber: "ORD-1089", CustomerID: 1, OrderDate: Today()-1, DeliveryDate: Today()+3, Status: "new", Notes: "Rush order", TotalAmount: 49.90, MaterialsReserved: false},
          {OrderID: 2, OrderNumber: "ORD-1088", CustomerID: 2, OrderDate: Today()-2, DeliveryDate: Today()+4, Status: "processing", Notes: "Standard delivery", TotalAmount: 89.90, MaterialsReserved: true},
          {OrderID: 3, OrderNumber: "ORD-1087", CustomerID: 3, OrderDate: Today()-3, DeliveryDate: Today()+2, Status: "shipped", Notes: "Express delivery", TotalAmount: 54.90, MaterialsReserved: true}
      );
      
      // Material Orders for Supplier Management
      ClearCollect(colMaterialOrders,
          {MaterialOrderID: 1, SupplierID: 1, OrderDate: Today()-10, DeliveryDate: Today()-3, Status: "delivered", TotalAmount: 6250.00, MaterialID: 1, Quantity: 500, UnitCost: 12.50},
          {MaterialOrderID: 2, SupplierID: 2, OrderDate: Today()-8, DeliveryDate: Today()-1, Status: "delivered", TotalAmount: 1500.00, MaterialID: 3, Quantity: 10000, UnitCost: 0.15},
          {MaterialOrderID: 3, SupplierID: 3, OrderDate: Today()-5, DeliveryDate: Today()+2, Status: "pending", TotalAmount: 1125.00, MaterialID: 5, Quantity: 1500, UnitCost: 0.75}
      );
      
      // Production Planning Data
      ClearCollect(colProductionPlans,
          {PlanID: 1, ProductID: 1, PlannedQuantity: 100, PlannedDate: Today()+1, Status: "scheduled", MaterialsAvailable: true},
          {PlanID: 2, ProductID: 2, PlannedQuantity: 50, PlannedDate: Today()+2, Status: "scheduled", MaterialsAvailable: true},
          {PlanID: 3, ProductID: 4, PlannedQuantity: 75, PlannedDate: Today()+3, Status: "pending", MaterialsAvailable: false}
      );
      
      // Quality Control Data
      ClearCollect(colQualityChecks,
          {QualityID: 1, MaterialID: 1, CheckDate: Today()-1, Result: "Pass", Inspector: "QC Manager", Notes: "Excellent quality beans"},
          {QualityID: 2, MaterialID: 3, CheckDate: Today()-2, Result: "Pass", Inspector: "QC Manager", Notes: "Capsules meet specifications"},
          {QualityID: 3, MaterialID: 7, CheckDate: Today()-3, Result: "Pass", Inspector: "QC Manager", Notes: "Flavor profile approved"}
      );
      
      // Initialize search and filter variables
      Set(varSearchText, "");
      Set(varFilterStatus, "All");
      Set(varSortBy, "Name");
      Set(varProductSearch, "");
      Set(varProductType, "All");
      Set(varPackageSize, "All");
      Set(varMaterialSearch, "");
      Set(varMaterialType, "All");
      Set(varSupplierSearch, "");
      Set(varStakeholderSearch, "");
      Set(varStakeholderType, "All");
      
      // Set default screen
      Set(varStartScreen, "Login_Screen")

# Data Connections (for production use with actual Excel files)
DataSources:
  # Excel Online connections for the new database files
  - Name: "Grondstoffen_Eurocaps"
    Type: "Excel"
    ConnectionString: "Excel Online (Business)"
    Tables:
      - "RawMaterials"
      
  - Name: "Leveranciers_Eurocaps_Grondstoffen" 
    Type: "Excel"
    ConnectionString: "Excel Online (Business)"
    Tables:
      - "Suppliers"
      
  - Name: "Eurocaps_Stakeholders"
    Type: "Excel"
    ConnectionString: "Excel Online (Business)"
    Tables:
      - "Stakeholders"

# Global Functions for Supply Chain Management
Functions:
  # Calculate material requirements for an order
  CalculateMaterialNeeds:
    Parameters: [OrderItems]
    Formula: |
      =AddColumns(
          AddColumns(
              OrderItems,
              "MaterialRequirements",
              Filter(colProductMaterials, ProductID = ThisRecord.ProductID)
          ),
          "TotalMaterialNeeded",
          Sum(MaterialRequirements, QuantityRequired * ThisRecord.Quantity)
      )
  
  # Check material availability
  CheckMaterialAvailability:
    Parameters: [MaterialID, RequiredQuantity]
    Formula: |
      =LookUp(colRawMaterials, MaterialID = MaterialID).StockLevel >= RequiredQuantity
  
  # Generate reorder alerts
  GenerateReorderAlerts:
    Parameters: []
    Formula: |
      =Filter(colRawMaterials, StockLevel <= ReorderPoint)
  
  # Calculate order profitability
  CalculateOrderProfitability:
    Parameters: [OrderID]
    Formula: |
      =// Calculate material costs vs selling price
      With(
          {OrderItems: Filter(colOrderItems, OrderID = OrderID)},
          Sum(OrderItems, 
              LookUp(colProducts, ProductID = ThisRecord.ProductID).Price * ThisRecord.Quantity
          ) - 
          Sum(OrderItems,
              Sum(Filter(colProductMaterials, ProductID = ThisRecord.ProductID),
                  LookUp(colRawMaterials, MaterialID = ThisRecord.MaterialID).UnitCost * 
                  ThisRecord.QuantityRequired * ThisRecord.Quantity
              )
          )
      )
