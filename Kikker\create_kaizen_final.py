from openpyxl import Workbook
from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Reference
from openpyxl.styles import <PERSON>ont, Alignment, Border, Side, PatternFill
from openpyxl.chart.label import DataLabelList
import os

def create_kaizen_excel():
    # Verwijder het bestaande bestand als het bestaat
    output_file = 'Kaizen_Definitief.xlsx'
    if os.path.exists(output_file):
        try:
            os.remove(output_file)
            print(f"Bestaand bestand {output_file} verwijderd")
        except:
            print(f"Kon bestaand bestand {output_file} niet verwijderen")
    
    # Maak een Excel workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Kaizen Analyse"
    
    # Definieer stijlen
    header_font = Font(bold=True, size=12)
    normal_font = Font(size=11)
    
    # Definieer borders
    thin_border = Border(left=Side(style='thin'), 
                         right=Side(style='thin'), 
                         top=Side(style='thin'), 
                         bottom=Side(style='thin'))
    
    # Definieer vulkleuren
    header_fill = PatternFill(start_color="8EAADB", end_color="8EAADB", fill_type="solid")
    subheader_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
    data_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    
    # Maak de tabelheaders
    ws.cell(row=1, column=1, value="Column1").font = header_font
    ws.cell(row=1, column=1).fill = header_fill
    ws.cell(row=1, column=1).alignment = Alignment(horizontal='left')
    ws.cell(row=1, column=1).border = thin_border
    
    ws.cell(row=1, column=2, value="Column2").font = header_font
    ws.cell(row=1, column=2).fill = header_fill
    ws.cell(row=1, column=2).alignment = Alignment(horizontal='left')
    ws.cell(row=1, column=2).border = thin_border
    
    # Titel
    ws.cell(row=2, column=1, value="KAIZEN ANALYSE").font = header_font
    ws.cell(row=2, column=1).fill = subheader_fill
    ws.cell(row=2, column=1).border = thin_border
    ws.cell(row=2, column=2).fill = subheader_fill
    ws.cell(row=2, column=2).border = thin_border
    
    # Subtitel
    ws.cell(row=3, column=1, value="Klanttevredenheid en Klantretourpercentage").font = Font(italic=True)
    ws.cell(row=3, column=1).fill = subheader_fill
    ws.cell(row=3, column=1).border = thin_border
    ws.cell(row=3, column=2).fill = subheader_fill
    ws.cell(row=3, column=2).border = thin_border
    
    # Lege rij
    ws.cell(row=4, column=1).fill = subheader_fill
    ws.cell(row=4, column=1).border = thin_border
    ws.cell(row=4, column=2).fill = subheader_fill
    ws.cell(row=4, column=2).border = thin_border
    
    # Klanttevredenheid Statistieken
    ws.cell(row=5, column=1, value="Klanttevredenheid Statistieken:").font = header_font
    ws.cell(row=5, column=1).fill = subheader_fill
    ws.cell(row=5, column=1).border = thin_border
    ws.cell(row=5, column=2).fill = subheader_fill
    ws.cell(row=5, column=2).border = thin_border
    
    # Klanttevredenheid data
    satisfaction_data = [
        ("- Gemiddelde", 3.49),
        ("- Mediaan", 4.00)
    ]
    
    for i, (label, value) in enumerate(satisfaction_data):
        row = 6 + i
        ws.cell(row=row, column=1, value=label).font = normal_font
        ws.cell(row=row, column=1).border = thin_border
        # Voeg de eenheid toe aan de waarde in de tabel
        ws.cell(row=row, column=2, value=f"{value}/5").font = normal_font
        ws.cell(row=row, column=2).fill = data_fill
        ws.cell(row=row, column=2).border = thin_border
    
    # Klantretourpercentage Statistieken
    ws.cell(row=8, column=1, value="Klantretourpercentage Statistieken:").font = header_font
    ws.cell(row=8, column=1).fill = subheader_fill
    ws.cell(row=8, column=1).border = thin_border
    ws.cell(row=8, column=2).fill = subheader_fill
    ws.cell(row=8, column=2).border = thin_border
    
    # Klantretourpercentage data
    return_data = [
        ("- Gemiddelde", 1.00),
        ("- Mediaan", 1.00)
    ]
    
    for i, (label, value) in enumerate(return_data):
        row = 9 + i
        ws.cell(row=row, column=1, value=label).font = normal_font
        ws.cell(row=row, column=1).border = thin_border
        # Voeg de eenheid toe aan de waarde in de tabel
        ws.cell(row=row, column=2, value=f"{value}%").font = normal_font
        ws.cell(row=row, column=2).fill = data_fill
        ws.cell(row=row, column=2).border = thin_border
    
    # Panel Test Resultaten
    ws.cell(row=11, column=1, value="Panel Test Resultaten:").font = header_font
    ws.cell(row=11, column=1).fill = subheader_fill
    ws.cell(row=11, column=1).border = thin_border
    ws.cell(row=11, column=2).fill = subheader_fill
    ws.cell(row=11, column=2).border = thin_border
    
    # Panel Test data
    panel_data = [
        ("- Voldoet", 71.00),
        ("- Voldoet gedeeltelijk", 14.56),
        ("- Voldoet niet", 14.44)
    ]
    
    for i, (label, value) in enumerate(panel_data):
        row = 12 + i
        ws.cell(row=row, column=1, value=label).font = normal_font
        ws.cell(row=row, column=1).border = thin_border
        # Voeg de eenheid toe aan de waarde in de tabel
        ws.cell(row=row, column=2, value=f"{value}%").font = normal_font
        ws.cell(row=row, column=2).fill = data_fill
        ws.cell(row=row, column=2).border = thin_border
    
    # Klanttevredenheid per Koffieboon Type
    ws.cell(row=15, column=1, value="Klanttevredenheid per Koffieboon Type:").font = header_font
    ws.cell(row=15, column=1).fill = subheader_fill
    ws.cell(row=15, column=1).border = thin_border
    ws.cell(row=15, column=2).fill = subheader_fill
    ws.cell(row=15, column=2).border = thin_border
    
    # Klanttevredenheid per Koffieboon Type data
    bean_satisfaction_data = [
        ("- Excelsa", 3.43),
        ("- Arabica", 3.47),
        ("- Robusta", 3.51),
        ("- Liberica", 3.57)
    ]
    
    for i, (label, value) in enumerate(bean_satisfaction_data):
        row = 16 + i
        ws.cell(row=row, column=1, value=label).font = normal_font
        ws.cell(row=row, column=1).border = thin_border
        # Voeg de eenheid toe aan de waarde in de tabel
        ws.cell(row=row, column=2, value=f"{value}/5").font = normal_font
        ws.cell(row=row, column=2).fill = data_fill
        ws.cell(row=row, column=2).border = thin_border
    
    # Klantretourpercentage per Koffieboon Type
    ws.cell(row=20, column=1, value="Klantretourpercentage per Koffieboon Type:").font = header_font
    ws.cell(row=20, column=1).fill = subheader_fill
    ws.cell(row=20, column=1).border = thin_border
    ws.cell(row=20, column=2).fill = subheader_fill
    ws.cell(row=20, column=2).border = thin_border
    
    # Klantretourpercentage per Koffieboon Type data
    bean_return_data = [
        ("- Robusta", 1.01),
        ("- Excelsa", 1.01),
        ("- Arabica", 0.99),
        ("- Liberica", 0.98)
    ]
    
    for i, (label, value) in enumerate(bean_return_data):
        row = 21 + i
        ws.cell(row=row, column=1, value=label).font = normal_font
        ws.cell(row=row, column=1).border = thin_border
        # Voeg de eenheid toe aan de waarde in de tabel
        ws.cell(row=row, column=2, value=f"{value}%").font = normal_font
        ws.cell(row=row, column=2).fill = data_fill
        ws.cell(row=row, column=2).border = thin_border
    
    # Pas kolombreedtes aan
    ws.column_dimensions['A'].width = 40
    ws.column_dimensions['B'].width = 15
    ws.column_dimensions['C'].width = 15
    
    # Maak een kolom voor de numerieke waarden voor de grafieken
    for i, (label, value) in enumerate(satisfaction_data):
        ws.cell(row=6+i, column=3, value=value)
    
    for i, (label, value) in enumerate(return_data):
        ws.cell(row=9+i, column=3, value=value)
    
    for i, (label, value) in enumerate(panel_data):
        ws.cell(row=12+i, column=3, value=value)
    
    for i, (label, value) in enumerate(bean_satisfaction_data):
        ws.cell(row=16+i, column=3, value=value)
    
    for i, (label, value) in enumerate(bean_return_data):
        ws.cell(row=21+i, column=3, value=value)
    
    # 1. Maak een cirkeldiagram voor Panel Test Resultaten
    pie_chart = PieChart()
    pie_chart.title = "Panel Test Resultaten"
    pie_chart.height = 10  # Hoogte van het diagram
    pie_chart.width = 15   # Breedte van het diagram
    
    # Definieer de data voor het cirkeldiagram vanuit de numerieke waarden
    pie_data = Reference(ws, min_col=3, min_row=12, max_row=14, max_col=3)
    
    # Gebruik de labels uit kolom A (rij 12-14)
    pie_cats = Reference(ws, min_col=1, min_row=12, max_row=14)
    
    # Voeg de data toe aan het cirkeldiagram
    pie_chart.add_data(pie_data)
    pie_chart.set_categories(pie_cats)
    
    # Voeg data labels toe om de exacte waarden te tonen
    pie_chart.dataLabels = DataLabelList()
    pie_chart.dataLabels.showVal = False  # Verberg de waarden
    pie_chart.dataLabels.showCatName = True  # Toon categorienamen
    pie_chart.dataLabels.showPercent = True  # Toon percentages
    pie_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels
    
    # Pas de stijl aan
    pie_chart.style = 10  # Kies een stijl
    
    # Voeg het cirkeldiagram toe aan het Kaizen Analyse werkblad naast de tabel
    ws.add_chart(pie_chart, "D11")
    
    # 2. Maak een staafdiagram voor Klanttevredenheid per Koffieboon Type
    satisfaction_chart = BarChart()
    satisfaction_chart.title = "Klanttevredenheid per Koffieboon Type"
    satisfaction_chart.y_axis.title = "Klanttevredenheid (op schaal van 5)"
    satisfaction_chart.x_axis.title = "Koffieboon Type"
    satisfaction_chart.height = 10  # Hoogte van het diagram
    satisfaction_chart.width = 15   # Breedte van het diagram
    
    # Definieer de data voor het staafdiagram vanuit de numerieke waarden
    satisfaction_data_ref = Reference(ws, min_col=3, min_row=16, max_row=19, max_col=3)
    
    # Gebruik de labels uit kolom A (rij 16-19)
    satisfaction_cats = Reference(ws, min_col=1, min_row=16, max_row=19)
    
    # Voeg de data toe aan het staafdiagram
    satisfaction_chart.add_data(satisfaction_data_ref)
    satisfaction_chart.set_categories(satisfaction_cats)
    
    # Voeg data labels toe om de exacte waarden te tonen
    satisfaction_chart.dataLabels = DataLabelList()
    satisfaction_chart.dataLabels.showVal = True  # Toon de waarden
    satisfaction_chart.dataLabels.showCatName = False  # Verberg categorienamen
    satisfaction_chart.dataLabels.showSerName = False  # Verberg serienamen
    satisfaction_chart.dataLabels.showPercent = False  # Verberg percentages
    satisfaction_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels
    
    # Pas de stijl aan
    satisfaction_chart.style = 11  # Kies een andere stijl
    
    # Voeg het staafdiagram toe aan het Kaizen Analyse werkblad naast de tabel
    ws.add_chart(satisfaction_chart, "D15")
    
    # 3. Maak een staafdiagram voor Klantretourpercentage per Koffieboon Type
    return_chart = BarChart()
    return_chart.title = "Klantretourpercentage per Koffieboon Type"
    return_chart.y_axis.title = "Klantretourpercentage (%)"
    return_chart.x_axis.title = "Koffieboon Type"
    return_chart.height = 10  # Hoogte van het diagram
    return_chart.width = 15   # Breedte van het diagram
    
    # Definieer de data voor het staafdiagram vanuit de numerieke waarden
    return_data_ref = Reference(ws, min_col=3, min_row=21, max_row=24, max_col=3)
    
    # Gebruik de labels uit kolom A (rij 21-24)
    return_cats = Reference(ws, min_col=1, min_row=21, max_row=24)
    
    # Voeg de data toe aan het staafdiagram
    return_chart.add_data(return_data_ref)
    return_chart.set_categories(return_cats)
    
    # Voeg data labels toe om de exacte waarden te tonen
    return_chart.dataLabels = DataLabelList()
    return_chart.dataLabels.showVal = True  # Toon de waarden
    return_chart.dataLabels.showCatName = False  # Verberg categorienamen
    return_chart.dataLabels.showSerName = False  # Verberg serienamen
    return_chart.dataLabels.showPercent = False  # Verberg percentages
    return_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels
    
    # Pas de stijl aan
    return_chart.style = 12  # Kies een andere stijl
    
    # Voeg het staafdiagram toe aan het Kaizen Analyse werkblad naast de tabel
    ws.add_chart(return_chart, "D20")
    
    # Sla het Excel bestand op
    try:
        wb.save(output_file)
        print(f"Excel bestand met visualisaties is opgeslagen als {output_file}")
    except Exception as e:
        print(f"Fout bij opslaan van bestand: {e}")
        # Als er een fout is, probeer op te slaan in een tijdelijke map
        import tempfile
        temp_dir = tempfile.gettempdir()
        temp_file = os.path.join(temp_dir, 'Kaizen_Definitief.xlsx')
        wb.save(temp_file)
        print(f"Excel bestand met visualisaties is opgeslagen als {temp_file}")

if __name__ == "__main__":
    create_kaizen_excel()
