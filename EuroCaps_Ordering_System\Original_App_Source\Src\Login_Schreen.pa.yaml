# ************************************************************************************************
# Warning: YAML source code for Canvas Apps should only be used to review changes made within Power Apps Studio and for minor edits (Preview).
# Use the maker portal to create and edit your Power Apps.
# 
# The schema file for Canvas Apps is available at https://go.microsoft.com/fwlink/?linkid=2304907
# 
# For more information, visit https://go.microsoft.com/fwlink/?linkid=2292623
# ************************************************************************************************
Screens:
  Login_Schreen:
    Properties:
      LoadingSpinnerColor: =RGBA(56, 96, 178, 1)
    Children:
      - TitleBar:
          Control: Rectangle@2.3.0
          Properties:
            BorderColor: =RGBA(0, 0, 0, 1)
            BorderThickness: =2
            Fill: =RGBA(255, 255, 255, 1)
            Height: =101
            Width: =833
            X: =305
            Y: =11
      - AccountBar:
          Control: Rectangle@2.3.0
          Properties:
            BorderColor: =RGBA(0, 0, 0, 1)
            BorderThickness: =2
            Fill: =RGBA(255, 255, 255, 1)
            Height: =306
            Width: =833
            X: =305
            Y: =165
      - Username:
          Control: Label@2.5.1
          Properties:
            BorderColor: =RGBA(0, 18, 107, 1)
            Font: =Font.'Open Sans'
            Text: ="Username:"
            Width: =107
            X: =328
            Y: =190
      - Password:
          Control: Label@2.5.1
          Properties:
            BorderColor: =RGBA(0, 18, 107, 1)
            Font: =Font.'Open Sans'
            Text: ="Password:"
            Width: =107
            X: =328
            Y: =245
      - Hint_Username:
          Control: Classic/TextInput@2.3.2
          Properties:
            BorderColor: =RGBA(0, 18, 107, 1)
            Font: =Font.'Open Sans'
            HintText: ="Type here your username"
            HoverBorderColor: =RGBA(0, 18, 107, 1)
            HoverFill: =RGBA(186, 202, 226, 1)
            X: =455
            Y: =190
      - Hint_Password:
          Control: Classic/TextInput@2.3.2
          Properties:
            BorderColor: =RGBA(0, 18, 107, 1)
            Default: =
            Font: =Font.'Open Sans'
            HintText: ="Type here your password"
            HoverBorderColor: =RGBA(0, 18, 107, 1)
            HoverFill: =RGBA(186, 202, 226, 1)
            X: =455
            Y: =245
      - Title:
          Control: Label@2.5.1
          Properties:
            BorderColor: =RGBA(0, 18, 107, 1)
            Font: =Font.'Open Sans'
            Height: =34
            Text: ="Welcome To eurocaps"
            Width: =237
            X: =627
            Y: =30
      - Label3:
          Control: Label@2.5.1
          Properties:
            BorderColor: =RGBA(0, 18, 107, 1)
            Font: =Font.'Open Sans'
            Height: =48
            Text: ="Coffee Capsule Ordering System"
            Width: =280
            X: =605
            Y: =64
      - Checkbox1:
          Control: Classic/CheckBox@2.1.0
          Properties:
            BorderColor: =RGBA(0, 18, 107, 1)
            CheckboxBorderColor: =RGBA(0, 18, 107, 1)
            CheckmarkFill: =RGBA(0, 0, 0, 1)
            Font: =Font.'Open Sans'
            Height: =52
            HoverColor: =RGBA(0, 18, 107, 1)
            Text: ="Remember me"
            Width: =197
            X: =328
            Y: =308
      - Button1:
          Control: Classic/Button@2.2.0
          Properties:
            BorderColor: =ColorFade(Self.Fill, -15%)
            Color: =RGBA(255, 255, 255, 1)
            DisabledBorderColor: =RGBA(166, 166, 166, 1)
            Fill: =RGBA(56, 96, 178, 1)
            Font: =Font.'Open Sans'
            HoverBorderColor: =ColorFade(Self.BorderColor, 20%)
            HoverColor: =RGBA(255, 255, 255, 1)
            HoverFill: =ColorFade(RGBA(56, 96, 178, 1), -20%)
            PressedBorderColor: =Self.Fill
            PressedColor: =Self.Fill
            PressedFill: =Self.Color
            Text: ="Login"
            X: =346
            Y: =397
      - Button1_1:
          Control: Classic/Button@2.2.0
          Properties:
            BorderColor: =ColorFade(Self.Fill, -15%)
            Color: =RGBA(255, 255, 255, 1)
            DisabledBorderColor: =RGBA(166, 166, 166, 1)
            Fill: =RGBA(56, 96, 178, 1)
            Font: =Font.'Open Sans'
            Height: =57
            HoverBorderColor: =ColorFade(Self.BorderColor, 20%)
            HoverColor: =RGBA(255, 255, 255, 1)
            HoverFill: =ColorFade(RGBA(56, 96, 178, 1), -20%)
            PressedBorderColor: =Self.Fill
            PressedColor: =Self.Fill
            PressedFill: =Self.Color
            Text: ="Forgot password?"
            Width: =185
            X: =906
            Y: =388
