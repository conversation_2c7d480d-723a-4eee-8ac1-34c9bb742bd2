<mxfile host="app.diagrams.net" modified="2024-06-01T12:00:00.000Z" agent="Mozilla/5.0" version="21.3.7">
  <diagram id="C5RBs43oDa-KdzZeNtuy" name="Fysiek ERD">
    <mxGraphModel dx="1200" dy="800" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title" value="EuroCaps - Fysiek ERD" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="160" y="20" width="520" height="40" as="geometry" />
        </mxCell>
        
        <!-- Entiteiten met attributen en datatypen -->
        <mxCell id="supplier" value="Suppliers&#xa;&#xa;SupplierID INT PK AUTO_INCREMENT&#xa;SupplierName VARCHAR(100) NOT NULL&#xa;ContactPerson VARCHAR(100)&#xa;Email VARCHAR(100)&#xa;Phone VARCHAR(20)&#xa;Address VARCHAR(255)&#xa;CreatedAt DATETIME&#xa;UpdatedAt DATETIME" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="40" y="100" width="240" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="material" value="Materials&#xa;&#xa;MaterialID INT PK AUTO_INCREMENT&#xa;MaterialName VARCHAR(100) NOT NULL&#xa;MaterialType ENUM('coffee_beans', 'capsules', 'foil', &#xa;  'additives', 'milk', 'nitrogen', 'packaging') NOT NULL&#xa;UnitOfMeasure VARCHAR(20) NOT NULL&#xa;CurrentStock DECIMAL(10,2) NOT NULL DEFAULT 0&#xa;MinimumStock DECIMAL(10,2) NOT NULL DEFAULT 0&#xa;SupplierID INT FK&#xa;CreatedAt DATETIME&#xa;UpdatedAt DATETIME&#xa;&#xa;FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="320" y="100" width="320" height="200" as="geometry" />
        </mxCell>
        
        <mxCell id="product" value="Products&#xa;&#xa;ProductID INT PK AUTO_INCREMENT&#xa;ProductName VARCHAR(100) NOT NULL&#xa;ProductType ENUM('espresso', 'lungo', 'ristretto', 'flavored') NOT NULL&#xa;PackageSize ENUM('10', '20', '44') NOT NULL&#xa;Description TEXT&#xa;CreatedAt DATETIME&#xa;UpdatedAt DATETIME" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="680" y="100" width="360" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="production_batch" value="ProductionBatches&#xa;&#xa;BatchID INT PK AUTO_INCREMENT&#xa;BatchNumber VARCHAR(20) NOT NULL UNIQUE&#xa;BatchDate DATETIME NOT NULL&#xa;ProductID INT FK NOT NULL&#xa;PlannedQuantity INT NOT NULL&#xa;ActualQuantity INT&#xa;Status ENUM('planned', 'in_progress', 'completed', 'cancelled') NOT NULL&#xa;CreatedAt DATETIME&#xa;UpdatedAt DATETIME&#xa;&#xa;FOREIGN KEY (ProductID) REFERENCES Products(ProductID)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="320" y="340" width="360" height="180" as="geometry" />
        </mxCell>
        
        <mxCell id="material_usage" value="MaterialUsage&#xa;&#xa;UsageID INT PK AUTO_INCREMENT&#xa;BatchID INT FK NOT NULL&#xa;MaterialID INT FK NOT NULL&#xa;Quantity DECIMAL(10,2) NOT NULL&#xa;UsageDate DATETIME NOT NULL&#xa;CreatedAt DATETIME&#xa;UpdatedAt DATETIME&#xa;&#xa;FOREIGN KEY (BatchID) REFERENCES ProductionBatches(BatchID)&#xa;FOREIGN KEY (MaterialID) REFERENCES Materials(MaterialID)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="40" y="340" width="240" height="180" as="geometry" />
        </mxCell>
        
        <mxCell id="production_step" value="ProductionSteps&#xa;&#xa;StepID INT PK AUTO_INCREMENT&#xa;BatchID INT FK NOT NULL&#xa;StepType ENUM('grinding', 'filling', 'packing') NOT NULL&#xa;StartTime DATETIME&#xa;EndTime DATETIME&#xa;MachineID INT FK&#xa;EmployeeID INT FK&#xa;QuantityProcessed INT&#xa;QuantityRejected INT DEFAULT 0&#xa;Notes TEXT&#xa;CreatedAt DATETIME&#xa;UpdatedAt DATETIME&#xa;&#xa;FOREIGN KEY (BatchID) REFERENCES ProductionBatches(BatchID)&#xa;FOREIGN KEY (MachineID) REFERENCES Machines(MachineID)&#xa;FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="320" y="560" width="360" height="260" as="geometry" />
        </mxCell>
        
        <mxCell id="quality_check" value="QualityChecks&#xa;&#xa;CheckID INT PK AUTO_INCREMENT&#xa;StepID INT FK NOT NULL&#xa;CheckTime DATETIME NOT NULL&#xa;CheckType ENUM('weight', 'fill_level', 'seal_integrity', &#xa;  'visual_inspection') NOT NULL&#xa;Result ENUM('OK', 'NOK') NOT NULL&#xa;MeasuredValue DECIMAL(10,2)&#xa;MinThreshold DECIMAL(10,2)&#xa;MaxThreshold DECIMAL(10,2)&#xa;EmployeeID INT FK&#xa;Notes TEXT&#xa;CreatedAt DATETIME&#xa;UpdatedAt DATETIME&#xa;&#xa;FOREIGN KEY (StepID) REFERENCES ProductionSteps(StepID)&#xa;FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="40" y="560" width="240" height="260" as="geometry" />
        </mxCell>
        
        <mxCell id="customer" value="Customers&#xa;&#xa;CustomerID INT PK AUTO_INCREMENT&#xa;CustomerName VARCHAR(100) NOT NULL&#xa;ContactPerson VARCHAR(100)&#xa;Email VARCHAR(100)&#xa;Phone VARCHAR(20)&#xa;Address VARCHAR(255)&#xa;CreatedAt DATETIME&#xa;UpdatedAt DATETIME" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="680" y="280" width="240" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="employee" value="Employees&#xa;&#xa;EmployeeID INT PK AUTO_INCREMENT&#xa;FirstName VARCHAR(50) NOT NULL&#xa;LastName VARCHAR(50) NOT NULL&#xa;Department ENUM('grinding', 'filling', 'packing', &#xa;  'quality', 'logistics', 'management') NOT NULL&#xa;Position VARCHAR(100)&#xa;HireDate DATE&#xa;CreatedAt DATETIME&#xa;UpdatedAt DATETIME" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="720" y="560" width="280" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="machine" value="Machines&#xa;&#xa;MachineID INT PK AUTO_INCREMENT&#xa;MachineName VARCHAR(100) NOT NULL&#xa;MachineType ENUM('grinder', 'filler', 'sealer', &#xa;  'packer', 'quality_control') NOT NULL&#xa;Location VARCHAR(100)&#xa;InstallationDate DATE&#xa;MaintenanceInterval INT&#xa;LastMaintenance DATE&#xa;CreatedAt DATETIME&#xa;UpdatedAt DATETIME" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="320" y="860" width="280" height="180" as="geometry" />
        </mxCell>
        
        <mxCell id="order" value="Orders&#xa;&#xa;OrderID INT PK AUTO_INCREMENT&#xa;OrderNumber VARCHAR(20) NOT NULL UNIQUE&#xa;CustomerID INT FK NOT NULL&#xa;OrderDate DATETIME NOT NULL&#xa;DeliveryDate DATETIME&#xa;Status ENUM('new', 'processing', 'shipped', &#xa;  'delivered', 'cancelled') NOT NULL DEFAULT 'new'&#xa;Notes TEXT&#xa;CreatedAt DATETIME&#xa;UpdatedAt DATETIME&#xa;&#xa;FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="680" y="760" width="320" height="200" as="geometry" />
        </mxCell>
        
        <mxCell id="order_item" value="OrderItems&#xa;&#xa;ItemID INT PK AUTO_INCREMENT&#xa;OrderID INT FK NOT NULL&#xa;ProductID INT FK NOT NULL&#xa;Quantity INT NOT NULL&#xa;Price DECIMAL(10,2) NOT NULL&#xa;CreatedAt DATETIME&#xa;UpdatedAt DATETIME&#xa;&#xa;FOREIGN KEY (OrderID) REFERENCES Orders(OrderID)&#xa;FOREIGN KEY (ProductID) REFERENCES Products(ProductID)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;align=left;spacingLeft=5;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="680" y="1000" width="320" height="180" as="geometry" />
        </mxCell>
        
        <!-- Toelichting -->
        <mxCell id="explanation_title" value="Toelichting Fysiek ERD" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="40" y="1200" width="400" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="explanation_text" value="Dit fysieke ERD toont de database implementatie voor EuroCaps met specifieke datatypen, constraints en relaties. Ontwerpkeuzes:&#xa;&#xa;1. Primaire sleutels: Alle tabellen gebruiken een auto-increment integer als primaire sleutel voor eenvoudige referentie en indexering.&#xa;&#xa;2. Vreemde sleutels: Relaties worden geïmplementeerd met vreemde sleutels die verwijzen naar de primaire sleutels van gerelateerde tabellen.&#xa;&#xa;3. ENUM types: Voor velden met een beperkte set waarden (zoals MaterialType, ProductType, StepType) worden ENUM types gebruikt om data-integriteit te waarborgen.&#xa;&#xa;4. NOT NULL constraints: Essentiële velden zijn gemarkeerd als NOT NULL om te garanderen dat kritieke informatie altijd wordt vastgelegd.&#xa;&#xa;5. DEFAULT waarden: Waar relevant zijn standaardwaarden ingesteld (bijv. CurrentStock DEFAULT 0).&#xa;&#xa;6. Timestamps: Alle tabellen bevatten CreatedAt en UpdatedAt velden voor audit-doeleinden.&#xa;&#xa;7. Unieke constraints: Velden zoals BatchNumber en OrderNumber hebben UNIQUE constraints om duplicaten te voorkomen.&#xa;&#xa;8. Decimale precisie: Voor numerieke waarden die precisie vereisen (zoals gewichten en hoeveelheden) wordt DECIMAL(10,2) gebruikt.&#xa;&#xa;9. Indexen: Naast primaire sleutels zouden indexen worden toegevoegd op veelgebruikte zoekvelden en vreemde sleutels voor optimale prestaties.&#xa;&#xa;Deze database ondersteunt alle aspecten van het EuroCaps productieproces, van grondstofbeheer tot kwaliteitscontrole en orderverwerking." style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="40" y="1240" width="960" height="320" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
