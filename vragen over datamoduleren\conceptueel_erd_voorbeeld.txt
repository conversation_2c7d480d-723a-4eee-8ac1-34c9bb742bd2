Een conceptueel ERD (Entity Relationship Diagram) is een visuele weergave van de belangrijkste entiteiten in een systeem en de relaties tussen deze entiteiten. Het is de meest abstracte vorm van een ERD en bevat:

1. Entiteiten: Weergegeven als rechthoeken met de naam van de entiteit
2. Relaties: Weergegeven als lijnen tussen entiteiten met een beschrijving van de relatie
3. Cardinaliteit: Aanduiding van het type relatie (één-op-één, één-op-veel, veel-op-veel)

Kenmerken van een conceptueel ERD:
- Bevat GEEN attributen (details van entiteiten)
- Toont alleen de hoofdentiteiten en hun relaties
- Is begrijpelijk voor niet-technische stakeholders
- Dient als basis voor het latere logische en fysieke ERD

Voorbeeld:
In een eenvoudig systeem voor een webwinkel:
- Entiteiten: KLANT, BESTELLING, PRODUCT, CATEGORIE
- Relaties: 
  * KLANT plaatst BESTELLING (één-op-veel)
  * BESTELLING bevat PRODUCT (veel-op-veel)
  * PRODUCT behoort tot CATEGORIE (veel-op-één)

Dit is de eerste stap in het databaseontwerpproces, gevolgd door het logische ERD (met attributen) en uiteindelijk het fysieke ERD (met technische details zoals datatypes en sleutels).
