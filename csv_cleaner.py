#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV Data Cleaner

This script loads a CSV file, performs various cleaning operations, and saves the cleaned data.
It handles common data cleaning tasks such as:
- Handling missing values
- Converting data types
- Removing duplicates
- Fixing formatting issues
- Standardizing text data
- Handling outliers

Usage:
    python csv_cleaner.py input.csv output.csv
    
    Optional arguments:
    --delimiter=","       CSV delimiter (default: comma)
    --encoding="utf-8"    File encoding (default: utf-8)
    --na-values="NA,N/A"  Values to treat as NA/NaN (comma-separated)
    --drop-duplicates     Remove duplicate rows
    --fill-numeric=mean   Fill missing numeric values (options: mean, median, mode, zero, none)
    --fill-text=mode      Fill missing text values (options: mode, empty, none)
    --trim-text           Trim whitespace from text columns
    --lowercase-text      Convert text columns to lowercase
    --remove-outliers     Remove statistical outliers from numeric columns
    --verbose             Print detailed information during processing
"""

import pandas as pd
import numpy as np
import argparse
import sys
import os
from datetime import datetime


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Clean CSV data.')
    
    parser.add_argument('input_file', help='Input CSV file path')
    parser.add_argument('output_file', nargs='?', help='Output CSV file path (default: input_file_cleaned.csv)')
    
    parser.add_argument('--delimiter', default=',', help='CSV delimiter (default: comma)')
    parser.add_argument('--encoding', default='utf-8', help='File encoding (default: utf-8)')
    parser.add_argument('--na-values', default='NA,N/A,nan,NaN,None', help='Values to treat as NA/NaN (comma-separated)')
    
    parser.add_argument('--drop-duplicates', action='store_true', help='Remove duplicate rows')
    parser.add_argument('--fill-numeric', default='none', 
                        choices=['mean', 'median', 'mode', 'zero', 'none'], 
                        help='Fill missing numeric values')
    parser.add_argument('--fill-text', default='none', 
                        choices=['mode', 'empty', 'none'], 
                        help='Fill missing text values')
    
    parser.add_argument('--trim-text', action='store_true', help='Trim whitespace from text columns')
    parser.add_argument('--lowercase-text', action='store_true', help='Convert text columns to lowercase')
    parser.add_argument('--remove-outliers', action='store_true', help='Remove statistical outliers from numeric columns')
    parser.add_argument('--verbose', action='store_true', help='Print detailed information during processing')
    
    return parser.parse_args()


def load_csv(file_path, delimiter, encoding, na_values):
    """Load CSV file into a pandas DataFrame."""
    try:
        na_values_list = na_values.split(',')
        df = pd.read_csv(file_path, delimiter=delimiter, encoding=encoding, na_values=na_values_list)
        return df
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        sys.exit(1)


def get_column_types(df):
    """Categorize columns by data type."""
    numeric_cols = df.select_dtypes(include=['int64', 'float64']).columns.tolist()
    datetime_cols = []
    
    # Try to identify datetime columns
    for col in df.columns:
        if col not in numeric_cols:
            try:
                pd.to_datetime(df[col], errors='raise')
                datetime_cols.append(col)
            except:
                pass
    
    text_cols = [col for col in df.columns if col not in numeric_cols and col not in datetime_cols]
    
    return {
        'numeric': numeric_cols,
        'datetime': datetime_cols,
        'text': text_cols
    }


def clean_data(df, args, column_types):
    """Perform data cleaning operations."""
    cleaning_summary = []
    df_cleaned = df.copy()
    
    # Record initial state
    initial_rows = len(df_cleaned)
    initial_missing = df_cleaned.isna().sum().sum()
    
    # 1. Handle duplicates
    if args.drop_duplicates:
        before_count = len(df_cleaned)
        df_cleaned = df_cleaned.drop_duplicates()
        after_count = len(df_cleaned)
        duplicates_removed = before_count - after_count
        if duplicates_removed > 0:
            cleaning_summary.append(f"Removed {duplicates_removed} duplicate rows")
    
    # 2. Handle missing values in numeric columns
    if args.fill_numeric != 'none' and column_types['numeric']:
        for col in column_types['numeric']:
            missing_count = df_cleaned[col].isna().sum()
            if missing_count > 0:
                if args.fill_numeric == 'mean':
                    fill_value = df_cleaned[col].mean()
                    df_cleaned[col] = df_cleaned[col].fillna(fill_value)
                    cleaning_summary.append(f"Filled {missing_count} missing values in '{col}' with mean ({fill_value:.2f})")
                elif args.fill_numeric == 'median':
                    fill_value = df_cleaned[col].median()
                    df_cleaned[col] = df_cleaned[col].fillna(fill_value)
                    cleaning_summary.append(f"Filled {missing_count} missing values in '{col}' with median ({fill_value:.2f})")
                elif args.fill_numeric == 'mode':
                    fill_value = df_cleaned[col].mode()[0]
                    df_cleaned[col] = df_cleaned[col].fillna(fill_value)
                    cleaning_summary.append(f"Filled {missing_count} missing values in '{col}' with mode ({fill_value})")
                elif args.fill_numeric == 'zero':
                    df_cleaned[col] = df_cleaned[col].fillna(0)
                    cleaning_summary.append(f"Filled {missing_count} missing values in '{col}' with zero")
    
    # 3. Handle missing values in text columns
    if args.fill_text != 'none' and column_types['text']:
        for col in column_types['text']:
            missing_count = df_cleaned[col].isna().sum()
            if missing_count > 0:
                if args.fill_text == 'mode':
                    if not df_cleaned[col].dropna().empty:
                        fill_value = df_cleaned[col].mode()[0]
                        df_cleaned[col] = df_cleaned[col].fillna(fill_value)
                        cleaning_summary.append(f"Filled {missing_count} missing values in '{col}' with mode ('{fill_value}')")
                elif args.fill_text == 'empty':
                    df_cleaned[col] = df_cleaned[col].fillna('')
                    cleaning_summary.append(f"Filled {missing_count} missing values in '{col}' with empty string")
    
    # 4. Clean text columns
    if column_types['text']:
        for col in column_types['text']:
            # Trim whitespace
            if args.trim_text:
                if df_cleaned[col].dtype == 'object':
                    df_cleaned[col] = df_cleaned[col].astype(str).str.strip()
                    cleaning_summary.append(f"Trimmed whitespace in '{col}'")
            
            # Convert to lowercase
            if args.lowercase_text:
                if df_cleaned[col].dtype == 'object':
                    df_cleaned[col] = df_cleaned[col].astype(str).str.lower()
                    cleaning_summary.append(f"Converted '{col}' to lowercase")
    
    # 5. Handle outliers in numeric columns
    if args.remove_outliers and column_types['numeric']:
        for col in column_types['numeric']:
            # Calculate IQR
            Q1 = df_cleaned[col].quantile(0.25)
            Q3 = df_cleaned[col].quantile(0.75)
            IQR = Q3 - Q1
            
            # Define outlier bounds
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # Count outliers
            outliers = ((df_cleaned[col] < lower_bound) | (df_cleaned[col] > upper_bound)).sum()
            
            if outliers > 0:
                # Replace outliers with NaN
                df_cleaned.loc[(df_cleaned[col] < lower_bound) | (df_cleaned[col] > upper_bound), col] = np.nan
                cleaning_summary.append(f"Identified {outliers} outliers in '{col}' (replaced with NaN)")
    
    # 6. Try to convert datetime columns
    for col in column_types['datetime']:
        try:
            df_cleaned[col] = pd.to_datetime(df_cleaned[col])
            cleaning_summary.append(f"Converted '{col}' to datetime format")
        except:
            cleaning_summary.append(f"Failed to convert '{col}' to datetime format")
    
    # Record final state
    final_rows = len(df_cleaned)
    final_missing = df_cleaned.isna().sum().sum()
    
    # Add summary statistics
    cleaning_summary.append(f"Rows: {initial_rows} → {final_rows} ({initial_rows - final_rows} removed)")
    cleaning_summary.append(f"Missing values: {initial_missing} → {final_missing} ({initial_missing - final_missing} filled)")
    
    return df_cleaned, cleaning_summary


def save_csv(df, file_path, delimiter, encoding):
    """Save DataFrame to CSV file."""
    try:
        df.to_csv(file_path, index=False, delimiter=delimiter, encoding=encoding)
        return True
    except Exception as e:
        print(f"Error saving CSV file: {e}")
        return False


def print_data_summary(df, column_types):
    """Print summary of the data."""
    print("\nData Summary:")
    print(f"Rows: {len(df)}")
    print(f"Columns: {len(df.columns)}")
    
    print("\nColumn Types:")
    print(f"- Numeric columns ({len(column_types['numeric'])}): {', '.join(column_types['numeric'])}")
    print(f"- Text columns ({len(column_types['text'])}): {', '.join(column_types['text'])}")
    print(f"- Datetime columns ({len(column_types['datetime'])}): {', '.join(column_types['datetime'])}")
    
    print("\nMissing Values:")
    missing = df.isna().sum()
    missing = missing[missing > 0]
    if len(missing) > 0:
        for col, count in missing.items():
            print(f"- {col}: {count} missing values ({count/len(df):.1%})")
    else:
        print("- No missing values")


def main():
    """Main function."""
    # Parse arguments
    args = parse_arguments()
    
    # Set default output file if not provided
    if not args.output_file:
        base, ext = os.path.splitext(args.input_file)
        args.output_file = f"{base}_cleaned{ext}"
    
    # Print header
    print("=" * 80)
    print("CSV DATA CLEANER")
    print("=" * 80)
    
    # Load data
    print(f"\nLoading CSV file: {args.input_file}")
    df = load_csv(args.input_file, args.delimiter, args.encoding, args.na_values)
    print(f"Loaded {len(df)} rows and {len(df.columns)} columns")
    
    # Get column types
    column_types = get_column_types(df)
    
    # Print data summary if verbose
    if args.verbose:
        print_data_summary(df, column_types)
    
    # Clean data
    print("\nCleaning data...")
    df_cleaned, cleaning_summary = clean_data(df, args, column_types)
    
    # Print cleaning summary
    print("\nCleaning Summary:")
    for item in cleaning_summary:
        print(f"- {item}")
    
    # Save cleaned data
    print(f"\nSaving cleaned data to: {args.output_file}")
    if save_csv(df_cleaned, args.output_file, args.delimiter, args.encoding):
        print("Data saved successfully")
    else:
        print("Failed to save data")
    
    print("\nDone!")


if __name__ == "__main__":
    main()
