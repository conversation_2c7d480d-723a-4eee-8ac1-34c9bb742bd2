import os
import sys
from PIL import Image
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
import time

# Setup Chrome options
chrome_options = Options()
chrome_options.add_argument("--headless")  # Run in headless mode
chrome_options.add_argument("--window-size=1000,800")  # Set window size

# Initialize the Chrome driver
driver = webdriver.Chrome(options=chrome_options)

# List of HTML files to convert
html_files = [
    "bedrijfsprocessen_ito_model.html",
    "bedrijfsprocessen_types.html",
    "bedrijfsprocessen_swimlane.html"
]

# Convert each HTML file to JPG
for html_file in html_files:
    try:
        # Get the file name without extension
        file_name = os.path.splitext(html_file)[0]
        
        # Get the absolute path of the HTML file
        html_path = os.path.abspath(html_file)
        
        # Load the HTML file
        driver.get(f"file:///{html_path}")
        
        # Wait for the page to load
        time.sleep(2)
        
        # Take a screenshot and save it as JPG
        driver.save_screenshot(f"{file_name}.jpg")
        
        print(f"Converted {html_file} to {file_name}.jpg")
    except Exception as e:
        print(f"Error converting {html_file}: {e}")

# Close the driver
driver.quit()

print("Conversion process completed.")
