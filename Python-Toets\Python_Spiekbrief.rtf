{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Courier New;}}
{\colortbl ;\red0\green0\blue255;\red0\green0\blue0;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 PYTHON SPIEKBRIEF (PAGINA 1)\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 PYTHON BASICS\b0\fs22\par
\b\fs22 Variabelen en Datatypes\b0\par
\pard\li360\sa200\sl276\slmult1 - Variabelen: naam = waarde (geen declaratie nodig)\par
- Naamgeving: letters, cijfers, underscore (_), start niet met cijfer\par
- Hoofdlettergevoelig: naam \u8800? Naam\par
- Datatypes:\par
\pard\li720\sa200\sl276\slmult1 * int: gehele getallen (42, -7)\par
* float: kommagetallen (3.14, -0.001)\par
* str: tekst ("Hallo", 'Python')\par
* bool: True of False\par
* list: [1, 2, 3] (geordend, muteerbaar)\par
* tuple: (1, 2, 3) (geordend, niet muteerbaar)\par
* dict: \{"naam": "Jan", "leeftijd": 25\} (key-value pairs)\par
* set: \{1, 2, 3\} (unieke waarden, ongeordend)\par

\pard\sa200\sl276\slmult1\b Operatoren\b0\par
\pard\li360\sa200\sl276\slmult1 - Rekenkundig: +, -, *, /, // (integer deling), % (modulo), ** (macht)\par
- Vergelijking: ==, !=, <, >, <=, >=\par
- Logisch: and, or, not\par
- Toewijzing: =, +=, -=, *=, /=, //=, %=, **=\par
- Membership: in, not in\par
- Identity: is, is not\par

\pard\sa200\sl276\slmult1\b Strings\b0\par
\pard\li360\sa200\sl276\slmult1 - Aanmaken: "tekst" of 'tekst'\par
- Meerdere regels: """tekst""" of '''tekst'''\par
- Indexering: tekst[0], tekst[-1] (laatste karakter)\par
- Slicing: tekst[2:5], tekst[:5], tekst[2:], tekst[::2]\par
- Methoden:\par
\pard\li720\sa200\sl276\slmult1 * upper(), lower(), capitalize(), title()\par
* strip(), lstrip(), rstrip()\par
* replace(oud, nieuw)\par
* split(separator), join(lijst)\par
* find(substr), count(substr)\par
* startswith(prefix), endswith(suffix)\par
* isalpha(), isdigit(), isalnum(), isspace()\par
\pard\li360\sa200\sl276\slmult1 - Formatteren:\par
\pard\li720\sa200\sl276\slmult1 * f-strings: f"Naam: \{naam\}, Leeftijd: \{leeftijd\}"\par
* format(): "Naam: \{\}, Leeftijd: \{\}".format(naam, leeftijd)\par
* %-notatie: "Naam: %s, Leeftijd: %d" % (naam, leeftijd)\par

\pard\sa200\sl276\slmult1\b Lijsten (Lists)\b0\par
\pard\li360\sa200\sl276\slmult1 - Aanmaken: lijst = [1, 2, 3] of lijst = list()\par
- Indexering: lijst[0], lijst[-1]\par
- Slicing: lijst[2:5], lijst[:5], lijst[2:], lijst[::2]\par
- Methoden:\par
\pard\li720\sa200\sl276\slmult1 * append(item): voegt item toe aan einde\par
* insert(index, item): voegt item toe op index\par
* extend(iterable): voegt meerdere items toe\par
* remove(item): verwijdert eerste voorkomen van item\par
* pop(index): verwijdert en retourneert item op index\par
* clear(): verwijdert alle items\par
* index(item): geeft index van eerste voorkomen\par
* count(item): telt aantal voorkomens\par
* sort(): sorteert lijst (in-place)\par
* reverse(): keert volgorde om (in-place)\par
\pard\li360\sa200\sl276\slmult1 - List comprehension: [x*2 for x in range(10) if x % 2 == 0]\par

\pard\sa200\sl276\slmult1\b Dictionaries\b0\par
\pard\li360\sa200\sl276\slmult1 - Aanmaken: d = \{"key": "value"\} of d = dict()\par
- Toegang: d["key"] of d.get("key", default)\par
- Methoden:\par
\pard\li720\sa200\sl276\slmult1 * keys(): geeft alle keys\par
* values(): geeft alle values\par
* items(): geeft alle key-value pairs als tuples\par
* update(dict2): voegt dict2 toe aan dict\par
* pop(key): verwijdert en retourneert value\par
* clear(): verwijdert alle items\par
\pard\li360\sa200\sl276\slmult1 - Dict comprehension: \{x: x**2 for x in range(5)\}\par

\pard\sa200\sl276\slmult1\b Controlestructuren\b0\par
\pard\li360\sa200\sl276\slmult1 - if-elif-else:\par
\pard\li720\sa200\sl276\slmult1\f1 if voorwaarde:\par
    # code\par
elif andere_voorwaarde:\par
    # code\par
else:\par
    # code\f0\par

\pard\li360\sa200\sl276\slmult1 - for-lus:\par
\pard\li720\sa200\sl276\slmult1\f1 for item in iterable:\par
    # code\f0\par

\pard\li360\sa200\sl276\slmult1 - while-lus:\par
\pard\li720\sa200\sl276\slmult1\f1 while voorwaarde:\par
    # code\f0\par

\pard\li360\sa200\sl276\slmult1 - break: verlaat de lus\par
- continue: gaat naar volgende iteratie\par
- pass: doet niets (placeholder)\par

\pard\sa200\sl276\slmult1\qc\b\fs28 PYTHON SPIEKBRIEF (PAGINA 2)\b0\fs22\par

\pard\sa200\sl276\slmult1\b Functies\b0\par
\pard\li360\sa200\sl276\slmult1 - Definitie:\par
\pard\li720\sa200\sl276\slmult1\f1 def functie_naam(param1, param2=default):\par
    # code\par
    return waarde\f0\par

\pard\li360\sa200\sl276\slmult1 - Argumenten:\par
\pard\li720\sa200\sl276\slmult1 * Positioneel: functie(1, 2)\par
* Keyword: functie(param2=2, param1=1)\par
* Variabel aantal: *args (tuple), **kwargs (dict)\par
\f1 def functie(*args, **kwargs):\par
    print(args)    # (1, 2, 3)\par
    print(kwargs)  # \{'a': 4, 'b': 5\}\par
\par
functie(1, 2, 3, a=4, b=5)\f0\par

\pard\li360\sa200\sl276\slmult1 - Lambda functies (anonieme functies):\par
\pard\li720\sa200\sl276\slmult1\f1 f = lambda x, y: x + y\par
print(f(2, 3))  # 5\f0\par

\pard\sa200\sl276\slmult1\b Modules en Packages\b0\par
\pard\li360\sa200\sl276\slmult1 - Import:\par
\pard\li720\sa200\sl276\slmult1\f1 import module\par
from module import functie\par
from module import functie as f\par
from module import *\par
import module as m\f0\par

\pard\li360\sa200\sl276\slmult1 - Standaard modules:\par
\pard\li720\sa200\sl276\slmult1 * math: math.sqrt(), math.pi, math.sin()\par
* random: random.randint(), random.choice()\par
* datetime: datetime.now(), datetime.strptime()\par
* os: os.path.join(), os.listdir()\par
* sys: sys.argv, sys.exit()\par
* json: json.loads(), json.dumps()\par
* re: re.search(), re.match(), re.findall()\par

\pard\sa200\sl276\slmult1\b Bestandsverwerking\b0\par
\pard\li360\sa200\sl276\slmult1 - Openen en sluiten:\par
\pard\li720\sa200\sl276\slmult1\f1 # Met context manager (aanbevolen)\par
with open('bestand.txt', 'r') as f:\par
    inhoud = f.read()\par
\par
# Handmatig\par
f = open('bestand.txt', 'r')\par
inhoud = f.read()\par
f.close()\f0\par

\pard\li360\sa200\sl276\slmult1 - Modes:\par
\pard\li720\sa200\sl276\slmult1 * 'r': lezen (default)\par
* 'w': schrijven (overschrijft bestand)\par
* 'a': toevoegen (append)\par
* 'b': binaire mode (rb, wb)\par
* 't': tekst mode (default)\par

\pard\li360\sa200\sl276\slmult1 - Methoden:\par
\pard\li720\sa200\sl276\slmult1 * read(): leest hele bestand\par
* readline(): leest \'e9\'e9n regel\par
* readlines(): leest alle regels in lijst\par
* write(str): schrijft string\par
* writelines(list): schrijft lijst van strings\par

\pard\sa200\sl276\slmult1\b Exceptions\b0\par
\pard\li360\sa200\sl276\slmult1 - Try-except:\par
\pard\li720\sa200\sl276\slmult1\f1 try:\par
    # code die exception kan veroorzaken\par
except ExceptionType as e:\par
    # code bij exception\par
else:\par
    # code als geen exception\par
finally:\par
    # code die altijd uitgevoerd wordt\f0\par

\pard\li360\sa200\sl276\slmult1 - Veelvoorkomende exceptions:\par
\pard\li720\sa200\sl276\slmult1 * ValueError: ongeldige waarde\par
* TypeError: ongeldige type\par
* IndexError: index buiten bereik\par
* KeyError: key niet in dictionary\par
* FileNotFoundError: bestand niet gevonden\par
* ZeroDivisionError: deling door nul\par
* ImportError: module niet gevonden\par

\pard\li360\sa200\sl276\slmult1 - Eigen exceptions:\par
\pard\li720\sa200\sl276\slmult1\f1 class MijnException(Exception):\par
    pass\par
\par
raise MijnException("Foutmelding")\f0\par

\pard\sa200\sl276\slmult1\b Object-Oriented Programming (OOP)\b0\par
\pard\li360\sa200\sl276\slmult1 - Klasse definitie:\par
\pard\li720\sa200\sl276\slmult1\f1 class Persoon:\par
    # Class variabele (gedeeld door alle instanties)\par
    soort = "mens"\par
    \par
    # Constructor\par
    def __init__(self, naam, leeftijd):\par
        # Instance variabelen\par
        self.naam = naam\par
        self.leeftijd = leeftijd\par
    \par
    # Instance methode\par
    def groet(self):\par
        return f"Hallo, ik ben \{self.naam\}"\par
    \par
    # Class methode\par
    @classmethod\par
    def van_geboortejaar(cls, naam, geboortejaar):\par
        leeftijd = 2024 - geboortejaar\par
        return cls(naam, leeftijd)\par
    \par
    # Static methode\par
    @staticmethod\par
    def is_volwassen(leeftijd):\par
        return leeftijd >= 18\f0\par

\pard\li360\sa200\sl276\slmult1 - Instantie aanmaken:\par
\pard\li720\sa200\sl276\slmult1\f1 p = Persoon("Jan", 25)\par
print(p.naam)        # Jan\par
print(p.groet())     # Hallo, ik ben Jan\f0\par

\pard\li360\sa200\sl276\slmult1 - Overerving:\par
\pard\li720\sa200\sl276\slmult1\f1 class Student(Persoon):\par
    def __init__(self, naam, leeftijd, studie):\par
        super().__init__(naam, leeftijd)\par
        self.studie = studie\par
    \par
    def groet(self):\par
        return f"\{super().groet()\} en ik studeer \{self.studie\}"\f0\par

\pard\sa200\sl276\slmult1\b Handige Built-in Functies\b0\par
\pard\li360\sa200\sl276\slmult1 - print(): output naar console\par
- input(): input van gebruiker\par
- len(): lengte van object\par
- range(): reeks getallen\par
- type(): type van object\par
- int(), float(), str(), bool(): type conversie\par
- list(), tuple(), dict(), set(): type conversie\par
- min(), max(), sum(): minimum, maximum, som\par
- sorted(): gesorteerde kopie\par
- enumerate(): index en waarde\par
- zip(): combineert iterables\par
- filter(), map(): functioneel programmeren\par
- any(), all(): controleert voorwaarden\par
- abs(): absolute waarde\par
- round(): afronden\par
- dir(): attributen van object\par
- help(): documentatie\par

\pard\sa200\sl276\slmult1\b Handige Tips\b0\par
\pard\li360\sa200\sl276\slmult1 - PEP 8: Python stijlgids (indentatie, naamgeving)\par
- Docstrings: documentatie in code\par
- Virtual environments: isolatie van dependencies\par
- pip: package manager\par
- List/dict/set comprehensions voor beknopte code\par
- f-strings voor leesbare string formatting\par
- Context managers (with) voor resource management\par
- Gebruik enumerate() in plaats van range(len())\par
- Gebruik in voor membership tests\par
- Gebruik is voor identity tests\par
- Gebruik get() voor dictionaries om KeyError te voorkomen\par
}
