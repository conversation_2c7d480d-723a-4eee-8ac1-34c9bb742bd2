#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
EuroCaps CSV Import Script

Dit script importeert CSV-bestanden in de EuroCaps MySQL database.
"""

import os
import pandas as pd
import mysql.connector
from mysql.connector import Error

def connect_to_mysql():
    """Maak verbinding met de MySQL database"""
    try:
        connection = mysql.connector.connect(
            host="localhost",
            user="root",  # Vervang door je eigen gebruikersnaam
            password="",  # Vervang door je eigen wachtwoord
            database="eurocaps"
        )
        if connection.is_connected():
            print("Verbonden met MySQL database")
            return connection
    except Error as e:
        print(f"Fout bij verbinden met MySQL: {e}")
        return None

def import_csv_to_table(connection, csv_file, table_name):
    """Importeer een CSV-bestand in een MySQL tabel"""
    try:
        # Lees het CSV-bestand
        df = pd.read_csv(f"csv_data/{csv_file}")
        
        # Maak een cursor
        cursor = connection.cursor()
        
        # Verwijder bestaande data uit de tabel
        cursor.execute(f"DELETE FROM {table_name}")
        
        # Genereer de INSERT query
        columns = ", ".join(df.columns)
        placeholders = ", ".join(["%s"] * len(df.columns))
        query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
        
        # Voer de query uit voor elke rij in het DataFrame
        for _, row in df.iterrows():
            cursor.execute(query, tuple(row))
        
        # Commit de wijzigingen
        connection.commit()
        
        print(f"{len(df)} rijen geïmporteerd in tabel {table_name}")
    except Error as e:
        print(f"Fout bij importeren van {csv_file} in {table_name}: {e}")
    except Exception as e:
        print(f"Algemene fout bij importeren van {csv_file}: {e}")

def import_all_csv_files():
    """Importeer alle CSV-bestanden in de juiste volgorde"""
    # Maak verbinding met de database
    connection = connect_to_mysql()
    if not connection:
        return
    
    try:
        # Definieer de tabellen en bijbehorende CSV-bestanden in de juiste volgorde
        tables_and_files = [
            ("SoortPartner", "soort_partner.csv"),
            ("Partner", "partner.csv"),
            ("PartnerContact", "partner_contact.csv"),
            ("SoortProduct", "soort_product.csv"),
            ("Product", "product.csv"),
            ("Grinding", "grinding.csv"),
            ("Grinding_Product", "grinding_product.csv"),
            ("Filling", "filling.csv"),
            ("Filling_Product", "filling_product.csv"),
            ("Packaging", "packaging.csv"),
            ("Packaging_Product", "packaging_product.csv"),
            ("Levering", "levering.csv"),
            ("Levering_Regel", "levering_regel.csv")
        ]
        
        # Importeer elk CSV-bestand in de bijbehorende tabel
        for table_name, csv_file in tables_and_files:
            import_csv_to_table(connection, csv_file, table_name)
        
        print("Alle CSV-bestanden zijn succesvol geïmporteerd in de database")
    except Exception as e:
        print(f"Fout bij importeren van CSV-bestanden: {e}")
    finally:
        # Sluit de verbinding
        if connection.is_connected():
            connection.close()
            print("Verbinding met MySQL database gesloten")

if __name__ == "__main__":
    # Controleer of de CSV-bestanden bestaan
    if not os.path.exists('csv_data'):
        print("De map 'csv_data' bestaat niet. Genereer eerst de CSV-bestanden met generate_eurocaps_csv_pandas.py")
    else:
        # Importeer alle CSV-bestanden
        import_all_csv_files()
