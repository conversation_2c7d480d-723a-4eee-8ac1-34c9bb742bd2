# Americaps Data Analysis

This project contains Python scripts for analyzing the Kikker.csv dataset from Americaps, a coffee capsule production company.

## Overview

Americaps produces coffee capsules in very large quantities. Small deviations can cause waste and delays. These scripts help analyze production and quality data to identify improvement opportunities in the production process.

## Files

- `Kikker.csv` - The original dataset containing production and quality data
- `explore_kikker.py` - Initial script to explore the dataset
- `analyze_kikker.py` - More comprehensive analysis script
- `kikker_data_processor.py` - <PERSON><PERSON><PERSON> to clean and process the dataset
- `kikker_visualizer.py` - <PERSON><PERSON><PERSON> to create visualizations from the cleaned data
- `<PERSON><PERSON>_cleaned_new.csv` - The cleaned dataset (created by running `kikker_data_processor.py`)
- `plots/` - Directory containing visualizations (created by running `kikker_visualizer.py`)

## How to Use

### 1. Initial Data Exploration

Run the initial exploration script to get a basic understanding of the dataset:

```
python explore_kikker.py
```

This script will:
- Import the CSV file
- Display basic information about the dataset
- Check for data quality issues like missing or unusual values

### 2. Comprehensive Data Analysis

Run the comprehensive analysis script for more detailed insights:

```
python analyze_kikker.py
```

This script will:
- Import the CSV file
- Display detailed information about the dataset
- Analyze data quality issues
- Provide column meanings and data types
- Show key statistics for important columns

### 3. Data Processing and Cleaning

Run the data processor script to clean the dataset:

```
python kikker_data_processor.py
```

This script will:
- Load the Kikker.csv file
- Clean the data (handle missing values, invalid formats, etc.)
- Provide a detailed exploration of the cleaned dataset
- Save the cleaned data to `Kikker_cleaned_new.csv`

### 4. Data Visualization

Run the visualization script to create visual representations of the data:

```
python kikker_visualizer.py
```

This script will:
- Load the cleaned dataset
- Create various visualizations:
  - Coffee bean distribution
  - Quality metrics
  - Machine performance
  - Cost analysis
  - Sustainability analysis
  - Correlation analysis
- Save all visualizations to the `plots/` directory

## Data Analysis Findings

The analysis of the Kikker.csv dataset reveals several key insights:

1. **Coffee Bean Types**: Arabica is the most common bean type (49.7%), followed by Robusta (29.9%), Excelsa (10.2%), and Liberica (10.1%).

2. **Quality Indicators**:
   - Average defect percentage: 2.03%
   - Average customer return percentage: 1.00%
   - Panel test results: 71.0% pass, 14.6% partially pass, 14.4% fail

3. **Production Metrics**:
   - Average production cost: 505.05 euros
   - Average cycle time: 4.12 hours
   - Average energy consumption: 276.34 kWh

4. **Sustainability Metrics**:
   - Average CO2 footprint: 5.52 kg CO2/kg
   - Average fair trade score: 49.31 (on a scale of 0-100)

5. **Machine Performance**: The analysis shows variations in defect rates and cycle times across different machines, which could indicate opportunities for process improvement.

## Requirements

- Python 3.x
- pandas
- numpy
- matplotlib
- seaborn

## Next Steps

Based on the analysis, consider:

1. Investigating machines with higher defect rates
2. Optimizing processes for coffee bean types with higher costs or CO2 footprints
3. Addressing data quality issues in the production tracking system
4. Implementing regular monitoring of key quality indicators
