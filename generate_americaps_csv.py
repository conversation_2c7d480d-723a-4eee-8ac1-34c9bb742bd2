#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Americaps CSV Generator Script

Dit script genereert CSV-bestanden met testdata voor de Americaps database.
De structuur is gebaseerd op een typisch productieproces voor capsules.

Auteur: Augment Agent
Datum: 2023-11-01
"""

import pandas as pd
import numpy as np
import datetime
import os
import random
import matplotlib.pyplot as plt
import seaborn as sns

# Maak een directory voor de CSV-bestanden als deze nog niet bestaat
if not os.path.exists('csv_data'):
    os.makedirs('csv_data')

# Hulpfunctie voor het genereren van willekeurige datums
def random_date(start_date, end_date):
    """Genereer een willekeurige datum tussen start_date en end_date"""
    time_between_dates = end_date - start_date
    days_between_dates = time_between_dates.days
    random_number_of_days = random.randrange(days_between_dates)
    return start_date + datetime.timedelta(days=random_number_of_days)

# Hulpfunctie voor het genereren van willekeurige datumtijden
def random_datetime(start_date, end_date):
    """<PERSON>reer een willekeurige datum en tijd tussen start_date en end_date"""
    random_date_value = random_date(start_date, end_date)
    random_hour = random.randint(6, 22)  # Werkuren tussen 6:00 en 22:00
    random_minute = random.randint(0, 59)
    random_second = random.randint(0, 59)
    random_datetime_value = datetime.datetime(
        random_date_value.year, 
        random_date_value.month, 
        random_date_value.day,
        random_hour,
        random_minute,
        random_second
    )
    return random_datetime_value.strftime("%Y-%m-%d %H:%M:%S")

# 1. ProductTypes data
def generate_product_types_data():
    """Genereer data voor de ProductTypes tabel met Pandas"""
    # Definieer verschillende producttypes
    product_types = [
        [1, "Energy Capsule", "Biologisch afbreekbaar plastic", 5.2, "36x22mm"],
        [2, "Vitamin Capsule", "Biologisch afbreekbaar plastic", 5.0, "36x22mm"],
        [3, "Protein Capsule", "Aluminium", 5.5, "36x22mm"],
        [4, "Hydration Capsule", "Biologisch afbreekbaar plastic", 4.8, "36x22mm"],
        [5, "Electrolyte Capsule", "Aluminium", 5.3, "36x22mm"],
        [6, "Herbal Capsule", "Biologisch afbreekbaar plastic", 4.5, "36x22mm"]
    ]
    
    # Maak een DataFrame
    df = pd.DataFrame(product_types, columns=['ProductTypeId', 'Description', 'Material', 'Weight', 'Dimensions'])
    
    # Sla op als CSV
    df.to_csv('csv_data/product_types.csv', index=False)
    
    print("ProductTypes data gegenereerd.")
    return df

# 2. Products data
def generate_products_data(product_types_df):
    """Genereer data voor de Products tabel met Pandas"""
    # Aantal producten
    n = 200
    
    # Start- en einddatum voor THT
    start_date = datetime.date(2023, 1, 1)
    end_date = datetime.date(2024, 12, 31)
    
    # Willekeurige product types
    product_type_ids = np.random.choice(product_types_df['ProductTypeId'].values, size=n)
    
    # Willekeurige THT datums
    tht_datums = [random_date(start_date, end_date).strftime("%Y-%m-%d") for _ in range(n)]
    
    # Willekeurige product statussen
    statussen = ["Geproduceerd", "In productie", "Kwaliteitscontrole", "Gereed voor verzending", "Verzonden"]
    product_statussen = np.random.choice(statussen, size=n)
    
    # Willekeurige processtatus (M, F, P of leeg)
    m_statussen = np.random.choice(["M", ""], size=n, p=[0.8, 0.2])  # M voor Mixing
    f_statussen = np.random.choice(["F", ""], size=n, p=[0.7, 0.3])  # F voor Filling
    p_statussen = np.random.choice(["P", ""], size=n, p=[0.6, 0.4])  # P voor Packaging
    
    # Maak een DataFrame
    data = {
        'ProductId': list(range(1, n+1)),
        'ProductTypeId': product_type_ids,
        'ExpiryDate': tht_datums,
        'ProductStatus': product_statussen,
        'MixingStatus': m_statussen,
        'FillingStatus': f_statussen,
        'PackagingStatus': p_statussen
    }
    
    df = pd.DataFrame(data)
    
    # Sla op als CSV
    df.to_csv('csv_data/products.csv', index=False)
    
    print("Products data gegenereerd.")
    return df

# 3. Machines data
def generate_machines_data():
    """Genereer data voor de Machines tabel met Pandas"""
    # Definieer machines
    machines = [
        [1, "Mixer-A1", "mixer", "Hal A", "2020-01-15", 90, "2023-09-15"],
        [2, "Mixer-A2", "mixer", "Hal A", "2020-01-15", 90, "2023-09-20"],
        [3, "Mixer-B1", "mixer", "Hal B", "2021-03-10", 90, "2023-10-05"],
        [4, "Filler-C1", "filler", "Hal C", "2020-02-20", 60, "2023-09-25"],
        [5, "Filler-C2", "filler", "Hal C", "2020-02-20", 60, "2023-10-10"],
        [6, "Filler-D1", "filler", "Hal D", "2021-05-15", 60, "2023-09-30"],
        [7, "Packer-E1", "packer", "Hal E", "2020-03-10", 120, "2023-08-15"],
        [8, "Packer-E2", "packer", "Hal E", "2020-03-10", 120, "2023-10-20"],
        [9, "QC-Scanner-1", "quality_control", "Hal F", "2021-01-05", 180, "2023-07-10"],
        [10, "QC-Scanner-2", "quality_control", "Hal F", "2021-01-05", 180, "2023-10-15"]
    ]
    
    # Maak een DataFrame
    df = pd.DataFrame(machines, columns=['MachineId', 'MachineName', 'MachineType', 
                                         'Location', 'InstallationDate', 'MaintenanceInterval', 
                                         'LastMaintenance'])
    
    # Sla op als CSV
    df.to_csv('csv_data/machines.csv', index=False)
    
    print("Machines data gegenereerd.")
    return df

# 4. ProductionBatches data
def generate_production_batches_data(products_df):
    """Genereer data voor de ProductionBatches tabel met Pandas"""
    # Aantal batches
    n = 50
    
    # Start- en einddatum
    start_date = datetime.datetime(2023, 1, 1)
    end_date = datetime.datetime(2023, 10, 31)
    
    # Willekeurige start datums/tijden
    start_datetimes = [random_datetime(start_date, end_date) for _ in range(n)]
    
    # Eind datums/tijden (8-24 uur na start)
    end_datetimes = []
    for start_dt in start_datetimes:
        start_dt_obj = datetime.datetime.strptime(start_dt, "%Y-%m-%d %H:%M:%S")
        hours_to_add = np.random.randint(8, 25)
        end_dt_obj = start_dt_obj + datetime.timedelta(hours=hours_to_add)
        end_datetimes.append(end_dt_obj.strftime("%Y-%m-%d %H:%M:%S"))
    
    # Willekeurige batch nummers
    batch_numbers = [f"B{2023}{i:04d}" for i in range(1, n+1)]
    
    # Willekeurige product IDs (meerdere producten per batch)
    product_ids = []
    for _ in range(n):
        # Selecteer 1-5 willekeurige product IDs voor deze batch
        num_products = np.random.randint(1, 6)
        batch_product_ids = np.random.choice(products_df['ProductId'].values, size=num_products, replace=False)
        product_ids.append(','.join(map(str, batch_product_ids)))
    
    # Willekeurige geplande en werkelijke hoeveelheden
    planned_quantities = np.random.randint(1000, 10000, size=n)
    actual_quantities = []
    for planned in planned_quantities:
        # Werkelijke hoeveelheid is meestal iets minder dan gepland (0-5% verlies)
        loss_factor = np.random.uniform(0, 0.05)
        actual = int(planned * (1 - loss_factor))
        actual_quantities.append(actual)
    
    # Willekeurige statussen
    statuses = ["planned", "in_progress", "completed", "cancelled"]
    status_weights = [0.1, 0.2, 0.6, 0.1]  # Meer completed dan andere statussen
    batch_statuses = np.random.choice(statuses, size=n, p=status_weights)
    
    # Maak een DataFrame
    data = {
        'BatchId': list(range(1, n+1)),
        'BatchNumber': batch_numbers,
        'StartDate': start_datetimes,
        'EndDate': end_datetimes,
        'ProductIds': product_ids,
        'PlannedQuantity': planned_quantities,
        'ActualQuantity': actual_quantities,
        'Status': batch_statuses
    }
    
    df = pd.DataFrame(data)
    
    # Sla op als CSV
    df.to_csv('csv_data/production_batches.csv', index=False)
    
    print("ProductionBatches data gegenereerd.")
    return df

# 5. ProductionSteps data
def generate_production_steps_data(batches_df, machines_df):
    """Genereer data voor de ProductionSteps tabel met Pandas"""
    # Lijst voor alle productiestappen
    steps_data = []
    
    # Step ID counter
    step_id = 1
    
    # Voor elke batch, genereer 3 stappen (mixing, filling, packing)
    for _, batch in batches_df.iterrows():
        batch_id = batch['BatchId']
        batch_start = datetime.datetime.strptime(batch['StartDate'], "%Y-%m-%d %H:%M:%S")
        batch_end = datetime.datetime.strptime(batch['EndDate'], "%Y-%m-%d %H:%M:%S")
        
        # Alleen stappen genereren voor batches die niet gepland of geannuleerd zijn
        if batch['Status'] not in ['planned', 'cancelled']:
            # Verdeel de batch tijd in 3 delen voor de 3 stappen
            total_hours = (batch_end - batch_start).total_seconds() / 3600
            step_hours = total_hours / 3
            
            # Mixing stap
            mixing_machines = machines_df[machines_df['MachineType'] == 'mixer']
            mixing_machine_id = np.random.choice(mixing_machines['MachineId'].values)
            mixing_start = batch_start
            mixing_end = mixing_start + datetime.timedelta(hours=step_hours)
            mixing_processed = batch['PlannedQuantity']
            mixing_rejected = np.random.randint(0, int(mixing_processed * 0.03))  # 0-3% afkeur
            
            steps_data.append({
                'StepId': step_id,
                'BatchId': batch_id,
                'StepType': 'mixing',
                'StartTime': mixing_start.strftime("%Y-%m-%d %H:%M:%S"),
                'EndTime': mixing_end.strftime("%Y-%m-%d %H:%M:%S"),
                'MachineId': mixing_machine_id,
                'EmployeeId': np.random.randint(1, 21),  # Willekeurige medewerker ID
                'QuantityProcessed': mixing_processed,
                'QuantityRejected': mixing_rejected,
                'Notes': f"Mixing step for batch {batch['BatchNumber']}"
            })
            step_id += 1
            
            # Filling stap
            filling_machines = machines_df[machines_df['MachineType'] == 'filler']
            filling_machine_id = np.random.choice(filling_machines['MachineId'].values)
            filling_start = mixing_end
            filling_end = filling_start + datetime.timedelta(hours=step_hours)
            filling_processed = mixing_processed - mixing_rejected
            filling_rejected = np.random.randint(0, int(filling_processed * 0.02))  # 0-2% afkeur
            
            steps_data.append({
                'StepId': step_id,
                'BatchId': batch_id,
                'StepType': 'filling',
                'StartTime': filling_start.strftime("%Y-%m-%d %H:%M:%S"),
                'EndTime': filling_end.strftime("%Y-%m-%d %H:%M:%S"),
                'MachineId': filling_machine_id,
                'EmployeeId': np.random.randint(1, 21),  # Willekeurige medewerker ID
                'QuantityProcessed': filling_processed,
                'QuantityRejected': filling_rejected,
                'Notes': f"Filling step for batch {batch['BatchNumber']}"
            })
            step_id += 1
            
            # Packing stap
            packing_machines = machines_df[machines_df['MachineType'] == 'packer']
            packing_machine_id = np.random.choice(packing_machines['MachineId'].values)
            packing_start = filling_end
            packing_end = packing_start + datetime.timedelta(hours=step_hours)
            packing_processed = filling_processed - filling_rejected
            packing_rejected = np.random.randint(0, int(packing_processed * 0.01))  # 0-1% afkeur
            
            steps_data.append({
                'StepId': step_id,
                'BatchId': batch_id,
                'StepType': 'packing',
                'StartTime': packing_start.strftime("%Y-%m-%d %H:%M:%S"),
                'EndTime': packing_end.strftime("%Y-%m-%d %H:%M:%S"),
                'MachineId': packing_machine_id,
                'EmployeeId': np.random.randint(1, 21),  # Willekeurige medewerker ID
                'QuantityProcessed': packing_processed,
                'QuantityRejected': packing_rejected,
                'Notes': f"Packing step for batch {batch['BatchNumber']}"
            })
            step_id += 1
    
    # Maak een DataFrame
    df = pd.DataFrame(steps_data)
    
    # Sla op als CSV
    df.to_csv('csv_data/production_steps.csv', index=False)
    
    print("ProductionSteps data gegenereerd.")
    return df

# 6. QualityControl data
def generate_quality_control_data(steps_df, machines_df):
    """Genereer data voor de QualityControl tabel met Pandas"""
    # Lijst voor alle kwaliteitscontroles
    qc_data = []
    
    # QC ID counter
    qc_id = 1
    
    # QC machines
    qc_machines = machines_df[machines_df['MachineType'] == 'quality_control']
    
    # Definieer mogelijke defecten per stap type
    defect_types = {
        'mixing': ['Uneven mixture', 'Contamination', 'Wrong ingredient ratio', 'Temperature issue'],
        'filling': ['Underfill', 'Overfill', 'Leakage', 'Seal issue', 'Foreign object'],
        'packing': ['Damaged packaging', 'Missing label', 'Wrong label', 'Incomplete seal', 'Box damage']
    }
    
    # Voor elke productiestap, genereer 0-2 kwaliteitscontroles
    for _, step in steps_df.iterrows():
        # Aantal QC checks voor deze stap (0-2)
        num_qc_checks = np.random.randint(0, 3)
        
        for _ in range(num_qc_checks):
            # Willekeurige QC machine
            qc_machine_id = np.random.choice(qc_machines['MachineId'].values)
            
            # QC tijdstip (binnen 1 uur na einde stap)
            step_end = datetime.datetime.strptime(step['EndTime'], "%Y-%m-%d %H:%M:%S")
            qc_time = step_end + datetime.timedelta(minutes=np.random.randint(5, 61))
            
            # Willekeurige steekproefgrootte (0.5-2% van verwerkte hoeveelheid)
            sample_size = int(step['QuantityProcessed'] * np.random.uniform(0.005, 0.02))
            
            # Willekeurig aantal defecten (0-5% van steekproef)
            defect_count = np.random.randint(0, int(sample_size * 0.05) + 1)
            
            # Willekeurig defect type voor deze stap
            defect_type = np.random.choice(defect_types[step['StepType']]) if defect_count > 0 else ""
            
            # QC resultaat
            result = "Pass" if defect_count == 0 else "Fail"
            
            qc_data.append({
                'QCId': qc_id,
                'StepId': step['StepId'],
                'MachineId': qc_machine_id,
                'QCDateTime': qc_time.strftime("%Y-%m-%d %H:%M:%S"),
                'SampleSize': sample_size,
                'DefectCount': defect_count,
                'DefectType': defect_type,
                'Result': result,
                'EmployeeId': np.random.randint(1, 21),  # Willekeurige medewerker ID
                'Notes': f"QC check for {step['StepType']} step"
            })
            qc_id += 1
    
    # Maak een DataFrame
    df = pd.DataFrame(qc_data)
    
    # Sla op als CSV
    df.to_csv('csv_data/quality_control.csv', index=False)
    
    print("QualityControl data gegenereerd.")
    return df

# Functie om CSV-bestanden te analyseren
def analyze_csv_data():
    """Lees en analyseer de gegenereerde CSV-bestanden"""
    print("\n=== CSV Data Analyse ===\n")
    
    # Controleer of de CSV-bestanden bestaan
    csv_files = [
        'csv_data/product_types.csv',
        'csv_data/products.csv',
        'csv_data/machines.csv',
        'csv_data/production_batches.csv',
        'csv_data/production_steps.csv',
        'csv_data/quality_control.csv'
    ]
    
    for file_path in csv_files:
        if not os.path.exists(file_path):
            print(f"Bestand {file_path} bestaat niet. Genereer eerst de data.")
            return
    
    # Lees de CSV-bestanden
    product_types_df = pd.read_csv('csv_data/product_types.csv')
    products_df = pd.read_csv('csv_data/products.csv')
    machines_df = pd.read_csv('csv_data/machines.csv')
    batches_df = pd.read_csv('csv_data/production_batches.csv')
    steps_df = pd.read_csv('csv_data/production_steps.csv')
    qc_df = pd.read_csv('csv_data/quality_control.csv')
    
    # Toon informatie over elk bestand
    print(f"ProductTypes: {len(product_types_df)} rijen, {len(product_types_df.columns)} kolommen")
    print(f"Kolommen: {', '.join(product_types_df.columns)}")
    print(product_types_df.head(3))
    print("\n")
    
    print(f"Products: {len(products_df)} rijen, {len(products_df.columns)} kolommen")
    print(f"Kolommen: {', '.join(products_df.columns)}")
    print(products_df.head(3))
    print("\n")
    
    print(f"Machines: {len(machines_df)} rijen, {len(machines_df.columns)} kolommen")
    print(f"Kolommen: {', '.join(machines_df.columns)}")
    print(machines_df.head(3))
    print("\n")
    
    print(f"ProductionBatches: {len(batches_df)} rijen, {len(batches_df.columns)} kolommen")
    print(f"Kolommen: {', '.join(batches_df.columns)}")
    print(batches_df.head(3))
    print("\n")
    
    print(f"ProductionSteps: {len(steps_df)} rijen, {len(steps_df.columns)} kolommen")
    print(f"Kolommen: {', '.join(steps_df.columns)}")
    print(steps_df.head(3))
    print("\n")
    
    print(f"QualityControl: {len(qc_df)} rijen, {len(qc_df.columns)} kolommen")
    print(f"Kolommen: {', '.join(qc_df.columns)}")
    print(qc_df.head(3))
    print("\n")
    
    # Controleer datakwaliteit
    print("=== Datakwaliteit Controle ===")
    
    # Controleer op ontbrekende waarden
    for df_name, df in [
        ("ProductTypes", product_types_df),
        ("Products", products_df),
        ("Machines", machines_df),
        ("ProductionBatches", batches_df),
        ("ProductionSteps", steps_df),
        ("QualityControl", qc_df)
    ]:
        missing_values = df.isnull().sum().sum()
        print(f"{df_name}: {missing_values} ontbrekende waarden")
    
    print("\n")
    
    # Controleer op ongewone waarden in numerieke kolommen
    print("Statistieken voor numerieke kolommen in Products:")
    print(products_df.describe())
    
    print("\nStatistieken voor numerieke kolommen in ProductionSteps:")
    print(steps_df[['QuantityProcessed', 'QuantityRejected']].describe())
    
    print("\nStatistieken voor numerieke kolommen in QualityControl:")
    print(qc_df[['SampleSize', 'DefectCount']].describe())
    
    # Visualiseer enkele interessante aspecten van de data
    # 1. Verdeling van producttypes
    plt.figure(figsize=(10, 6))
    product_type_counts = products_df['ProductTypeId'].value_counts().sort_index()
    product_type_counts.plot(kind='bar')
    plt.title('Verdeling van ProductTypes')
    plt.xlabel('ProductTypeId')
    plt.ylabel('Aantal Producten')
    plt.savefig('csv_data/product_type_distribution.png')
    
    # 2. Verdeling van productstatus
    plt.figure(figsize=(10, 6))
    status_counts = products_df['ProductStatus'].value_counts()
    status_counts.plot(kind='pie', autopct='%1.1f%%')
    plt.title('Verdeling van ProductStatus')
    plt.savefig('csv_data/product_status_distribution.png')
    
    # 3. Afkeurpercentages per stap type
    plt.figure(figsize=(12, 6))
    steps_df['RejectionRate'] = steps_df['QuantityRejected'] / steps_df['QuantityProcessed'] * 100
    sns.boxplot(x='StepType', y='RejectionRate', data=steps_df)
    plt.title('Afkeurpercentages per Stap Type')
    plt.xlabel('Stap Type')
    plt.ylabel('Afkeurpercentage (%)')
    plt.savefig('csv_data/rejection_rates.png')
    
    # 4. QC resultaten per stap type
    plt.figure(figsize=(12, 6))
    qc_result_by_step = pd.crosstab(
        qc_df['Result'], 
        steps_df.loc[qc_df['StepId'].values - 1, 'StepType'].values
    )
    qc_result_by_step.plot(kind='bar', stacked=True)
    plt.title('QC Resultaten per Stap Type')
    plt.xlabel('QC Resultaat')
    plt.ylabel('Aantal')
    plt.savefig('csv_data/qc_results_by_step.png')
    
    print("\nVisualisaties opgeslagen in de map 'csv_data'.")
    print("Analyse voltooid.")

# Hoofdfunctie om alle data te genereren
def generate_all_data():
    """Genereer alle CSV-bestanden voor de Americaps database"""
    print("Start genereren van CSV-bestanden voor Americaps database...")
    
    # Genereer data in de juiste volgorde (rekening houdend met afhankelijkheden)
    product_types_df = generate_product_types_data()
    products_df = generate_products_data(product_types_df)
    machines_df = generate_machines_data()
    batches_df = generate_production_batches_data(products_df)
    steps_df = generate_production_steps_data(batches_df, machines_df)
    generate_quality_control_data(steps_df, machines_df)
    
    print("Alle CSV-bestanden zijn succesvol gegenereerd in de map 'csv_data'.")

# Voer het script uit
if __name__ == "__main__":
    generate_all_data()
    analyze_csv_data()
