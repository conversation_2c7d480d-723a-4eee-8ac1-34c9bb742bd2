from flask import Flask, render_template, request, jsonify
import os
from dotenv import load_dotenv
from image_generator import generate_image

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Store conversation history
conversation_history = []

@app.route('/')
def index():
    """Render the main chat interface."""
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Process chat messages and generate responses."""
    data = request.json
    user_message = data.get('message', '')
    
    # Add user message to history
    conversation_history.append({"role": "user", "content": user_message})
    
    # Check if this is an image generation request
    if "generate" in user_message.lower() and ("image" in user_message.lower() or "picture" in user_message.lower()):
        # Extract the prompt from the message
        prompt = user_message.lower().replace("generate", "").replace("image", "").replace("picture", "").strip()
        if not prompt:
            response = "Please provide a description of the image you want to generate."
        else:
            try:
                # Call the image generation function
                image_url = generate_image(prompt)
                response = f"I've generated an image based on your prompt: '{prompt}'. [IMAGE:{image_url}]"
            except Exception as e:
                response = f"Sorry, I couldn't generate that image. Error: {str(e)}"
    else:
        # Regular chat response
        response = "I'm a simple chatbot that can generate images. Try asking me to 'generate an image of [your description]'."
    
    # Add bot response to history
    conversation_history.append({"role": "assistant", "content": response})
    
    return jsonify({"response": response})

if __name__ == '__main__':
    app.run(debug=True)
