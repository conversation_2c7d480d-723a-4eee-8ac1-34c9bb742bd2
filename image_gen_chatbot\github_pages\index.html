<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Image Generator Chatbot</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>AI Image Generator Chatbot</h1>
        </div>

        <div class="welcome-container" id="welcome-container">
            <h2>Free Image Generator</h2>
            <p>Welcome to the AI Image Generator Chatbot! This app uses free APIs to generate images based on your descriptions.</p>

            <div class="generation-options">
                <h3>Image Generation Options:</h3>
                <ul>
                    <li><strong>Random Images</strong>: Get high-quality random images related to your prompt</li>
                    <li><strong>AI-Generated</strong>: Use free AI services to create images (may be lower quality)</li>
                </ul>

                <div class="image-settings">
                    <h4>Image Settings:</h4>
                    <div class="setting-group">
                        <label for="image-resolution">Resolution:</label>
                        <select id="image-resolution">
                            <option value="standard">Standard (1200×800)</option>
                            <option value="hd">HD (1920×1080)</option>
                            <option value="ultrawide">Ultra Wide (3440×1440)</option>
                            <option value="4k">4K (3840×2160)</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label for="image-orientation">Orientation:</label>
                        <select id="image-orientation">
                            <option value="landscape">Landscape</option>
                            <option value="portrait">Portrait</option>
                            <option value="square">Square</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="start-button-container">
                <button id="start-chat-button" onclick="window.startChatGlobal && window.startChatGlobal(); return false;">Start Chatting</button>
                <a href="#" id="start-chat-link" style="display: inline-block; margin-top: 10px; text-decoration: none;" onclick="window.startChatGlobal && window.startChatGlobal(); return false;">Click here if button doesn't work</a>
            </div>

            <script>
                // Simplified start chat function that works without requiring the main script to be loaded
                function emergencyStartChat() {
                    console.log('Emergency start chat function called');

                    // Get elements
                    const welcomeContainer = document.getElementById('welcome-container');
                    const chatMessages = document.getElementById('chat-messages');
                    const chatInput = document.getElementById('message-form');

                    // Show chat interface
                    if (welcomeContainer) welcomeContainer.style.display = 'none';
                    if (chatMessages) chatMessages.style.display = 'block';
                    if (chatInput) chatInput.style.display = 'flex';

                    return false;
                }

                // Add emergency handlers after a delay
                setTimeout(function() {
                    const startButton = document.getElementById('start-chat-button');
                    if (startButton) {
                        // Add emergency handler as a fallback
                        startButton.addEventListener('click', function(e) {
                            console.log('Emergency click handler activated');
                            e.preventDefault();
                            if (window.startChatGlobal) {
                                window.startChatGlobal();
                            } else {
                                emergencyStartChat();
                            }
                            return false;
                        });
                    }
                }, 2000);
            </script>

            <div class="disclaimer">
                <p>Note: This app uses free public APIs with limited capabilities. For higher quality AI-generated images, consider using paid services like DALL-E, Midjourney, or Stable Diffusion.</p>
                <p>Tip: For better results, be specific in your prompts. For example, instead of "sunset", try "beautiful sunset over mountains with orange sky".</p>
            </div>
        </div>

        <div class="chat-messages" id="chat-messages" style="display: none;">
            <div class="message bot">
                <div class="message-content">
                    Hello! I'm an AI chatbot that can generate images. Try asking me to "generate an image of [your description]".
                </div>
            </div>
        </div>

        <!-- New simplified chat input using a form for better cross-browser compatibility -->
        <form id="message-form" onsubmit="handleFormSubmit(event); return false;" class="chat-input" style="display: none;">
            <input type="text" id="user-input" placeholder="Type your message here... (Press Enter to send)" autocomplete="off"
                   onkeydown="if(event.key === 'Enter') { event.preventDefault(); directSendMessage(); return false; }">
            <button type="submit" id="send-button">Send</button>
            <input type="button" id="direct-send-button" value="Send Now" onclick="directSendMessage();" style="margin-left: 5px; display: none;">
        </form>

        <!-- Extra buttons for accessibility -->
        <div id="extra-buttons" style="display: none; text-align: center; margin-top: 10px;">
            <button onclick="directSendMessage('generate cat'); return false;" style="margin: 5px; padding: 8px 15px;">Generate Cat</button>
            <button onclick="directSendMessage('generate beach'); return false;" style="margin: 5px; padding: 8px 15px;">Generate Beach</button>
            <button onclick="directSendMessage('generate sunset'); return false;" style="margin: 5px; padding: 8px 15px;">Generate Sunset</button>
            <button onclick="directSendMessage('generate cute girls with bikinis on the beach'); return false;" style="margin: 5px; padding: 8px 15px; background-color: #4a6fa5; color: white;">Beach Girls</button>
            <script>
                // Show extra buttons immediately when chat starts
                document.addEventListener('DOMContentLoaded', function() {
                    // Global function to show extra buttons
                    window.showExtraButtons = function() {
                        const extraButtons = document.getElementById('extra-buttons');
                        if (extraButtons) {
                            extraButtons.style.display = 'block';
                        }
                    };

                    // Also show after a short delay as a fallback
                    setTimeout(function() {
                        const extraButtons = document.getElementById('extra-buttons');
                        const chatInput = document.getElementById('message-form');
                        if (extraButtons && chatInput && chatInput.style.display !== 'none') {
                            extraButtons.style.display = 'block';
                        }
                    }, 1000);
                });
            </script>
        </div>

        <!-- Direct script for handling messages -->
        <script>
            // Function to download images directly
            function downloadImageDirectly(imageUrl, prompt) {
                console.log('Downloading image directly:', imageUrl);

                try {
                    // Create a temporary link element
                    const a = document.createElement('a');
                    a.href = imageUrl;
                    a.download = `image-${prompt.replace(/[^a-z0-9]/gi, '-').toLowerCase()}.jpg`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                } catch (error) {
                    console.error('Error downloading image:', error);
                    alert('Could not download the image. Try right-clicking on it and selecting "Save image as..."');
                }
            }

            // Function to handle form submission
            function handleFormSubmit(event) {
                event.preventDefault();
                console.log('Form submitted');
                directSendMessage();
                return false;
            }

            // Direct function to send messages
            function directSendMessage(customMessage) {
                console.log('Direct send message called');
                const userInput = document.getElementById('user-input');
                const chatMessages = document.getElementById('chat-messages');

                // Use custom message if provided, otherwise get from input field
                const message = customMessage || (userInput ? userInput.value.trim() : '');

                if (message) {
                    console.log('Sending message:', message);

                    // Add user message to chat
                    const userMessageDiv = document.createElement('div');
                    userMessageDiv.className = 'message user';
                    userMessageDiv.innerHTML = `<div class="message-content">${message}</div>`;
                    if (chatMessages) {
                        chatMessages.appendChild(userMessageDiv);
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    }

                    // Clear input
                    userInput.value = '';

                    // Process the message
                    processUserMessage(message);
                }
            }

            // Function to process user messages
            function processUserMessage(message) {
                console.log('Processing message:', message);

                // Check if this is an image generation request
                if (message.toLowerCase().match(/\b(generate|create|show|make|get|find|draw)\b/i)) {
                    // Extract the prompt
                    const promptMatch = message.match(/\b(generate|create|show|make|get|find|draw)\b\s+(an?|the)?\s*(image|picture|photo|pic)?\s*(of)?\s*(.*)/i);
                    let prompt = message;

                    if (promptMatch && promptMatch[5] && promptMatch[5].trim().length > 0) {
                        prompt = promptMatch[5].trim();
                    }

                    console.log('Extracted prompt:', prompt);

                    // Show loading indicator
                    const loadingIndicator = document.getElementById('loading-indicator');
                    if (loadingIndicator) {
                        loadingIndicator.style.display = 'flex';
                    }

                    // Add bot response with placeholder
                    const botMessageDiv = document.createElement('div');
                    botMessageDiv.className = 'message bot';
                    botMessageDiv.innerHTML = `<div class="message-content">Generating an image of "${prompt}"...</div>`;

                    const chatMessages = document.getElementById('chat-messages');
                    if (chatMessages) {
                        chatMessages.appendChild(botMessageDiv);
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    }

                    // Try to generate an image using our own implementation
                    generateImageDirectly(prompt, botMessageDiv);

                    // Direct image generation function that doesn't rely on external scripts
                    function generateImageDirectly(prompt, messageElement) {
                        console.log('Generating image directly for prompt:', prompt);

                        // Show loading indicator
                        const loadingIndicator = document.getElementById('loading-indicator');
                        if (loadingIndicator) {
                            loadingIndicator.style.display = 'flex';
                        }

                        // Generate a random image URL based on the prompt
                        let imageUrl;

                        // Use cat images for cat-related prompts
                        if (prompt.toLowerCase().includes('cat') || prompt.toLowerCase().includes('kitten')) {
                            const catImages = [
                                'https://images.unsplash.com/photo-1514888286974-6c03e2ca1dba',
                                'https://images.unsplash.com/photo-1573865526739-10659fec78a5',
                                'https://images.unsplash.com/photo-1495360010541-f48722b34f7d',
                                'https://images.unsplash.com/photo-1518791841217-8f162f1e1131'
                            ];
                            const randomIndex = Math.floor(Math.random() * catImages.length);
                            imageUrl = catImages[randomIndex];
                        }
                        // Use beach/people images for beach-related prompts
                        else if (prompt.toLowerCase().includes('beach')) {
                            // Special case for beach + people/girls/women
                            if (prompt.toLowerCase().match(/\b(girl|woman|women|girls|people|person|bikini|swimwear|swimsuit)\b/)) {
                                // Even more specific case for bikini/swimwear
                                if (prompt.toLowerCase().match(/\b(bikini|swimwear|swimsuit)\b/) &&
                                    prompt.toLowerCase().match(/\b(girl|woman|women|girls)\b/)) {
                                    console.log('Detected specific beach/bikini/girls prompt');
                                    const beachBikiniImages = [
                                        'https://images.unsplash.com/photo-1544961371-516024f8e267',
                                        'https://images.unsplash.com/photo-1583900985737-6d0495555783',
                                        'https://images.unsplash.com/photo-1581704906775-891dd5207444',
                                        'https://images.unsplash.com/photo-1583939003579-730e3918a45a',
                                        'https://images.unsplash.com/photo-1590739225287-bd31519780c3',
                                        'https://images.unsplash.com/photo-1437622368342-7a3d73a34c8f',
                                        'https://images.unsplash.com/photo-1534080564583-6be75777b70a',
                                        'https://images.unsplash.com/photo-1527212986666-4d2d47a80d5f',
                                        'https://images.unsplash.com/photo-1473496169904-658ba7c44d8a',
                                        'https://images.unsplash.com/photo-1527781833826-f10bcd5c58b9',
                                        'https://images.unsplash.com/photo-1531897084867-6e8c64aa3bc1'
                                    ];
                                    const randomIndex = Math.floor(Math.random() * beachBikiniImages.length);
                                    imageUrl = beachBikiniImages[randomIndex];
                                } else {
                                    const beachPeopleImages = [
                                        'https://images.unsplash.com/photo-1544961371-516024f8e267',
                                        'https://images.unsplash.com/photo-1583900985737-6d0495555783',
                                        'https://images.unsplash.com/photo-1581704906775-891dd5207444',
                                        'https://images.unsplash.com/photo-1583939003579-730e3918a45a',
                                        'https://images.unsplash.com/photo-1590739225287-bd31519780c3',
                                        'https://images.unsplash.com/photo-1437622368342-7a3d73a34c8f',
                                        'https://images.unsplash.com/photo-1534080564583-6be75777b70a'
                                    ];
                                    const randomIndex = Math.floor(Math.random() * beachPeopleImages.length);
                                    imageUrl = beachPeopleImages[randomIndex];
                                }
                            } else {
                                const beachImages = [
                                    'https://images.unsplash.com/photo-1507525428034-b723cf961d3e',
                                    'https://images.unsplash.com/photo-1519046904884-53103b34b206',
                                    'https://images.unsplash.com/photo-1535498730771-e735b998cd64',
                                    'https://images.unsplash.com/photo-1473496169904-658ba7c44d8a'
                                ];
                                const randomIndex = Math.floor(Math.random() * beachImages.length);
                                imageUrl = beachImages[randomIndex];
                            }
                        }
                        // Use sunset images for sunset-related prompts
                        else if (prompt.toLowerCase().includes('sunset')) {
                            const sunsetImages = [
                                'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b',
                                'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
                                'https://images.unsplash.com/photo-1540979388789-6cee28a1cdc9',
                                'https://images.unsplash.com/photo-1592853598064-a3a5f67e44ad'
                            ];
                            const randomIndex = Math.floor(Math.random() * sunsetImages.length);
                            imageUrl = sunsetImages[randomIndex];
                        }
                        // Use a default image for other prompts
                        else {
                            // Try to use Unsplash source API
                            imageUrl = 'https://source.unsplash.com/random/800x600/?'+encodeURIComponent(prompt);
                        }

                        // Add cache-busting parameters
                        const timestamp = Date.now();
                        imageUrl = `${imageUrl}?t=${timestamp}`;

                        // Update the message with the image after a short delay
                        setTimeout(() => {
                            if (messageElement) {
                                messageElement.innerHTML = `
                                    <div class="message-content">
                                        Here's an image of "${prompt}".
                                        <div class="image-container">
                                            <img src="${imageUrl}" alt="Image of ${prompt}" class="generated-image">
                                            <div class="image-controls">
                                                <div class="image-info">Generated image</div>
                                                <div class="image-actions">
                                                    <button class="download-button" onclick="downloadImageDirectly('${imageUrl}', '${prompt}')">Download</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            }

                            // Hide loading indicator
                            if (loadingIndicator) {
                                loadingIndicator.style.display = 'none';
                            }
                        }, 1500);
                    }

                    // Also try the global function if it exists (as a backup)
                    if (window.generateImageAndRespond) {
                        console.log('Global image generation function exists, using as backup');
                        window.generateImageAndRespond(prompt, botMessageDiv);
                    }
                } else {
                    // Regular chat response
                    const botMessageDiv = document.createElement('div');
                    botMessageDiv.className = 'message bot';
                    botMessageDiv.innerHTML = `<div class="message-content">I'm a simple chatbot that can generate images. Try asking me to 'generate an image of [your description]' or simply 'generate [subject]'.</div>`;

                    const chatMessages = document.getElementById('chat-messages');
                    if (chatMessages) {
                        chatMessages.appendChild(botMessageDiv);
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    }
                }
            }

            // Show direct send button after 3 seconds
            setTimeout(function() {
                const directSendButton = document.getElementById('direct-send-button');
                if (directSendButton) {
                    directSendButton.style.display = 'inline-block';
                }
            }, 3000);
        </script>

        <div class="loading-indicator" id="loading-indicator" style="display: none;">
            <div class="spinner"></div>
            <p>Generating image... This may take a few seconds.</p>
        </div>

        <!-- Debug panel that appears if buttons don't work -->
        <div id="debug-panel" style="display: none; position: fixed; bottom: 10px; right: 10px; background: #f0f4f8; border: 1px solid #ccc; padding: 10px; border-radius: 5px; z-index: 1000;">
            <h4>Debug Controls</h4>
            <p>If buttons don't work, use these:</p>
            <button onclick="window.sendMessageGlobal && window.sendMessageGlobal('generate cat'); return false;">Test: Generate Cat</button>
            <button onclick="window.sendMessageGlobal && window.sendMessageGlobal('generate beach'); return false;">Test: Generate Beach</button>
            <button onclick="document.getElementById('debug-panel').style.display = 'none'; return false;">Close</button>

            <script>
                // Show debug panel after 10 seconds if chat is visible but no messages sent
                setTimeout(function() {
                    const chatInput = document.getElementById('chat-input');
                    const chatMessages = document.getElementById('chat-messages');
                    const debugPanel = document.getElementById('debug-panel');

                    if (chatInput && chatInput.style.display !== 'none' &&
                        chatMessages && chatMessages.children.length <= 1 &&
                        debugPanel) {
                        debugPanel.style.display = 'block';
                    }
                }, 10000);
            </script>
        </div>
    </div>

    <script src="js/script.js"></script>
</body>
</html>
