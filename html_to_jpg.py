import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# HTML bestanden die we willen converteren
html_files = [
    "superhelden_many_to_many.html",
    "normalisatie_3nf_voorbeeld.html",
    "recursieve_relatie_voorbeeld.html",
    "erd_types_vergelijking.html"
]

# Configureer Chrome opties
chrome_options = Options()
chrome_options.add_argument("--headless")  # Run in headless mode
chrome_options.add_argument("--window-size=1000,1500")  # Set window size

# Initialiseer de webdriver
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

# Converteer elk HTML bestand naar JPG
for html_file in html_files:
    # Volledige pad naar het HTML bestand
    html_path = os.path.abspath(html_file)
    
    # Converteer naar file:// URL
    url = f"file:///{html_path}"
    
    # Bestandsnaam voor de JPG
    jpg_file = html_file.replace(".html", ".jpg")
    
    print(f"Converting {html_file} to {jpg_file}...")
    
    # Open de HTML in de browser
    driver.get(url)
    
    # Wacht even zodat de pagina volledig kan laden
    time.sleep(2)
    
    # Neem een screenshot
    driver.save_screenshot(jpg_file)
    
    print(f"Saved {jpg_file}")

# Sluit de browser
driver.quit()

print("Conversion complete!")
