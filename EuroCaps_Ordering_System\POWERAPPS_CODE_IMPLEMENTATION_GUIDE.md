# 🎯 POWERAPPS CODE IMPLEMENTATIE HANDLEIDING
## Stap-voor-stap instructies waar elke code moet worden ingevuld

---

## 📱 **APP CONFIGURATIE (EERST DOEN)**

### **🔧 App OnStart - Globale Variabelen en Data**
**Waar:** App → OnStart eigenschap

```powerapps
// Gebruikers authenticatie variabelen
Set(varUserRole, "");
Set(varUserName, "");
Set(varRememberMe, false);

// Order management variabelen
Set(varCurrentOrder, {Items: []});
Set(varSelectedCustomer, Blank());
Set(varSelectedSupplier, Blank());
Set(varSelectedMaterial, Blank());

// Zoek en filter variabelen
Set(varSearchText, "");
Set(varFilterStatus, "All");
Set(varSortBy, "Name");
Set(varProductSearch, "");
Set(varProductType, "All");
Set(varPackageSize, "All");
Set(varMaterialSearch, "");
Set(varMaterialType, "All");
Set(varSupplierSearch, "");
Set(varStakeholderSearch, "");
Set(varStakeholderType, "All");

// Sample data collections (vervang later met echte Excel connecties)
ClearCollect(colCustomers,
    {CustomerID: 1, CustomerName: "Coffee World", ContactPerson: "David Lee", Email: "<EMAIL>", Phone: "+31 20 123 4567", Address: "Koffieweg 10, Amsterdam", Status: "Active", StakeholderID: 1, CreditLimit: 10000, PaymentTerms: "30 days"},
    {CustomerID: 2, CustomerName: "Bean Lovers", ContactPerson: "John Smith", Email: "<EMAIL>", Phone: "+31 30 234 5678", Address: "Bonenstraat 25, Utrecht", Status: "Active", StakeholderID: 2, CreditLimit: 15000, PaymentTerms: "45 days"},
    {CustomerID: 3, CustomerName: "Café Express", ContactPerson: "Maria Garcia", Email: "<EMAIL>", Phone: "+31 10 345 6789", Address: "Espressolaan 5, Rotterdam", Status: "Active", StakeholderID: 0, CreditLimit: 8000, PaymentTerms: "30 days"}
);

ClearCollect(colProducts,
    {ProductID: 1, ProductName: "Espresso Classic", ProductType: "espresso", PackageSize: 10, Description: "Traditional Italian-style espresso", Price: 4.99, Status: "Active"},
    {ProductID: 2, ProductName: "Lungo Intense", ProductType: "lungo", PackageSize: 20, Description: "Rich and intense lungo", Price: 8.99, Status: "Active"},
    {ProductID: 3, ProductName: "Ristretto Strong", ProductType: "ristretto", PackageSize: 10, Description: "Extra strong ristretto", Price: 5.49, Status: "Active"},
    {ProductID: 4, ProductName: "Vanilla Flavored", ProductType: "flavored", PackageSize: 20, Description: "Smooth vanilla flavored coffee", Price: 9.99, Status: "Active"},
    {ProductID: 5, ProductName: "Caramel Delight", ProductType: "flavored", PackageSize: 20, Description: "Rich caramel flavored coffee", Price: 10.49, Status: "Active"}
);

ClearCollect(colOrders,
    {OrderID: 1, OrderNumber: "ORD-1089", CustomerID: 1, OrderDate: Today()-1, DeliveryDate: Today()+3, Status: "new", Notes: "Rush order", TotalAmount: 49.90, MaterialsReserved: false},
    {OrderID: 2, OrderNumber: "ORD-1088", CustomerID: 2, OrderDate: Today()-2, DeliveryDate: Today()+4, Status: "processing", Notes: "Standard delivery", TotalAmount: 89.90, MaterialsReserved: true},
    {OrderID: 3, OrderNumber: "ORD-1087", CustomerID: 3, OrderDate: Today()-3, DeliveryDate: Today()+2, Status: "shipped", Notes: "Express delivery", TotalAmount: 54.90, MaterialsReserved: true}
);

ClearCollect(colRawMaterials,
    {MaterialID: 1, MaterialName: "Arabica Coffee Beans - Premium", MaterialType: "Coffee", SupplierID: 1, StockLevel: 500, UnitCost: 12.50, ReorderPoint: 100, Unit: "kg", QualityGrade: "A", LastUpdated: Today()},
    {MaterialID: 2, MaterialName: "Robusta Coffee Beans - Strong", MaterialType: "Coffee", SupplierID: 1, StockLevel: 300, UnitCost: 10.75, ReorderPoint: 75, Unit: "kg", QualityGrade: "A", LastUpdated: Today()},
    {MaterialID: 3, MaterialName: "Aluminum Capsules - Standard", MaterialType: "Packaging", SupplierID: 2, StockLevel: 10000, UnitCost: 0.15, ReorderPoint: 2000, Unit: "pieces", QualityGrade: "A", LastUpdated: Today()},
    {MaterialID: 4, MaterialName: "Aluminum Capsules - Premium", MaterialType: "Packaging", SupplierID: 2, StockLevel: 5000, UnitCost: 0.18, ReorderPoint: 1000, Unit: "pieces", QualityGrade: "A+", LastUpdated: Today()},
    {MaterialID: 5, MaterialName: "Cardboard Boxes - 10 pack", MaterialType: "Packaging", SupplierID: 3, StockLevel: 1500, UnitCost: 0.75, ReorderPoint: 300, Unit: "pieces", QualityGrade: "B", LastUpdated: Today()},
    {MaterialID: 6, MaterialName: "Cardboard Boxes - 20 pack", MaterialType: "Packaging", SupplierID: 3, StockLevel: 800, UnitCost: 1.25, ReorderPoint: 150, Unit: "pieces", QualityGrade: "B", LastUpdated: Today()},
    {MaterialID: 7, MaterialName: "Vanilla Flavoring", MaterialType: "Flavoring", SupplierID: 4, StockLevel: 50, UnitCost: 25.00, ReorderPoint: 10, Unit: "liters", QualityGrade: "A+", LastUpdated: Today()},
    {MaterialID: 8, MaterialName: "Caramel Flavoring", MaterialType: "Flavoring", SupplierID: 4, StockLevel: 30, UnitCost: 28.00, ReorderPoint: 8, Unit: "liters", QualityGrade: "A", LastUpdated: Today()}
);

ClearCollect(colSuppliers,
    {SupplierID: 1, SupplierName: "Premium Coffee Co.", ContactPerson: "Maria Santos", Email: "<EMAIL>", Phone: "+31 20 555 0101", Address: "Koffiestraat 15, 1012 Amsterdam", Country: "Netherlands", PaymentTerms: "30 days", DeliveryTerms: "FOB", Rating: 4.8, Status: "Active", LastOrder: Today()-5},
    {SupplierID: 2, SupplierName: "Aluminum Solutions BV", ContactPerson: "Jan de Vries", Email: "<EMAIL>", Phone: "+31 30 555 0202", Address: "Industrieweg 42, 3542 Utrecht", Country: "Netherlands", PaymentTerms: "45 days", DeliveryTerms: "DDP", Rating: 4.6, Status: "Active", LastOrder: Today()-3},
    {SupplierID: 3, SupplierName: "PackTech Europe", ContactPerson: "Sophie Mueller", Email: "<EMAIL>", Phone: "+49 30 555 0303", Address: "Verpackungsallee 8, 10115 Berlin", Country: "Germany", PaymentTerms: "30 days", DeliveryTerms: "FOB", Rating: 4.2, Status: "Active", LastOrder: Today()-7},
    {SupplierID: 4, SupplierName: "FlavorMasters International", ContactPerson: "Pierre Dubois", Email: "<EMAIL>", Phone: "+33 1 555 0404", Address: "Rue des Arômes 25, 75001 Paris", Country: "France", PaymentTerms: "60 days", DeliveryTerms: "EXW", Rating: 4.9, Status: "Active", LastOrder: Today()-2}
);

ClearCollect(colStakeholders,
    {StakeholderID: 1, Name: "Coffee World", Type: "Customer", ContactPerson: "David Lee", Email: "<EMAIL>", Phone: "+31 20 123 4567", Department: "Procurement", AccessLevel: "Standard", Status: "Active", LastContact: Today()-1},
    {StakeholderID: 2, Name: "Bean Lovers", Type: "Customer", ContactPerson: "John Smith", Email: "<EMAIL>", Phone: "+31 30 234 5678", Department: "Purchasing", AccessLevel: "Standard", Status: "Active", LastContact: Today()-3},
    {StakeholderID: 3, Name: "Premium Coffee Co.", Type: "Supplier", ContactPerson: "Maria Santos", Email: "<EMAIL>", Phone: "+31 20 555 0101", Department: "Sales", AccessLevel: "Supplier", Status: "Active", LastContact: Today()-5},
    {StakeholderID: 4, Name: "EuroCaps Management", Type: "Internal", ContactPerson: "Director", Email: "<EMAIL>", Phone: "+31 20 999 0001", Department: "Management", AccessLevel: "Admin", Status: "Active", LastContact: Today()},
    {StakeholderID: 5, Name: "EuroCaps Production", Type: "Internal", ContactPerson: "Production Manager", Email: "<EMAIL>", Phone: "+31 20 999 0002", Department: "Production", AccessLevel: "Manager", Status: "Active", LastContact: Today()},
    {StakeholderID: 6, Name: "EuroCaps Quality Control", Type: "Internal", ContactPerson: "QC Manager", Email: "<EMAIL>", Phone: "+31 20 999 0003", Department: "Quality", AccessLevel: "Manager", Status: "Active", LastContact: Today()}
)
```

---

## 🔐 **1. LOGIN SCREEN IMPLEMENTATIE**

### **Scherm Eigenschappen:**
**Waar:** Login Screen → Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```

**Waar:** Login Screen → OnVisible eigenschap
```powerapps
Set(varUserRole, "");
Set(varUserName, "");
Set(varRememberMe, false);
If(
    !IsBlank(Get("RememberedUser")),
    Set(varRememberMe, true);
    UpdateContext({ctxRememberedUser: Get("RememberedUser")})
)
```

### **Controls toevoegen en configureren:**

#### **1. Background Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```
**Waar:** Height eigenschap: `768`
**Waar:** Width eigenschap: `1366`
**Waar:** X eigenschap: `0`
**Waar:** Y eigenschap: `0`

#### **2. Logo Image**
**Toevoegen:** Insert → Media → Image
**Waar:** Height eigenschap: `150`
**Waar:** Width eigenschap: `150`
**Waar:** X eigenschap: `608`
**Waar:** Y eigenschap: `80`

#### **3. App Title Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"Coffee Capsule Ordering System"
```
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Font eigenschap: `Font.Arial`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `18`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `400`
**Waar:** X eigenschap: `483`
**Waar:** Y eigenschap: `250`
**Waar:** Align eigenschap: `Align.Center`

#### **4. Login Panel Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** Height eigenschap: `350`
**Waar:** Width eigenschap: `400`
**Waar:** X eigenschap: `483`
**Waar:** Y eigenschap: `300`
**Waar:** BorderColor eigenschap
```powerapps
RGBA(169, 198, 232, 1)
```
**Waar:** BorderThickness eigenschap: `2`

#### **5. Username Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"Username:"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Height eigenschap: `25`
**Waar:** Width eigenschap: `100`
**Waar:** X eigenschap: `503`
**Waar:** Y eigenschap: `330`

#### **6. Username Input**
**Toevoegen:** Insert → Input → Text input
**Waar:** Default eigenschap
```powerapps
If(varRememberMe, ctxRememberedUser, "")
```
**Waar:** HintText eigenschap: `"Enter your username"`
**Waar:** BorderColor eigenschap
```powerapps
RGBA(169, 198, 232, 1)
```
**Waar:** HoverBorderColor eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `360`
**Waar:** X eigenschap: `503`
**Waar:** Y eigenschap: `355`

#### **7. Password Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"Password:"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Height eigenschap: `25`
**Waar:** Width eigenschap: `100`
**Waar:** X eigenschap: `503`
**Waar:** Y eigenschap: `410`

#### **8. Password Input**
**Toevoegen:** Insert → Input → Text input
**Waar:** Mode eigenschap: `TextMode.Password`
**Waar:** HintText eigenschap: `"Enter your password"`
**Waar:** BorderColor eigenschap
```powerapps
RGBA(169, 198, 232, 1)
```
**Waar:** HoverBorderColor eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `360`
**Waar:** X eigenschap: `503`
**Waar:** Y eigenschap: `435`

#### **9. Remember Me Checkbox**
**Toevoegen:** Insert → Input → Check box
**Waar:** Text eigenschap: `"Remember me"`
**Waar:** Default eigenschap
```powerapps
varRememberMe
```
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** CheckboxBorderColor eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** CheckmarkFill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** Height eigenschap: `30`
**Waar:** Width eigenschap: `150`
**Waar:** X eigenschap: `503`
**Waar:** Y eigenschap: `490`
**Waar:** OnCheck eigenschap
```powerapps
Set(varRememberMe, true)
```
**Waar:** OnUncheck eigenschap
```powerapps
Set(varRememberMe, false)
```

#### **10. Login Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"LOGIN"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Fill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** HoverFill eigenschap
```powerapps
RGBA(230, 140, 5, 1)
```
**Waar:** PressedFill eigenschap
```powerapps
RGBA(200, 120, 0, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `14`
**Waar:** Height eigenschap: `45`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `503`
**Waar:** Y eigenschap: `535`
**Waar:** OnSelect eigenschap
```powerapps
If(
    !IsBlank(TextInput1.Text) && !IsBlank(TextInput2.Text),
    Switch(
        Lower(TextInput1.Text),
        "sales", Set(varUserRole, "Sales Representative"); Set(varUserName, TextInput1.Text),
        "service", Set(varUserRole, "Customer Service"); Set(varUserName, TextInput1.Text),
        "manager", Set(varUserRole, "Manager"); Set(varUserName, TextInput1.Text),
        "admin", Set(varUserRole, "Admin"); Set(varUserName, TextInput1.Text),
        Set(varUserRole, "Sales Representative"); Set(varUserName, TextInput1.Text)
    );
    If(CheckBox1.Value, Set("RememberedUser", TextInput1.Text), Remove("RememberedUser"));
    Navigate(Dashboard_Screen, ScreenTransition.Fade);
    Notify("Welcome " & varUserName & " (" & varUserRole & ")", NotificationType.Success),
    Notify("Please enter both username and password", NotificationType.Error)
)
```

**BELANGRIJK:** Vervang `TextInput1`, `TextInput2`, en `CheckBox1` met de werkelijke namen van je controls!

---

## 📊 **2. DASHBOARD SCREEN IMPLEMENTATIE**

### **Scherm Eigenschappen:**
**Waar:** Dashboard Screen → Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```

**Waar:** Dashboard Screen → OnVisible eigenschap
```powerapps
// Refresh data collections
Refresh(colCustomers);
Refresh(colProducts);
Refresh(colOrders);
Refresh(colRawMaterials);
Refresh(colSuppliers)
```

### **Controls toevoegen en configureren:**

#### **1. Header Bar Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** Height eigenschap: `60`
**Waar:** Width eigenschap: `1366`
**Waar:** X eigenschap: `0`
**Waar:** Y eigenschap: `0`

#### **2. Header Logo**
**Toevoegen:** Insert → Media → Image
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `40`
**Waar:** X eigenschap: `20`
**Waar:** Y eigenschap: `10`

#### **3. Header Title Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"EuroCaps Order Management Pro"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `16`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `300`
**Waar:** X eigenschap: `70`
**Waar:** Y eigenschap: `10`

#### **4. User Menu Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap
```powerapps
varUserName & " (" & varUserRole & ") ▼"
```
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Fill eigenschap
```powerapps
RGBA(0, 0, 0, 0)
```
**Waar:** BorderColor eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `1100`
**Waar:** Y eigenschap: `10`

#### **5. Navigation Menu Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** Height eigenschap: `708`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `0`
**Waar:** Y eigenschap: `60`

#### **6. Menu Dashboard Button (Active)**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"≡ Dashboard"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Fill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** Height eigenschap: `50`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `0`
**Waar:** Y eigenschap: `60`
**Waar:** Align eigenschap: `Align.Left`

#### **7. Menu Customers Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"👥 Customers"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Fill eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** HoverFill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** Height eigenschap: `50`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `0`
**Waar:** Y eigenschap: `110`
**Waar:** Align eigenschap: `Align.Left`
**Waar:** OnSelect eigenschap
```powerapps
Navigate(Customer_List_Screen, ScreenTransition.Fade)
```

#### **8. Menu Products Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"📦 Products"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Fill eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** HoverFill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** Height eigenschap: `50`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `0`
**Waar:** Y eigenschap: `160`
**Waar:** Align eigenschap: `Align.Left`
**Waar:** OnSelect eigenschap
```powerapps
Navigate(Product_Catalog_Screen, ScreenTransition.Fade)
```

#### **9. Menu Raw Materials Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"🏭 Raw Materials"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Fill eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** HoverFill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** Height eigenschap: `50`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `0`
**Waar:** Y eigenschap: `210`
**Waar:** Align eigenschap: `Align.Left`
**Waar:** OnSelect eigenschap
```powerapps
Navigate(Raw_Materials_Screen, ScreenTransition.Fade)
```

#### **10. Menu Suppliers Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"🚚 Suppliers"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Fill eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** HoverFill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** Height eigenschap: `50`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `0`
**Waar:** Y eigenschap: `260`
**Waar:** Align eigenschap: `Align.Left`
**Waar:** OnSelect eigenschap
```powerapps
Navigate(Supplier_Management_Screen, ScreenTransition.Fade)
```

#### **11. Menu Orders Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"📋 Orders"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Fill eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** HoverFill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** Height eigenschap: `50`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `0`
**Waar:** Y eigenschap: `310`
**Waar:** Align eigenschap: `Align.Left`
**Waar:** OnSelect eigenschap
```powerapps
Navigate(Order_History_Screen, ScreenTransition.Fade)
```

#### **12. Main Content Area Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```
**Waar:** Height eigenschap: `708`
**Waar:** Width eigenschap: `1166`
**Waar:** X eigenschap: `200`
**Waar:** Y eigenschap: `60`

#### **13. Page Title Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"Dashboard - " & varUserRole
```
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `18`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `300`
**Waar:** X eigenschap: `220`
**Waar:** Y eigenschap: `80`

#### **14. New Orders Card Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap
```powerapps
RGBA(169, 198, 232, 1)
```
**Waar:** BorderColor eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `120`
**Waar:** Width eigenschap: `250`
**Waar:** X eigenschap: `220`
**Waar:** Y eigenschap: `140`

#### **15. New Orders Title Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"NEW ORDERS"`
**Waar:** Color eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `12`
**Waar:** Height eigenschap: `25`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `245`
**Waar:** Y eigenschap: `155`

#### **16. New Orders Count Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
CountRows(Filter(colOrders, Status = "new"))
```
**Waar:** Color eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `24`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `100`
**Waar:** X eigenschap: `320`
**Waar:** Y eigenschap: `185`
**Waar:** Align eigenschap: `Align.Center`

#### **17. Total Customers Card Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap
```powerapps
RGBA(169, 198, 232, 1)
```
**Waar:** BorderColor eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `120`
**Waar:** Width eigenschap: `250`
**Waar:** X eigenschap: `490`
**Waar:** Y eigenschap: `140`

#### **18. Total Customers Title Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"TOTAL CUSTOMERS"`
**Waar:** Color eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `12`
**Waar:** Height eigenschap: `25`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `515`
**Waar:** Y eigenschap: `155`

#### **19. Total Customers Count Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
CountRows(colCustomers)
```
**Waar:** Color eigenschap
```powerapps
RGBA(40, 167, 69, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `24`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `100`
**Waar:** X eigenschap: `590`
**Waar:** Y eigenschap: `185`
**Waar:** Align eigenschap: `Align.Center`

#### **20. Low Stock Alert Card Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap
```powerapps
RGBA(169, 198, 232, 1)
```
**Waar:** BorderColor eigenschap
```powerapps
If(CountRows(Filter(colRawMaterials, StockLevel <= ReorderPoint)) > 0, RGBA(220, 53, 69, 1), RGBA(243, 156, 18, 1))
```
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `120`
**Waar:** Width eigenschap: `250`
**Waar:** X eigenschap: `760`
**Waar:** Y eigenschap: `140`

#### **21. Low Stock Alert Title Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"LOW STOCK ALERTS"`
**Waar:** Color eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `12`
**Waar:** Height eigenschap: `25`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `785`
**Waar:** Y eigenschap: `155`

#### **22. Low Stock Alert Count Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
CountRows(Filter(colRawMaterials, StockLevel <= ReorderPoint))
```
**Waar:** Color eigenschap
```powerapps
If(CountRows(Filter(colRawMaterials, StockLevel <= ReorderPoint)) > 0, RGBA(220, 53, 69, 1), RGBA(40, 167, 69, 1))
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `24`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `100`
**Waar:** X eigenschap: `860`
**Waar:** Y eigenschap: `185`
**Waar:** Align eigenschap: `Align.Center`

#### **23. Total Revenue Card Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap
```powerapps
RGBA(169, 198, 232, 1)
```
**Waar:** BorderColor eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `120`
**Waar:** Width eigenschap: `250`
**Waar:** X eigenschap: `1030`
**Waar:** Y eigenschap: `140`

#### **24. Total Revenue Title Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"TOTAL REVENUE"`
**Waar:** Color eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `12`
**Waar:** Height eigenschap: `25`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `1055`
**Waar:** Y eigenschap: `155`

#### **25. Total Revenue Amount Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"€" & Text(Sum(colOrders, TotalAmount), "0,000")
```
**Waar:** Color eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `20`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `200`
**Waar:** X eigenschap: `1055`
**Waar:** Y eigenschap: `185`
**Waar:** Align eigenschap: `Align.Center`

---

## 🏭 **3. RAW MATERIALS MANAGEMENT SCREEN**

### **Nieuwe Screen Maken:**
1. **Insert → New screen → Blank**
2. **Hernoem naar:** `Raw_Materials_Screen`

### **Scherm Eigenschappen:**
**Waar:** Raw Materials Screen → Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```

**Waar:** Raw Materials Screen → OnVisible eigenschap
```powerapps
Set(varMaterialSearch, "");
Set(varMaterialType, "All");
Set(varSupplierFilter, "All");
Set(varStockFilter, "All");
Set(varSelectedMaterial, Blank());
Refresh(colRawMaterials);
Refresh(colSuppliers);
Set(varReorderAlerts, Filter(colRawMaterials, StockLevel <= ReorderPoint))
```

### **Controls toevoegen (gebruik dezelfde header als Dashboard):**

#### **1-12. Header en Navigation Menu**
**Kopieer exact dezelfde header en menu controls van Dashboard Screen**
**BELANGRIJK:** Verander alleen de actieve menu button:

**Menu Raw Materials Button (Active):**
**Waar:** Fill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```

**Alle andere menu buttons:**
**Waar:** Fill eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```

#### **13. Page Title Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"Raw Materials Management"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `18`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `300`
**Waar:** X eigenschap: `220`
**Waar:** Y eigenschap: `80`

#### **14. Reorder Alerts Panel Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap
```powerapps
RGBA(220, 53, 69, 1)
```
**Waar:** BorderColor eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `50`
**Waar:** Width eigenschap: `1100`
**Waar:** X eigenschap: `220`
**Waar:** Y eigenschap: `130`
**Waar:** Visible eigenschap
```powerapps
CountRows(varReorderAlerts) > 0
```

#### **15. Reorder Alerts Text Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"⚠️ REORDER ALERT: " & CountRows(varReorderAlerts) & " materials below reorder point!"
```
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `14`
**Waar:** Height eigenschap: `30`
**Waar:** Width eigenschap: `800`
**Waar:** X eigenschap: `240`
**Waar:** Y eigenschap: `140`
**Waar:** Visible eigenschap
```powerapps
CountRows(varReorderAlerts) > 0
```

#### **16. View Alerts Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"View Details"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Fill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** HoverFill eigenschap
```powerapps
RGBA(230, 140, 5, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `12`
**Waar:** Height eigenschap: `30`
**Waar:** Width eigenschap: `120`
**Waar:** X eigenschap: `1180`
**Waar:** Y eigenschap: `140`
**Waar:** Visible eigenschap
```powerapps
CountRows(varReorderAlerts) > 0
```
**Waar:** OnSelect eigenschap
```powerapps
Set(varMaterialType, "Reorder"); Set(varStockFilter, "Low Stock")
```

#### **17. Search Filter Section Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap
```powerapps
RGBA(169, 198, 232, 1)
```
**Waar:** BorderColor eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `80`
**Waar:** Width eigenschap: `1100`
**Waar:** X eigenschap: `220`
**Waar:** Y eigenschap
```powerapps
If(CountRows(varReorderAlerts) > 0, 190, 140)
```

#### **18. Material Search Input**
**Toevoegen:** Insert → Input → Text input
**Waar:** Default eigenschap
```powerapps
varMaterialSearch
```
**Waar:** HintText eigenschap: `"Search materials by name..."`
**Waar:** BorderColor eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** Height eigenschap: `35`
**Waar:** Width eigenschap: `250`
**Waar:** X eigenschap: `240`
**Waar:** Y eigenschap
```powerapps
If(CountRows(varReorderAlerts) > 0, 205, 155)
```
**Waar:** OnChange eigenschap
```powerapps
Set(varMaterialSearch, Self.Text)
```

#### **19. Material Type Filter Dropdown**
**Toevoegen:** Insert → Input → Drop down
**Waar:** Items eigenschap
```powerapps
["All", "Coffee", "Packaging", "Flavoring"]
```
**Waar:** DefaultSelectedItems eigenschap
```powerapps
["All"]
```
**Waar:** BorderColor eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** Height eigenschap: `35`
**Waar:** Width eigenschap: `120`
**Waar:** X eigenschap: `510`
**Waar:** Y eigenschap
```powerapps
If(CountRows(varReorderAlerts) > 0, 205, 155)
```
**Waar:** OnChange eigenschap
```powerapps
Set(varMaterialType, Self.Selected.Value)
```

#### **20. Stock Filter Dropdown**
**Toevoegen:** Insert → Input → Drop down
**Waar:** Items eigenschap
```powerapps
["All", "In Stock", "Low Stock", "Out of Stock"]
```
**Waar:** DefaultSelectedItems eigenschap
```powerapps
["All"]
```
**Waar:** BorderColor eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** Height eigenschap: `35`
**Waar:** Width eigenschap: `120`
**Waar:** X eigenschap: `650`
**Waar:** Y eigenschap
```powerapps
If(CountRows(varReorderAlerts) > 0, 205, 155)
```
**Waar:** OnChange eigenschap
```powerapps
Set(varStockFilter, Self.Selected.Value)

#### **21. Reset Filters Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"Reset"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Fill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** HoverFill eigenschap
```powerapps
RGBA(230, 140, 5, 1)
```
**Waar:** Height eigenschap: `35`
**Waar:** Width eigenschap: `80`
**Waar:** X eigenschap: `790`
**Waar:** Y eigenschap
```powerapps
If(CountRows(varReorderAlerts) > 0, 205, 155)
```
**Waar:** OnSelect eigenschap
```powerapps
Set(varMaterialSearch, "");
Set(varMaterialType, "All");
Set(varStockFilter, "All");
Reset(TextInput1_1);
Reset(Dropdown1_1);
Reset(Dropdown2_1)
```

#### **22. New Material Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"+ New Material"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Fill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** HoverFill eigenschap
```powerapps
RGBA(230, 140, 5, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Height eigenschap: `35`
**Waar:** Width eigenschap: `130`
**Waar:** X eigenschap: `1170`
**Waar:** Y eigenschap
```powerapps
If(CountRows(varReorderAlerts) > 0, 205, 155)
```

#### **23. Materials Gallery**
**Toevoegen:** Insert → Layout → Vertical gallery
**Waar:** Items eigenschap
```powerapps
Filter(
    AddColumns(
        colRawMaterials,
        "SupplierName", LookUp(colSuppliers, SupplierID = ThisRecord.SupplierID).SupplierName,
        "StockStatus",
        If(StockLevel <= 0, "Out of Stock",
           If(StockLevel <= ReorderPoint, "Low Stock", "In Stock"))
    ),
    (IsBlank(varMaterialSearch) || varMaterialSearch in MaterialName) &&
    (varMaterialType = "All" || MaterialType = varMaterialType) &&
    (varStockFilter = "All" || StockStatus = varStockFilter)
)
```
**Waar:** Height eigenschap: `400`
**Waar:** Width eigenschap: `1100`
**Waar:** X eigenschap: `220`
**Waar:** Y eigenschap
```powerapps
If(CountRows(varReorderAlerts) > 0, 290, 240)
```
**Waar:** TemplateSize eigenschap: `80`
**Waar:** ShowScrollbar eigenschap: `true`

### **Gallery Template Configuratie:**
**Selecteer de Gallery en ga naar Template bewerken**

#### **24. Material Card Rectangle (in Gallery Template)**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap
```powerapps
RGBA(169, 198, 232, 1)
```
**Waar:** BorderColor eigenschap
```powerapps
Switch(
    ThisItem.StockStatus,
    "Out of Stock", RGBA(220, 53, 69, 1),
    "Low Stock", RGBA(255, 193, 7, 1),
    "In Stock", RGBA(243, 156, 18, 1)
)
```
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `70`
**Waar:** Width eigenschap: `1080`
**Waar:** X eigenschap: `10`
**Waar:** Y eigenschap: `5`

#### **25. Material Name Label (in Gallery Template)**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
ThisItem.MaterialName
```
**Waar:** Color eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `14`
**Waar:** Height eigenschap: `25`
**Waar:** Width eigenschap: `250`
**Waar:** X eigenschap: `20`
**Waar:** Y eigenschap: `15`

#### **26. Material Type Label (in Gallery Template)**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
ThisItem.MaterialType
```
**Waar:** Color eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** Size eigenschap: `12`
**Waar:** Height eigenschap: `20`
**Waar:** Width eigenschap: `100`
**Waar:** X eigenschap: `20`
**Waar:** Y eigenschap: `40`

#### **27. Supplier Name Label (in Gallery Template)**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
ThisItem.SupplierName
```
**Waar:** Color eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** Size eigenschap: `12`
**Waar:** Height eigenschap: `20`
**Waar:** Width eigenschap: `150`
**Waar:** X eigenschap: `280`
**Waar:** Y eigenschap: `25`

#### **28. Stock Level Label (in Gallery Template)**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
ThisItem.StockLevel & " " & ThisItem.Unit
```
**Waar:** Color eigenschap
```powerapps
Switch(
    ThisItem.StockStatus,
    "Out of Stock", RGBA(220, 53, 69, 1),
    "Low Stock", RGBA(255, 193, 7, 1),
    "In Stock", RGBA(40, 167, 69, 1)
)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `12`
**Waar:** Height eigenschap: `20`
**Waar:** Width eigenschap: `100`
**Waar:** X eigenschap: `450`
**Waar:** Y eigenschap: `25`

#### **29. Unit Cost Label (in Gallery Template)**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"€" & Text(ThisItem.UnitCost, "0.00")
```
**Waar:** Color eigenschap
```powerapps
RGBA(44, 62, 80, 1)
```
**Waar:** Size eigenschap: `12`
**Waar:** Height eigenschap: `20`
**Waar:** Width eigenschap: `80`
**Waar:** X eigenschap: `570`
**Waar:** Y eigenschap: `25`

#### **30. Stock Status Label (in Gallery Template)**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
ThisItem.StockStatus
```
**Waar:** Color eigenschap
```powerapps
Switch(
    ThisItem.StockStatus,
    "Out of Stock", RGBA(220, 53, 69, 1),
    "Low Stock", RGBA(255, 193, 7, 1),
    "In Stock", RGBA(40, 167, 69, 1)
)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `11`
**Waar:** Height eigenschap: `20`
**Waar:** Width eigenschap: `100`
**Waar:** X eigenschap: `670`
**Waar:** Y eigenschap: `25`

#### **31. View Details Button (in Gallery Template)**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"👁️ View"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Fill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** HoverFill eigenschap
```powerapps
RGBA(230, 140, 5, 1)
```
**Waar:** Size eigenschap: `10`
**Waar:** Height eigenschap: `25`
**Waar:** Width eigenschap: `60`
**Waar:** X eigenschap: `900`
**Waar:** Y eigenschap: `15`
**Waar:** OnSelect eigenschap
```powerapps
Set(varSelectedMaterial, ThisItem);
Notify("Material details: " & ThisItem.MaterialName, NotificationType.Information)
```

#### **32. Edit Button (in Gallery Template)**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"📝 Edit"`
**Waar:** Color eigenschap
```powerapps
RGBA(255, 255, 255, 1)
```
**Waar:** Fill eigenschap
```powerapps
RGBA(243, 156, 18, 1)
```
**Waar:** HoverFill eigenschap
```powerapps
RGBA(230, 140, 5, 1)
```
**Waar:** Size eigenschap: `10`
**Waar:** Height eigenschap: `25`
**Waar:** Width eigenschap: `60`
**Waar:** X eigenschap: `970`
**Waar:** Y eigenschap: `15`
**Waar:** OnSelect eigenschap
```powerapps
Set(varSelectedMaterial, ThisItem);
Notify("Edit material: " & ThisItem.MaterialName, NotificationType.Information)
```

**BELANGRIJK:** Vervang `TextInput1_1`, `Dropdown1_1`, `Dropdown2_1` met de werkelijke namen van je controls!
```
