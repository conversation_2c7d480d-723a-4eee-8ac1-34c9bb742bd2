<!DOCTYPE html>
<html>
<head>
    <title>Conceptueel ERD</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .diagram {
            width: 800px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: white;
            padding: 20px;
        }
        .entity {
            border: 2px solid #333;
            background-color: #f8f8f8;
            border-radius: 5px;
            text-align: center;
            margin: 10px;
            display: inline-block;
            vertical-align: top;
        }
        .entity-header {
            background-color: #333;
            color: white;
            padding: 8px;
            font-weight: bold;
        }
        .entity-body {
            padding: 10px;
        }
        .attribute {
            text-align: left;
            margin: 5px;
            padding: 3px;
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .erd-type {
            margin: 30px 0;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .erd-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }
        .relationship {
            display: inline-block;
            margin: 10px;
            padding: 5px;
            border: 1px dashed #555;
            border-radius: 5px;
        }
        .arrow {
            display: inline-block;
            margin: 0 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="title">Conceptueel ERD</div>
    <div class="subtitle">Hoogste abstractieniveau, focus op entiteiten en relaties</div>
    
    <div class="diagram">
        <div class="erd-type">
            <div class="erd-title">Conceptueel ERD</div>
            <p>Hoogste abstractieniveau, focus op entiteiten en relaties zonder technische details.</p>
            
            <div style="text-align: center;">
                <div class="entity" style="width: 150px;">
                    <div class="entity-header">Klant</div>
                    <div class="entity-body">
                        <div class="attribute">Naam</div>
                        <div class="attribute">Adres</div>
                    </div>
                </div>
                
                <div class="arrow">←→</div>
                
                <div class="relationship">
                    Plaatst
                </div>
                
                <div class="arrow">←→</div>
                
                <div class="entity" style="width: 150px;">
                    <div class="entity-header">Order</div>
                    <div class="entity-body">
                        <div class="attribute">Datum</div>
                        <div class="attribute">Totaal</div>
                    </div>
                </div>
                
                <div class="arrow">←→</div>
                
                <div class="relationship">
                    Bevat
                </div>
                
                <div class="arrow">←→</div>
                
                <div class="entity" style="width: 150px;">
                    <div class="entity-header">Product</div>
                    <div class="entity-body">
                        <div class="attribute">Naam</div>
                        <div class="attribute">Prijs</div>
                    </div>
                </div>
            </div>
            
            <p>Kenmerken:</p>
            <ul>
                <li>Toont alleen entiteiten en hun relaties</li>
                <li>Geen technische details zoals primaire of vreemde sleutels</li>
                <li>Bedoeld voor communicatie met niet-technische stakeholders</li>
                <li>Kardinaliteit kan worden weergegeven (1:1, 1:N, N:M)</li>
                <li>Veel-op-veel relaties worden direct weergegeven</li>
                <li>Onafhankelijk van specifieke databasetechnologie</li>
            </ul>
            
            <p>Doel:</p>
            <ul>
                <li>Het vastleggen van de belangrijkste entiteiten in het domein</li>
                <li>Het vastleggen van de relaties tussen deze entiteiten</li>
                <li>Communicatie met business stakeholders</li>
                <li>Basis voor het logische ERD</li>
            </ul>
        </div>
    </div>
</body>
</html>
