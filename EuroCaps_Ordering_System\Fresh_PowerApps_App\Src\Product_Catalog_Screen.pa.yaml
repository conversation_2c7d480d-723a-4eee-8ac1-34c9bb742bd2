Product_Catalog_Screen As screen:
    Fill: =RGBA(245, 245, 245, 1)
    LoadingSpinnerColor: =RGBA(74, 111, 165, 1)

    HeaderBar As rectangle:
        Fill: =RGBA(74, 111, 165, 1)
        Height: =60
        Width: =Parent.Width
        X: =0
        Y: =0

    HeaderLogo As image:
        Height: =40
        Image: ="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ3aGl0ZSIvPgo8dGV4dCB4PSIyMCIgeT0iMjUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI4IiBmaWxsPSIjNGE2ZmE1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5FQzwvdGV4dD4KPC9zdmc+"
        Width: =40
        X: =20
        Y: =10

    HeaderTitle As label:
        Color: =RGBA(255, 255, 255, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =16
        Text: ="EuroCaps Order Management Pro"
        Width: =300
        X: =70
        Y: =10

    NavigationMenu As rectangle:
        Fill: =RGBA(58, 90, 128, 1)
        Height: =Parent.Height - 60
        Width: =200
        X: =0
        Y: =60

    MenuDashboard As button:
        Align: =Align.Left
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(58, 90, 128, 1)
        Font: =Font.Arial
        Height: =50
        OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="≡ Dashboard"
        Width: =200
        X: =0
        Y: =60

    MenuCustomers As button:
        Align: =Align.Left
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(58, 90, 128, 1)
        Font: =Font.Arial
        Height: =50
        OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="👥 Customers"
        Width: =200
        X: =0
        Y: =110

    MenuProducts As button:
        Align: =Align.Left
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        Height: =50
        Size: =14
        Text: ="📦 Products"
        Width: =200
        X: =0
        Y: =160

    MenuOrders As button:
        Align: =Align.Left
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(58, 90, 128, 1)
        Font: =Font.Arial
        Height: =50
        OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="📋 Orders"
        Width: =200
        X: =0
        Y: =210

    PageTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =18
        Text: ="Products"
        Width: =200
        X: =220
        Y: =80

    SearchInput As text:
        BorderColor: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        Height: =40
        HintText: ="Search Products..."
        Size: =12
        Width: =400
        X: =220
        Y: =140

    FiltersLabel As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =25
        Size: =12
        Text: ="FILTERS:"
        Width: =80
        X: =220
        Y: =200

    TypeFilter As dropdown:
        BorderColor: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        Height: =35
        Items: =["All", "Espresso", "Lungo", "Ristretto", "Flavored"]
        Size: =12
        Width: =100
        X: =310
        Y: =195

    SizeFilter As dropdown:
        BorderColor: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        Height: =35
        Items: =["All", "10", "20", "44"]
        Size: =12
        Width: =80
        X: =420
        Y: =195

    Product1Card As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =180
        RadiusBottomLeft: =8
        RadiusBottomRight: =8
        RadiusTopLeft: =8
        RadiusTopRight: =8
        Width: =250
        X: =220
        Y: =260

    Product1Image As rectangle:
        Fill: =RGBA(93, 64, 55, 1)
        Height: =80
        RadiusBottomLeft: =4
        RadiusBottomRight: =4
        RadiusTopLeft: =4
        RadiusTopRight: =4
        Width: =200
        X: =245
        Y: =280

    Product1Name As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =25
        Size: =14
        Text: ="Espresso"
        Width: =200
        X: =245
        Y: =370

    Product1Type As label:
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =20
        Size: =12
        Text: ="Classic"
        Width: =100
        X: =245
        Y: =390

    Product1Size As label:
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =20
        Size: =12
        Text: ="Size: 10"
        Width: =100
        X: =245
        Y: =410

    Product1AddButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(76, 175, 80, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        HoverFill: =RGBA(60, 159, 64, 1)
        Size: =12
        Text: ="+ Add"
        Width: =80
        X: =365
        Y: =405

    Product2Card As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =180
        RadiusBottomLeft: =8
        RadiusBottomRight: =8
        RadiusTopLeft: =8
        RadiusTopRight: =8
        Width: =250
        X: =490
        Y: =260

    Product2Image As rectangle:
        Fill: =RGBA(141, 110, 99, 1)
        Height: =80
        RadiusBottomLeft: =4
        RadiusBottomRight: =4
        RadiusTopLeft: =4
        RadiusTopRight: =4
        Width: =200
        X: =515
        Y: =280

    Product2Name As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =25
        Size: =14
        Text: ="Lungo"
        Width: =200
        X: =515
        Y: =370

    Product2Type As label:
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =20
        Size: =12
        Text: ="Intense"
        Width: =100
        X: =515
        Y: =390

    Product2Size As label:
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =20
        Size: =12
        Text: ="Size: 20"
        Width: =100
        X: =515
        Y: =410

    Product2AddButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(76, 175, 80, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        HoverFill: =RGBA(60, 159, 64, 1)
        Size: =12
        Text: ="+ Add"
        Width: =80
        X: =635
        Y: =405

    Product3Card As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =180
        RadiusBottomLeft: =8
        RadiusBottomRight: =8
        RadiusTopLeft: =8
        RadiusTopRight: =8
        Width: =250
        X: =760
        Y: =260

    Product3Image As rectangle:
        Fill: =RGBA(33, 33, 33, 1)
        Height: =80
        RadiusBottomLeft: =4
        RadiusBottomRight: =4
        RadiusTopLeft: =4
        RadiusTopRight: =4
        Width: =200
        X: =785
        Y: =280

    Product3Name As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =25
        Size: =14
        Text: ="Ristretto"
        Width: =200
        X: =785
        Y: =370

    Product3Type As label:
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =20
        Size: =12
        Text: ="Strong"
        Width: =100
        X: =785
        Y: =390

    Product3Size As label:
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =20
        Size: =12
        Text: ="Size: 10"
        Width: =100
        X: =785
        Y: =410

    Product3AddButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(76, 175, 80, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        HoverFill: =RGBA(60, 159, 64, 1)
        Size: =12
        Text: ="+ Add"
        Width: =80
        X: =905
        Y: =405

    Product4Card As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =180
        RadiusBottomLeft: =8
        RadiusBottomRight: =8
        RadiusTopLeft: =8
        RadiusTopRight: =8
        Width: =250
        X: =1030
        Y: =260

    Product4Image As rectangle:
        Fill: =RGBA(255, 152, 0, 1)
        Height: =80
        RadiusBottomLeft: =4
        RadiusBottomRight: =4
        RadiusTopLeft: =4
        RadiusTopRight: =4
        Width: =200
        X: =1055
        Y: =280

    Product4Name As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =25
        Size: =14
        Text: ="Vanilla"
        Width: =200
        X: =1055
        Y: =370

    Product4Type As label:
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =20
        Size: =12
        Text: ="Flavored"
        Width: =100
        X: =1055
        Y: =390

    Product4Size As label:
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =20
        Size: =12
        Text: ="Size: 20"
        Width: =100
        X: =1055
        Y: =410

    Product4AddButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(76, 175, 80, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        HoverFill: =RGBA(60, 159, 64, 1)
        Size: =12
        Text: ="+ Add"
        Width: =80
        X: =1175
        Y: =405

    PaginationInfo As label:
        Align: =Align.Center
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =35
        Size: =12
        Text: ="Showing 1-8 of 24 products"
        Width: =200
        X: =(Parent.Width - 200) / 2
        Y: =680
