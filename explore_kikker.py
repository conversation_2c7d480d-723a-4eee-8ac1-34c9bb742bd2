import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def explore_kikker_data():
    """
    Function to explore the Kikker.csv dataset for Americaps coffee capsule production.
    This function performs initial data exploration and quality checks.
    """
    print("=" * 80)
    print("AMERICAPS DATA EXPLORATION - KIKKER.CSV")
    print("=" * 80)
    
    # 1. Import the CSV file
    print("\n1. Importing CSV file...")
    try:
        # Try to read with different encodings as needed
        df = pd.read_csv('Kikker.csv', sep=',')
        print(f"Successfully loaded dataset with {df.shape[0]} rows and {df.shape[1]} columns.")
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        return
    
    # 2. Display basic information about the dataset
    print("\n2. Basic dataset information:")
    print("\nFirst 5 rows of the dataset:")
    print(df.head())
    
    print("\nColumn names and data types:")
    print(df.dtypes)
    
    print("\nSummary statistics:")
    print(df.describe(include='all').transpose())
    
    # 3. Check for data quality issues
    print("\n3. Data quality check:")
    
    # Check for missing values
    missing_values = df.isnull().sum()
    print("\nMissing values per column:")
    print(missing_values[missing_values > 0])
    
    # Check for duplicate rows
    duplicates = df.duplicated().sum()
    print(f"\nNumber of duplicate rows: {duplicates}")
    
    # Check for unusual values or potential errors
    print("\nPotential data quality issues:")
    
    # Check date formats
    date_columns = [col for col in df.columns if 'Datum' in col or 'datum' in col]
    for col in date_columns:
        try:
            # Try to convert to datetime to check for invalid dates
            pd.to_datetime(df[col], errors='raise')
            print(f"Column {col}: Valid date format")
        except Exception as e:
            print(f"Column {col}: Invalid date format detected - {e}")
            # Show examples of problematic values
            problematic = df[~pd.to_datetime(df[col], errors='coerce').notna()][col].unique()
            if len(problematic) > 0:
                print(f"  Examples of problematic values: {problematic[:5]}")
    
    # Check for unusual numeric values
    numeric_cols = df.select_dtypes(include=['float64', 'int64']).columns
    for col in numeric_cols:
        if df[col].min() < 0:
            print(f"Column {col}: Contains negative values")
        
    # Check for unusual categorical values
    categorical_cols = df.select_dtypes(include=['object']).columns
    for col in categorical_cols:
        unique_values = df[col].nunique()
        if unique_values == 1:
            print(f"Column {col}: Contains only one unique value: {df[col].unique()[0]}")
        elif unique_values > 0 and unique_values <= 10:
            print(f"Column {col}: Contains {unique_values} unique values: {df[col].unique()}")
    
    # 4. Summary of findings
    print("\n4. Summary of initial findings:")
    print(f"- Dataset contains {df.shape[0]} rows and {df.shape[1]} columns")
    print(f"- {missing_values.sum()} total missing values across all columns")
    print(f"- {duplicates} duplicate rows found")
    
    # Return the dataframe for further analysis if needed
    return df

if __name__ == "__main__":
    df = explore_kikker_data()
    print("\nData exploration complete.")
