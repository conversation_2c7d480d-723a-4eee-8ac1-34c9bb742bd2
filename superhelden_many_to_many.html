<!DOCTYPE html>
<html>
<head>
    <title>Superhelden Many-to-Many ERD</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .diagram {
            width: 800px;
            height: 500px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: white;
        }
        .entity {
            position: absolute;
            width: 200px;
            border: 2px solid #333;
            background-color: #f8f8f8;
            border-radius: 5px;
            text-align: center;
        }
        .entity-header {
            background-color: #333;
            color: white;
            padding: 8px;
            font-weight: bold;
        }
        .entity-body {
            padding: 10px;
        }
        .attribute {
            text-align: left;
            margin: 5px;
            padding: 3px;
        }
        .pk {
            font-weight: bold;
            text-decoration: underline;
        }
        .fk {
            font-style: italic;
            color: #555;
        }
        .relationship {
            position: absolute;
            border: 1px solid #555;
            background-color: #eee;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .line {
            position: absolute;
            background-color: #555;
        }
        .cardinality {
            position: absolute;
            font-weight: bold;
            color: #333;
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .explanation {
            margin: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="title">Many-to-Many (N:M) Relatie Voorbeeld</div>
    <div class="subtitle">Superhelden en Groepen</div>
    
    <div class="diagram">
        <!-- Incorrect implementatie -->
        <div style="position: absolute; top: 20px; left: 250px; font-weight: bold; color: red;">
            Incorrecte implementatie (Primaire sleutel niet uniek)
        </div>
        <div class="entity" style="top: 60px; left: 250px; width: 300px;">
            <div class="entity-header">Superheld_Groep (Incorrect)</div>
            <div class="entity-body">
                <div class="attribute pk">SuperheldID</div>
                <div class="attribute">Naam</div>
                <div class="attribute">GroepID</div>
                <div class="attribute">GroepNaam</div>
            </div>
        </div>
        
        <!-- Correcte implementatie -->
        <div style="position: absolute; top: 200px; left: 250px; font-weight: bold; color: green;">
            Correcte implementatie (3NF genormaliseerd)
        </div>
        
        <!-- Superheld entiteit -->
        <div class="entity" style="top: 240px; left: 50px;">
            <div class="entity-header">Superheld</div>
            <div class="entity-body">
                <div class="attribute pk">SuperheldID</div>
                <div class="attribute">Naam</div>
                <!-- Andere attributen -->
            </div>
        </div>
        
        <!-- Groep entiteit -->
        <div class="entity" style="top: 240px; left: 550px;">
            <div class="entity-header">Groep</div>
            <div class="entity-body">
                <div class="attribute pk">GroepID</div>
                <div class="attribute">GroepNaam</div>
                <!-- Andere attributen -->
            </div>
        </div>
        
        <!-- Junction table -->
        <div class="entity" style="top: 350px; left: 300px;">
            <div class="entity-header">Superheld_Groep</div>
            <div class="entity-body">
                <div class="attribute pk fk">SuperheldID</div>
                <div class="attribute pk fk">GroepID</div>
                <!-- Eventuele extra attributen van de relatie -->
            </div>
        </div>
        
        <!-- Lijnen voor relaties -->
        <!-- Superheld naar junction -->
        <div class="line" style="top: 280px; left: 250px; width: 50px; height: 2px;"></div>
        <div class="line" style="top: 280px; left: 300px; width: 2px; height: 70px;"></div>
        
        <!-- Groep naar junction -->
        <div class="line" style="top: 280px; left: 550px; width: 50px; height: 2px;"></div>
        <div class="line" style="top: 280px; left: 500px; width: 2px; height: 70px;"></div>
        
        <!-- Cardinaliteit -->
        <div class="cardinality" style="top: 260px; left: 260px;">1</div>
        <div class="cardinality" style="top: 330px; left: 280px;">N</div>
        
        <div class="cardinality" style="top: 260px; left: 530px;">1</div>
        <div class="cardinality" style="top: 330px; left: 510px;">N</div>
    </div>
    
    <div class="explanation">
        <p><strong>Toelichting:</strong></p>
        <p>In dit voorbeeld hebben we een veel-op-veel (N:M) relatie tussen Superhelden en Groepen:</p>
        <ul>
            <li>Een Superheld kan tot 0 of meerdere groepen behoren</li>
            <li>Een Groep kan 1 of meerdere superhelden bevatten</li>
        </ul>
        <p>De incorrecte implementatie probeert alle gegevens in één tabel op te slaan, maar dit leidt tot problemen omdat de primaire sleutel (SuperheldID) niet uniek is. Thor komt bijvoorbeeld twee keer voor met hetzelfde SuperheldID.</p>
        <p>De correcte implementatie gebruikt drie tabellen:</p>
        <ul>
            <li><strong>Superheld</strong>: Bevat unieke superhelden met hun eigenschappen</li>
            <li><strong>Groep</strong>: Bevat unieke groepen met hun eigenschappen</li>
            <li><strong>Superheld_Groep</strong>: Een koppeltabel (junction table) die de relaties tussen superhelden en groepen vastlegt. De primaire sleutel is een samengestelde sleutel van SuperheldID en GroepID.</li>
        </ul>
        <p>Deze structuur is in 3NF en vermijdt redundantie en update-anomalieën.</p>
    </div>
</body>
</html>
