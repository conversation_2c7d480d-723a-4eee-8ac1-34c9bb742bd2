# New Order Screen - EuroCaps Ordering System

## Screen Layout

```
+---------------------------------------------------------------+
| [Logo] EuroCaps Ordering System           [User ▼] [Settings] |
+---------------------------------------------------------------+
| [≡ MENU]  | New Order                                         |
|           |                                                    |
| Dashboard | ORDER INFORMATION                                  |
| Customers | +------------------------------------------------+ |
| Products  | | Customer: [Select Customer ▼]                  | |
| Orders    | |                                                | |
| Reports   | | Order Date: [16/05/2025] (Today)               | |
|           | |                                                | |
| Settings  | | Requested Delivery: [23/05/2025] (Calendar)    | |
| Logout    | |                                                | |
|           | | Notes:                                         | |
|           | | [                                            ] | |
|           | | [                                            ] | |
|           | +------------------------------------------------+ |
|           |                                                    |
|           | ORDER ITEMS                                        |
|           | +------------------------------------------------+ |
|           | | No items added yet.                            | |
|           | |                                                | |
|           | | [+ ADD PRODUCTS]                               | |
|           | +------------------------------------------------+ |
|           |                                                    |
|           | ORDER SUMMARY                                      |
|           | +------------------------------------------------+ |
|           | | Total Items: 0                                 | |
|           | | Total Quantity: 0                              | |
|           | +------------------------------------------------+ |
|           |                                                    |
|           | [SAVE AS DRAFT]  [SUBMIT ORDER]  [CANCEL]         |
|           |                                                    |
+---------------------------------------------------------------+
```

## Design Elements

### Colors
- Header: Blue (#4a6fa5)
- Menu sidebar: Dark blue (#3a5a80)
- Background: Light gray (#f5f5f5)
- Form sections: White (#ffffff)
- Primary button: Green (#4caf50)
- Secondary button: Blue (#4a6fa5)
- Cancel button: Gray (#9e9e9e)

### Typography
- Header: Arial, 16pt, Bold, White
- Menu items: Arial, 14pt, White
- Page title: Arial, 18pt, Bold, Dark gray
- Section titles: Arial, 16pt, Bold, Dark gray
- Form labels: Arial, 12pt, Bold
- Form inputs: Arial, 12pt
- Button text: Arial, 14pt, Bold, White

### Components

1. **Header Bar**
   - EuroCaps logo (left-aligned)
   - Application title
   - User profile dropdown (right-aligned)
   - Settings icon (right-aligned)

2. **Navigation Menu**
   - Vertical sidebar with menu items
   - Icons for each menu item

3. **Order Information Section**
   - Customer dropdown (searchable)
   - Order date picker (defaults to today)
   - Requested delivery date picker (defaults to today + 7 days)
   - Notes text area (optional)

4. **Order Items Section**
   - Initially empty with message
   - "Add Products" button
   - Once items added:
     - Table with columns: Product, Type, Size, Quantity, Price, Total, Actions
     - Remove button for each item
     - Quantity adjustment controls

5. **Order Summary Section**
   - Total items count
   - Total quantity
   - Once items added:
     - Subtotal
     - Any applicable discounts
     - Total price

6. **Action Buttons**
   - "Save as Draft" (secondary)
   - "Submit Order" (primary)
   - "Cancel" (neutral)

## Interactions

1. **Customer Selection**
   - Dropdown with search functionality
   - Shows recent customers at top
   - Option to create new customer

2. **Date Selection**
   - Calendar popup for date selection
   - Validation to ensure delivery date is after order date
   - Minimum delivery time enforcement (e.g., +3 days)

3. **Add Products**
   - Click "Add Products" to navigate to product selection screen
   - Return to this screen with selected products added to order
   - Alternative: Open product selection in modal/panel

4. **Item Management**
   - Adjust quantities with +/- buttons or direct input
   - Remove items with delete button
   - Real-time update of order summary on changes

5. **Form Actions**
   - "Save as Draft": Saves order with "draft" status
   - "Submit Order": Validates form and creates order with "new" status
   - "Cancel": Prompts confirmation if changes made, then returns to previous screen

## Validation Rules

1. **Required Fields**
   - Customer
   - Order date
   - Delivery date
   - At least one order item

2. **Business Rules**
   - Delivery date must be at least 3 business days after order date
   - Quantities must be positive integers
   - Customer must have valid status (active, approved)

## Accessibility Considerations
- Clear form structure with logical tab order
- Visible labels for all form fields
- Error messages are clear and descriptive
- Sufficient contrast for all text elements

## Notes for Implementation
- Consider adding customer quick info display after selection
- Add product search within the order screen
- For prototype: Use mock data for customers and products
- Implement basic validation for demonstration purposes
