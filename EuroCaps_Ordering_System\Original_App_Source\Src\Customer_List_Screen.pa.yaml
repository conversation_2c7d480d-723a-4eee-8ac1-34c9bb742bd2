# ************************************************************************************************
# Warning: YAML source code for Canvas Apps should only be used to review changes made within Power Apps Studio and for minor edits (Preview).
# Use the maker portal to create and edit your Power Apps.
#
# The schema file for Canvas Apps is available at https://go.microsoft.com/fwlink/?linkid=2304907
#
# For more information, visit https://go.microsoft.com/fwlink/?linkid=2292623
# ************************************************************************************************
Screens:
  Customer_List_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(56, 96, 178, 1)
    Children:
      - HeaderBar:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(56, 96, 178, 1)
            Height: =60
            Width: =1366
            X: =0
            Y: =0
      - PageTitle:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="Customers"
            Size: =18
            Color: =RGBA(51, 51, 51, 1)
            Height: =40
            Width: =200
            X: =20
            Y: =80
      - SearchInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            Font: =Font.'Open Sans'
            HintText: ="Search Customers..."
            BorderColor: =RGBA(56, 96, 178, 1)
            Height: =40
            Width: =400
            X: =20
            Y: =140
      - CustomerTable:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            Height: =400
            Width: =1300
            X: =20
            Y: =200
      - HeaderName:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="Name"
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =40
            Y: =220
      - HeaderContact:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="Contact"
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =260
            Y: =220
      - HeaderEmail:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="Email"
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =480
            Y: =220
      - Customer1Name:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            Text: ="Bean Lovers"
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =40
            Y: =260
      - Customer1Contact:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            Text: ="John Smith"
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =260
            Y: =260
      - Customer1Email:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            Text: ="<EMAIL>"
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =480
            Y: =260
      - Customer2Name:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            Text: ="Café Express"
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =40
            Y: =300
      - Customer2Contact:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            Text: ="Maria Garcia"
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =260
            Y: =300
      - Customer2Email:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            Text: ="<EMAIL>"
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =480
            Y: =300
      - NewCustomerButton:
          Control: Classic/Button@2.2.0
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="+ NEW CUSTOMER"
            Fill: =RGBA(76, 175, 80, 1)
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =150
            X: =1150
            Y: =80
