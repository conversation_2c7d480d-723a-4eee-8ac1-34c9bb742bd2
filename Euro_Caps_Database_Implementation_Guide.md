# Euro Caps Database Implementation Guide

This guide focuses specifically on the database implementation component of the Euro Caps project, providing detailed instructions for designing, building, and evaluating a database that supports Euro Caps' business processes.

## Database Design Process

### 1. Analyze Business Processes

Before designing the database, thoroughly understand Euro Caps' key business processes:

- **Production Process**:
  - Grinding: 500kg Big Bags of coffee beans are ground
  - Filling: Capsules filled with 4.0-6.0g coffee and sealed with aluminum foil
  - Packing: Capsules packed in boxes of 10, 20, or 44 units
  - Quality control at each stage (OK/NOK products)

- **Supply Chain**:
  - Materials received: capsules, coffee beans, additives, foil, milk, nitrogen, pallets
  - Supplier relationships and ordering processes
  - Inventory management
  - Distribution to customers

- **Quality Management**:
  - Weight checks during filling and packing
  - Tracking of NOK (not OK) products
  - Quality standards and compliance

### 2. Conceptual ERD

Create a high-level conceptual ERD that identifies the main entities and their relationships:

- **Key Entities to Consider**:
  - Materials (raw materials, packaging)
  - Suppliers
  - Production Batches
  - Production Steps (grinding, filling, packing)
  - Quality Control Checks
  - Products (finished capsules)
  - Orders (customer orders)
  - Customers
  - Employees/Departments
  - Machines/Equipment

- **Relationships to Consider**:
  - Suppliers provide Materials
  - Materials are used in Production Batches
  - Production Batches go through Production Steps
  - Quality Control Checks are performed on Production Batches
  - Production Batches result in Products
  - Products fulfill Orders
  - Customers place Orders
  - Employees work in Departments
  - Employees operate Machines
  - Machines are used in Production Steps

### 3. Logical ERD

Transform the conceptual ERD into a logical model:

- Define attributes for each entity
- Establish primary keys
- Define foreign keys to represent relationships
- Normalize the database structure (aim for 3NF)
- Resolve many-to-many relationships with junction tables

**Example Entities with Attributes**:

- **Materials**:
  - MaterialID (PK)
  - MaterialName
  - MaterialType (coffee beans, capsules, foil, etc.)
  - UnitOfMeasure
  - CurrentStock
  - MinimumStock
  - SupplierID (FK)

- **ProductionBatch**:
  - BatchID (PK)
  - BatchDate
  - ProductID (FK)
  - PlannedQuantity
  - ActualQuantity
  - Status (planned, in progress, completed)

- **ProductionStep**:
  - StepID (PK)
  - BatchID (FK)
  - StepType (grinding, filling, packing)
  - StartTime
  - EndTime
  - MachineID (FK)
  - EmployeeID (FK)
  - QuantityProcessed
  - QuantityRejected

### 4. Physical ERD

Develop the physical ERD with implementation details:

- Specify data types for all attributes
- Define constraints (NOT NULL, UNIQUE, CHECK)
- Create indexes for performance optimization
- Consider partitioning for large tables
- Plan for data archiving if needed

### 5. MySQL Implementation

Implement the physical ERD in MySQL Workbench:

- Create tables with appropriate data types
- Define primary and foreign keys
- Set up constraints and triggers
- Create indexes for frequently queried fields
- Implement views for common queries

**Example MySQL Table Creation**:

```sql
CREATE TABLE Materials (
    MaterialID INT AUTO_INCREMENT PRIMARY KEY,
    MaterialName VARCHAR(100) NOT NULL,
    MaterialType ENUM('coffee_beans', 'capsules', 'foil', 'additives', 'milk', 'nitrogen', 'pallets') NOT NULL,
    UnitOfMeasure VARCHAR(20) NOT NULL,
    CurrentStock DECIMAL(10,2) NOT NULL DEFAULT 0,
    MinimumStock DECIMAL(10,2) NOT NULL DEFAULT 0,
    SupplierID INT,
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)
);

CREATE TABLE ProductionBatches (
    BatchID INT AUTO_INCREMENT PRIMARY KEY,
    BatchDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ProductID INT NOT NULL,
    PlannedQuantity INT NOT NULL,
    ActualQuantity INT,
    Status ENUM('planned', 'in_progress', 'completed', 'cancelled') NOT NULL DEFAULT 'planned',
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);
```

### 6. Python Script for Data Import

Create a Python script to import data from CSV files:

- Define the structure of CSV files
- Create sample data that reflects realistic business scenarios
- Write a script that reads CSV files and inserts data into MySQL
- Include error handling and validation
- Document the script and CSV structure

**Example Python Script Structure**:

```python
import csv
import mysql.connector

# Database connection
db = mysql.connector.connect(
    host="localhost",
    user="username",
    password="password",
    database="euro_caps_db"
)
cursor = db.cursor()

# Import suppliers
def import_suppliers():
    with open('suppliers.csv', 'r') as file:
        csv_reader = csv.reader(file)
        next(csv_reader)  # Skip header row
        for row in csv_reader:
            supplier_name, contact_person, email, phone, address = row
            query = "INSERT INTO Suppliers (SupplierName, ContactPerson, Email, Phone, Address) VALUES (%s, %s, %s, %s, %s)"
            cursor.execute(query, (supplier_name, contact_person, email, phone, address))
    db.commit()
    print("Suppliers imported successfully")

# Import materials
def import_materials():
    with open('materials.csv', 'r') as file:
        csv_reader = csv.reader(file)
        next(csv_reader)  # Skip header row
        for row in csv_reader:
            material_name, material_type, unit, current_stock, min_stock, supplier_id = row
            query = "INSERT INTO Materials (MaterialName, MaterialType, UnitOfMeasure, CurrentStock, MinimumStock, SupplierID) VALUES (%s, %s, %s, %s, %s, %s)"
            cursor.execute(query, (material_name, material_type, unit, float(current_stock), float(min_stock), int(supplier_id)))
    db.commit()
    print("Materials imported successfully")

# Main function
def main():
    import_suppliers()
    import_materials()
    # Add more import functions as needed
    print("All data imported successfully")

if __name__ == "__main__":
    main()
    db.close()
```

### 7. SQL Queries for Analysis

Develop SQL queries that provide valuable insights:

#### Operational Queries:
- List of materials with low stock levels
- Production batches completed in the last week
- Quality control results by production step
- Employee productivity metrics

#### Analytical Queries:
- Percentage of NOK products by production step
- Average production time per batch
- Material usage efficiency
- Supplier reliability metrics
- Production capacity utilization

**Example Queries**:

```sql
-- Operational: Materials with stock below minimum level
SELECT MaterialName, MaterialType, CurrentStock, MinimumStock
FROM Materials
WHERE CurrentStock < MinimumStock
ORDER BY (MinimumStock - CurrentStock) DESC;

-- Analytical: NOK percentage by production step
SELECT 
    StepType,
    SUM(QuantityRejected) AS TotalRejected,
    SUM(QuantityProcessed) AS TotalProcessed,
    (SUM(QuantityRejected) / SUM(QuantityProcessed)) * 100 AS RejectionPercentage
FROM ProductionStep
GROUP BY StepType
ORDER BY RejectionPercentage DESC;
```

### 8. Reflection on Database Design

Write a reflection that covers:

- How the database model translates Euro Caps' business processes
- Design choices made and their rationale
- Data organization principles applied
- Challenges encountered and solutions implemented
- Lessons learned about database modeling for manufacturing
- Insights gained about data quality and business operations

## CSV File Structures

Create the following CSV files with sample data:

1. **suppliers.csv**:
   - SupplierName, ContactPerson, Email, Phone, Address

2. **materials.csv**:
   - MaterialName, MaterialType, UnitOfMeasure, CurrentStock, MinimumStock, SupplierID

3. **products.csv**:
   - ProductName, ProductType, PackageSize, Description

4. **machines.csv**:
   - MachineName, MachineType, Location, InstallationDate, MaintenanceInterval

5. **employees.csv**:
   - FirstName, LastName, Department, Position, HireDate

6. **production_batches.csv**:
   - BatchDate, ProductID, PlannedQuantity, ActualQuantity, Status

7. **production_steps.csv**:
   - BatchID, StepType, StartTime, EndTime, MachineID, EmployeeID, QuantityProcessed, QuantityRejected

## Deliverables Checklist

Ensure you include all required deliverables:

- [ ] Conceptual ERD with explanation
- [ ] Logical ERD with explanation
- [ ] Physical ERD with detailed explanation of design choices
- [ ] MySQL export (DDL + data)
- [ ] Python script for data import
- [ ] CSV files with sample data
- [ ] SQL queries (both operational and analytical) with results
- [ ] Reflection on database design process and lessons learned

## Best Practices

1. **Documentation**: Document all aspects of your database design and implementation
2. **Normalization**: Aim for 3NF to reduce redundancy and improve data integrity
3. **Naming Conventions**: Use consistent naming conventions for tables, columns, and constraints
4. **Data Types**: Choose appropriate data types to optimize storage and performance
5. **Constraints**: Implement constraints to ensure data integrity
6. **Indexing**: Create indexes for frequently queried fields
7. **Testing**: Test your database with realistic data volumes and query patterns
8. **Security**: Consider security aspects even in a prototype database

By following this guide, you will create a comprehensive database solution that effectively supports Euro Caps' business processes and provides valuable insights for decision-making.
