#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
EuroCaps CSV Generator Script met NumPy en Pandas

Dit script genereert CSV-bestanden met testdata voor de EuroCaps database.
De structuur is gebaseerd op het fysieke ERD voor het EuroCaps productieproces.
"""

import numpy as np
import pandas as pd
import random
import datetime
import os

# Maak een directory voor de CSV-bestanden als deze nog niet bestaat
if not os.path.exists('csv_data'):
    os.makedirs('csv_data')

# Hulpfuncties
def random_date(start_date, end_date):
    """Genereer een willekeurige datum tussen start_date en end_date"""
    time_between_dates = end_date - start_date
    days_between_dates = time_between_dates.days
    random_number_of_days = random.randrange(days_between_dates)
    return start_date + datetime.timedelta(days=random_number_of_days)

def random_datetime(start_date, end_date):
    """<PERSON>reer een willekeurige datum en tijd tussen start_date en end_date"""
    random_date = start_date + datetime.timedelta(
        seconds=random.randint(0, int((end_date - start_date).total_seconds()))
    )
    return random_date.strftime("%Y-%m-%d %H:%M:%S")

# Hoofdfunctie om alle data te genereren
def generate_all_data():
    """Genereer alle CSV-bestanden voor de EuroCaps database"""
    print("Start genereren van CSV-bestanden voor EuroCaps database...")

    # Genereer data in de juiste volgorde (rekening houdend met afhankelijkheden)
    soort_partners_df = generate_soort_partner_data()
    partners_df = generate_partner_data(soort_partners_df)
    generate_partner_contact_data(partners_df)

    soort_producten_df = generate_soort_product_data()
    products_df = generate_product_data(soort_producten_df)

    grinding_df = generate_grinding_data()
    generate_grinding_product_data(grinding_df, products_df)

    filling_df = generate_filling_data()
    generate_filling_product_data(filling_df, products_df)

    packaging_df = generate_packaging_data()
    generate_packaging_product_data(packaging_df, products_df)

    leveringen_df = generate_levering_data(partners_df)
    generate_levering_regel_data(leveringen_df, products_df)

    print("Alle CSV-bestanden zijn succesvol gegenereerd in de map 'csv_data'.")

# 1. SoortPartner data
def generate_soort_partner_data():
    """Genereer data voor de SoortPartner tabel met Pandas"""
    # Maak een DataFrame
    data = {
        'SoortPartnerId': [1, 2, 3, 4],
        'Omschrijving': ["Leverancier", "Klant", "Transporteur", "Dienstverlener"]
    }

    df = pd.DataFrame(data)

    # Sla op als CSV
    df.to_csv('csv_data/soort_partner.csv', index=False)

    print("SoortPartner data gegenereerd.")
    return df

# 2. Partner data
def generate_partner_data(soort_partners_df):
    """Genereer data voor de Partner tabel met Pandas"""
    # Bedrijfsnamen
    bedrijfsnamen = [
        "Koffie Groothandel BV", "Bean Masters", "Capsule Experts",
        "Koffie Deluxe", "Aroma Supplies", "Packaging Solutions",
        "Transport Express", "Logistiek Partners", "Quality Control Services",
        "Machine Maintenance BV", "Coffee Distributors", "Capsule Innovations"
    ]

    # Aantal partners
    n = len(bedrijfsnamen)

    # Maak een DataFrame
    data = {
        'PartnerId': list(range(1, n+1)),
        'SoortPartnerId': np.random.choice(soort_partners_df['SoortPartnerId'], size=n),
        'Bedrijfsnaam': bedrijfsnamen,
        'Straatnaam': [f"Industrieweg {i}" for i in range(1, n+1)],
        'Huisnummer': [str(np.random.randint(1, 100)) for _ in range(n)],
        'Postcode': [f"{np.random.randint(1000, 9999)} {chr(65 + np.random.randint(0, 25))}{chr(65 + np.random.randint(0, 25))}" for _ in range(n)],
        'Plaats': np.random.choice(["Amsterdam", "Rotterdam", "Utrecht", "Eindhoven", "Groningen", "Den Haag"], size=n),
        'Land': ["Nederland"] * n,
        'Email': [f"info@{naam.lower().replace(' ', '')}.nl" for naam in bedrijfsnamen],
        'Telnr': [f"0{np.random.randint(10, 99)}-{np.random.randint(1000000, 9999999)}" for _ in range(n)]
    }

    df = pd.DataFrame(data)

    # Sla op als CSV
    df.to_csv('csv_data/partner.csv', index=False)

    print("Partner data gegenereerd.")
    return df

# 3. PartnerContact data
def generate_partner_contact_data(partners_df):
    """Genereer data voor de PartnerContact tabel met Pandas"""
    # Voornamen en achternamen
    voornamen = ["Jan", "Piet", "Klaas", "Marie", "Sophie", "Lisa", "Thomas", "Mark", "Emma", "Lotte"]
    achternamen = ["de Vries", "Jansen", "Bakker", "Visser", "Smit", "Meijer", "Bos", "van Dijk", "Mulder", "de Boer"]
    functies = ["Directeur", "Inkoper", "Verkoper", "Logistiek Manager", "Kwaliteitsmanager", "Administratie"]

    # Aantal contactpersonen
    n = 20

    # Willekeurige partner IDs
    partner_ids = np.random.choice(partners_df['PartnerId'], size=n)

    # Willekeurige voornamen en achternamen
    random_voornamen = np.random.choice(voornamen, size=n)
    random_achternamen = np.random.choice(achternamen, size=n)

    # Maak een DataFrame
    data = {
        'PartnerContactId': list(range(1, n+1)),
        'PartnerId': partner_ids,
        'Voornaam': random_voornamen,
        'Achternaam': random_achternamen,
        'Functie': np.random.choice(functies, size=n),
        'Email': [f"{v.lower()}.{a.lower().replace(' ', '')}@bedrijf.nl" for v, a in zip(random_voornamen, random_achternamen)],
        'Telnr': [f"06-{np.random.randint(10000000, 99999999)}" for _ in range(n)]
    }

    df = pd.DataFrame(data)

    # Sla op als CSV
    df.to_csv('csv_data/partner_contact.csv', index=False)

    print("PartnerContact data gegenereerd.")
    return df

# 4. SoortProduct data
def generate_soort_product_data():
    """Genereer data voor de SoortProduct tabel met Pandas"""
    # Maak een DataFrame
    data = {
        'SoortProductId': [1, 2, 3, 4, 5, 6],
        'Omschrijving': [
            "Espresso Capsule",
            "Lungo Capsule",
            "Ristretto Capsule",
            "Decaf Capsule",
            "Biologische Capsule",
            "Thee Capsule"
        ],
        'Materiaal': [
            "Aluminium",
            "Aluminium",
            "Aluminium",
            "Aluminium",
            "Biologisch afbreekbaar plastic",
            "Biologisch afbreekbaar plastic"
        ],
        'Gewicht': [5.5, 6.0, 5.0, 5.5, 4.8, 4.5],
        'Afmeting': ["36x22mm"] * 6
    }

    df = pd.DataFrame(data)

    # Sla op als CSV
    df.to_csv('csv_data/soort_product.csv', index=False)

    print("SoortProduct data gegenereerd.")
    return df

# 5. Product data
def generate_product_data(soort_producten_df):
    """Genereer data voor de Product tabel met Pandas"""
    # Aantal producten
    n = 100

    # Start- en einddatum voor THT
    start_date = datetime.date(2023, 1, 1)
    end_date = datetime.date(2024, 12, 31)

    # Statussen
    statussen = ["Geproduceerd", "In productie", "Kwaliteitscontrole", "Gereed voor verzending", "Verzonden"]

    # Willekeurige soort product IDs
    soort_product_ids = np.random.choice(soort_producten_df['SoortProductId'], size=n)

    # Willekeurige THT datums
    tht_datums = [random_date(start_date, end_date).strftime("%Y-%m-%d") for _ in range(n)]

    # Willekeurige statussen
    product_statussen = np.random.choice(statussen, size=n)

    # Willekeurige processtatus (C, F, P of leeg)
    c_statussen = np.random.choice(["C", ""], size=n, p=[0.8, 0.2])
    f_statussen = np.random.choice(["F", ""], size=n, p=[0.7, 0.3])
    p_statussen = np.random.choice(["P", ""], size=n, p=[0.6, 0.4])

    # Maak een DataFrame
    data = {
        'ProductId': list(range(1, n+1)),
        'SoortProductId': soort_product_ids,
        'ProductTHTDatum': tht_datums,
        'StatusProduct': product_statussen,
        'CStatusProduct': c_statussen,
        'FStatusProduct': f_statussen,
        'PStatusProduct': p_statussen
    }

    df = pd.DataFrame(data)

    # Sla op als CSV
    df.to_csv('csv_data/product.csv', index=False)

    print("Product data gegenereerd.")
    return df

# 6. Grinding data
def generate_grinding_data():
    """Genereer data voor de Grinding tabel met Pandas"""
    # Aantal grinding batches
    n = 30

    # Start- en einddatum
    start_date = datetime.datetime(2023, 1, 1)
    end_date = datetime.datetime(2023, 12, 31)

    # Machines
    machines = ["Grinder-1000", "Grinder-2000", "Grinder-3000", "Grinder-4000"]

    # Willekeurige start datums/tijden
    start_datetimes = [random_datetime(start_date, end_date) for _ in range(n)]

    # Eind datums/tijden (1-4 uur na start)
    end_datetimes = []
    for start_dt in start_datetimes:
        start_dt_obj = datetime.datetime.strptime(start_dt, "%Y-%m-%d %H:%M:%S")
        hours_to_add = np.random.randint(1, 5)
        end_dt_obj = start_dt_obj + datetime.timedelta(hours=hours_to_add)
        end_datetimes.append(end_dt_obj.strftime("%Y-%m-%d %H:%M:%S"))

    # Willekeurige machines
    grinding_machines = np.random.choice(machines, size=n)

    # Maak een DataFrame
    data = {
        'GrindingId': list(range(1, n+1)),
        'G_DatumTijdStart': start_datetimes,
        'G_DatumTijdEind': end_datetimes,
        'G_Machine': grinding_machines
    }

    df = pd.DataFrame(data)

    # Sla op als CSV
    df.to_csv('csv_data/grinding.csv', index=False)

    print("Grinding data gegenereerd.")
    return df

# 7. Grinding_Product data
def generate_grinding_product_data(grinding_df, products_df):
    """Genereer data voor de Grinding_Product koppeltabel met Pandas"""
    # Lijst voor alle grinding-product combinaties
    grinding_product_list = []

    # Voor elke grinding batch
    for grinding_id in grinding_df['GrindingId']:
        # Kies 3-8 willekeurige producten
        num_products = np.random.randint(3, 9)
        selected_products = np.random.choice(products_df['ProductId'], size=num_products, replace=False)

        # Voeg elke combinatie toe met een willekeurig aantal
        for product_id in selected_products:
            aantal = np.random.randint(100, 1001)
            grinding_product_list.append([grinding_id, product_id, aantal])

    # Maak een DataFrame
    df = pd.DataFrame(grinding_product_list, columns=['GrindingId', 'ProductId', 'Aantal'])

    # Sla op als CSV
    df.to_csv('csv_data/grinding_product.csv', index=False)

    print("Grinding_Product data gegenereerd.")
    return df

# 8. Filling data
def generate_filling_data():
    """Genereer data voor de Filling tabel met Pandas"""
    # Aantal filling batches
    n = 40

    # Start- en einddatum
    start_date = datetime.datetime(2023, 1, 1)
    end_date = datetime.datetime(2023, 12, 31)

    # Machines
    machines = ["Filler-A", "Filler-B", "Filler-C", "Filler-D"]

    # Willekeurige start datums/tijden
    start_datetimes = [random_datetime(start_date, end_date) for _ in range(n)]

    # Eind datums/tijden (2-6 uur na start)
    end_datetimes = []
    for start_dt in start_datetimes:
        start_dt_obj = datetime.datetime.strptime(start_dt, "%Y-%m-%d %H:%M:%S")
        hours_to_add = np.random.randint(2, 7)
        end_dt_obj = start_dt_obj + datetime.timedelta(hours=hours_to_add)
        end_datetimes.append(end_dt_obj.strftime("%Y-%m-%d %H:%M:%S"))

    # Willekeurige machines
    filling_machines = np.random.choice(machines, size=n)

    # Maak een DataFrame
    data = {
        'FillingId': list(range(1, n+1)),
        'F_DatumTijdStart': start_datetimes,
        'F_DatumTijdEind': end_datetimes,
        'F_Machine': filling_machines
    }

    df = pd.DataFrame(data)

    # Sla op als CSV
    df.to_csv('csv_data/filling.csv', index=False)

    print("Filling data gegenereerd.")
    return df

# 9. Filling_Product data
def generate_filling_product_data(filling_df, products_df):
    """Genereer data voor de Filling_Product koppeltabel met Pandas"""
    # Lijst voor alle filling-product combinaties
    filling_product_list = []

    # Voor elke filling batch
    for filling_id in filling_df['FillingId']:
        # Kies 2-5 willekeurige producten
        num_products = np.random.randint(2, 6)
        selected_products = np.random.choice(products_df['ProductId'], size=num_products, replace=False)

        # Voeg elke combinatie toe met een willekeurig aantal
        for product_id in selected_products:
            aantal = np.random.randint(500, 2001)
            filling_product_list.append([filling_id, product_id, aantal])

    # Maak een DataFrame
    df = pd.DataFrame(filling_product_list, columns=['FillingId', 'ProductId', 'Aantal'])

    # Sla op als CSV
    df.to_csv('csv_data/filling_product.csv', index=False)

    print("Filling_Product data gegenereerd.")
    return df

# 10. Packaging data
def generate_packaging_data():
    """Genereer data voor de Packaging tabel met Pandas"""
    # Aantal packaging batches
    n = 50

    # Start- en einddatum
    start_date = datetime.datetime(2023, 1, 1)
    end_date = datetime.datetime(2023, 12, 31)

    # Machines
    machines = ["Packager-X1", "Packager-X2", "Packager-Y1", "Packager-Z1"]

    # Willekeurige start datums/tijden
    start_datetimes = [random_datetime(start_date, end_date) for _ in range(n)]

    # Eind datums/tijden (3-8 uur na start)
    end_datetimes = []
    for start_dt in start_datetimes:
        start_dt_obj = datetime.datetime.strptime(start_dt, "%Y-%m-%d %H:%M:%S")
        hours_to_add = np.random.randint(3, 9)
        end_dt_obj = start_dt_obj + datetime.timedelta(hours=hours_to_add)
        end_datetimes.append(end_dt_obj.strftime("%Y-%m-%d %H:%M:%S"))

    # Willekeurige machines
    packaging_machines = np.random.choice(machines, size=n)

    # Maak een DataFrame
    data = {
        'PackagingId': list(range(1, n+1)),
        'P_DatumTijdStart': start_datetimes,
        'P_DatumTijdEind': end_datetimes,
        'P_Machine': packaging_machines
    }

    df = pd.DataFrame(data)

    # Sla op als CSV
    df.to_csv('csv_data/packaging.csv', index=False)

    print("Packaging data gegenereerd.")
    return df

# 11. Packaging_Product data
def generate_packaging_product_data(packaging_df, products_df):
    """Genereer data voor de Packaging_Product koppeltabel met Pandas"""
    # Lijst voor alle packaging-product combinaties
    packaging_product_list = []

    # Voor elke packaging batch
    for packaging_id in packaging_df['PackagingId']:
        # Kies 1-3 willekeurige producten
        num_products = np.random.randint(1, 4)
        selected_products = np.random.choice(products_df['ProductId'], size=num_products, replace=False)

        # Voeg elke combinatie toe met een willekeurig aantal
        for product_id in selected_products:
            aantal = np.random.randint(1000, 5001)
            aantal_stuks_in_doos = np.random.choice([10, 20, 30, 40, 50])
            packaging_product_list.append([packaging_id, product_id, aantal, aantal_stuks_in_doos])

    # Maak een DataFrame
    df = pd.DataFrame(packaging_product_list, columns=['PackagingId', 'ProductId', 'Aantal', 'AantalStuksInDoos'])

    # Sla op als CSV
    df.to_csv('csv_data/packaging_product.csv', index=False)

    print("Packaging_Product data gegenereerd.")
    return df

# 12. Levering data
def generate_levering_data(partners_df):
    """Genereer data voor de Levering tabel met Pandas"""
    # Aantal leveringen
    n = 30

    # Start- en einddatum
    start_date = datetime.date(2023, 1, 1)
    end_date = datetime.date(2023, 12, 31)

    # Filter alleen partners die klanten zijn (SoortPartnerId = 2)
    klanten_df = partners_df[partners_df['SoortPartnerId'] == 2]

    # Als er geen klanten zijn, gebruik alle partners
    if len(klanten_df) == 0:
        klanten_df = partners_df

    # Willekeurige klant IDs
    klant_ids = np.random.choice(klanten_df['PartnerId'], size=n)

    # Willekeurige leveringsdatums
    levering_datums = [random_date(start_date, end_date).strftime("%Y-%m-%d") for _ in range(n)]

    # Verwachte leverdatums (1-7 dagen na leveringsdatum)
    verwachte_leverdatums = []
    for levering_datum in levering_datums:
        levering_datum_obj = datetime.datetime.strptime(levering_datum, "%Y-%m-%d")
        days_to_add = np.random.randint(1, 8)
        verwachte_leverdatum_obj = levering_datum_obj + datetime.timedelta(days=days_to_add)
        verwachte_leverdatums.append(verwachte_leverdatum_obj.strftime("%Y-%m-%d"))

    # Maak een DataFrame
    data = {
        'LeveringId': list(range(1, n+1)),
        'PartnerId': klant_ids,
        'LeveringDatum': levering_datums,
        'VerwachteLeverdatum': verwachte_leverdatums
    }

    df = pd.DataFrame(data)

    # Sla op als CSV
    df.to_csv('csv_data/levering.csv', index=False)

    print("Levering data gegenereerd.")
    return df

# 13. LeveringRegel data
def generate_levering_regel_data(leveringen_df, products_df):
    """Genereer data voor de LeveringRegel koppeltabel met Pandas"""
    # Lijst voor alle levering-product combinaties
    levering_regel_list = []

    # Voor elke levering
    for levering_id in leveringen_df['LeveringId']:
        # Kies 1-5 willekeurige producten
        num_products = np.random.randint(1, 6)
        selected_products = np.random.choice(products_df['ProductId'], size=num_products, replace=False)

        # Voeg elke combinatie toe met een willekeurig aantal
        for product_id in selected_products:
            aantal = np.random.randint(100, 1001)
            levering_regel_list.append([levering_id, product_id, aantal])

    # Maak een DataFrame
    df = pd.DataFrame(levering_regel_list, columns=['LeveringId', 'ProductId', 'Aantal'])

    # Sla op als CSV
    df.to_csv('csv_data/levering_regel.csv', index=False)

    print("LeveringRegel data gegenereerd.")
    return df

# Voer het script uit
if __name__ == "__main__":
    generate_all_data()