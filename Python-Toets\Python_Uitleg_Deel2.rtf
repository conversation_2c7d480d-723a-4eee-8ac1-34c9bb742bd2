{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Courier New;}}
{\colortbl ;\red0\green0\blue255;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 PYTHON UITLEG - DEEL 2\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 2. OPERATOREN & EXPRESSIES\b0\fs22\par

\pard\sa200\sl276\slmult1\b Rekenkundige operatoren\b0\par
\pard\li360\sa200\sl276\slmult1\f1 a = 10\par
b = 3\par
\par
som = a + b          # 13\par
verschil = a - b     # 7\par
product = a * b      # 30\par
quotient = a / b     # 3.3333... (float deling)\par
int_quotient = a // b  # 3 (integer deling, afgerond naar beneden)\par
rest = a % b         # 1 (modulo/rest)\par
macht = a ** b       # 1000 (10^3)\f0\par

\pard\sa200\sl276\slmult1\b Vergelijkingsoperatoren\b0\par
\pard\li360\sa200\sl276\slmult1\f1 a = 10\par
b = 5\par
\par
is_gelijk = a == b      # False\par
is_ongelijk = a != b    # True\par
is_groter = a > b       # True\par
is_kleiner = a < b      # False\par
is_groter_gelijk = a >= b  # True\par
is_kleiner_gelijk = a <= b  # False\par
\par
# Vergelijkingen kunnen worden geketend\par
x = 5\par
print(1 < x < 10)  # True (x is tussen 1 en 10)\par
print(10 > x > 1)  # True (zelfde betekenis)\f0\par

\pard\sa200\sl276\slmult1\b Toewijzingsoperatoren\b0\par
\pard\li360\sa200\sl276\slmult1\f1 x = 10  # Basis toewijzing\par
\par
# Samengestelde toewijzingen\par
x += 5   # x = x + 5 (x wordt 15)\par
x -= 3   # x = x - 3 (x wordt 12)\par
x *= 2   # x = x * 2 (x wordt 24)\par
x /= 4   # x = x / 4 (x wordt 6.0)\par
x //= 2  # x = x // 2 (x wordt 3.0)\par
x %= 2   # x = x % 2 (x wordt 1.0)\par
x **= 3  # x = x ** 3 (x wordt 1.0)\f0\par

\pard\sa200\sl276\slmult1\b Logische operatoren\b0\par
\pard\li360\sa200\sl276\slmult1\f1 a = True\par
b = False\par
\par
en_resultaat = a and b  # False (beide moeten waar zijn)\par
of_resultaat = a or b   # True (minstens één moet waar zijn)\par
niet_a = not a          # False (keert de waarde om)\par
\par
# Kortsluiting (short-circuit evaluation)\par
# 'and' stopt zodra het een False tegenkomt\par
# 'or' stopt zodra het een True tegenkomt\par
\par
x = 5\par
y = 0\par
\par
# Dit voorkomt een ZeroDivisionError\par
resultaat = (y != 0) and (x / y > 2)  # False\f0\par

\pard\sa200\sl276\slmult1\b Bitwise operatoren\b0\par
\pard\li360\sa200\sl276\slmult1\f1 a = 60  # 0011 1100 in binair\par
b = 13  # 0000 1101 in binair\par
\par
# Bitwise AND\par
c = a & b  # 0000 1100 = 12\par
\par
# Bitwise OR\par
c = a | b  # 0011 1101 = 61\par
\par
# Bitwise XOR\par
c = a ^ b  # 0011 0001 = 49\par
\par
# Bitwise NOT\par
c = ~a  # 1100 0011 = -61 (in 2's complement)\par
\par
# Bitwise shift links\par
c = a << 2  # 1111 0000 = 240\par
\par
# Bitwise shift rechts\par
c = a >> 2  # 0000 1111 = 15\f0\par

\pard\sa200\sl276\slmult1\b Membership operatoren\b0\par
\pard\li360\sa200\sl276\slmult1\f1 lijst = [1, 2, 3, 4, 5]\par
tekst = "Python"\par
dict = \{"naam": "Jan", "leeftijd": 25\}\par
\par
# in operator\par
print(3 in lijst)        # True\par
print(6 in lijst)        # False\par
print("th" in tekst)     # True\par
print("naam" in dict)    # True (controleert keys)\par
\par
# not in operator\par
print(6 not in lijst)    # True\par
print("Java" not in tekst)  # True\f0\par

\pard\sa200\sl276\slmult1\b Identity operatoren\b0\par
\pard\li360\sa200\sl276\slmult1\f1 a = [1, 2, 3]\par
b = [1, 2, 3]\par
c = a\par
\par
# is operator (controleert of objecten dezelfde identiteit hebben)\par
print(a is b)  # False (verschillende objecten met dezelfde waarde)\par
print(a is c)  # True (c verwijst naar hetzelfde object als a)\par
\par
# is not operator\par
print(a is not b)  # True\par
\par
# Vergelijk met == (controleert gelijkheid van waarden)\par
print(a == b)  # True (dezelfde waarden)\f0\par

\pard\sa200\sl276\slmult1\b\fs24 3. CONTROLESTRUCTUREN\b0\fs22\par

\pard\sa200\sl276\slmult1\b if-elif-else statements\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Basis if-statement\par
x = 10\par
if x > 0:\par
    print("x is positief")\par
\par
# if-else\par
x = -5\par
if x > 0:\par
    print("x is positief")\par
else:\par
    print("x is niet positief")\par
\par
# if-elif-else (meerdere voorwaarden)\par
x = 0\par
if x > 0:\par
    print("x is positief")\par
elif x < 0:\par
    print("x is negatief")\par
else:\par
    print("x is nul")\par
\par
# Geneste if-statements\par
x = 10\par
y = 5\par
if x > 0:\par
    if y > 0:\par
        print("Beide zijn positief")\par
    else:\par
        print("Alleen x is positief")\par
\par
# Voorwaardelijke expressie (ternary operator)\par
x = 10\par
resultaat = "positief" if x > 0 else "niet positief"\par
print(resultaat)  # "positief"\f0\par

\pard\sa200\sl276\slmult1\b for-lus\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Itereren over een reeks getallen\par
for i in range(5):  # 0, 1, 2, 3, 4\par
    print(i)\par
\par
# range() met start, stop, stap\par
for i in range(2, 10, 2):  # 2, 4, 6, 8\par
    print(i)\par
\par
# Itereren over een lijst\par
kleuren = ["rood", "groen", "blauw"]\par
for kleur in kleuren:\par
    print(kleur)\par
\par
# Itereren over een string\par
for letter in "Python":\par
    print(letter)\par
\par
# Itereren over een dictionary\par
persoon = \{"naam": "Jan", "leeftijd": 25\}\par
\par
# Itereren over keys (standaard)\par
for key in persoon:\par
    print(key, persoon[key])\par
\par
# Itereren over key-value pairs\par
for key, value in persoon.items():\par
    print(key, value)\par
\par
# Itereren met index (enumerate)\par
kleuren = ["rood", "groen", "blauw"]\par
for index, kleur in enumerate(kleuren):\par
    print(index, kleur)  # 0 rood, 1 groen, 2 blauw\par
\par
# Itereren over meerdere lijsten tegelijk (zip)\par
namen = ["Alice", "Bob", "Charlie"]\par
scores = [85, 92, 78]\par
for naam, score in zip(namen, scores):\par
    print(naam, score)  # Alice 85, Bob 92, Charlie 78\f0\par

\pard\sa200\sl276\slmult1\b while-lus\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Basis while-lus\par
i = 0\par
while i < 5:\par
    print(i)  # 0, 1, 2, 3, 4\par
    i += 1\par
\par
# while met else (uitgevoerd als de voorwaarde False wordt)\par
i = 0\par
while i < 5:\par
    print(i)\par
    i += 1\par
else:\par
    print("Lus voltooid")\par
\par
# Oneindige lus met break\par
i = 0\par
while True:\par
    print(i)\par
    i += 1\par
    if i >= 5:\par
        break  # Verlaat de lus\f0\par

\pard\sa200\sl276\slmult1\b break, continue en pass\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # break: verlaat de lus\par
for i in range(10):\par
    if i == 5:\par
        break  # Stopt bij i=5\par
    print(i)  # 0, 1, 2, 3, 4\par
\par
# continue: gaat naar de volgende iteratie\par
for i in range(10):\par
    if i % 2 == 0:  # Als i even is\par
        continue  # Slaat de rest van de code over voor deze iteratie\par
    print(i)  # 1, 3, 5, 7, 9\par
\par
# pass: doet niets (placeholder)\par
for i in range(5):\par
    if i == 2:\par
        pass  # Placeholder voor toekomstige code\par
    print(i)  # 0, 1, 2, 3, 4\f0\par

\pard\sa200\sl276\slmult1\b\fs24 4. FUNCTIES\b0\fs22\par

\pard\sa200\sl276\slmult1\b Functie definitie en aanroep\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Basis functie definitie\par
def groet():\par
    print("Hallo, wereld!")\par
\par
# Functie aanroepen\par
groet()  # Hallo, wereld!\par
\par
# Functie met parameters\par
def groet_persoon(naam):\par
    print(f"Hallo, \{naam\}!")\par
\par
groet_persoon("Alice")  # Hallo, Alice!\par
\par
# Functie met return waarde\par
def som(a, b):\par
    return a + b\par
\par
resultaat = som(5, 3)  # 8\par
print(resultaat)\par
\par
# Functie met meerdere return waarden\par
def bereken(a, b):\par
    som = a + b\par
    verschil = a - b\par
    product = a * b\par
    return som, verschil, product\par
\par
s, v, p = bereken(5, 3)  # s=8, v=2, p=15\f0\par

\pard\sa200\sl276\slmult1\b Parameters en argumenten\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Verplichte parameters\par
def groet(naam, bericht):\par
    print(f"\{bericht\}, \{naam\}!")\par
\par
groet("Alice", "Hallo")  # Hallo, Alice!\par
\par
# Standaardwaarden (default parameters)\par
def groet(naam, bericht="Hallo"):\par
    print(f"\{bericht\}, \{naam\}!")\par
\par
groet("Alice")  # Hallo, Alice!\par
groet("Bob", "Goedemorgen")  # Goedemorgen, Bob!\par
\par
# Positionele argumenten\par
def beschrijf_huisdier(soort, naam):\par
    print(f"\{naam\} is een \{soort\}")\par
\par
beschrijf_huisdier("kat", "Whiskers")  # Whiskers is een kat\par
\par
# Keyword argumenten\par
beschrijf_huisdier(naam="Rex", soort="hond")  # Rex is een hond\par
\par
# Combinatie van positioneel en keyword\par
beschrijf_huisdier("hamster", naam="Fluffy")  # Fluffy is een hamster\f0\par

\pard\sa200\sl276\slmult1\b Variabel aantal argumenten\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # *args: variabel aantal positionele argumenten (tuple)\par
def som(*getallen):\par
    resultaat = 0\par
    for getal in getallen:\par
        resultaat += getal\par
    return resultaat\par
\par
print(som(1, 2))  # 3\par
print(som(1, 2, 3, 4, 5))  # 15\par
\par
# **kwargs: variabel aantal keyword argumenten (dictionary)\par
def beschrijf_persoon(**eigenschappen):\par
    for key, value in eigenschappen.items():\par
        print(f"\{key\}: \{value\}")\par
\par
beschrijf_persoon(naam="Alice", leeftijd=30, baan="Ontwikkelaar")\par
# naam: Alice\par
# leeftijd: 30\par
# baan: Ontwikkelaar\par
\par
# Combinatie van normale parameters, *args en **kwargs\par
def voorbeeld(a, b, *args, **kwargs):\par
    print(f"a = \{a\}, b = \{b\}")\par
    print(f"args = \{args\}")\par
    print(f"kwargs = \{kwargs\}")\par
\par
voorbeeld(1, 2, 3, 4, 5, x=10, y=20)\par
# a = 1, b = 2\par
# args = (3, 4, 5)\par
# kwargs = \{'x': 10, 'y': 20\}\f0\par

\pard\sa200\sl276\slmult1\b Lambda functies\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Lambda functie (anonieme functie)\par
som = lambda a, b: a + b\par
print(som(5, 3))  # 8\par
\par
# Lambda met meerdere parameters\par
berekening = lambda x, y, z: x * y + z\par
print(berekening(2, 3, 4))  # 10\par
\par
# Lambda in combinatie met andere functies\par
getallen = [1, 5, 2, 8, 3]\par
\par
# Sorteren met een aangepaste sleutel\par
gesorteerd = sorted(getallen, key=lambda x: abs(x - 5))\par
print(gesorteerd)  # [5, 3, 8, 2, 1] (gesorteerd op afstand tot 5)\par
\par
# Lambda met filter\par
even = list(filter(lambda x: x % 2 == 0, getallen))\par
print(even)  # [2, 8]\par
\par
# Lambda met map\par
kwadraten = list(map(lambda x: x**2, getallen))\par
print(kwadraten)  # [1, 25, 4, 64, 9]\f0\par

\pard\sa200\sl276\slmult1\b Scope en levensduur van variabelen\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Lokale en globale variabelen\par
x = 10  # Globale variabele\par
\par
def functie():\par
    y = 5  # Lokale variabele\par
    print(x)  # Toegang tot globale variabele\par
    print(y)  # Toegang tot lokale variabele\par
\par
functie()  # 10, 5\par
# print(y)  # NameError: y is niet gedefinieerd buiten de functie\par
\par
# Wijzigen van globale variabelen\par
x = 10\par
\par
def wijzig_x():\par
    global x  # Geeft aan dat we de globale x willen wijzigen\par
    x = 20\par
\par
print(x)  # 10\par
wijzig_x()\par
print(x)  # 20\par
\par
# Geneste functies en nonlocal\par
def outer():\par
    x = 10\par
    \par
    def inner():\par
        nonlocal x  # Geeft aan dat we de x van de omsluitende functie willen wijzigen\par
        x = 20\par
    \par
    print(x)  # 10\par
    inner()\par
    print(x)  # 20\par
\par
outer()\f0\par
}
