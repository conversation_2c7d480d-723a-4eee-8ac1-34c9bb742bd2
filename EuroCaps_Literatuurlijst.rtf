{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 LITERATUURLIJST\b0\fs22\par

\pard\sa200\sl276\slmult1 Connolly, T., & Be<PERSON>, C. (2020). \i Database systems: A practical approach to design, implementation, and management\i0  (7th ed.). Pearson.\par
Date, C. J. (2019). \i Database design and relational theory: Normal forms and all that jazz\i0  (2nd ed.). Apress.\par
<PERSON>, R., & <PERSON>, S. B. (2017). \i Fundamentals of database systems\i0  (7th ed.). Pearson.\par
Euro Caps. (n.d.). \i Euro Caps case study documents\i0 .\par
<PERSON>, <PERSON>, <PERSON>, J. D., & <PERSON>, J. (2020). \i Database systems: The complete book\i0  (3rd ed.). <PERSON>.\<PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON> (2016). \i Modern database management\i0  (12th ed.). Pearson.\par
<PERSON>ball, R., & <PERSON>, M. (2013). \i The data warehouse toolkit: The definitive guide to dimensional modeling\i0  (3rd ed.). Wiley.\par
Kroenke, D. M., & Auer, D. J. (2016). \i Database processing: Fundamentals, design, and implementation\i0  (14th ed.). Pearson.\par
McKinney, W. (2017). \i Python for data analysis: Data wrangling with Pandas, NumPy, and IPython\i0  (2nd ed.). O'Reilly Media.\par
MySQL. (2023). \i MySQL 8.0 reference manual\i0 . https://dev.mysql.com/doc/refman/8.0/en/\par
Nintex. (n.d.). \i Process automation maximizes product quality at Euro Caps\i0  [PDF case study].\par
Oppel, A. (2011). \i Databases: A beginner's guide\i0 . McGraw-Hill Education.\par
Silberschatz, A., Korth, H. F., & Sudarshan, S. (2019). \i Database system concepts\i0  (7th ed.). McGraw-Hill Education.\par
Teorey, T. J., Lightstone, S. S., Nadeau, T., & Jagadish, H. V. (2011). \i Database modeling and design: Logical design\i0  (5th ed.). Morgan Kaufmann.\par
VanderPlas, J. (2016). \i Python data science handbook: Essential tools for working with data\i0 . O'Reilly Media.\par
Wambler, S. (2015). \i Agile data warehouse design: Collaborative dimensional modeling, from whiteboard to star schema\i0 . DecisionOne Press.\par
}
