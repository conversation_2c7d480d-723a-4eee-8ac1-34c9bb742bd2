# 🎯 ALLE SCHERMEN IMPLEMENTATIE GIDS
## Complete PowerApps Implementatie met Exacte Code Locaties

---

## 📋 **OVERZICHT: 10 SCHERMEN + DATABASE INTEGRATIE**

### **✅ ALLE SCHERMEN DIE GEÏMPLEMENTEERD MOETEN WORDEN:**

1. **UC1 - Login Screen** ✅ (Ba<PERSON> k<PERSON>, functionaliteit uitbreiden)
2. **UC2 - Dashboard Screen** 🔄 (Uitbreiden met nieuwe data)
3. **UC3 - Customer List Screen** 🆕 (Nieuw maken)
4. **UC4 - Product Catalog Screen** 🆕 (Nieuw maken)
5. **UC5 - New Order Screen** 🆕 (Nieuw maken)
6. **UC6 - Order Details Screen** 🆕 (Nieuw maken)
7. **UC7 - Order Items Screen** 🆕 (Nieuw maken)
8. **UC8 - Order History Screen** 🆕 (Nieuw maken)
9. **UC9 - Order Confirmation Screen** 🆕 (Nieuw maken)
10. **UC10 - Settings Screen** 🆕 (Nieuw maken)

### **🗄️ NIEUWE DATABASE INTEGRATIE:**
- **Raw Materials Management** (Grondstoffen)
- **Supplier Management** (Leveranciers)
- **Stakeholder Management** (Stakeholders)

---

## 🚀 **STAP 1: APP CONFIGURATIE (VERPLICHT EERST)**

### **Waar:** App → OnStart eigenschap

```powerapps
// ===== GEBRUIKERS AUTHENTICATIE =====
Set(varUserRole, "");
Set(varUserName, "");
Set(varRememberMe, false);

// ===== NAVIGATIE VARIABELEN =====
Set(varCurrentScreen, "Login");
Set(varSelectedCustomer, Blank());
Set(varSelectedProduct, Blank());
Set(varSelectedOrder, Blank());

// ===== ZOEK EN FILTER VARIABELEN =====
Set(varSearchText, "");
Set(varFilterStatus, "All");
Set(varProductSearch, "");
Set(varProductType, "All");
Set(varPackageSize, "All");

// ===== ORDER MANAGEMENT =====
Set(varCurrentOrder, {Items: [], TotalAmount: 0, ItemCount: 0});
Set(varOrderMode, "New");

// ===== KLANTEN DATA =====
ClearCollect(colCustomers,
    {CustomerID: 1, CustomerName: "Coffee World", ContactPerson: "David Lee", Email: "<EMAIL>", Phone: "+31 20 123 4567", Address: "Koffieweg 10, Amsterdam", Status: "Active", CreditLimit: 10000, PaymentTerms: "30 days"},
    {CustomerID: 2, CustomerName: "Bean Lovers", ContactPerson: "John Smith", Email: "<EMAIL>", Phone: "+31 30 234 5678", Address: "Bonenstraat 25, Utrecht", Status: "Active", CreditLimit: 15000, PaymentTerms: "45 days"},
    {CustomerID: 3, CustomerName: "Café Express", ContactPerson: "Maria Garcia", Email: "<EMAIL>", Phone: "+31 10 345 6789", Address: "Espressolaan 5, Rotterdam", Status: "Active", CreditLimit: 8000, PaymentTerms: "30 days"},
    {CustomerID: 4, CustomerName: "Morning Brew Co.", ContactPerson: "James Wilson", Email: "<EMAIL>", Phone: "+31 40 456 7890", Address: "Koffiepark 12, Eindhoven", Status: "Active", CreditLimit: 12000, PaymentTerms: "30 days"},
    {CustomerID: 5, CustomerName: "Espresso Central", ContactPerson: "Anna Müller", Email: "<EMAIL>", Phone: "+49 30 567 8901", Address: "Kaffeestraße 8, Berlin", Status: "Active", CreditLimit: 20000, PaymentTerms: "60 days"}
);

// ===== PRODUCTEN DATA =====
ClearCollect(colProducts,
    {ProductID: 1, ProductName: "Espresso Classic", ProductType: "espresso", PackageSize: 10, Description: "Traditional Italian-style espresso", Price: 4.99, Status: "Active", StockLevel: 150},
    {ProductID: 2, ProductName: "Lungo Intense", ProductType: "lungo", PackageSize: 20, Description: "Rich and intense lungo", Price: 8.99, Status: "Active", StockLevel: 120},
    {ProductID: 3, ProductName: "Ristretto Strong", ProductType: "ristretto", PackageSize: 10, Description: "Extra strong ristretto", Price: 5.49, Status: "Active", StockLevel: 80},
    {ProductID: 4, ProductName: "Vanilla Flavored", ProductType: "flavored", PackageSize: 20, Description: "Smooth vanilla flavored coffee", Price: 9.99, Status: "Active", StockLevel: 90},
    {ProductID: 5, ProductName: "Caramel Delight", ProductType: "flavored", PackageSize: 20, Description: "Rich caramel flavored coffee", Price: 10.49, Status: "Active", StockLevel: 75},
    {ProductID: 6, ProductName: "Espresso Decaf", ProductType: "espresso", PackageSize: 10, Description: "Decaffeinated espresso", Price: 5.29, Status: "Active", StockLevel: 60},
    {ProductID: 7, ProductName: "Lungo Mild", ProductType: "lungo", PackageSize: 44, Description: "Mild lungo blend", Price: 18.99, Status: "Active", StockLevel: 45},
    {ProductID: 8, ProductName: "Hazelnut Special", ProductType: "flavored", PackageSize: 44, Description: "Premium hazelnut flavored", Price: 22.99, Status: "Active", StockLevel: 30}
);

// ===== ORDERS DATA =====
ClearCollect(colOrders,
    {OrderID: 1, OrderNumber: "ORD-1089", CustomerID: 1, OrderDate: Today()-1, DeliveryDate: Today()+3, Status: "new", Notes: "Rush order", TotalAmount: 49.90, CreatedBy: "sales"},
    {OrderID: 2, OrderNumber: "ORD-1088", CustomerID: 2, OrderDate: Today()-2, DeliveryDate: Today()+4, Status: "processing", Notes: "Standard delivery", TotalAmount: 89.90, CreatedBy: "service"},
    {OrderID: 3, OrderNumber: "ORD-1087", CustomerID: 3, OrderDate: Today()-3, DeliveryDate: Today()+2, Status: "shipped", Notes: "Express delivery", TotalAmount: 54.90, CreatedBy: "sales"},
    {OrderID: 4, OrderNumber: "ORD-1086", CustomerID: 4, OrderDate: Today()-5, DeliveryDate: Today()+1, Status: "delivered", Notes: "Regular order", TotalAmount: 129.80, CreatedBy: "service"},
    {OrderID: 5, OrderNumber: "ORD-1085", CustomerID: 5, OrderDate: Today()-7, DeliveryDate: Today()-2, Status: "cancelled", Notes: "Customer cancelled", TotalAmount: 0, CreatedBy: "manager"}
);

// ===== ORDER ITEMS DATA =====
ClearCollect(colOrderItems,
    {OrderItemID: 1, OrderID: 1, ProductID: 1, Quantity: 10, UnitPrice: 4.99, TotalPrice: 49.90},
    {OrderItemID: 2, OrderID: 2, ProductID: 2, Quantity: 10, UnitPrice: 8.99, TotalPrice: 89.90},
    {OrderItemID: 3, OrderID: 3, ProductID: 3, Quantity: 10, UnitPrice: 5.49, TotalPrice: 54.90},
    {OrderItemID: 4, OrderID: 4, ProductID: 4, Quantity: 5, UnitPrice: 9.99, TotalPrice: 49.95},
    {OrderItemID: 5, OrderID: 4, ProductID: 5, Quantity: 8, UnitPrice: 10.49, TotalPrice: 79.92}
);

// ===== RAW MATERIALS DATA =====
ClearCollect(colRawMaterials,
    {MaterialID: 1, MaterialName: "Arabica Coffee Beans - Premium", MaterialType: "Coffee", SupplierID: 1, StockLevel: 500, UnitCost: 12.50, ReorderPoint: 100, Unit: "kg", QualityGrade: "A"},
    {MaterialID: 2, MaterialName: "Robusta Coffee Beans - Strong", MaterialType: "Coffee", SupplierID: 1, StockLevel: 300, UnitCost: 10.75, ReorderPoint: 75, Unit: "kg", QualityGrade: "A"},
    {MaterialID: 3, MaterialName: "Aluminum Capsules - Standard", MaterialType: "Packaging", SupplierID: 2, StockLevel: 10000, UnitCost: 0.15, ReorderPoint: 2000, Unit: "pieces", QualityGrade: "A"},
    {MaterialID: 4, MaterialName: "Aluminum Capsules - Premium", MaterialType: "Packaging", SupplierID: 2, StockLevel: 5000, UnitCost: 0.18, ReorderPoint: 1000, Unit: "pieces", QualityGrade: "A+"}
);

// ===== SUPPLIERS DATA =====
ClearCollect(colSuppliers,
    {SupplierID: 1, SupplierName: "Premium Coffee Co.", ContactPerson: "Maria Santos", Email: "<EMAIL>", Phone: "+31 20 555 0101", Country: "Netherlands", Rating: 4.8, Status: "Active"},
    {SupplierID: 2, SupplierName: "Aluminum Solutions BV", ContactPerson: "Jan de Vries", Email: "<EMAIL>", Phone: "+31 30 555 0202", Country: "Netherlands", Rating: 4.6, Status: "Active"},
    {SupplierID: 3, SupplierName: "PackTech Europe", ContactPerson: "Sophie Mueller", Email: "<EMAIL>", Phone: "+49 30 555 0303", Country: "Germany", Rating: 4.2, Status: "Active"},
    {SupplierID: 4, SupplierName: "FlavorMasters International", ContactPerson: "Pierre Dubois", Email: "<EMAIL>", Phone: "+33 1 555 0404", Country: "France", Rating: 4.9, Status: "Active"}
);

// ===== BEREKENINGEN =====
Set(varReorderAlerts, Filter(colRawMaterials, StockLevel <= ReorderPoint));
Set(varLowStockCount, CountRows(varReorderAlerts));
Set(varTotalRevenue, Sum(colOrders, TotalAmount));
Set(varNewOrdersCount, CountRows(Filter(colOrders, Status = "new")));
Set(varActiveCustomersCount, CountRows(Filter(colCustomers, Status = "Active")));

// ===== SETTINGS =====
Set(varDefaultDeliveryDays, 7);
Set(varItemsPerPage, 10);
Set(varDateFormat, "dd/mm/yyyy")
```

---

## 🔐 **STAP 2: LOGIN SCREEN (UC1) - FUNCTIONALITEIT TOEVOEGEN**

### **Login Screen is al klaar, voeg alleen functionaliteit toe:**

**Waar:** Login Screen → OnVisible eigenschap
```powerapps
Set(varCurrentScreen, "Login");
If(!IsBlank(Get("RememberedUser")), Set(varRememberMe, true))
```

**Waar:** Login Button → OnSelect eigenschap (vervang bestaande)
```powerapps
If(
    !IsBlank(UsernameInput.Text) && !IsBlank(PasswordInput.Text),
    Switch(
        Lower(UsernameInput.Text),
        "sales", Set(varUserRole, "Sales Representative"); Set(varUserName, "Sales User"),
        "service", Set(varUserRole, "Customer Service"); Set(varUserName, "Service User"),
        "manager", Set(varUserRole, "Manager"); Set(varUserName, "Manager User"),
        "admin", Set(varUserRole, "Admin"); Set(varUserName, "Admin User"),
        Set(varUserRole, "Sales Representative"); Set(varUserName, UsernameInput.Text)
    );
    If(RememberMeCheckbox.Value, Set("RememberedUser", UsernameInput.Text), Remove("RememberedUser"));
    Navigate(Dashboard_Screen, ScreenTransition.Fade);
    Notify("Welcome " & varUserName & " (" & varUserRole & ")", NotificationType.Success),
    Notify("Please enter both username and password", NotificationType.Error)
)
```

---

## 📊 **STAP 3: DASHBOARD SCREEN (UC2) - UITBREIDEN**

### **Dashboard Screen uitbreiden:**

**Waar:** Dashboard Screen → OnVisible eigenschap
```powerapps
Set(varCurrentScreen, "Dashboard");
Refresh(colCustomers);
Refresh(colProducts);
Refresh(colOrders);
Set(varReorderAlerts, Filter(colRawMaterials, StockLevel <= ReorderPoint));
Set(varLowStockCount, CountRows(varReorderAlerts));
Set(varTotalRevenue, Sum(colOrders, TotalAmount));
Set(varNewOrdersCount, CountRows(Filter(colOrders, Status = "new")));
Set(varActiveCustomersCount, CountRows(Filter(colCustomers, Status = "Active")))
```

### **Nieuwe Menu Items Toevoegen:**

#### **Raw Materials Menu Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"🏭 Raw Materials"`
**Waar:** Fill eigenschap: `RGBA(44, 62, 80, 1)`
**Waar:** HoverFill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `50`
**Waar:** Width eigenschap: `200`
**Waar:** OnSelect eigenschap
```powerapps
Navigate(Raw_Materials_Screen, ScreenTransition.Fade)
```

#### **Suppliers Menu Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"🚚 Suppliers"`
**Waar:** Fill eigenschap: `RGBA(44, 62, 80, 1)`
**Waar:** HoverFill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `50`
**Waar:** Width eigenschap: `200`
**Waar:** OnSelect eigenschap
```powerapps
Navigate(Supplier_Management_Screen, ScreenTransition.Fade)
```

### **Dashboard Cards Uitbreiden:**

#### **Low Stock Alert Card**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap
```powerapps
If(varLowStockCount > 0, RGBA(220, 53, 69, 1), RGBA(243, 156, 18, 1))
```
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `120`
**Waar:** Width eigenschap: `250`

**Toevoegen:** Insert → Text → Label (titel)
**Waar:** Text eigenschap: `"LOW STOCK ALERTS"`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`

**Toevoegen:** Insert → Text → Label (aantal)
**Waar:** Text eigenschap: `varLowStockCount`
**Waar:** Color eigenschap
```powerapps
If(varLowStockCount > 0, RGBA(220, 53, 69, 1), RGBA(40, 167, 69, 1))
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `24`

**BELANGRIJK:** Vervang `UsernameInput`, `PasswordInput`, `RememberMeCheckbox` met werkelijke control namen!

---

## 👥 **STAP 4: CUSTOMER LIST SCREEN (UC3) - NIEUW MAKEN**

### **Nieuwe Screen Maken:**
1. **Insert → New screen → Blank**
2. **Hernoem naar:** `Customer_List_Screen`

### **Scherm Eigenschappen:**
**Waar:** Customer List Screen → Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```

**Waar:** Customer List Screen → OnVisible eigenschap
```powerapps
Set(varCurrentScreen, "CustomerList");
Set(varSearchText, "");
Set(varFilterStatus, "All");
Refresh(colCustomers)
```

### **Header en Navigation (Kopieer van Dashboard):**
**Kopieer alle header en navigation controls van Dashboard Screen**
**Verander alleen:** Customer menu button → Fill eigenschap: `RGBA(243, 156, 18, 1)`

### **Customer List Specifieke Controls:**

#### **Page Title**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"Customer Management"`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `18`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `300`

#### **Search Section Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `80`
**Waar:** Width eigenschap: `1100`

#### **Customer Search Input**
**Toevoegen:** Insert → Input → Text input
**Waar:** HintText eigenschap: `"Search customers by name or contact..."`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Height eigenschap: `35`
**Waar:** Width eigenschap: `300`
**Waar:** OnChange eigenschap
```powerapps
Set(varSearchText, Self.Text)
```

#### **Status Filter Dropdown**
**Toevoegen:** Insert → Input → Drop down
**Waar:** Items eigenschap
```powerapps
["All", "Active", "Inactive"]
```
**Waar:** DefaultSelectedItems eigenschap: `["All"]`
**Waar:** Height eigenschap: `35`
**Waar:** Width eigenschap: `120`
**Waar:** OnChange eigenschap
```powerapps
Set(varFilterStatus, Self.Selected.Value)
```

#### **New Customer Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"+ New Customer"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** HoverFill eigenschap: `RGBA(230, 140, 5, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `35`
**Waar:** Width eigenschap: `150`
**Waar:** OnSelect eigenschap
```powerapps
Notify("New Customer functionality will be implemented", NotificationType.Information)
```

#### **Customers Gallery**
**Toevoegen:** Insert → Layout → Vertical gallery
**Waar:** Items eigenschap
```powerapps
Filter(
    colCustomers,
    (IsBlank(varSearchText) ||
     varSearchText in CustomerName ||
     varSearchText in ContactPerson) &&
    (varFilterStatus = "All" || Status = varFilterStatus)
)
```
**Waar:** TemplateSize eigenschap: `80`
**Waar:** ShowScrollbar eigenschap: `true`
**Waar:** Height eigenschap: `400`
**Waar:** Width eigenschap: `1100`

### **Gallery Template Configuratie:**
**Selecteer Gallery → Edit template**

#### **Customer Card Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `70`
**Waar:** Width eigenschap: `1080`

#### **Customer Name Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `ThisItem.CustomerName`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`
**Waar:** Size eigenschap: `14`
**Waar:** Height eigenschap: `25`
**Waar:** Width eigenschap: `200`

#### **Contact Person Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `ThisItem.ContactPerson`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`
**Waar:** Size eigenschap: `12`
**Waar:** Height eigenschap: `20`
**Waar:** Width eigenschap: `150`

#### **Email Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `ThisItem.Email`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`
**Waar:** Size eigenschap: `12`
**Waar:** Height eigenschap: `20`
**Waar:** Width eigenschap: `200`

#### **Phone Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `ThisItem.Phone`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`
**Waar:** Size eigenschap: `12`
**Waar:** Height eigenschap: `20`
**Waar:** Width eigenschap: `150`

#### **Status Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `ThisItem.Status`
**Waar:** Color eigenschap
```powerapps
If(ThisItem.Status = "Active", RGBA(40, 167, 69, 1), RGBA(220, 53, 69, 1))
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `12`
**Waar:** Height eigenschap: `20`
**Waar:** Width eigenschap: `80`

#### **View Details Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"👁️ View"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `25`
**Waar:** Width eigenschap: `60`
**Waar:** OnSelect eigenschap
```powerapps
Set(varSelectedCustomer, ThisItem);
Notify("Customer details: " & ThisItem.CustomerName, NotificationType.Information)
```

#### **New Order Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"📋 New Order"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `25`
**Waar:** Width eigenschap: `100`
**Waar:** OnSelect eigenschap
```powerapps
Set(varSelectedCustomer, ThisItem);
Navigate(New_Order_Screen, ScreenTransition.Fade)
```

---

## 📦 **STAP 5: PRODUCT CATALOG SCREEN (UC4) - NIEUW MAKEN**

### **Nieuwe Screen Maken:**
1. **Insert → New screen → Blank**
2. **Hernoem naar:** `Product_Catalog_Screen`

### **Scherm Eigenschappen:**
**Waar:** Product Catalog Screen → Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```

**Waar:** Product Catalog Screen → OnVisible eigenschap
```powerapps
Set(varCurrentScreen, "ProductCatalog");
Set(varProductSearch, "");
Set(varProductType, "All");
Set(varPackageSize, "All");
Refresh(colProducts)
```

### **Header en Navigation (Kopieer van Dashboard):**
**Kopieer alle header en navigation controls van Dashboard Screen**
**Verander alleen:** Products menu button → Fill eigenschap: `RGBA(243, 156, 18, 1)`

### **Product Catalog Specifieke Controls:**

#### **Page Title**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"Product Catalog"`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `18`

#### **Search and Filter Section Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `80`
**Waar:** Width eigenschap: `1100`

#### **Product Search Input**
**Toevoegen:** Insert → Input → Text input
**Waar:** HintText eigenschap: `"Search products by name..."`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Height eigenschap: `35`
**Waar:** Width eigenschap: `250`
**Waar:** OnChange eigenschap
```powerapps
Set(varProductSearch, Self.Text)
```

#### **Product Type Filter**
**Toevoegen:** Insert → Input → Drop down
**Waar:** Items eigenschap
```powerapps
["All", "espresso", "lungo", "ristretto", "flavored"]
```
**Waar:** DefaultSelectedItems eigenschap: `["All"]`
**Waar:** Height eigenschap: `35`
**Waar:** Width eigenschap: `120`
**Waar:** OnChange eigenschap
```powerapps
Set(varProductType, Self.Selected.Value)
```

#### **Package Size Filter**
**Toevoegen:** Insert → Input → Drop down
**Waar:** Items eigenschap
```powerapps
["All", "10", "20", "44"]
```
**Waar:** DefaultSelectedItems eigenschap: `["All"]`
**Waar:** Height eigenschap: `35`
**Waar:** Width eigenschap: `100`
**Waar:** OnChange eigenschap
```powerapps
Set(varPackageSize, Self.Selected.Value)
```

#### **Products Gallery**
**Toevoegen:** Insert → Layout → Flexible height gallery
**Waar:** Items eigenschap
```powerapps
Filter(
    colProducts,
    (IsBlank(varProductSearch) ||
     varProductSearch in ProductName ||
     varProductSearch in Description) &&
    (varProductType = "All" || ProductType = varProductType) &&
    (varPackageSize = "All" || Text(PackageSize) = varPackageSize)
)
```
**Waar:** TemplateSize eigenschap: `200`
**Waar:** ShowScrollbar eigenschap: `true`
**Waar:** Height eigenschap: `400`
**Waar:** Width eigenschap: `1100`

### **Gallery Template Configuratie:**
**Selecteer Gallery → Edit template**

#### **Product Card Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `180`
**Waar:** Width eigenschap: `250`

#### **Product Image**
**Toevoegen:** Insert → Media → Image
**Waar:** Image eigenschap: `"coffee-capsule-placeholder"`
**Waar:** Height eigenschap: `80`
**Waar:** Width eigenschap: `80`

#### **Product Name Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `ThisItem.ProductName`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`
**Waar:** Size eigenschap: `14`

#### **Product Type Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `Upper(ThisItem.ProductType)`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`
**Waar:** Size eigenschap: `12`

#### **Package Size Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `ThisItem.PackageSize & " capsules"`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`
**Waar:** Size eigenschap: `12`

#### **Price Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"€" & Text(ThisItem.Price, "0.00")`
**Waar:** Color eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `16`

#### **Add to Order Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"+ Add to Order"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `30`
**Waar:** Width eigenschap: `120`
**Waar:** OnSelect eigenschap
```powerapps
Set(varSelectedProduct, ThisItem);
Notify("Product added: " & ThisItem.ProductName, NotificationType.Success)
```

---

## 📋 **STAP 6: NEW ORDER SCREEN (UC5) - NIEUW MAKEN**

### **Nieuwe Screen Maken:**
1. **Insert → New screen → Blank**
2. **Hernoem naar:** `New_Order_Screen`

### **Scherm Eigenschappen:**
**Waar:** New Order Screen → Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```

**Waar:** New Order Screen → OnVisible eigenschap
```powerapps
Set(varCurrentScreen, "NewOrder");
Set(varOrderMode, "New");
Set(varCurrentOrder, {Items: [], TotalAmount: 0, ItemCount: 0});
Refresh(colCustomers);
Refresh(colProducts)
```

### **Header en Navigation (Kopieer van Dashboard):**
**Kopieer alle header en navigation controls van Dashboard Screen**

### **New Order Specifieke Controls:**

#### **Page Title**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"Create New Order"`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `18`

#### **Order Information Section Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `200`
**Waar:** Width eigenschap: `500`

#### **Customer Dropdown**
**Toevoegen:** Insert → Input → Drop down
**Waar:** Items eigenschap: `colCustomers`
**Waar:** DisplayFields eigenschap: `["CustomerName"]`
**Waar:** SearchFields eigenschap: `["CustomerName", "ContactPerson"]`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `300`
**Waar:** OnChange eigenschap
```powerapps
Set(varSelectedCustomer, Self.Selected)
```

#### **Order Date Picker**
**Toevoegen:** Insert → Input → Date picker
**Waar:** DefaultDate eigenschap: `Today()`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `200`

#### **Delivery Date Picker**
**Toevoegen:** Insert → Input → Date picker
**Waar:** DefaultDate eigenschap: `Today() + varDefaultDeliveryDays`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `200`

#### **Notes Text Input**
**Toevoegen:** Insert → Input → Text input
**Waar:** Mode eigenschap: `TextMode.MultiLine`
**Waar:** HintText eigenschap: `"Order notes (optional)"`
**Waar:** Height eigenschap: `60`
**Waar:** Width eigenschap: `300`

#### **Add Products Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"+ Add Products"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `150`
**Waar:** OnSelect eigenschap
```powerapps
Navigate(Order_Items_Screen, ScreenTransition.Fade)
```

#### **Order Items Gallery**
**Toevoegen:** Insert → Layout → Vertical gallery
**Waar:** Items eigenschap: `varCurrentOrder.Items`
**Waar:** TemplateSize eigenschap: `60`
**Waar:** Height eigenschap: `200`
**Waar:** Width eigenschap: `600`
**Waar:** Visible eigenschap
```powerapps
CountRows(varCurrentOrder.Items) > 0
```

#### **Order Summary Section Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `100`
**Waar:** Width eigenschap: `300`

#### **Total Amount Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"Total: €" & Text(varCurrentOrder.TotalAmount, "0.00")
```
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `16`

#### **Submit Order Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"Submit Order"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `45`
**Waar:** Width eigenschap: `150`
**Waar:** OnSelect eigenschap
```powerapps
If(
    !IsBlank(varSelectedCustomer) && CountRows(varCurrentOrder.Items) > 0,
    // Create new order
    Patch(colOrders, Defaults(colOrders), {
        OrderNumber: "ORD-" & Text(Rand() * 10000, "0000"),
        CustomerID: varSelectedCustomer.CustomerID,
        OrderDate: OrderDatePicker.SelectedDate,
        DeliveryDate: DeliveryDatePicker.SelectedDate,
        Status: "new",
        Notes: NotesInput.Text,
        TotalAmount: varCurrentOrder.TotalAmount,
        CreatedBy: varUserRole
    });
    Navigate(Order_Confirmation_Screen, ScreenTransition.Fade);
    Notify("Order submitted successfully!", NotificationType.Success),
    Notify("Please select customer and add products", NotificationType.Error)
)
```

#### **Save as Draft Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"Save as Draft"`
**Waar:** Fill eigenschap: `RGBA(108, 117, 125, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `45`
**Waar:** Width eigenschap: `150`
**Waar:** OnSelect eigenschap
```powerapps
Notify("Draft saved", NotificationType.Information)
```

---

## 📄 **STAP 7: ORDER DETAILS SCREEN (UC6) - NIEUW MAKEN**

### **Nieuwe Screen Maken:**
1. **Insert → New screen → Blank**
2. **Hernoem naar:** `Order_Details_Screen`

### **Scherm Eigenschappen:**
**Waar:** Order Details Screen → Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```

**Waar:** Order Details Screen → OnVisible eigenschap
```powerapps
Set(varCurrentScreen, "OrderDetails");
If(!IsBlank(varSelectedOrder),
    Set(varOrderCustomer, LookUp(colCustomers, CustomerID = varSelectedOrder.CustomerID));
    Set(varOrderItems, Filter(colOrderItems, OrderID = varSelectedOrder.OrderID))
)
```

### **Header en Navigation (Kopieer van Dashboard):**
**Kopieer alle header en navigation controls van Dashboard Screen**

### **Order Details Specifieke Controls:**

#### **Page Title**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"Order Details - " & varSelectedOrder.OrderNumber
```
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `18`

#### **Order Information Section Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `200`
**Waar:** Width eigenschap: `500`

#### **Order Number Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"Order Number: " & varSelectedOrder.OrderNumber
```
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`

#### **Customer Name Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"Customer: " & varOrderCustomer.CustomerName
```
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`

#### **Order Date Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"Order Date: " & Text(varSelectedOrder.OrderDate, "dd/mm/yyyy")
```
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`

#### **Delivery Date Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"Delivery Date: " & Text(varSelectedOrder.DeliveryDate, "dd/mm/yyyy")
```
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`

#### **Status Dropdown**
**Toevoegen:** Insert → Input → Drop down
**Waar:** Items eigenschap
```powerapps
["new", "processing", "shipped", "delivered", "cancelled"]
```
**Waar:** DefaultSelectedItems eigenschap
```powerapps
[varSelectedOrder.Status]
```
**Waar:** OnChange eigenschap
```powerapps
Patch(colOrders, varSelectedOrder, {Status: Self.Selected.Value});
Set(varSelectedOrder, Patch(varSelectedOrder, {Status: Self.Selected.Value}));
Notify("Order status updated", NotificationType.Success)
```

#### **Order Items Gallery**
**Toevoegen:** Insert → Layout → Vertical gallery
**Waar:** Items eigenschap
```powerapps
AddColumns(
    varOrderItems,
    "ProductName", LookUp(colProducts, ProductID = ThisRecord.ProductID).ProductName,
    "ProductType", LookUp(colProducts, ProductID = ThisRecord.ProductID).ProductType
)
```
**Waar:** TemplateSize eigenschap: `60`
**Waar:** Height eigenschap: `300`
**Waar:** Width eigenschap: `800`

#### **Total Amount Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"Total Amount: €" & Text(varSelectedOrder.TotalAmount, "0.00")
```
**Waar:** Color eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `18`

#### **Edit Order Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"Edit Order"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** OnSelect eigenschap
```powerapps
Set(varOrderMode, "Edit");
Navigate(New_Order_Screen, ScreenTransition.Fade)
```

#### **Print Order Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"🖨️ Print"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** OnSelect eigenschap
```powerapps
Notify("Print functionality will be implemented", NotificationType.Information)
```

---

## 🛒 **STAP 8: ORDER ITEMS SCREEN (UC7) - NIEUW MAKEN**

### **Nieuwe Screen Maken:**
1. **Insert → New screen → Blank**
2. **Hernoem naar:** `Order_Items_Screen`

### **Scherm Eigenschappen:**
**Waar:** Order Items Screen → Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```

**Waar:** Order Items Screen → OnVisible eigenschap
```powerapps
Set(varCurrentScreen, "OrderItems");
Set(varProductSearch, "");
Set(varProductType, "All");
Refresh(colProducts)
```

### **Header en Navigation (Kopieer van Dashboard):**
**Kopieer alle header en navigation controls van Dashboard Screen**

### **Order Items Specifieke Controls:**

#### **Page Title**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"Add Products to Order"`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `18`

#### **Product Selection Gallery**
**Toevoegen:** Insert → Layout → Flexible height gallery
**Waar:** Items eigenschap
```powerapps
Filter(
    colProducts,
    (IsBlank(varProductSearch) || varProductSearch in ProductName) &&
    (varProductType = "All" || ProductType = varProductType)
)
```
**Waar:** TemplateSize eigenschap: `120`
**Waar:** Height eigenschap: `400`
**Waar:** Width eigenschap: `800`

### **Gallery Template voor Product Selection:**

#### **Product Card Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** BorderThickness eigenschap: `2`

#### **Product Name Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `ThisItem.ProductName`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`

#### **Price Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"€" & Text(ThisItem.Price, "0.00")`
**Waar:** Color eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`

#### **Quantity Slider**
**Toevoegen:** Insert → Input → Slider
**Waar:** Min eigenschap: `1`
**Waar:** Max eigenschap: `50`
**Waar:** Default eigenschap: `1`
**Waar:** Height eigenschap: `30`
**Waar:** Width eigenschap: `150`

#### **Quantity Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"Qty: " & Text(Slider1.Value, "0")`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`

#### **Add Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"+ Add"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** OnSelect eigenschap
```powerapps
// Add item to current order
Set(varCurrentOrder,
    Patch(varCurrentOrder, {
        Items: Append(varCurrentOrder.Items, {
            ProductID: ThisItem.ProductID,
            ProductName: ThisItem.ProductName,
            Quantity: Slider1.Value,
            UnitPrice: ThisItem.Price,
            TotalPrice: ThisItem.Price * Slider1.Value
        }),
        TotalAmount: varCurrentOrder.TotalAmount + (ThisItem.Price * Slider1.Value),
        ItemCount: varCurrentOrder.ItemCount + 1
    })
);
Notify("Added " & ThisItem.ProductName & " to order", NotificationType.Success)
```

#### **Current Order Items Section Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `300`
**Waar:** Width eigenschap: `400`

#### **Current Order Items Gallery**
**Toevoegen:** Insert → Layout → Vertical gallery
**Waar:** Items eigenschap: `varCurrentOrder.Items`
**Waar:** TemplateSize eigenschap: `60`
**Waar:** Height eigenschap: `250`
**Waar:** Width eigenschap: `380`

#### **Done Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"Done"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `40`
**Waar:** Width eigenschap: `100`
**Waar:** OnSelect eigenschap
```powerapps
Navigate(New_Order_Screen, ScreenTransition.Fade)
```

---

## 📋 **STAP 9: ORDER HISTORY SCREEN (UC8) - NIEUW MAKEN**

### **Nieuwe Screen Maken:**
1. **Insert → New screen → Blank**
2. **Hernoem naar:** `Order_History_Screen`

### **Scherm Eigenschappen:**
**Waar:** Order History Screen → Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```

**Waar:** Order History Screen → OnVisible eigenschap
```powerapps
Set(varCurrentScreen, "OrderHistory");
Set(varSearchText, "");
Set(varFilterStatus, "All");
Refresh(colOrders)
```

### **Header en Navigation (Kopieer van Dashboard):**
**Kopieer alle header en navigation controls van Dashboard Screen**
**Verander alleen:** Orders menu button → Fill eigenschap: `RGBA(243, 156, 18, 1)`

### **Order History Specifieke Controls:**

#### **Page Title**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"Order History"`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `18`

#### **Search and Filter Section Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `80`
**Waar:** Width eigenschap: `1100`

#### **Order Search Input**
**Toevoegen:** Insert → Input → Text input
**Waar:** HintText eigenschap: `"Search by order number or customer..."`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** OnChange eigenschap
```powerapps
Set(varSearchText, Self.Text)
```

#### **Status Filter Dropdown**
**Toevoegen:** Insert → Input → Drop down
**Waar:** Items eigenschap
```powerapps
["All", "new", "processing", "shipped", "delivered", "cancelled"]
```
**Waar:** DefaultSelectedItems eigenschap: `["All"]`
**Waar:** OnChange eigenschap
```powerapps
Set(varFilterStatus, Self.Selected.Value)
```

#### **Orders Gallery**
**Toevoegen:** Insert → Layout → Vertical gallery
**Waar:** Items eigenschap
```powerapps
Filter(
    AddColumns(
        colOrders,
        "CustomerName", LookUp(colCustomers, CustomerID = ThisRecord.CustomerID).CustomerName
    ),
    (IsBlank(varSearchText) ||
     varSearchText in OrderNumber ||
     varSearchText in CustomerName) &&
    (varFilterStatus = "All" || Status = varFilterStatus)
)
```
**Waar:** TemplateSize eigenschap: `80`
**Waar:** Height eigenschap: `500`
**Waar:** Width eigenschap: `1100`

### **Gallery Template voor Orders:**

#### **Order Card Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap
```powerapps
Switch(
    ThisItem.Status,
    "new", RGBA(243, 156, 18, 1),
    "processing", RGBA(255, 193, 7, 1),
    "shipped", RGBA(13, 202, 240, 1),
    "delivered", RGBA(40, 167, 69, 1),
    "cancelled", RGBA(220, 53, 69, 1),
    RGBA(243, 156, 18, 1)
)
```
**Waar:** BorderThickness eigenschap: `2`

#### **Order Number Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `ThisItem.OrderNumber`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`

#### **Customer Name Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `ThisItem.CustomerName`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`

#### **Order Date Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `Text(ThisItem.OrderDate, "dd/mm/yyyy")`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`

#### **Status Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `Upper(ThisItem.Status)`
**Waar:** Color eigenschap
```powerapps
Switch(
    ThisItem.Status,
    "new", RGBA(243, 156, 18, 1),
    "processing", RGBA(255, 193, 7, 1),
    "shipped", RGBA(13, 202, 240, 1),
    "delivered", RGBA(40, 167, 69, 1),
    "cancelled", RGBA(220, 53, 69, 1),
    RGBA(44, 62, 80, 1)
)
```
**Waar:** FontWeight eigenschap: `FontWeight.Bold`

#### **Total Amount Label**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"€" & Text(ThisItem.TotalAmount, "0.00")`
**Waar:** Color eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`

#### **View Details Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"👁️ View"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** OnSelect eigenschap
```powerapps
Set(varSelectedOrder, ThisItem);
Navigate(Order_Details_Screen, ScreenTransition.Fade)
```

**BELANGRIJK:** Vervang `Slider1` met werkelijke control naam van je slider!

---

## ✅ **STAP 10: ORDER CONFIRMATION SCREEN (UC9) - NIEUW MAKEN**

### **Nieuwe Screen Maken:**
1. **Insert → New screen → Blank**
2. **Hernoem naar:** `Order_Confirmation_Screen`

### **Scherm Eigenschappen:**
**Waar:** Order Confirmation Screen → Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```

**Waar:** Order Confirmation Screen → OnVisible eigenschap
```powerapps
Set(varCurrentScreen, "OrderConfirmation")
```

### **Header en Navigation (Kopieer van Dashboard):**
**Kopieer alle header en navigation controls van Dashboard Screen**

### **Order Confirmation Specifieke Controls:**

#### **Success Icon**
**Toevoegen:** Insert → Icons → Check
**Waar:** Color eigenschap: `RGBA(40, 167, 69, 1)`
**Waar:** Height eigenschap: `100`
**Waar:** Width eigenschap: `100`

#### **Confirmation Title**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"Order Submitted Successfully!"`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `24`

#### **Order Number Display**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"Order Number: " & Last(colOrders).OrderNumber
```
**Waar:** Color eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `18`

#### **Confirmation Message**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap
```powerapps
"Your order has been submitted and will be processed shortly. You will receive a confirmation email at " & varSelectedCustomer.Email
```
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Size eigenschap: `14`

#### **View Order Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"View Order Details"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `45`
**Waar:** Width eigenschap: `200`
**Waar:** OnSelect eigenschap
```powerapps
Set(varSelectedOrder, Last(colOrders));
Navigate(Order_Details_Screen, ScreenTransition.Fade)
```

#### **Create New Order Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"Create New Order"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `45`
**Waar:** Width eigenschap: `200`
**Waar:** OnSelect eigenschap
```powerapps
Navigate(New_Order_Screen, ScreenTransition.Fade)
```

#### **Back to Dashboard Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"Back to Dashboard"`
**Waar:** Fill eigenschap: `RGBA(108, 117, 125, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `45`
**Waar:** Width eigenschap: `200`
**Waar:** OnSelect eigenschap
```powerapps
Navigate(Dashboard_Screen, ScreenTransition.Fade)
```

---

## ⚙️ **STAP 11: SETTINGS SCREEN (UC10) - NIEUW MAKEN**

### **Nieuwe Screen Maken:**
1. **Insert → New screen → Blank**
2. **Hernoem naar:** `Settings_Screen`

### **Scherm Eigenschappen:**
**Waar:** Settings Screen → Fill eigenschap
```powerapps
RGBA(27, 58, 75, 1)
```

**Waar:** Settings Screen → OnVisible eigenschap
```powerapps
Set(varCurrentScreen, "Settings")
```

### **Header en Navigation (Kopieer van Dashboard):**
**Kopieer alle header en navigation controls van Dashboard Screen**

### **Settings Specifieke Controls:**

#### **Page Title**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"Application Settings"`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`
**Waar:** Size eigenschap: `18`

#### **User Profile Section Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `150`
**Waar:** Width eigenschap: `500`

#### **User Name Display**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"User: " & varUserName`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`
**Waar:** FontWeight eigenschap: `FontWeight.Bold`

#### **User Role Display**
**Toevoegen:** Insert → Text → Label
**Waar:** Text eigenschap: `"Role: " & varUserRole`
**Waar:** Color eigenschap: `RGBA(44, 62, 80, 1)`

#### **Default Settings Section Rectangle**
**Toevoegen:** Insert → Icons → Rectangle
**Waar:** Fill eigenschap: `RGBA(169, 198, 232, 1)`
**Waar:** BorderColor eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** BorderThickness eigenschap: `2`
**Waar:** Height eigenschap: `200`
**Waar:** Width eigenschap: `500`

#### **Default Delivery Days Slider**
**Toevoegen:** Insert → Input → Slider
**Waar:** Min eigenschap: `1`
**Waar:** Max eigenschap: `30`
**Waar:** Default eigenschap: `varDefaultDeliveryDays`
**Waar:** OnChange eigenschap
```powerapps
Set(varDefaultDeliveryDays, Self.Value)
```

#### **Items Per Page Dropdown**
**Toevoegen:** Insert → Input → Drop down
**Waar:** Items eigenschap: `[5, 10, 20, 50]`
**Waar:** DefaultSelectedItems eigenschap: `[varItemsPerPage]`
**Waar:** OnChange eigenschap
```powerapps
Set(varItemsPerPage, Self.Selected.Value)
```

#### **Date Format Dropdown**
**Toevoegen:** Insert → Input → Drop down
**Waar:** Items eigenschap
```powerapps
["dd/mm/yyyy", "mm/dd/yyyy", "yyyy-mm-dd"]
```
**Waar:** DefaultSelectedItems eigenschap: `[varDateFormat]`
**Waar:** OnChange eigenschap
```powerapps
Set(varDateFormat, Self.Selected.Value)
```

#### **Save Settings Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"Save Settings"`
**Waar:** Fill eigenschap: `RGBA(243, 156, 18, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `45`
**Waar:** Width eigenschap: `150`
**Waar:** OnSelect eigenschap
```powerapps
// Save settings to local storage
Set("DefaultDeliveryDays", varDefaultDeliveryDays);
Set("ItemsPerPage", varItemsPerPage);
Set("DateFormat", varDateFormat);
Notify("Settings saved successfully", NotificationType.Success)
```

#### **Reset to Defaults Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"Reset to Defaults"`
**Waar:** Fill eigenschap: `RGBA(108, 117, 125, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `45`
**Waar:** Width eigenschap: `180`
**Waar:** OnSelect eigenschap
```powerapps
Set(varDefaultDeliveryDays, 7);
Set(varItemsPerPage, 10);
Set(varDateFormat, "dd/mm/yyyy");
Reset(DeliveryDaysSlider);
Reset(ItemsPerPageDropdown);
Reset(DateFormatDropdown);
Notify("Settings reset to defaults", NotificationType.Information)
```

#### **Logout Button**
**Toevoegen:** Insert → Input → Button
**Waar:** Text eigenschap: `"🚪 Logout"`
**Waar:** Fill eigenschap: `RGBA(220, 53, 69, 1)`
**Waar:** Color eigenschap: `RGBA(255, 255, 255, 1)`
**Waar:** Height eigenschap: `45`
**Waar:** Width eigenschap: `120`
**Waar:** OnSelect eigenschap
```powerapps
Set(varUserRole, "");
Set(varUserName, "");
Set(varRememberMe, false);
Navigate(Login_Screen, ScreenTransition.Fade);
Notify("Logged out successfully", NotificationType.Information)
```

---

## 🗄️ **STAP 12: DATABASE INTEGRATIE SCHERMEN**

### **Raw Materials Management Screen:**
1. **Insert → New screen → Blank**
2. **Hernoem naar:** `Raw_Materials_Screen`
3. **Volg de code uit:** `Raw_Materials_Management_Screen.pa.yaml`

### **Supplier Management Screen:**
1. **Insert → New screen → Blank**
2. **Hernoem naar:** `Supplier_Management_Screen`
3. **Volg de code uit:** `Supplier_Management_Screen.pa.yaml`

**Deze schermen zijn al volledig gespecificeerd in de eerdere documenten.**

---

## 🎯 **IMPLEMENTATIE VOLGORDE**

### **Prioriteit 1 (Basis Functionaliteit):**
1. ✅ App OnStart configureren
2. ✅ Login Screen functionaliteit
3. ✅ Dashboard Screen uitbreiden
4. ✅ Customer List Screen
5. ✅ Product Catalog Screen

### **Prioriteit 2 (Order Management):**
6. ✅ New Order Screen
7. ✅ Order Items Screen
8. ✅ Order Details Screen
9. ✅ Order History Screen
10. ✅ Order Confirmation Screen

### **Prioriteit 3 (Configuratie & Database):**
11. ✅ Settings Screen
12. ✅ Raw Materials Screen
13. ✅ Supplier Management Screen

---

## ✅ **TESTING CHECKLIST**

### **Basis Functionaliteit:**
- [ ] Login werkt met alle rollen (sales, service, manager, admin)
- [ ] Dashboard toont juiste metrics
- [ ] Navigatie tussen alle schermen werkt
- [ ] Zoeken en filteren werkt op alle schermen

### **Order Management:**
- [ ] Nieuwe order aanmaken werkt
- [ ] Producten toevoegen aan order werkt
- [ ] Order status wijzigen werkt
- [ ] Order history toont alle orders
- [ ] Order confirmation toont juiste informatie

### **Database Integratie:**
- [ ] Raw materials toont voorraad levels
- [ ] Reorder alerts werken
- [ ] Supplier management toont leveranciers
- [ ] Alle data calculations kloppen

### **Settings & Configuratie:**
- [ ] Settings opslaan werkt
- [ ] Default waarden worden toegepast
- [ ] Logout functionaliteit werkt
- [ ] Remember me functionaliteit werkt

**ALLE 10 SCHERMEN ZIJN NU VOLLEDIG GESPECIFICEERD MET EXACTE CODE LOCATIES!**
```
```
