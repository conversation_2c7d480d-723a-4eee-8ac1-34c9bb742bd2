# Conceptueel ERD: Uitleg en Visualisatie

## Wat is een conceptueel ERD?

Een conceptueel ERD (Entity Relationship Diagram) is de meest abstracte vorm van een Entity Relationship Diagram. Het is een visuele weergave van de belangrijkste entiteiten (objecten of concepten) in een systeem en de relaties tussen deze entiteiten, zonder technische details.

## Hoe ziet een conceptueel ERD eruit?

Een conceptueel ERD bestaat uit:

1. **Rechthoeken**: Deze stellen entiteiten voor (zoals KLANT, PRODUCT, BESTELLING)
2. **Lijnen**: Deze verbinden de entiteiten en stellen relaties voor
3. **Labels op lijnen**: Deze beschrijven de relatie (zoals "plaatst", "bevat", "behoort tot")
4. **Cardinaliteit-notaties**: Deze geven aan of een relatie één-op-é<PERSON>, één-op-veel, of veel-op-veel is

## Voorbeeld van een conceptueel ERD voor een webwinkel:

```
+----------+       plaatst       +------------+
|          |-------------------->|            |
|  KLANT   | 1                 N | BESTELLING |
|          |                     |            |
+----------+                     +------------+
                                       |
                                       | bevat
                                       | N:M
                                       v
+----------+     behoort tot     +------------+
|          |<--------------------|            |
| CATEGORIE| 1                 N |  PRODUCT   |
|          |                     |            |
+----------+                     +------------+
```

In dit voorbeeld:
- KLANT, BESTELLING, PRODUCT en CATEGORIE zijn entiteiten (rechthoeken)
- De lijnen tonen relaties tussen entiteiten
- De labels (plaatst, bevat, behoort tot) beschrijven de relaties
- De cardinaliteit (1, N, M) toont het type relatie:
  * 1:N (één-op-veel): Eén KLANT kan meerdere BESTELLINGEN plaatsen
  * N:M (veel-op-veel): Een BESTELLING kan meerdere PRODUCTEN bevatten, en een PRODUCT kan in meerdere BESTELLINGEN voorkomen
  * 1:N (één-op-veel): Eén CATEGORIE kan meerdere PRODUCTEN bevatten

## Wat maakt een conceptueel ERD anders?

- **Geen attributen**: Een conceptueel ERD toont geen attributen (zoals klantnaam, adres, prijs)
- **Geen primaire sleutels**: Het toont geen technische details zoals primaire of vreemde sleutels
- **Geen implementatiedetails**: Het bevat geen informatie over datatypes of databasespecifieke elementen
- **Focus op het domein**: Het richt zich op het begrijpen van het domein en de belangrijkste concepten

## Volgende stappen na een conceptueel ERD:

1. **Logisch ERD**: Voegt attributen toe aan entiteiten en verfijnt relaties
2. **Fysiek ERD**: Voegt technische details toe zoals datatypes, primaire en vreemde sleutels, en implementatiespecifieke elementen

Voor uw examen kunt u een conceptueel ERD tekenen door rechthoeken voor entiteiten te maken, deze te verbinden met lijnen voor relaties, en de cardinaliteit aan te geven met 1, N of M bij de uiteinden van de lijnen.
