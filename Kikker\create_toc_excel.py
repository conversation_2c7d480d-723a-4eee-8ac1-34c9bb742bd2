from openpyxl import Workbook
from openpyxl.chart import Bar<PERSON><PERSON>, Reference
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill

def create_toc_excel():
    # Maak een Excel workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "TOC Analyse"

    # Definieer stijlen
    header_font = Font(bold=True, size=12)
    normal_font = Font(size=11)

    # Definieer borders
    thin_border = Border(left=Side(style='thin'),
                         right=Side(style='thin'),
                         top=Side(style='thin'),
                         bottom=Side(style='thin'))

    # Definieer vulkleuren
    header_fill = PatternFill(start_color="8EAADB", end_color="8EAADB", fill_type="solid")
    subheader_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
    data_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")

    # Maak de tabelheaders
    ws.cell(row=1, column=1, value="Column1").font = header_font
    ws.cell(row=1, column=1).fill = header_fill
    ws.cell(row=1, column=1).alignment = Alignment(horizontal='left')
    ws.cell(row=1, column=1).border = thin_border

    ws.cell(row=1, column=2, value="Column2").font = header_font
    ws.cell(row=1, column=2).fill = header_fill
    ws.cell(row=1, column=2).alignment = Alignment(horizontal='left')
    ws.cell(row=1, column=2).border = thin_border

    # Titel
    ws.cell(row=2, column=1, value="THEORY OF CONSTRAINTS ANALYSE").font = header_font
    ws.cell(row=2, column=1).fill = subheader_fill
    ws.cell(row=2, column=1).border = thin_border
    ws.cell(row=2, column=2).fill = subheader_fill
    ws.cell(row=2, column=2).border = thin_border

    # Subtitel
    ws.cell(row=3, column=1, value="Knelpunten in het proces").font = Font(italic=True)
    ws.cell(row=3, column=1).fill = subheader_fill
    ws.cell(row=3, column=1).border = thin_border
    ws.cell(row=3, column=2).fill = subheader_fill
    ws.cell(row=3, column=2).border = thin_border

    # Lege rij
    ws.cell(row=4, column=1).fill = subheader_fill
    ws.cell(row=4, column=1).border = thin_border
    ws.cell(row=4, column=2).fill = subheader_fill
    ws.cell(row=4, column=2).border = thin_border

    # Procestijd Analyse
    ws.cell(row=5, column=1, value="Procestijd Analyse").font = header_font
    ws.cell(row=5, column=1).fill = subheader_fill
    ws.cell(row=5, column=1).border = thin_border
    ws.cell(row=5, column=2).fill = subheader_fill
    ws.cell(row=5, column=2).border = thin_border

    ws.cell(row=6, column=1, value="- Grinding").font = normal_font
    ws.cell(row=6, column=1).border = thin_border
    ws.cell(row=6, column=2, value="0.34 uur").font = normal_font
    ws.cell(row=6, column=2).fill = data_fill
    ws.cell(row=6, column=2).border = thin_border

    ws.cell(row=7, column=1, value="- Filling").font = normal_font
    ws.cell(row=7, column=1).border = thin_border
    ws.cell(row=7, column=2, value="0.25 uur").font = normal_font
    ws.cell(row=7, column=2).fill = data_fill
    ws.cell(row=7, column=2).border = thin_border

    ws.cell(row=8, column=1, value="- Packaging").font = normal_font
    ws.cell(row=8, column=1).border = thin_border
    ws.cell(row=8, column=2, value="0.50 uur").font = normal_font
    ws.cell(row=8, column=2).fill = data_fill
    ws.cell(row=8, column=2).border = thin_border

    # Bottleneck Identificatie
    ws.cell(row=9, column=1, value="Bottleneck Identificatie").font = header_font
    ws.cell(row=9, column=1).fill = subheader_fill
    ws.cell(row=9, column=1).border = thin_border
    ws.cell(row=9, column=2).fill = subheader_fill
    ws.cell(row=9, column=2).border = thin_border

    ws.cell(row=10, column=1, value="- Bottleneck proces").font = normal_font
    ws.cell(row=10, column=1).border = thin_border
    ws.cell(row=10, column=2, value="Packaging").font = normal_font
    ws.cell(row=10, column=2).fill = data_fill
    ws.cell(row=10, column=2).border = thin_border

    ws.cell(row=11, column=1, value="- Gemiddelde procestijd").font = normal_font
    ws.cell(row=11, column=1).border = thin_border
    ws.cell(row=11, column=2, value="0.50 uur").font = normal_font
    ws.cell(row=11, column=2).fill = data_fill
    ws.cell(row=11, column=2).border = thin_border

    # Lege rij
    ws.cell(row=12, column=1).fill = subheader_fill
    ws.cell(row=12, column=1).border = thin_border
    ws.cell(row=12, column=2).fill = subheader_fill
    ws.cell(row=12, column=2).border = thin_border

    # Energieverbruik per Verpakkingsmachine
    ws.cell(row=13, column=1, value="Energieverbruik per Verpakkingsmachine").font = header_font
    ws.cell(row=13, column=1).fill = subheader_fill
    ws.cell(row=13, column=1).border = thin_border
    ws.cell(row=13, column=2).fill = subheader_fill
    ws.cell(row=13, column=2).border = thin_border

    ws.cell(row=14, column=1, value="- Onbekend").font = normal_font
    ws.cell(row=14, column=1).border = thin_border
    ws.cell(row=14, column=2, value="281.60 kWh").font = normal_font
    ws.cell(row=14, column=2).fill = data_fill
    ws.cell(row=14, column=2).border = thin_border

    ws.cell(row=15, column=1, value="- Packager 4").font = normal_font
    ws.cell(row=15, column=1).border = thin_border
    ws.cell(row=15, column=2, value="278.37 kWh").font = normal_font
    ws.cell(row=15, column=2).fill = data_fill
    ws.cell(row=15, column=2).border = thin_border

    ws.cell(row=16, column=1, value="- Packager 1").font = normal_font
    ws.cell(row=16, column=1).border = thin_border
    ws.cell(row=16, column=2, value="277.56 kWh").font = normal_font
    ws.cell(row=16, column=2).fill = data_fill
    ws.cell(row=16, column=2).border = thin_border

    ws.cell(row=17, column=1, value="- Packager 5").font = normal_font
    ws.cell(row=17, column=1).border = thin_border
    ws.cell(row=17, column=2, value="275.95 kWh").font = normal_font
    ws.cell(row=17, column=2).fill = data_fill
    ws.cell(row=17, column=2).border = thin_border

    ws.cell(row=18, column=1, value="- Packager 3").font = normal_font
    ws.cell(row=18, column=1).border = thin_border
    ws.cell(row=18, column=2, value="275.83 kWh").font = normal_font
    ws.cell(row=18, column=2).fill = data_fill
    ws.cell(row=18, column=2).border = thin_border

    ws.cell(row=19, column=1, value="- Packager 2").font = normal_font
    ws.cell(row=19, column=1).border = thin_border
    ws.cell(row=19, column=2, value="273.17 kWh").font = normal_font
    ws.cell(row=19, column=2).fill = data_fill
    ws.cell(row=19, column=2).border = thin_border

    # Pas kolombreedtes aan
    ws.column_dimensions['A'].width = 40
    ws.column_dimensions['B'].width = 15
    ws.column_dimensions['C'].width = 15

    # Pas de data in de tabel aan (verwijder 'uur' en 'kWh' en 'Onbekend')
    # Procestijd Analyse
    ws.cell(row=6, column=2, value=0.34)
    ws.cell(row=7, column=2, value=0.25)
    ws.cell(row=8, column=2, value=0.50)

    # Energieverbruik (verwijder 'Onbekend' en sorteer op volgorde van Packager 1 tot 6)
    ws.cell(row=14, column=1, value="- Packager 1")
    ws.cell(row=14, column=2, value=277.56)

    ws.cell(row=15, column=1, value="- Packager 2")
    ws.cell(row=15, column=2, value=273.17)

    ws.cell(row=16, column=1, value="- Packager 3")
    ws.cell(row=16, column=2, value=275.83)

    ws.cell(row=17, column=1, value="- Packager 4")
    ws.cell(row=17, column=2, value=278.37)

    ws.cell(row=18, column=1, value="- Packager 5")
    ws.cell(row=18, column=2, value=275.95)

    ws.cell(row=19, column=1, value="- Packager 6")
    ws.cell(row=19, column=2, value=274.50)  # Voorbeeld waarde

    # 1. Maak een staafdiagram voor procestijden
    process_chart = BarChart()
    process_chart.title = "Procestijden"
    process_chart.y_axis.title = "Procestijd"
    process_chart.x_axis.title = "Proces"
    process_chart.height = 10  # Hoogte van het diagram
    process_chart.width = 15   # Breedte van het diagram

    # Definieer de data voor het staafdiagram direct vanuit de tabel
    # Gebruik de waarden uit kolom B (rij 6-8)
    process_data = Reference(ws, min_col=2, min_row=6, max_row=8, max_col=2)

    # Gebruik de labels uit kolom A (rij 6-8)
    process_cats = Reference(ws, min_col=1, min_row=6, max_row=8)

    # Voeg de data toe aan het staafdiagram
    process_chart.add_data(process_data)
    process_chart.set_categories(process_cats)

    # Voeg data labels toe om de exacte waarden te tonen
    from openpyxl.chart.label import DataLabelList
    process_chart.dataLabels = DataLabelList()
    process_chart.dataLabels.showVal = True  # Toon de waarden
    process_chart.dataLabels.showCatName = False  # Verberg categorienamen
    process_chart.dataLabels.showSerName = False  # Verberg serienamen
    process_chart.dataLabels.showPercent = False  # Verberg percentages
    process_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels

    # Pas de stijl aan
    process_chart.style = 10  # Kies een stijl

    # Voeg het staafdiagram toe aan het TOC Analyse werkblad naast de tabel
    ws.add_chart(process_chart, "D5")

    # 2. Maak een staafdiagram voor bottleneck identificatie
    bottleneck_chart = BarChart()
    bottleneck_chart.title = "Bottleneck Identificatie"
    bottleneck_chart.y_axis.title = "Procestijd"
    bottleneck_chart.x_axis.title = "Proces"
    bottleneck_chart.height = 10  # Hoogte van het diagram
    bottleneck_chart.width = 15   # Breedte van het diagram

    # Gebruik dezelfde data als voor het procestijden diagram
    bottleneck_chart.add_data(process_data)
    bottleneck_chart.set_categories(process_cats)

    # Voeg data labels toe om de exacte waarden te tonen
    bottleneck_chart.dataLabels = DataLabelList()
    bottleneck_chart.dataLabels.showVal = True  # Toon de waarden
    bottleneck_chart.dataLabels.showCatName = False  # Verberg categorienamen
    bottleneck_chart.dataLabels.showSerName = False  # Verberg serienamen
    bottleneck_chart.dataLabels.showPercent = False  # Verberg percentages
    bottleneck_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels

    # Pas de stijl aan
    bottleneck_chart.style = 11  # Kies een andere stijl

    # Voeg het staafdiagram toe aan het TOC Analyse werkblad naast de tabel
    ws.add_chart(bottleneck_chart, "D9")

    # 3. Maak een staafdiagram voor energieverbruik
    energy_chart = BarChart()
    energy_chart.title = "Energieverbruik per Verpakkingsmachine"
    energy_chart.y_axis.title = "Energieverbruik (kWh)"
    energy_chart.x_axis.title = "Verpakkingsmachine"
    energy_chart.height = 15  # Hoogte van het diagram
    energy_chart.width = 20   # Breedte van het diagram

    # Definieer de data voor het staafdiagram direct vanuit de tabel
    # Gebruik de waarden uit kolom B (rij 14-19)
    energy_data_ref = Reference(ws, min_col=2, min_row=14, max_row=19, max_col=2)

    # Gebruik de labels uit kolom A (rij 14-19)
    energy_cats = Reference(ws, min_col=1, min_row=14, max_row=19)

    # Voeg de data toe aan het staafdiagram
    energy_chart.add_data(energy_data_ref, titles_from_data=True)
    energy_chart.set_categories(energy_cats)

    # Voeg data labels toe om de exacte waarden te tonen
    from openpyxl.chart.label import DataLabelList
    energy_chart.dataLabels = DataLabelList()
    energy_chart.dataLabels.showVal = True  # Toon de waarden
    energy_chart.dataLabels.showCatName = False  # Verberg categorienamen
    energy_chart.dataLabels.showSerName = False  # Verberg serienamen
    energy_chart.dataLabels.showPercent = False  # Verberg percentages
    energy_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels

    # Pas de stijl aan
    energy_chart.style = 12  # Kies een andere stijl

    # Voeg het staafdiagram toe aan het TOC Analyse werkblad naast de tabel
    ws.add_chart(energy_chart, "D13")

    # Voeg kleuren toe aan de bottleneck in de tabel
    bottleneck_fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")
    ws.cell(row=8, column=1).fill = bottleneck_fill  # Packaging rij
    ws.cell(row=8, column=2).fill = bottleneck_fill
    ws.cell(row=10, column=2).fill = bottleneck_fill  # Bottleneck proces waarde
    ws.cell(row=11, column=2).fill = bottleneck_fill  # Gemiddelde procestijd waarde

    # Sla het Excel bestand op
    excel_file = 'TOC_Analyse_Final.xlsx'
    try:
        wb.save(excel_file)
        print(f"Excel bestand met visualisaties is opgeslagen als {excel_file}")
    except PermissionError:
        # Als er een permissie error is, probeer op te slaan in een tijdelijke map
        import tempfile
        import os
        temp_dir = tempfile.gettempdir()
        excel_file = os.path.join(temp_dir, 'TOC_Analyse_Final.xlsx')
        wb.save(excel_file)
        print(f"Excel bestand met visualisaties is opgeslagen als {excel_file}")

if __name__ == "__main__":
    create_toc_excel()
