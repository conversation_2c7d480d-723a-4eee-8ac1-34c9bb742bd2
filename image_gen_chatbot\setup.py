import os
import sys
import subprocess
import platform

def check_python_version():
    """Check if Python version is 3.7 or higher."""
    if sys.version_info < (3, 7):
        print("Error: Python 3.7 or higher is required.")
        sys.exit(1)
    print(f"Python version {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} detected.")

def create_virtual_environment():
    """Create a virtual environment if it doesn't exist."""
    if not os.path.exists("venv"):
        print("Creating virtual environment...")
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("Virtual environment created.")
    else:
        print("Virtual environment already exists.")

def install_requirements():
    """Install required packages."""
    print("Installing required packages...")
    
    # Determine the path to the pip executable in the virtual environment
    if platform.system() == "Windows":
        pip_path = os.path.join("venv", "Scripts", "pip")
    else:
        pip_path = os.path.join("venv", "bin", "pip")
    
    # Install requirements
    subprocess.run([pip_path, "install", "-r", "requirements.txt"], check=True)
    print("Required packages installed.")

def check_env_file():
    """Check if .env file exists and remind user to add API keys."""
    if os.path.exists(".env"):
        print("Found .env file. Remember to add your API keys.")
    else:
        print("Creating .env file...")
        with open(".env", "w") as f:
            f.write("""# API Keys for Image Generation Services
# Uncomment and add your API key for the service you want to use

# OpenAI API Key (for DALL-E)
# OPENAI_API_KEY=your_openai_api_key_here

# Stability AI API Key (for Stable Diffusion)
# STABILITY_API_KEY=your_stability_api_key_here

# Replicate API Key (for various models)
# REPLICATE_API_KEY=your_replicate_api_key_here""")
        print(".env file created. Please add your API keys.")

def main():
    """Main setup function."""
    print("Setting up AI Image Generator Chatbot...")
    
    check_python_version()
    create_virtual_environment()
    install_requirements()
    check_env_file()
    
    print("\nSetup complete!")
    print("\nTo start the application:")
    if platform.system() == "Windows":
        print("1. Activate the virtual environment: venv\\Scripts\\activate")
    else:
        print("1. Activate the virtual environment: source venv/bin/activate")
    print("2. Run the application: python app.py")
    print("3. Open your web browser and go to http://127.0.0.1:5000/")

if __name__ == "__main__":
    main()
