import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Set display options for better readability
pd.set_option('display.max_columns', None)
pd.set_option('display.width', 1000)
pd.set_option('display.max_rows', 50)

# Load the CSV file
print("Loading the CSV file...")
df = pd.read_csv('Kikker.csv')

# Basic information about the dataset
print("\n=== BASIC INFORMATION ===")
print(f"Number of rows: {df.shape[0]}")
print(f"Number of columns: {df.shape[1]}")

# Display column names and their data types
print("\n=== COLUMNS AND DATA TYPES ===")
print(df.dtypes)

# Display first few rows to understand the data structure
print("\n=== FIRST 5 ROWS OF DATA ===")
print(df.head())

# Check for missing values
print("\n=== MISSING VALUES ===")
missing_values = df.isnull().sum()
missing_percent = (df.isnull().sum() / len(df)) * 100
missing_data = pd.concat([missing_values, missing_percent], axis=1)
missing_data.columns = ['Missing Values', 'Percentage']
print(missing_data[missing_data['Missing Values'] > 0].sort_values('Percentage', ascending=False))

# Check for unusual values
print("\n=== UNUSUAL VALUES CHECK ===")
# Check for unusual date formats
date_columns = ['Registratiedatum', 'FillingDatumTijdEind', 'PackagingDatumTijdEind', 
                'FillingDatumTijdStart', 'GrindingDatumTijdEind', 'GrindingDatumTijdStart',
                'PackagingDatumTijdStart', 'Laatste Audit']

print("Checking for unusual date formats...")
for col in date_columns:
    if col in df.columns:
        unusual_dates = df[col].astype(str).str.contains('31-02-2025|0000-00-00|onbekend', na=False).sum()
        if unusual_dates > 0:
            print(f"Column {col} has {unusual_dates} unusual date values")

# Check for unusual numeric values
numeric_columns = ['Klantretourpercentage', 'Cost', 'Voorraadniveaus', 'Benuttingsgraad', 
                  'Cyclustijd', 'LeverancierLevertijd', 'Leveranciersbeoordeling', 
                  'Klanttevredenheid', 'Gewichtscontrole', 'Duurzaamheid Score', 
                  'CO2-Footprint', 'ProcessTime', 'Fair-Trade Score', 'Defectpercentage', 
                  'Energieverbruik']

print("\nChecking for unusual numeric values...")
for col in numeric_columns:
    if col in df.columns:
        # Convert to string to handle mixed types
        col_str = df[col].astype(str)
        # Check for negative values, ERROR, NaN, etc.
        unusual_values = col_str.str.contains('-|ERROR|NaN|abc|onbekend|null', na=False).sum()
        if unusual_values > 0:
            print(f"Column {col} has {unusual_values} unusual values")
            print("Examples:", df[col_str.str.contains('-|ERROR|NaN|abc|onbekend|null', na=False)][col].unique()[:5])

# Summary statistics for numeric columns
print("\n=== SUMMARY STATISTICS ===")
# Try to convert percentage columns to numeric
for col in df.columns:
    if 'percentage' in col.lower():
        df[col] = df[col].str.replace('%', '').astype(float) / 100 if df[col].dtype == 'object' else df[col]

# Get numeric columns
numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
if numeric_cols:
    print(df[numeric_cols].describe())

# Unique values for categorical columns
print("\n=== CATEGORICAL COLUMNS ===")
categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
for col in categorical_cols[:10]:  # Limit to first 10 to avoid too much output
    unique_values = df[col].nunique()
    print(f"{col}: {unique_values} unique values")
    if unique_values < 10:  # Only show all values if there are fewer than 10
        print(df[col].value_counts())
    print()

# Check for potential data quality issues
print("\n=== POTENTIAL DATA QUALITY ISSUES ===")
# Check for duplicate rows
duplicates = df.duplicated().sum()
print(f"Number of duplicate rows: {duplicates}")

# Check for outliers in numeric columns (using IQR method)
for col in numeric_cols:
    try:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
        if outliers > 0:
            print(f"Column {col} has {outliers} outliers")
    except:
        print(f"Could not check for outliers in {col}")

print("\nData exploration complete!")
