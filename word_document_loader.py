#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word Document Loader

This script provides functions to load and extract text from Microsoft Word documents.
It can be used as a standalone script or imported as a module.

Usage:
    python word_document_loader.py <path_to_word_document>
"""

import os
import sys
import docx
import tkinter as tk
from tkinter import filedialog, scrolledtext, messagebox
from tkinter import ttk

def extract_text_from_docx(file_path):
    """
    Extract text from a .docx file
    
    Parameters:
    -----------
    file_path : str
        Path to the .docx file
    
    Returns:
    --------
    str
        Extracted text from the document
    """
    try:
        doc = docx.Document(file_path)
        full_text = []
        
        # Extract text from paragraphs
        for para in doc.paragraphs:
            full_text.append(para.text)
        
        # Extract text from tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        full_text.append(paragraph.text)
        
        return '\n'.join(full_text)
    except Exception as e:
        return f"Error extracting text: {str(e)}"

def get_document_properties(file_path):
    """
    Get properties of a .docx file
    
    Parameters:
    -----------
    file_path : str
        Path to the .docx file
    
    Returns:
    --------
    dict
        Document properties
    """
    try:
        doc = docx.Document(file_path)
        properties = {}
        
        # Core properties
        core_properties = doc.core_properties
        if core_properties:
            properties['author'] = core_properties.author
            properties['created'] = core_properties.created
            properties['last_modified_by'] = core_properties.last_modified_by
            properties['modified'] = core_properties.modified
            properties['title'] = core_properties.title
            properties['subject'] = core_properties.subject
            properties['keywords'] = core_properties.keywords
            properties['comments'] = core_properties.comments
            properties['category'] = core_properties.category
            
        # Document statistics
        properties['paragraphs'] = len(doc.paragraphs)
        properties['tables'] = len(doc.tables)
        properties['sections'] = len(doc.sections)
        
        return properties
    except Exception as e:
        return {'error': str(e)}

class WordDocumentLoaderApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Word Document Loader")
        self.root.geometry("800x600")
        
        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Create Open button
        open_button = ttk.Button(buttons_frame, text="Open Word Document", command=self.open_document)
        open_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Create Save button
        save_button = ttk.Button(buttons_frame, text="Save Extracted Text", command=self.save_text)
        save_button.pack(side=tk.LEFT)
        
        # Create notebook (tabs)
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create Text tab
        text_frame = ttk.Frame(self.notebook)
        self.notebook.add(text_frame, text="Document Text")
        
        # Create text area
        self.text_area = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD)
        self.text_area.pack(fill=tk.BOTH, expand=True)
        
        # Create Properties tab
        properties_frame = ttk.Frame(self.notebook)
        self.notebook.add(properties_frame, text="Document Properties")
        
        # Create properties area
        self.properties_area = scrolledtext.ScrolledText(properties_frame, wrap=tk.WORD)
        self.properties_area.pack(fill=tk.BOTH, expand=True)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Current file path
        self.current_file = None
        
    def open_document(self):
        """Open a Word document and display its content"""
        file_path = filedialog.askopenfilename(
            title="Select Word Document",
            filetypes=[("Word Documents", "*.docx"), ("All Files", "*.*")]
        )
        
        if file_path:
            self.current_file = file_path
            self.status_var.set(f"Loading: {os.path.basename(file_path)}")
            self.root.update_idletasks()
            
            # Extract text
            text = extract_text_from_docx(file_path)
            self.text_area.delete(1.0, tk.END)
            self.text_area.insert(tk.END, text)
            
            # Get properties
            properties = get_document_properties(file_path)
            self.properties_area.delete(1.0, tk.END)
            for key, value in properties.items():
                self.properties_area.insert(tk.END, f"{key}: {value}\n")
            
            self.status_var.set(f"Loaded: {os.path.basename(file_path)}")
    
    def save_text(self):
        """Save the extracted text to a file"""
        if not self.text_area.get(1.0, tk.END).strip():
            messagebox.showwarning("Warning", "No text to save")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="Save Text As",
            defaultextension=".txt",
            filetypes=[("Text Files", "*.txt"), ("All Files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.text_area.get(1.0, tk.END))
                self.status_var.set(f"Saved to: {os.path.basename(file_path)}")
                messagebox.showinfo("Success", f"Text saved to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Could not save file: {str(e)}")

def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = WordDocumentLoaderApp(root)
    root.mainloop()

if __name__ == "__main__":
    # If a file path is provided as a command-line argument, extract and print its text
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        if os.path.exists(file_path):
            print(extract_text_from_docx(file_path))
        else:
            print(f"File not found: {file_path}")
    else:
        # Otherwise, launch the GUI
        main()
