{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Courier New;}}
{\colortbl ;\red0\green0\blue255;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 PYTHON UITLEG - DEEL 3\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 5. MODULES EN PACKAGES\b0\fs22\par

\pard\sa200\sl276\slmult1\b Modules importeren\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Hele module importeren\par
import math\par
print(math.sqrt(16))  # 4.0\par
\par
# Specifieke functies importeren\par
from math import sqrt, pi\par
print(sqrt(16))  # 4.0\par
print(pi)  # 3.141592653589793\par
\par
# Alles importeren (niet aanbevolen vanwege namespace vervuiling)\par
from math import *\par
print(sqrt(16))  # 4.0\par
\par
# Module importeren met een alias\par
import math as m\par
print(m.sqrt(16))  # 4.0\par
\par
# Functie importeren met een alias\par
from math import sqrt as vierkantswortel\par
print(vierkantswortel(16))  # 4.0\f0\par

\pard\sa200\sl276\slmult1\b Eigen modules maken\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Bestand: mijn_module.py\par
def groet(naam):\par
    return f"Hallo, \{naam\}!"\par
\par
PI = 3.14159\par
\par
class Persoon:\par
    def __init__(self, naam):\par
        self.naam = naam\par
    \par
    def zeg_hallo(self):\par
        return f"Hallo, ik ben \{self.naam\}"\par
\par
# In een ander bestand:\par
import mijn_module\par
\par
print(mijn_module.groet("Alice"))  # Hallo, Alice!\par
print(mijn_module.PI)  # 3.14159\par
p = mijn_module.Persoon("Bob")\par
print(p.zeg_hallo())  # Hallo, ik ben Bob\f0\par

\pard\sa200\sl276\slmult1\b Packages (pakketten)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Mapstructuur voor een package:\par
# mijn_package/\par
#   __init__.py\par
#   module1.py\par
#   module2.py\par
#   subpackage/\par
#     __init__.py\par
#     module3.py\par
\par
# In module1.py\par
def functie1():\par
    return "Dit is functie1 uit module1"\par
\par
# In module2.py\par
def functie2():\par
    return "Dit is functie2 uit module2"\par
\par
# In subpackage/module3.py\par
def functie3():\par
    return "Dit is functie3 uit module3"\par
\par
# Importeren uit een package\par
import mijn_package.module1\par
print(mijn_package.module1.functie1())\par
\par
from mijn_package import module2\par
print(module2.functie2())\par
\par
from mijn_package.subpackage.module3 import functie3\par
print(functie3())\f0\par

\pard\sa200\sl276\slmult1\b Standaard bibliotheek modules\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # math - wiskundige functies\par
import math\par
print(math.sqrt(16))  # 4.0\par
print(math.sin(math.pi/2))  # 1.0\par
\par
# random - willekeurige getallen\par
import random\par
print(random.randint(1, 10))  # willekeurig getal tussen 1 en 10\par
print(random.choice(["appel", "peer", "banaan"]))  # willekeurig element\par
\par
# datetime - datum en tijd\par
from datetime import datetime, timedelta\par
nu = datetime.now()\par
print(nu)  # huidige datum en tijd\par
morgen = nu + timedelta(days=1)\par
print(morgen)  # datum en tijd van morgen\par
\par
# os - besturingssysteem interface\par
import os\par
print(os.getcwd())  # huidige werkdirectory\par
print(os.listdir())  # bestanden in huidige directory\par
\par
# sys - systeemspecifieke parameters en functies\par
import sys\par
print(sys.version)  # Python versie\par
print(sys.argv)  # commandoregel argumenten\par
\par
# json - JSON data verwerken\par
import json\par
data = \{"naam": "Alice", "leeftijd": 30\}\par
json_str = json.dumps(data)\par
print(json_str)  # {"naam": "Alice", "leeftijd": 30}\par
\par
# re - reguliere expressies\par
import re\par
tekst = "Mijn telefoonnummer is 06-12345678"\par
match = re.search(r"\\d\\d-\\d{8}", tekst)\par
print(match.group())  # 06-12345678\f0\par

\pard\sa200\sl276\slmult1\b\fs24 6. BESTANDSVERWERKING\b0\fs22\par

\pard\sa200\sl276\slmult1\b Bestanden openen en sluiten\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Bestand openen met context manager (aanbevolen)\par
with open("bestand.txt", "r") as f:\par
    inhoud = f.read()\par
    print(inhoud)\par
# Bestand wordt automatisch gesloten na het with-blok\par
\par
# Bestand handmatig openen en sluiten\par
f = open("bestand.txt", "r")\par
inhoud = f.read()\par
f.close()  # Vergeet niet te sluiten!\par
\par
# Bestandsmodi:\par
# "r" - lezen (default)\par
# "w" - schrijven (overschrijft bestaand bestand)\par
# "a" - toevoegen (append)\par
# "x" - exclusief aanmaken (fout als bestand bestaat)\par
# "b" - binaire modus (bijv. "rb", "wb")\par
# "t" - tekstmodus (default)\f0\par

\pard\sa200\sl276\slmult1\b Bestanden lezen\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Hele bestand lezen\par
with open("bestand.txt", "r") as f:\par
    inhoud = f.read()\par
    print(inhoud)\par
\par
# Specifiek aantal bytes lezen\par
with open("bestand.txt", "r") as f:\par
    deel = f.read(10)  # Eerste 10 bytes\par
    print(deel)\par
\par
# Regel voor regel lezen\par
with open("bestand.txt", "r") as f:\par
    regel = f.readline()  # Leest één regel\par
    print(regel)\par
\par
# Alle regels lezen in een lijst\par
with open("bestand.txt", "r") as f:\par
    regels = f.readlines()  # Lijst van alle regels\par
    print(regels)\par
\par
# Itereren over regels (meest efficiënt voor grote bestanden)\par
with open("bestand.txt", "r") as f:\par
    for regel in f:  # Leest regel voor regel\par
        print(regel.strip())  # strip() verwijdert newline karakters\f0\par

\pard\sa200\sl276\slmult1\b Bestanden schrijven\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Naar een bestand schrijven (overschrijft bestaande inhoud)\par
with open("output.txt", "w") as f:\par
    f.write("Hallo, wereld!\\n")\par
    f.write("Dit is een test.\\n")\par
\par
# Toevoegen aan een bestand (append)\par
with open("output.txt", "a") as f:\par
    f.write("Deze regel wordt toegevoegd.\\n")\par
\par
# Meerdere regels schrijven\par
regels = ["Regel 1\\n", "Regel 2\\n", "Regel 3\\n"]\par
with open("output.txt", "w") as f:\par
    f.writelines(regels)\f0\par

\pard\sa200\sl276\slmult1\b Bestandsposities en zoeken\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Huidige positie in bestand\par
with open("bestand.txt", "r") as f:\par
    print(f.tell())  # 0 (begin van bestand)\par
    f.read(5)  # Lees 5 bytes\par
    print(f.tell())  # 5\par
\par
# Positie wijzigen (seek)\par
with open("bestand.txt", "r") as f:\par
    f.seek(10)  # Ga naar positie 10\par
    print(f.read(5))  # Lees 5 bytes vanaf positie 10\par
    \par
    # Relatief zoeken\par
    f.seek(0)  # Terug naar begin\par
    f.read(5)  # Lees 5 bytes\par
    f.seek(3, 1)  # 3 bytes vooruit vanaf huidige positie\par
    print(f.read(5))\par
    \par
    # Vanaf einde\par
    f.seek(0, 2)  # Ga naar einde van bestand\par
    f.seek(-10, 2)  # 10 bytes terug vanaf einde\par
    print(f.read())  # Lees tot einde\f0\par

\pard\sa200\sl276\slmult1\b CSV-bestanden\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import csv\par
\par
# CSV-bestand lezen\par
with open("data.csv", "r", newline="") as f:\par
    reader = csv.reader(f)\par
    for rij in reader:\par
        print(rij)  # Lijst van waarden per rij\par
\par
# CSV-bestand lezen met kolomnamen\par
with open("data.csv", "r", newline="") as f:\par
    reader = csv.DictReader(f)  # Eerste rij als kolomnamen\par
    for rij in reader:\par
        print(rij["naam"], rij["leeftijd"])  # Toegang via kolomnamen\par
\par
# CSV-bestand schrijven\par
with open("output.csv", "w", newline="") as f:\par
    writer = csv.writer(f)\par
    writer.writerow(["naam", "leeftijd"])  # Kolomkoppen\par
    writer.writerow(["Alice", 30])\par
    writer.writerow(["Bob", 25])\par
\par
# CSV-bestand schrijven met kolomnamen\par
with open("output.csv", "w", newline="") as f:\par
    veldnamen = ["naam", "leeftijd"]\par
    writer = csv.DictWriter(f, fieldnames=veldnamen)\par
    writer.writeheader()  # Schrijf kolomkoppen\par
    writer.writerow(\{"naam": "Alice", "leeftijd": 30\})\par
    writer.writerow(\{"naam": "Bob", "leeftijd": 25\})\f0\par

\pard\sa200\sl276\slmult1\b JSON-bestanden\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import json\par
\par
# Python object naar JSON string\par
data = \{\par
    "naam": "Alice",\par
    "leeftijd": 30,\par
    "hobbies": ["lezen", "programmeren", "fietsen"],\par
    "actief": True\par
\}\par
\par
json_string = json.dumps(data)\par
print(json_string)\par
\par
# Met opmaak (pretty print)\par
json_string_mooi = json.dumps(data, indent=4)\par
print(json_string_mooi)\par
\par
# JSON string naar Python object\par
json_data = '\\{"naam": "Bob", "leeftijd": 25\\}'\par
python_dict = json.loads(json_data)\par
print(python_dict["naam"])  # "Bob"\par
\par
# Schrijven naar JSON bestand\par
with open("data.json", "w") as f:\par
    json.dump(data, f, indent=4)\par
\par
# Lezen van JSON bestand\par
with open("data.json", "r") as f:\par
    geladen_data = json.load(f)\par
    print(geladen_data["naam"])  # "Alice"\f0\par

\pard\sa200\sl276\slmult1\b\fs24 7. EXCEPTIONS (UITZONDERINGEN)\b0\fs22\par

\pard\sa200\sl276\slmult1\b Try-except blokken\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Basis try-except\par
try:\par
    x = int(input("Voer een getal in: "))\par
    print(f"Je hebt \{x\} ingevoerd")\par
except ValueError:\par
    print("Dat is geen geldig getal!")\par
\par
# Meerdere except blokken\par
try:\par
    x = int(input("Voer een getal in: "))\par
    resultaat = 10 / x\par
    print(f"10 / \{x\} = \{resultaat\}")\par
except ValueError:\par
    print("Dat is geen geldig getal!")\par
except ZeroDivisionError:\par
    print("Delen door nul is niet toegestaan!")\par
\par
# Meerdere exceptions in één except\par
try:\par
    # code die een exception kan veroorzaken\par
    pass\par
except (ValueError, TypeError):\par
    print("Er is een waarde- of typefout opgetreden")\par
\par
# Algemene exception handler (niet aanbevolen, te breed)\par
try:\par
    # code die een exception kan veroorzaken\par
    pass\par
except Exception as e:\par
    print(f"Er is een fout opgetreden: \{e\}")\f0\par

\pard\sa200\sl276\slmult1\b Else en finally\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # try-except-else\par
try:\par
    x = int(input("Voer een getal in: "))\par
except ValueError:\par
    print("Dat is geen geldig getal!")\par
else:\par
    # Wordt alleen uitgevoerd als geen exception optreedt\par
    print(f"Je hebt \{x\} ingevoerd")\par
\par
# try-except-finally\par
try:\par
    f = open("bestand.txt", "r")\par
    inhoud = f.read()\par
except FileNotFoundError:\par
    print("Het bestand bestaat niet!")\par
finally:\par
    # Wordt altijd uitgevoerd, ongeacht of er een exception was\par
    print("Opruimen...")\par
    try:\par
        f.close()\par
    except:\par
        pass  # f was mogelijk niet gedefinieerd\par
\par
# Compleet try-except-else-finally\par
try:\par
    x = int(input("Voer een getal in: "))\par
    resultaat = 10 / x\par
except ValueError:\par
    print("Dat is geen geldig getal!")\par
except ZeroDivisionError:\par
    print("Delen door nul is niet toegestaan!")\par
else:\par
    print(f"10 / \{x\} = \{resultaat\}")\par
finally:\par
    print("Berekening voltooid")\f0\par

\pard\sa200\sl276\slmult1\b Exceptions opwerpen (raise)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Exception opwerpen\par
x = -5\par
if x < 0:\par
    raise ValueError("x mag niet negatief zijn")\par
\par
# Exception opnieuw opwerpen\par
try:\par
    x = int(input("Voer een positief getal in: "))\par
    if x <= 0:\par
        raise ValueError("Het getal moet positief zijn")\par
except ValueError as e:\par
    print(f"Fout: \{e\}")\par
    raise  # Werpt de exception opnieuw op\f0\par

\pard\sa200\sl276\slmult1\b Eigen exceptions\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Eigen exception klasse\par
class NegatiefGetalError(Exception):\par
    """Exception voor negatieve getallen."""\par
    pass\par
\par
# Eigen exception gebruiken\par
def bereken_vierkantswortel(x):\par
    if x < 0:\par
        raise NegatiefGetalError("Kan geen vierkantswortel van een negatief getal berekenen")\par
    return x ** 0.5\par
\par
try:\par
    print(bereken_vierkantswortel(-5))\par
except NegatiefGetalError as e:\par
    print(f"Fout: \{e\}")\par
\par
# Meer geavanceerde eigen exception\par
class MijnError(Exception):\par
    def __init__(self, waarde, bericht):\par
        self.waarde = waarde\par
        self.bericht = bericht\par
        super().__init__(f"\{bericht\}: \{waarde\}")\par
\par
try:\par
    raise MijnError(42, "Ongeldige waarde")\par
except MijnError as e:\par
    print(f"Fout: \{e\}")\par
    print(f"Waarde: \{e.waarde\}")\par
    print(f"Bericht: \{e.bericht\}")\f0\par

\pard\sa200\sl276\slmult1\b Veelvoorkomende exceptions\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # ValueError - ongeldige waarde\par
try:\par
    x = int("abc")  # Kan "abc" niet converteren naar int\par
except ValueError:\par
    print("Ongeldige waarde")\par
\par
# TypeError - ongeldige operatie voor een type\par
try:\par
    x = "5" + 5  # Kan string niet optellen bij int\par
except TypeError:\par
    print("Ongeldige operatie")\par
\par
# IndexError - index buiten bereik\par
try:\par
    lijst = [1, 2, 3]\par
    print(lijst[10])  # Index 10 bestaat niet\par
except IndexError:\par
    print("Index buiten bereik")\par
\par
# KeyError - key niet in dictionary\par
try:\par
    d = \{"a": 1, "b": 2\}\par
    print(d["c"])  # Key "c" bestaat niet\par
except KeyError:\par
    print("Key niet gevonden")\par
\par
# FileNotFoundError - bestand niet gevonden\par
try:\par
    with open("niet_bestaand_bestand.txt", "r") as f:\par
        pass\par
except FileNotFoundError:\par
    print("Bestand niet gevonden")\par
\par
# ZeroDivisionError - delen door nul\par
try:\par
    x = 10 / 0\par
except ZeroDivisionError:\par
    print("Delen door nul")\par
\par
# ImportError - module niet gevonden\par
try:\par
    import niet_bestaande_module\par
except ImportError:\par
    print("Module niet gevonden")\f0\par
}
