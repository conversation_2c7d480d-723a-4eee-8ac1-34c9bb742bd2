# ************************************************************************************************
# Enhanced Dashboard Screen for EuroCaps Ordering System
# Based on detailed mockup specifications with role-based functionality
# Color Scheme: Header/Sidebar: 2C3E50, Background: 1B3A4B, Cards: A9C6E8, Buttons: F39C12, Text: FFFFFF
# ************************************************************************************************
Screens:
  Dashboard_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(243, 156, 18, 1)
      Fill: =RGBA(27, 58, 75, 1)
      OnVisible: |
        =// Initialize sample data collections
        ClearCollect(colCustomers,
            {CustomerID: 1, CustomerName: "Coffee World", <PERSON><PERSON>erson: "David Lee", Email: "<EMAIL>", Phone: "+31 20 123 4567", Address: "Koffieweg 10, Amsterdam"},
            {CustomerID: 2, CustomerName: "Bean Lovers", ContactPerson: "John Smith", Email: "<EMAIL>", Phone: "+31 30 234 5678", Address: "Bonenstraat 25, Utrecht"},
            {CustomerID: 3, CustomerName: "Café Express", ContactPerson: "Maria Garcia", Email: "<EMAIL>", Phone: "+31 10 345 6789", Address: "Espressolaan 5, Rotterdam"}
        );
        ClearCollect(colProducts,
            {ProductID: 1, ProductName: "Espresso Classic", ProductType: "espresso", PackageSize: 10, Description: "Traditional Italian-style espresso", Price: 4.99},
            {ProductID: 2, ProductName: "Lungo Intense", ProductType: "lungo", PackageSize: 20, Description: "Rich and intense lungo", Price: 8.99},
            {ProductID: 3, ProductName: "Ristretto Strong", ProductType: "ristretto", PackageSize: 10, Description: "Extra strong ristretto", Price: 5.49}
        );
        ClearCollect(colOrders,
            {OrderID: 1, OrderNumber: "ORD-1089", CustomerID: 1, OrderDate: Today()-1, DeliveryDate: Today()+3, Status: "new", Notes: "Rush order"},
            {OrderID: 2, OrderNumber: "ORD-1088", CustomerID: 2, OrderDate: Today()-2, DeliveryDate: Today()+4, Status: "processing", Notes: "Standard delivery"},
            {OrderID: 3, OrderNumber: "ORD-1087", CustomerID: 3, OrderDate: Today()-3, DeliveryDate: Today()+2, Status: "shipped", Notes: "Express delivery"}
        )
    Children:
      # Header Bar
      - HeaderBar:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(44, 62, 80, 1)
            Height: =60
            Width: =1366
            X: =0
            Y: =0
            BorderThickness: =0

      # Logo in Header
      - HeaderLogo:
          Control: Image@2.1.0
          Properties:
            Image: ="eurocaps-logo"
            Height: =40
            Width: =40
            X: =20
            Y: =10

      # App Title in Header
      - HeaderTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="EuroCaps Order Management Pro"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =300
            X: =70
            Y: =10

      # User Menu
      - UserMenu:
          Control: Classic/Button@2.2.0
          Properties:
            Text: =varUserName & " (" & varUserRole & ") ▼"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =200
            X: =1100
            Y: =10
            OnSelect: =Navigate(Settings_Screen, ScreenTransition.Fade)

      # Settings Icon
      - SettingsIcon:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="⚙"
            Font: =Font.Arial
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =40
            Width: =40
            X: =1310
            Y: =10
            OnSelect: =Navigate(Settings_Screen, ScreenTransition.Fade)

      # Navigation Menu
      - NavigationMenu:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(44, 62, 80, 1)
            Height: =708
            Width: =200
            X: =0
            Y: =60
            BorderThickness: =0

      # Menu Items
      - MenuDashboard:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="≡ Dashboard"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =60
            Align: =Align.Left

      - MenuCustomers:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="👥 Customers"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =110
            Align: =Align.Left
            OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)

      - MenuProducts:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📦 Products"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =160
            Align: =Align.Left
            OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)

      - MenuOrders:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📋 Orders"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =210
            Align: =Align.Left
            OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade)

      # Main Content Area
      - MainContent:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(27, 58, 75, 1)
            Height: =708
            Width: =1166
            X: =200
            Y: =60
            BorderThickness: =0

      # Page Title
      - PageTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="Dashboard - " & varUserRole
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =18
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =300
            X: =220
            Y: =80

      # Status Cards Row 1
      - NewOrdersCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =120
            Width: =250
            X: =220
            Y: =140
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8
            OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade, {FilterStatus: "new"})

      - NewOrdersTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="NEW ORDERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(44, 62, 80, 1)
            Height: =30
            Width: =200
            X: =245
            Y: =155

      - NewOrdersCount:
          Control: Label@2.5.1
          Properties:
            Text: =CountRows(Filter(colOrders, Status = "new"))
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =24
            Color: =RGBA(243, 156, 18, 1)
            Height: =40
            Width: =100
            X: =320
            Y: =190
            Align: =Align.Center

      - ProcessingOrdersCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =120
            Width: =250
            X: =490
            Y: =140
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8
            OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade, {FilterStatus: "processing"})

      - ProcessingOrdersTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="PROCESSING ORDERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(44, 62, 80, 1)
            Height: =30
            Width: =200
            X: =515
            Y: =155

      - ProcessingOrdersCount:
          Control: Label@2.5.1
          Properties:
            Text: =CountRows(Filter(colOrders, Status = "processing"))
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =24
            Color: =RGBA(243, 156, 18, 1)
            Height: =40
            Width: =100
            X: =590
            Y: =190
            Align: =Align.Center

      # Status Cards Row 2
      - ShippedOrdersCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =120
            Width: =250
            X: =220
            Y: =280
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - ShippedOrdersTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="SHIPPED ORDERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =245
            Y: =295

      - ShippedOrdersCount:
          Control: Label@2.5.1
          Properties:
            Text: ="15"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =24
            Color: =RGBA(156, 39, 176, 1)
            Height: =40
            Width: =100
            X: =320
            Y: =330
            Align: =Align.Center

      - DeliveredOrdersCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =120
            Width: =250
            X: =490
            Y: =280
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - DeliveredOrdersTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="DELIVERED ORDERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =515
            Y: =295

      - DeliveredOrdersCount:
          Control: Label@2.5.1
          Properties:
            Text: ="42"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =24
            Color: =RGBA(76, 175, 80, 1)
            Height: =40
            Width: =100
            X: =590
            Y: =330
            Align: =Align.Center

      # Recent Orders Section
      - RecentOrdersTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="RECENT ORDERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Height: =30
            Width: =200
            X: =220
            Y: =430

      # Recent Orders Gallery
      - RecentOrdersGallery:
          Control: Gallery@2.3.0
          Properties:
            Items: =FirstN(SortByColumns(colOrders, "OrderDate", Descending), 5)
            Height: =150
            Width: =700
            X: =220
            Y: =460
            TemplateSize: =30
            Fill: =RGBA(0, 0, 0, 0)
            BorderThickness: =0

      # Quick Actions
      - QuickActionsTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="QUICK ACTIONS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Height: =30
            Width: =200
            X: =220
            Y: =630

      - NewOrderButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="+ NEW ORDER"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =40
            Width: =150
            X: =220
            Y: =670
            OnSelect: =Navigate(New_Order_Screen, ScreenTransition.Fade)

      - ViewCustomersButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="VIEW CUSTOMERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =40
            Width: =150
            X: =390
            Y: =670
            OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)

      - ViewProductsButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="VIEW PRODUCTS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =40
            Width: =150
            X: =560
            Y: =670
            OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)
