# ************************************************************************************************
# Enhanced Dashboard Screen for EuroCaps Ordering System
# Based on detailed mockup specifications
# ************************************************************************************************
Screens:
  Dashboard_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(74, 111, 165, 1)
      Fill: =RGBA(245, 245, 245, 1)
    Children:
      # Header Bar
      - HeaderBar:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(74, 111, 165, 1)
            Height: =60
            Width: =1366
            X: =0
            Y: =0
            BorderThickness: =0

      # Logo in Header
      - HeaderLogo:
          Control: Image@2.1.0
          Properties:
            Image: ="eurocaps-logo"
            Height: =40
            Width: =40
            X: =20
            Y: =10

      # App Title in Header
      - HeaderTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="EuroCaps Order Management Pro"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =300
            X: =70
            Y: =10

      # User Menu
      - UserMenu:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="User ▼"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =100
            X: =1200
            Y: =10

      # Settings Icon
      - SettingsIcon:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="⚙"
            Font: =Font.Arial
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =40
            Width: =40
            X: =1310
            Y: =10
            OnSelect: =Navigate(Settings_Screen, ScreenTransition.Fade)

      # Navigation Menu
      - NavigationMenu:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(58, 90, 128, 1)
            Height: =708
            Width: =200
            X: =0
            Y: =60
            BorderThickness: =0

      # Menu Items
      - MenuDashboard:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="≡ Dashboard"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(74, 111, 165, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =60
            Align: =Align.Left

      - MenuCustomers:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="👥 Customers"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(58, 90, 128, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =110
            Align: =Align.Left
            OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)

      - MenuProducts:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📦 Products"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(58, 90, 128, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =160
            Align: =Align.Left
            OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)

      - MenuOrders:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📋 Orders"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(58, 90, 128, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =210
            Align: =Align.Left
            OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade)

      # Main Content Area
      - MainContent:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(245, 245, 245, 1)
            Height: =708
            Width: =1166
            X: =200
            Y: =60
            BorderThickness: =0

      # Page Title
      - PageTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="Dashboard"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =18
            Color: =RGBA(51, 51, 51, 1)
            Height: =40
            Width: =200
            X: =220
            Y: =80

      # Status Cards Row 1
      - NewOrdersCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =120
            Width: =250
            X: =220
            Y: =140
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - NewOrdersTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="NEW ORDERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =245
            Y: =155

      - NewOrdersCount:
          Control: Label@2.5.1
          Properties:
            Text: ="12"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =24
            Color: =RGBA(255, 152, 0, 1)
            Height: =40
            Width: =100
            X: =320
            Y: =190
            Align: =Align.Center

      - ProcessingOrdersCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =120
            Width: =250
            X: =490
            Y: =140
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - ProcessingOrdersTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="PROCESSING ORDERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =515
            Y: =155

      - ProcessingOrdersCount:
          Control: Label@2.5.1
          Properties:
            Text: ="8"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =24
            Color: =RGBA(74, 111, 165, 1)
            Height: =40
            Width: =100
            X: =590
            Y: =190
            Align: =Align.Center

      # Status Cards Row 2
      - ShippedOrdersCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =120
            Width: =250
            X: =220
            Y: =280
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - ShippedOrdersTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="SHIPPED ORDERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =245
            Y: =295

      - ShippedOrdersCount:
          Control: Label@2.5.1
          Properties:
            Text: ="15"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =24
            Color: =RGBA(156, 39, 176, 1)
            Height: =40
            Width: =100
            X: =320
            Y: =330
            Align: =Align.Center

      - DeliveredOrdersCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =120
            Width: =250
            X: =490
            Y: =280
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - DeliveredOrdersTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="DELIVERED ORDERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =515
            Y: =295

      - DeliveredOrdersCount:
          Control: Label@2.5.1
          Properties:
            Text: ="42"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =24
            Color: =RGBA(76, 175, 80, 1)
            Height: =40
            Width: =100
            X: =590
            Y: =330
            Align: =Align.Center

      # Recent Orders Section
      - RecentOrdersTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="RECENT ORDERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =220
            Y: =430

      # Quick Actions
      - QuickActionsTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="QUICK ACTIONS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =220
            Y: =650

      - NewOrderButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="+ NEW ORDER"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(76, 175, 80, 1)
            HoverFill: =RGBA(60, 159, 64, 1)
            Height: =40
            Width: =150
            X: =220
            Y: =690
            OnSelect: =Navigate(New_Order_Screen, ScreenTransition.Fade)

      - ViewCustomersButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="VIEW CUSTOMERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(74, 111, 165, 1)
            HoverFill: =RGBA(58, 95, 149, 1)
            Height: =40
            Width: =150
            X: =390
            Y: =690
            OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)

      - ViewProductsButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="VIEW PRODUCTS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(74, 111, 165, 1)
            HoverFill: =RGBA(58, 95, 149, 1)
            Height: =40
            Width: =150
            X: =560
            Y: =690
            OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)
