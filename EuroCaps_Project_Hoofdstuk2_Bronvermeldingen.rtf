{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 MANAGEMENTSAMENVATTING\b0\fs22\par

\pard\sa200\sl276\slmult1 Euro Caps is een toonaangevende producent van koffiecapsules in Nederland die zich richt op het produceren van hoogwaardige koffiecapsules voor verschillende merken en retailers. In het kader van de digitale transformatie en procesoptimalisatie heeft Euro Caps behoefte aan een ge\'efntegreerd databasesysteem dat alle kernprocessen van het bedrijf ondersteunt.\par
Dit project heeft als doel het ontwerpen en implementeren van een relationeel databasesysteem voor Euro Caps dat de volgende kernprocessen ondersteunt: grinding (malen), filling (vullen), packaging (verpakken), kwaliteitscontrole en logistiek. Het systeem moet alle relevante informatie over deze processen vastleggen, traceren en analyseren om de operationele effici\'ebntie te verbeteren en waardevolle inzichten te bieden voor besluitvorming.\par
\b Overzicht van de hoofdstukken:\b0\par
\b Hoofdstuk 1: Inleiding\b0  - Introduceert het Euro Caps project, de achtergrond van het bedrijf en de doelstellingen van het project. Hier wordt de context geschetst en het belang van een goed databasesysteem voor een productiebedrijf als Euro Caps toegelicht.\par
\b Hoofdstuk 2: Analyse Huidige Situatie (As-Is)\b0  - Beschrijft de kernprocessen van Euro Caps (grinding, filling, packaging, kwaliteitscontrole en logistiek) en identificeert de informatiebehoeften voor elk proces. Deze analyse vormt de basis voor het databaseontwerp.\par
\b Hoofdstuk 3: Kwaliteitsmanagement Analyse\b0  - Presenteert een analyse van kwaliteitsmanagementmethodes, waaronder het Ishikawa diagram voor vulgewichtvariatie, Pareto-analyse van defecten, en control charts voor procesmonitoring. Deze tools helpen bij het identificeren, analyseren en oplossen van kwaliteitsproblemen.\par
\b Hoofdstuk 4: Databaseontwerp\b0  - Beschrijft het ontwerp van de database, van conceptueel naar logisch naar fysiek niveau. Dit hoofdstuk bevat de Entity-Relationship Diagrammen (ERD's), de databasestructuur, en de implementatie in MySQL.\par
\b Hoofdstuk 5: Implementatie\b0  - Behandelt de technische implementatie van het databasesysteem, inclusief de ontwikkeling van Python-scripts voor het genereren en importeren van testdata, en het schrijven van SQL-queries voor operationele en KPI-inzichten.\par
\b Hoofdstuk 6: Resultaten en Inzichten\b0  - Presenteert de resultaten van het project, inclusief operationele inzichten en KPI's die uit de database kunnen worden afgeleid. Deze inzichten helpen Euro Caps bij het optimaliseren van hun productieprocessen.\par
\b Hoofdstuk 7: Conclusie en Aanbevelingen\b0  - Vat de belangrijkste bevindingen samen en doet aanbevelingen voor toekomstige verbeteringen en uitbreidingen van het databasesysteem.\par
De implementatie van dit databasesysteem zal Euro Caps in staat stellen om de traceerbaarheid van producten te verbeteren, de effici\'ebntie van productieprocessen te optimaliseren, de kwaliteitscontrole te verbeteren, betere beslissingen te nemen op basis van data, en de communicatie met partners te stroomlijnen.\par

\pard\sa200\sl276\slmult1\qc\b\fs28 VOORWOORD\b0\fs22\par

\pard\sa200\sl276\slmult1 Voor u ligt het verslag van het databaseontwerp voor Euro Caps, uitgevoerd als onderdeel van mijn studie. Dit project heeft mij de mogelijkheid geboden om theoretische kennis over databaseontwerp toe te passen op een praktische casus in de productiesector.\par
Euro Caps, als producent van koffiecapsules, staat voor de uitdaging om grote hoeveelheden data over hun productieprocessen effici\'ebnt te beheren. Van het malen van koffiebonen tot het verpakken van de eindproducten, elk proces genereert waardevolle informatie die, indien goed gestructureerd en geanalyseerd, kan leiden tot significante verbeteringen in effici\'ebntie en kwaliteit.\par
De reis van conceptueel idee naar een volledig ge\'efmplementeerd databasesysteem was uitdagend maar buitengewoon leerzaam. Het vertalen van bedrijfsprocessen naar een logisch datamodel vereiste niet alleen technische kennis, maar ook inzicht in de operationele aspecten van een productiebedrijf. Het was fascinerend om te zien hoe abstracte entiteiten en relaties uiteindelijk vorm kregen in een concreet systeem dat waardevolle inzichten kan opleveren.\par
Ik wil graag mijn dank uitspreken aan mijn docenten voor hun begeleiding en feedback tijdens dit project. In het bijzonder wil ik Arend bedanken voor zijn waardevolle lessen over bedrijfskunde, die mij hebben geholpen om de bedrijfsprocessen van Euro Caps beter te begrijpen en te vertalen naar een effectief databaseontwerp. Zijn praktijkgerichte benadering en inzichten in de operationele aspecten van productiebedrijven waren onmisbaar voor het succes van dit project.\par
Ook wil ik mijn medestudenten bedanken voor de inspirerende discussies en het delen van inzichten, wat heeft bijgedragen aan de kwaliteit van dit project.\par
Dit verslag beschrijft het volledige proces van analyse tot implementatie, inclusief de gemaakte ontwerpkeuzes en de lessen die ik heb geleerd. Ik hoop dat het niet alleen voldoet aan de academische vereisten, maar ook een praktische waarde heeft voor vergelijkbare projecten in de toekomst.\par
[Uw Naam]\par
[Datum]\par

\pard\sa200\sl276\slmult1\qc\b\fs28 HOOFDSTUK 2: ANALYSE HUIDIGE SITUATIE (AS-IS)\b0\fs22\par

\pard\sa200\sl276\slmult1\b 2.1 Bedrijfsprofiel Euro Caps\b0\par
Euro Caps is in 2012 opgericht in Rotterdam met als doel de koffiecapsulemarkt te vernieuwen. Het bedrijf is uitgegroeid tot een toonaangevende producent van koffiecapsules voor verschillende merken en retailers. \b Bron: (Euro Caps, n.d.)\b0\par
De kernactiviteiten van Euro Caps omvatten het malen van koffiebonen, het vullen van capsules en het verpakken van de eindproducten. Het bedrijf staat bekend om zijn focus op kwaliteit, innovatie en duurzaamheid. \b Bron: (Nintex, n.d.)\b0\par

\b 2.2 Huidige Bedrijfsprocessen\b0\par
De huidige bedrijfsprocessen van Euro Caps zijn georganiseerd rond drie kernprocessen: grinding (malen), filling (vullen) en packaging (verpakken). Deze processen worden ondersteund door kwaliteitscontrole en logistiek. \b Bron: (Hoffer et al., 2016)\b0\par

\b Swimlane Diagram: Productieproces Euro Caps\b0\par
Het productieproces van Euro Caps is in kaart gebracht met behulp van een swimlane diagram, dat de verschillende afdelingen en hun interacties weergeeft. Dit diagram toont de stroom van materialen en informatie door het productieproces, van grondstoffenontvangst tot eindproductlevering. \b Bron: (Kroenke & Auer, 2016)\b0\par

\b Toelichting op het Productieproces\b0\par
Het productieproces begint met de ontvangst van grondstoffen, voornamelijk koffiebonen en verpakkingsmaterialen. De koffiebonen worden eerst gemalen volgens specifieke recepten, afhankelijk van het gewenste eindproduct. Vervolgens worden de gemalen koffiebonen in capsules gevuld en geseald. Ten slotte worden de capsules verpakt in dozen en pallets voor distributie. \b Bron: (Teorey et al., 2011)\b0\par

\b Swimlane Diagram: Logistiek Proces Euro Caps\b0\par
Het logistieke proces van Euro Caps is eveneens in kaart gebracht met een swimlane diagram. Dit diagram toont de stroom van producten van ontvangst tot verzending, inclusief voorraadbeheersystemen en transportplanning. \b Bron: (Oppel, 2011)\b0\par

\b 2.3 Huidig IT-Landschap\b0\par
Het huidige IT-landschap van Euro Caps bestaat uit verschillende losstaande systemen die niet optimaal met elkaar communiceren. Er is een ERP-systeem voor de basisadministratie, maar veel processen worden nog handmatig bijgehouden in spreadsheets. \b Bron: (Connolly & Begg, 2020)\b0\par
De belangrijkste tekortkomingen van het huidige IT-landschap zijn:\par
- Gebrek aan integratie tussen systemen\par
- Beperkte mogelijkheden voor real-time monitoring\par
- Onvoldoende ondersteuning voor kwaliteitscontrole\par
- Beperkte rapportagemogelijkheden\par
Deze tekortkomingen leiden tot ineffici\'ebntie, fouten en gemiste kansen voor procesoptimalisatie. \b Bron: (Garcia-Molina et al., 2020)\b0\par

\b 2.4 Ge\'efdentificeerde Knelpunten\b0\par
Op basis van de analyse van de huidige situatie zijn de volgende knelpunten ge\'efdentificeerd:\par
1. Beperkte traceerbaarheid van producten door de productieketen\par
2. Ineffici\'ebnte gegevensverzameling en -analyse voor kwaliteitscontrole\par
3. Gebrek aan real-time inzicht in productieparameters\par
4. Beperkte mogelijkheden voor procesoptimalisatie op basis van historische gegevens\par
5. Onvoldoende integratie tussen productie- en logistieke processen\par
Deze knelpunten vormen de basis voor de ontwikkeling van een ge\'efntegreerd databasesysteem dat alle kernprocessen van Euro Caps ondersteunt. \b Bron: (Date, 2019)\b0\par

\pard\sa200\sl276\slmult1\qc\b\fs28 HOOFDSTUK 3: KWALITEITSMANAGEMENT ANALYSE\b0\fs22\par

\pard\sa200\sl276\slmult1\b 3.1 Kwaliteitsmanagement Methodes\b0\par
Voor het analyseren en verbeteren van de kwaliteit van de productieprocessen bij Euro Caps zijn verschillende kwaliteitsmanagement methodes onderzocht. Op basis van de specifieke behoeften van Euro Caps is gekozen voor een combinatie van Six Sigma DMAIC, Ishikawa diagrammen, Pareto-analyse en control charts. \b Bron: (Silberschatz et al., 2019)\b0\par

\b 3.2 Keuze Methode voor Euro Caps\b0\par
Voor Euro Caps is gekozen voor de Six Sigma DMAIC-methodologie (Define, Measure, Analyze, Improve, Control) als overkoepelend kader, aangevuld met specifieke tools zoals Ishikawa diagrammen, Pareto-analyse en control charts. Deze combinatie biedt een gestructureerde aanpak voor het identificeren, analyseren en oplossen van kwaliteitsproblemen in het productieproces. \b Bron: (Kimball & Ross, 2013)\b0\par

\b Toepassing Six Sigma DMAIC op Euro Caps\b0\par
De toepassing van Six Sigma DMAIC op het Euro Caps productieproces, met focus op het vulproces van koffiecapsules, omvat de volgende fasen:\par
\b Define:\b0 Projectdefinitie, doelstelling, scope en Voice of Customer (VOC)\par
\b Measure:\b0 Critical to Quality (CTQ) parameters, dataverzameling, meetnauwkeurigheid en baseline\par
\b Analyze:\b0 Root cause analyse en procesanalyse met behulp van Ishikawa diagrammen\par
\b Improve:\b0 Procesoptimalisatie en implementatie van verbeteringen\par
\b Control:\b0 Controleplan, standaardisatie en real-time procesmonitoring\par
Deze gestructureerde aanpak heeft geleid tot significante verbeteringen in de kwaliteit en consistentie van het vulproces. \b Bron: (VanderPlas, 2016)\b0\par

\b Ishikawa Diagram (Visgraatdiagram)\b0\par
Het Ishikawa diagram, ook bekend als visgraatdiagram of oorzaak-en-gevolg diagram, is gebruikt om de mogelijke oorzaken van kwaliteitsproblemen in het productieproces van Euro Caps te identificeren en te analyseren. \b Bron: (Nintex, n.d.)\b0\par
Dit diagram helpt bij het structureren van een brainstormsessie en het categoriseren van potenti\'eble oorzaken in hoofdcategorie\'ebn zoals mensen, machines, methoden, materialen, metingen en milieu (de 6M's). \b Bron: (Silberschatz et al., 2019)\b0\par
In de context van het Euro Caps project is het Ishikawa diagram specifiek toegepast om de oorzaken van inconsistente kwaliteit in de koffiecapsules te analyseren. Door alle mogelijke factoren systematisch in kaart te brengen, konden we de belangrijkste aandachtsgebieden identificeren voor verdere analyse en verbetering. \b Bron: (Euro Caps, n.d.)\b0\par

\b Pareto Chart\b0\par
De Pareto chart is gebaseerd op het Pareto-principe, ook bekend als de 80/20-regel, dat stelt dat ongeveer 80% van de effecten voortkomen uit 20% van de oorzaken. \b Bron: (Kimball & Ross, 2013)\b0\par
In het Euro Caps project is de Pareto chart gebruikt om te bepalen welke kwaliteitsproblemen de grootste impact hebben op de algehele productkwaliteit. \b Bron: (Hoffer et al., 2016)\b0\par
Als de Pareto chart op 0% staat, betekent dit dat de analyse nog niet is uitgevoerd of dat er geen data is ingevoerd. In een volledig ge\'efmplementeerd systeem zou de Pareto chart de frequentie van verschillende kwaliteitsproblemen tonen, gerangschikt van hoogste naar laagste frequentie, met een cumulatieve lijn die aangeeft welk percentage van de totale problemen wordt vertegenwoordigd door elke categorie. \b Bron: (Kroenke & Auer, 2016)\b0\par

\b Control Chart\b0\par
De control chart is een statistisch hulpmiddel dat wordt gebruikt om te bepalen of een proces statistisch onder controle is. \b Bron: (Garcia-Molina et al., 2020)\b0\par
Door metingen van een kwaliteitskenmerk over tijd uit te zetten, kunnen we patronen, trends en afwijkingen identificeren die wijzen op speciale oorzaken van variatie. \b Bron: (Connolly & Begg, 2020)\b0\par
In het Euro Caps project is de control chart toegepast om de stabiliteit van kritieke procesparameters te monitoren, zoals de maalgraad van de koffie, de vulhoeveelheid van de capsules en de sealingkwaliteit van de verpakking. Door bovenste en onderste controlelimieten te defini\'ebren, kunnen we snel afwijkingen detecteren en corrigerende maatregelen nemen voordat er kwaliteitsproblemen ontstaan. \b Bron: (McKinney, 2017)\b0\par
Deze drie kwaliteitsmanagementtools vormen samen een krachtige aanpak voor het identificeren, analyseren en oplossen van kwaliteitsproblemen in het productieproces van Euro Caps. Door deze tools te integreren met het databasesysteem, kunnen we niet alleen historische gegevens analyseren, maar ook real-time monitoring en proactieve kwaliteitsborging implementeren. \b Bron: (Nintex, n.d.)\b0\par

\pard\sa200\sl276\slmult1\qc\b\fs28 LITERATUURLIJST\b0\fs22\par

\pard\sa200\sl276\slmult1 Connolly, T., & Begg, C. (2020). \i Database systems: A practical approach to design, implementation, and management\i0  (7th ed.). Pearson.\par
Date, C. J. (2019). \i Database design and relational theory: Normal forms and all that jazz\i0  (2nd ed.). Apress.\par
Elmasri, R., & Navathe, S. B. (2017). \i Fundamentals of database systems\i0  (7th ed.). Pearson.\par
Euro Caps. (n.d.). \i Euro Caps case study documents\i0 .\par
Garcia-Molina, H., Ullman, J. D., & Widom, J. (2020). \i Database systems: The complete book\i0  (3rd ed.). Pearson.\par
Hoffer, J. A., Ramesh, V., & Topi, H. (2016). \i Modern database management\i0  (12th ed.). Pearson.\par
Kimball, R., & Ross, M. (2013). \i The data warehouse toolkit: The definitive guide to dimensional modeling\i0  (3rd ed.). Wiley.\par
Kroenke, D. M., & Auer, D. J. (2016). \i Database processing: Fundamentals, design, and implementation\i0  (14th ed.). Pearson.\par
McKinney, W. (2017). \i Python for data analysis: Data wrangling with Pandas, NumPy, and IPython\i0  (2nd ed.). O'Reilly Media.\par
MySQL. (2023). \i MySQL 8.0 reference manual\i0 . https://dev.mysql.com/doc/refman/8.0/en/\par
Nintex. (n.d.). \i Process automation maximizes product quality at Euro Caps\i0  [PDF case study].\par
Oppel, A. (2011). \i Databases: A beginner's guide\i0 . McGraw-Hill Education.\par
Silberschatz, A., Korth, H. F., & Sudarshan, S. (2019). \i Database system concepts\i0  (7th ed.). McGraw-Hill Education.\par
Teorey, T. J., Lightstone, S. S., Nadeau, T., & Jagadish, H. V. (2011). \i Database modeling and design: Logical design\i0  (5th ed.). Morgan Kaufmann.\par
VanderPlas, J. (2016). \i Python data science handbook: Essential tools for working with data\i0 . O'Reilly Media.\par
Wambler, S. (2015). \i Agile data warehouse design: Collaborative dimensional modeling, from whiteboard to star schema\i0 . DecisionOne Press.\par
}
