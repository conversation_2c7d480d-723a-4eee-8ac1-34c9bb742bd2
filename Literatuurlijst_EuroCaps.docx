Li<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, & <PERSON>, <PERSON> (2020). Database systems: A practical approach to design, implementation, and management (7th ed.). Pearson.

Date, C. J. (2019). Database design and relational theory: Normal forms and all that jazz (2nd ed.). <PERSON><PERSON><PERSON>, <PERSON>, & <PERSON>, S. B<PERSON> (2017). Fundamentals of database systems (7th ed.). Pearson.

Garcia-<PERSON>, <PERSON>, <PERSON>, J. <PERSON>., & <PERSON>, J. (2020). Database systems: The complete book (3rd ed.). Pearson.

Ho<PERSON>, <PERSON>, <PERSON>, V., & <PERSON>, H. (2016). Modern database management (12th ed.). Pearson.

Kimball, R., & Ross, M. (2013). The data warehouse toolkit: The definitive guide to dimensional modeling (3rd ed.). Wiley.

Kroenke, D. M., & <PERSON>, <PERSON>. <PERSON> (2016). Database processing: Fundamentals, design, and implementation (14th ed.). Pearson.

McK<PERSON>, W. (2017). Python for data analysis: Data wrangling with Pandas, NumPy, and IPython (2nd ed.). O'Reilly Media.

MySQL. (2023). MySQL 8.0 reference manual. https://dev.mysql.com/doc/refman/8.0/en/

<PERSON>, A. (2011). Databases: A beginner's guide. McGraw-Hill Education.

Silberschatz, A., Korth, H. F., & <PERSON>dar<PERSON>, S. (2019). Database system concepts (7th ed.). McGraw-Hill Education.

Teorey, T. J., Lightstone, S. S., Nadeau, T., & Jagadish, H. V. (2011). Database modeling and design: Logical design (5th ed.). Morgan Kaufmann.

VanderPlas, J. (2016). Python data science handbook: Essential tools for working with data. O'Reilly Media.

Wambler, S. (2015). Agile data warehouse design: Collaborative dimensional modeling, from whiteboard to star schema. DecisionOne Press.
