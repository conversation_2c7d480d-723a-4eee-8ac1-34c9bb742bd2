<!DOCTYPE html>
<html>
<head>
    <title>Many-to-Many Relatie (Correct)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .diagram {
            width: 600px;
            height: 400px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: white;
        }
        .entity {
            position: absolute;
            width: 200px;
            border: 2px solid #333;
            background-color: #f8f8f8;
            border-radius: 5px;
            text-align: center;
        }
        .entity-header {
            background-color: #333;
            color: white;
            padding: 8px;
            font-weight: bold;
        }
        .entity-body {
            padding: 10px;
        }
        .attribute {
            text-align: left;
            margin: 5px;
            padding: 3px;
        }
        .pk {
            font-weight: bold;
            text-decoration: underline;
        }
        .fk {
            font-style: italic;
            color: #555;
        }
        .line {
            position: absolute;
            background-color: #555;
        }
        .cardinality {
            position: absolute;
            font-weight: bold;
            color: #333;
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .explanation {
            margin: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="title">Many-to-Many (N:M) Relatie</div>
    <div class="subtitle">Correcte Implementatie</div>
    
    <div class="diagram">
        <div style="position: absolute; top: 20px; left: 150px; font-weight: bold; color: green;">
            Correcte implementatie (3NF genormaliseerd)
        </div>
        
        <!-- Superheld entiteit -->
        <div class="entity" style="top: 60px; left: 50px;">
            <div class="entity-header">Superheld</div>
            <div class="entity-body">
                <div class="attribute pk">SuperheldID</div>
                <div class="attribute">Naam</div>
                <!-- Andere attributen -->
            </div>
        </div>
        
        <!-- Groep entiteit -->
        <div class="entity" style="top: 60px; left: 350px;">
            <div class="entity-header">Groep</div>
            <div class="entity-body">
                <div class="attribute pk">GroepID</div>
                <div class="attribute">GroepNaam</div>
                <!-- Andere attributen -->
            </div>
        </div>
        
        <!-- Junction table -->
        <div class="entity" style="top: 200px; left: 200px;">
            <div class="entity-header">Superheld_Groep</div>
            <div class="entity-body">
                <div class="attribute pk fk">SuperheldID</div>
                <div class="attribute pk fk">GroepID</div>
                <!-- Eventuele extra attributen van de relatie -->
            </div>
        </div>
        
        <!-- Lijnen voor relaties -->
        <!-- Superheld naar junction -->
        <div class="line" style="top: 120px; left: 150px; width: 50px; height: 2px;"></div>
        <div class="line" style="top: 120px; left: 200px; width: 2px; height: 80px;"></div>
        
        <!-- Groep naar junction -->
        <div class="line" style="top: 120px; left: 350px; width: 50px; height: 2px;"></div>
        <div class="line" style="top: 120px; left: 300px; width: 2px; height: 80px;"></div>
        
        <!-- Cardinaliteit -->
        <div class="cardinality" style="top: 100px; left: 160px;">1</div>
        <div class="cardinality" style="top: 180px; left: 180px;">N</div>
        
        <div class="cardinality" style="top: 100px; left: 330px;">1</div>
        <div class="cardinality" style="top: 180px; left: 310px;">N</div>
    </div>
    
    <div class="explanation">
        <p><strong>Toelichting:</strong></p>
        <p>Dit is een correcte implementatie van een veel-op-veel (N:M) relatie tussen Superhelden en Groepen.</p>
        <p>De oplossing gebruikt drie tabellen:</p>
        <ul>
            <li><strong>Superheld</strong>: Bevat unieke superhelden met hun eigenschappen</li>
            <li><strong>Groep</strong>: Bevat unieke groepen met hun eigenschappen</li>
            <li><strong>Superheld_Groep</strong>: Een koppeltabel (junction table) die de relaties tussen superhelden en groepen vastlegt. De primaire sleutel is een samengestelde sleutel van SuperheldID en GroepID.</li>
        </ul>
        <p>Deze structuur is in 3NF en vermijdt redundantie en update-anomalieën.</p>
        <p>Voorbeeld uit de cursus:</p>
        <ul>
            <li>Thor (SuperheldID=1) is lid van Avengers (GroepID=1) en Guardians of the Galaxy (GroepID=2)</li>
            <li>In de koppeltabel staan dan twee records: (1,1) en (1,2)</li>
        </ul>
    </div>
</body>
</html>
