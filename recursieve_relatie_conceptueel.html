<!DOCTYPE html>
<html>
<head>
    <title>Recursieve <PERSON>latie - Conceptueel ERD</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .diagram {
            width: 600px;
            height: 400px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: white;
        }
        .entity {
            position: absolute;
            width: 200px;
            border: 2px solid #333;
            background-color: #f8f8f8;
            border-radius: 5px;
            text-align: center;
        }
        .entity-header {
            background-color: #333;
            color: white;
            padding: 8px;
            font-weight: bold;
        }
        .entity-body {
            padding: 10px;
        }
        .attribute {
            text-align: left;
            margin: 5px;
            padding: 3px;
        }
        .cardinality {
            position: absolute;
            font-weight: bold;
            color: #333;
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .explanation {
            margin: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="title">Recursieve Relatie</div>
    <div class="subtitle">Conceptueel ERD</div>
    
    <div class="diagram">
        <div style="position: absolute; top: 20px; left: 200px; font-weight: bold;">
            Conceptueel ERD
        </div>
        
        <div class="entity" style="top: 80px; left: 200px;">
            <div class="entity-header">Verkoper</div>
            <div class="entity-body">
                <div class="attribute">ID</div>
                <div class="attribute">Naam</div>
                <div class="attribute">Attribuut1</div>
                <div class="attribute">Attribuut2</div>
            </div>
        </div>
        
        <!-- Recursieve relatie -->
        <div style="position: absolute; top: 200px; left: 350px; width: 100px; height: 100px; border: 2px solid #555; border-radius: 50%; border-style: dashed;"></div>
        <div style="position: absolute; top: 240px; left: 360px; font-weight: bold;">Managet</div>
        
        <!-- Cardinaliteit -->
        <div class="cardinality" style="top: 180px; left: 360px;">1</div>
        <div class="cardinality" style="top: 280px; left: 360px;">N</div>
        
        <!-- Pijl -->
        <div style="position: absolute; top: 160px; left: 350px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-bottom: 15px solid #555;"></div>
    </div>
    
    <div class="explanation">
        <p><strong>Toelichting:</strong></p>
        <p>Dit is een conceptueel ERD van een recursieve relatie, gebaseerd op het voorbeeld uit de cursus:</p>
        <ul>
            <li>Een Manager kan veel andere verkopers managen</li>
            <li>Een verkoper wordt beheerd door slechts één manager</li>
        </ul>
        <p>Een recursieve relatie is een relatie van een entiteit met zichzelf. In dit geval is de "Verkoper" entiteit gerelateerd aan zichzelf via de "Managet" relatie.</p>
        <p>In het conceptuele ERD wordt dit weergegeven door een relatie die terugverwijst naar dezelfde entiteit.</p>
        <p>De kardinaliteit is één-op-veel (1:N):</p>
        <ul>
            <li>Eén manager (1) kan meerdere verkopers (N) managen</li>
            <li>Elke verkoper heeft maximaal één manager (1)</li>
        </ul>
        <p>In een conceptueel ERD ligt de focus op de entiteiten en hun relaties, zonder technische details zoals primaire of vreemde sleutels.</p>
    </div>
</body>
</html>
