* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f5f5;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.chat-container {
    width: 90%;
    max-width: 800px;
    height: 90vh;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    background-color: #4a6fa5;
    color: white;
    padding: 15px;
    text-align: center;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.message {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

.user {
    align-items: flex-end;
}

.bot {
    align-items: flex-start;
}

.message-content {
    padding: 10px 15px;
    border-radius: 20px;
    max-width: 80%;
    word-wrap: break-word;
}

.user .message-content {
    background-color: #4a6fa5;
    color: white;
}

.bot .message-content {
    background-color: #e9e9eb;
    color: #333;
}

.chat-input {
    display: flex;
    padding: 15px;
    border-top: 1px solid #e9e9eb;
}

#user-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ccc;
    border-radius: 20px;
    outline: none;
    font-size: 16px;
}

#send-button {
    margin-left: 10px;
    padding: 10px 20px;
    background-color: #4a6fa5;
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 16px;
}

#send-button:hover {
    background-color: #3a5a8f;
}

.generated-image {
    max-width: 100%;
    max-height: 300px;
    border-radius: 10px;
    margin-top: 10px;
}
