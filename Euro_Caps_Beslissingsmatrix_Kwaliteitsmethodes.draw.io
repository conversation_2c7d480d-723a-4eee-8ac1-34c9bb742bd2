<mxfile host="app.diagrams.net" modified="2023-11-15T10:00:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36" etag="1234567890" version="14.9.8">
  <diagram id="prtHgNgQTEPvFCAcTncT" name="Page-1">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="Beslissingsmatrix Kwaliteitsmethodes voor Euro Caps" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="120" y="40" width="600" height="30" as="geometry" />
        </mxCell>
        <mxCell id="3" value="" style="shape=table;startSize=0;container=1;collapsible=0;childLayout=tableLayout;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="40" y="90" width="760" height="280" as="geometry" />
        </mxCell>
        <mxCell id="4" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=#f5f5f5;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;fontStyle=1;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="3">
          <mxGeometry width="760" height="40" as="geometry" />
        </mxCell>
        <mxCell id="5" value="Criteria" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#f5f5f5;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="4">
          <mxGeometry width="120" height="40" as="geometry">
            <mxRectangle width="120" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="6" value="Six Sigma" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#f5f5f5;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="4">
          <mxGeometry x="120" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="7" value="Lean Manufacturing" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#f5f5f5;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="4">
          <mxGeometry x="280" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="8" value="HACCP" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#f5f5f5;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="4">
          <mxGeometry x="440" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="9" value="ISO 9001" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#f5f5f5;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="4">
          <mxGeometry x="600" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="10" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;" vertex="1" parent="3">
          <mxGeometry y="40" width="760" height="40" as="geometry" />
        </mxCell>
        <mxCell id="11" value="Geschiktheid voor precisieprocessen" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=12;align=left;spacingLeft=5;" vertex="1" parent="10">
          <mxGeometry width="120" height="40" as="geometry">
            <mxRectangle width="120" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="12" value="5" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#dae8fc;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#6c8ebf;" vertex="1" parent="10">
          <mxGeometry x="120" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="13" value="3" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="10">
          <mxGeometry x="280" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="14" value="2" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="10">
          <mxGeometry x="440" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="15" value="3" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="10">
          <mxGeometry x="600" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="16" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;" vertex="1" parent="3">
          <mxGeometry y="80" width="760" height="40" as="geometry" />
        </mxCell>
        <mxCell id="17" value="Voedselveiligheid" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=12;align=left;spacingLeft=5;" vertex="1" parent="16">
          <mxGeometry width="120" height="40" as="geometry">
            <mxRectangle width="120" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="18" value="2" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="16">
          <mxGeometry x="120" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="19" value="2" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="16">
          <mxGeometry x="280" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="20" value="5" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#ffe6cc;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#d79b00;" vertex="1" parent="16">
          <mxGeometry x="440" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="21" value="3" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="16">
          <mxGeometry x="600" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="22" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;" vertex="1" parent="3">
          <mxGeometry y="120" width="760" height="40" as="geometry" />
        </mxCell>
        <mxCell id="23" value="Implementatie-gemak" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=12;align=left;spacingLeft=5;" vertex="1" parent="22">
          <mxGeometry width="120" height="40" as="geometry">
            <mxRectangle width="120" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="24" value="3" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="22">
          <mxGeometry x="120" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="25" value="4" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#d5e8d4;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#82b366;" vertex="1" parent="22">
          <mxGeometry x="280" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="26" value="3" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="22">
          <mxGeometry x="440" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="27" value="2" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="22">
          <mxGeometry x="600" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="28" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;" vertex="1" parent="3">
          <mxGeometry y="160" width="760" height="40" as="geometry" />
        </mxCell>
        <mxCell id="29" value="Aansluiting bij Euro Caps cultuur" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=12;align=left;spacingLeft=5;" vertex="1" parent="28">
          <mxGeometry width="120" height="40" as="geometry">
            <mxRectangle width="120" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="30" value="5" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#dae8fc;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#6c8ebf;" vertex="1" parent="28">
          <mxGeometry x="120" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="31" value="3" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="28">
          <mxGeometry x="280" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="32" value="4" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#ffe6cc;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#d79b00;" vertex="1" parent="28">
          <mxGeometry x="440" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="33" value="3" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="28">
          <mxGeometry x="600" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="34" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;" vertex="1" parent="3">
          <mxGeometry y="200" width="760" height="40" as="geometry" />
        </mxCell>
        <mxCell id="35" value="Snelheid van implementatie" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=12;align=left;spacingLeft=5;" vertex="1" parent="34">
          <mxGeometry width="120" height="40" as="geometry">
            <mxRectangle width="120" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="36" value="3" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="34">
          <mxGeometry x="120" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="37" value="4" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#d5e8d4;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#82b366;" vertex="1" parent="34">
          <mxGeometry x="280" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="38" value="3" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="34">
          <mxGeometry x="440" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="39" value="2" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;" vertex="1" parent="34">
          <mxGeometry x="600" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="40" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=#f5f5f5;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=14;fontStyle=1;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="3">
          <mxGeometry y="240" width="760" height="40" as="geometry" />
        </mxCell>
        <mxCell id="41" value="TOTAALSCORE" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#f5f5f5;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="40">
          <mxGeometry width="120" height="40" as="geometry">
            <mxRectangle width="120" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="42" value="18" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#dae8fc;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;fontStyle=1;strokeColor=#6c8ebf;" vertex="1" parent="40">
          <mxGeometry x="120" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="43" value="16" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#d5e8d4;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#82b366;" vertex="1" parent="40">
          <mxGeometry x="280" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="44" value="17" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#ffe6cc;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;strokeColor=#d79b00;" vertex="1" parent="40">
          <mxGeometry x="440" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="45" value="13" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=14;fontStyle=1;" vertex="1" parent="40">
          <mxGeometry x="600" width="160" height="40" as="geometry">
            <mxRectangle width="160" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="46" value="Conclusie: Combinatie van Six Sigma (hoogste score) met HACCP-elementen (tweede hoogste score) is optimaal voor Euro Caps" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="120" y="390" width="600" height="30" as="geometry" />
        </mxCell>
        <mxCell id="47" value="Schaal: 1 (laag) tot 5 (hoog)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=2" vertex="1" parent="1">
          <mxGeometry x="40" y="390" width="160" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
