# ************************************************************************************************
# Supplier Management Screen for EuroCaps Ordering System
# Manages supplier relationships, performance tracking, and material sourcing
# Color Scheme: Header/Sidebar: 2C3E50, Background: 1B3A4B, Cards: A9C6E8, Buttons: F39C12, Text: FFFFFF
# ************************************************************************************************
Screens:
  Supplier_Management_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(243, 156, 18, 1)
      Fill: =RGBA(27, 58, 75, 1)
      OnVisible: |
        =// Initialize supplier management variables
        Set(varSupplierSearch, "");
        Set(varSupplierCountry, "All");
        Set(varSupplierStatus, "All");
        Set(varSupplierRating, "All");
        Set(varSelectedSupplier, Blank());
        // Refresh supplier data
        Refresh(colSuppliers);
        Refresh(colMaterialOrders);
        // Calculate supplier performance metrics
        Set(varSupplierPerformance, 
            AddColumns(
                colSuppliers,
                "TotalOrders", CountRows(Filter(colMaterialOrders, SupplierID = ThisRecord.SupplierID)),
                "TotalValue", Sum(Filter(colMaterialOrders, SupplierID = ThisRecord.SupplierID), TotalAmount),
                "LastOrderDays", DateDiff(LastOrder, Today(), Days),
                "MaterialsSupplied", CountRows(Filter(colRawMaterials, SupplierID = ThisRecord.SupplierID))
            )
        )
    Children:
      # Header Bar
      - HeaderBar:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(44, 62, 80, 1)
            Height: =60
            Width: =1366
            X: =0
            Y: =0
            BorderThickness: =0

      # Logo in Header
      - HeaderLogo:
          Control: Image@2.1.0
          Properties:
            Image: ="eurocaps-logo"
            Height: =40
            Width: =40
            X: =20
            Y: =10

      # App Title in Header
      - HeaderTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="EuroCaps Order Management Pro - Suppliers"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =400
            X: =70
            Y: =10

      # User Menu
      - UserMenu:
          Control: Classic/Button@2.2.0
          Properties:
            Text: =varUserName & " (" & varUserRole & ") ▼"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =200
            X: =1100
            Y: =10

      # Navigation Menu
      - NavigationMenu:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(44, 62, 80, 1)
            Height: =708
            Width: =200
            X: =0
            Y: =60
            BorderThickness: =0

      # Menu Items
      - MenuDashboard:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="≡ Dashboard"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =60
            Align: =Align.Left
            OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)

      - MenuCustomers:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="👥 Customers"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =110
            Align: =Align.Left
            OnSelect: =Navigate(Customer_List_Screen, ScreenTransition.Fade)

      - MenuProducts:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📦 Products"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =160
            Align: =Align.Left
            OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)

      - MenuMaterials:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="🏭 Raw Materials"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =210
            Align: =Align.Left
            OnSelect: =Navigate(Raw_Materials_Management_Screen, ScreenTransition.Fade)

      - MenuSuppliers:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="🚚 Suppliers"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =260
            Align: =Align.Left

      - MenuOrders:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📋 Orders"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(44, 62, 80, 1)
            HoverFill: =RGBA(243, 156, 18, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =310
            Align: =Align.Left
            OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade)

      # Main Content Area
      - MainContent:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(27, 58, 75, 1)
            Height: =708
            Width: =1166
            X: =200
            Y: =60
            BorderThickness: =0

      # Page Title
      - PageTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="Supplier Management"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =18
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =300
            X: =220
            Y: =80

      # Supplier Performance Summary Cards
      - PerformanceCardsSection:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(0, 0, 0, 0)
            Height: =120
            Width: =1100
            X: =220
            Y: =130
            BorderThickness: =0

      # Total Suppliers Card
      - TotalSuppliersCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =100
            Width: =250
            X: =220
            Y: =140
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - TotalSuppliersTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="TOTAL SUPPLIERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =200
            X: =245
            Y: =155

      - TotalSuppliersCount:
          Control: Label@2.5.1
          Properties:
            Text: =CountRows(colSuppliers)
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =24
            Color: =RGBA(243, 156, 18, 1)
            Height: =40
            Width: =100
            X: =320
            Y: =185
            Align: =Align.Center

      # Active Suppliers Card
      - ActiveSuppliersCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =100
            Width: =250
            X: =490
            Y: =140
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - ActiveSuppliersTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="ACTIVE SUPPLIERS"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =200
            X: =515
            Y: =155

      - ActiveSuppliersCount:
          Control: Label@2.5.1
          Properties:
            Text: =CountRows(Filter(colSuppliers, Status = "Active"))
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =24
            Color: =RGBA(40, 167, 69, 1)
            Height: =40
            Width: =100
            X: =590
            Y: =185
            Align: =Align.Center

      # Total Orders Value Card
      - TotalOrdersCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =100
            Width: =250
            X: =760
            Y: =140
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - TotalOrdersTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="TOTAL ORDERS VALUE"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =200
            X: =785
            Y: =155

      - TotalOrdersValue:
          Control: Label@2.5.1
          Properties:
            Text: ="€" & Text(Sum(colMaterialOrders, TotalAmount), "0,000")
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =20
            Color: =RGBA(243, 156, 18, 1)
            Height: =40
            Width: =200
            X: =785
            Y: =185
            Align: =Align.Center

      # Average Rating Card
      - AverageRatingCard:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =100
            Width: =250
            X: =1030
            Y: =140
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      - AverageRatingTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="AVERAGE RATING"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(44, 62, 80, 1)
            Height: =25
            Width: =200
            X: =1055
            Y: =155

      - AverageRatingValue:
          Control: Label@2.5.1
          Properties:
            Text: =Text(Average(colSuppliers, Rating), "0.0") & " ⭐"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =20
            Color: =RGBA(243, 156, 18, 1)
            Height: =40
            Width: =200
            X: =1055
            Y: =185
            Align: =Align.Center

      # Search and Filter Section
      - SearchFilterSection:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(169, 198, 232, 1)
            Height: =80
            Width: =1100
            X: =220
            Y: =270
            BorderColor: =RGBA(243, 156, 18, 1)
            BorderThickness: =2
            RadiusTopLeft: =8
            RadiusTopRight: =8
            RadiusBottomLeft: =8
            RadiusBottomRight: =8

      # Supplier Search Input
      - SupplierSearchInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            Default: =varSupplierSearch
            HintText: ="Search suppliers by name or contact..."
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =250
            X: =240
            Y: =285
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varSupplierSearch, SupplierSearchInput.Text)

      # Country Filter
      - CountryFilter:
          Control: Classic/Dropdown@2.2.0
          Properties:
            Items: =Distinct(colSuppliers, Country)
            DefaultSelectedItems: =["All"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =120
            X: =510
            Y: =285
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varSupplierCountry, CountryFilter.Selected.Value)

      # Status Filter
      - StatusFilter:
          Control: Classic/Dropdown@2.2.0
          Properties:
            Items: =["All", "Active", "Inactive"]
            DefaultSelectedItems: =["All"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =100
            X: =650
            Y: =285
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varSupplierStatus, StatusFilter.Selected.Value)

      # Rating Filter
      - RatingFilter:
          Control: Classic/Dropdown@2.2.0
          Properties:
            Items: =["All", "5 Stars", "4+ Stars", "3+ Stars", "Below 3"]
            DefaultSelectedItems: =["All"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =120
            X: =770
            Y: =285
            BorderColor: =RGBA(243, 156, 18, 1)
            OnChange: =Set(varSupplierRating, RatingFilter.Selected.Value)

      # Reset Filters Button
      - ResetFiltersButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="Reset"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =35
            Width: =80
            X: =910
            Y: =285
            OnSelect: |
              =Set(varSupplierSearch, "");
              Set(varSupplierCountry, "All");
              Set(varSupplierStatus, "All");
              Set(varSupplierRating, "All");
              Reset(SupplierSearchInput);
              Reset(CountryFilter);
              Reset(StatusFilter);
              Reset(RatingFilter)

      # New Supplier Button
      - NewSupplierButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="+ New Supplier"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(243, 156, 18, 1)
            HoverFill: =RGBA(230, 140, 5, 1)
            Height: =35
            Width: =130
            X: =1170
            Y: =285
            OnSelect: =Navigate(Supplier_Details_Screen, ScreenTransition.Fade, {Mode: "New"})

      # Suppliers Gallery
      - SuppliersGallery:
          Control: Gallery@2.3.0
          Properties:
            Items: |
              =Filter(
                  varSupplierPerformance,
                  (IsBlank(varSupplierSearch) ||
                   varSupplierSearch in SupplierName ||
                   varSupplierSearch in ContactPerson) &&
                  (varSupplierCountry = "All" || Country = varSupplierCountry) &&
                  (varSupplierStatus = "All" || Status = varSupplierStatus) &&
                  (varSupplierRating = "All" ||
                   (varSupplierRating = "5 Stars" && Rating >= 5) ||
                   (varSupplierRating = "4+ Stars" && Rating >= 4) ||
                   (varSupplierRating = "3+ Stars" && Rating >= 3) ||
                   (varSupplierRating = "Below 3" && Rating < 3))
              )
            Height: =350
            Width: =1100
            X: =220
            Y: =370
            TemplateSize: =100
            Fill: =RGBA(0, 0, 0, 0)
            BorderThickness: =0
            ShowScrollbar: =true
