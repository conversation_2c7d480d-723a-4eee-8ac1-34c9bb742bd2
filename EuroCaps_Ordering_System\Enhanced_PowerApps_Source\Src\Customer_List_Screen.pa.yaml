# ************************************************************************************************
# Enhanced Customer List Screen for EuroCaps Order Management Pro
# Based on detailed mockup specifications
# ************************************************************************************************
Screens:
  Customer_List_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(74, 111, 165, 1)
      Fill: =RGBA(245, 245, 245, 1)
    Children:
      # Header Bar
      - HeaderBar:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(74, 111, 165, 1)
            Height: =60
            Width: =1366
            X: =0
            Y: =0
            BorderThickness: =0

      # Logo in Header
      - HeaderLogo:
          Control: Image@2.1.0
          Properties:
            Image: ="eurocaps-logo"
            Height: =40
            Width: =40
            X: =20
            Y: =10

      # App Title in Header
      - HeaderTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="EuroCaps Order Management Pro"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =300
            X: =70
            Y: =10

      # User Menu
      - UserMenu:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="User ▼"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =100
            X: =1200
            Y: =10

      # Settings Icon
      - SettingsIcon:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="⚙"
            Font: =Font.Arial
            Size: =16
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =40
            Width: =40
            X: =1310
            Y: =10
            OnSelect: =Navigate(Settings_Screen, ScreenTransition.Fade)

      # Navigation Menu
      - NavigationMenu:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(58, 90, 128, 1)
            Height: =708
            Width: =200
            X: =0
            Y: =60
            BorderThickness: =0

      # Menu Items
      - MenuDashboard:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="≡ Dashboard"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(58, 90, 128, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =60
            Align: =Align.Left
            OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)

      - MenuCustomers:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="👥 Customers"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(74, 111, 165, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =110
            Align: =Align.Left

      - MenuProducts:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📦 Products"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(58, 90, 128, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =160
            Align: =Align.Left
            OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)

      - MenuOrders:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📋 Orders"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(58, 90, 128, 1)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =50
            Width: =200
            X: =0
            Y: =210
            Align: =Align.Left
            OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade)

      # Main Content Area
      - MainContent:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(245, 245, 245, 1)
            Height: =708
            Width: =1166
            X: =200
            Y: =60
            BorderThickness: =0

      # Page Title and New Customer Button
      - PageTitle:
          Control: Label@2.5.1
          Properties:
            Text: ="Customers"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =18
            Color: =RGBA(51, 51, 51, 1)
            Height: =40
            Width: =200
            X: =220
            Y: =80

      - NewCustomerButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="+ NEW CUSTOMER"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =14
            Color: =RGBA(255, 255, 255, 1)
            Fill: =RGBA(76, 175, 80, 1)
            HoverFill: =RGBA(60, 159, 64, 1)
            Height: =40
            Width: =150
            X: =1150
            Y: =80

      # Search Bar
      - SearchInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            HintText: ="Search Customers..."
            Font: =Font.Arial
            Size: =12
            BorderColor: =RGBA(74, 111, 165, 1)
            Height: =40
            Width: =400
            X: =220
            Y: =140

      - SearchIcon:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="🔍"
            Font: =Font.Arial
            Size: =14
            Color: =RGBA(74, 111, 165, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =40
            Width: =40
            X: =630
            Y: =140

      # Filters
      - FiltersLabel:
          Control: Label@2.5.1
          Properties:
            Text: ="FILTERS:"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =80
            X: =220
            Y: =200

      - FilterAllDropdown:
          Control: Classic/Dropdown@2.1.0
          Properties:
            Items: =["All", "Active", "Inactive"]
            DefaultSelectedItems: =["All"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =100
            X: =310
            Y: =195

      - SortDropdown:
          Control: Classic/Dropdown@2.1.0
          Properties:
            Items: =["Sort: Name", "Sort: Recent", "Sort: Email"]
            DefaultSelectedItems: =["Sort: Name"]
            Font: =Font.Arial
            Size: =12
            Height: =35
            Width: =120
            X: =420
            Y: =195

      - ResetFiltersButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="Reset Filters"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(74, 111, 165, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(74, 111, 165, 1)
            Height: =35
            Width: =100
            X: =550
            Y: =195

      # Customer Table Background
      - CustomerTableBackground:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            Height: =400
            Width: =1100
            X: =220
            Y: =250
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1

      # Table Headers
      - HeaderName:
          Control: Label@2.5.1
          Properties:
            Text: ="Name"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =240
            Y: =260

      - HeaderContact:
          Control: Label@2.5.1
          Properties:
            Text: ="Contact"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =460
            Y: =260

      - HeaderEmail:
          Control: Label@2.5.1
          Properties:
            Text: ="Email"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =680
            Y: =260

      - HeaderActions:
          Control: Label@2.5.1
          Properties:
            Text: ="Actions"
            Font: =Font.Arial
            FontWeight: =FontWeight.Bold
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =100
            X: =1200
            Y: =260

      # Sample Customer Rows
      - Customer1Name:
          Control: Label@2.5.1
          Properties:
            Text: ="Bean Lovers"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =240
            Y: =300

      - Customer1Contact:
          Control: Label@2.5.1
          Properties:
            Text: ="John Smith"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =460
            Y: =300

      - Customer1Email:
          Control: Label@2.5.1
          Properties:
            Text: ="john@..."
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =200
            X: =680
            Y: =300

      - Customer1ViewButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="👁️"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(74, 111, 165, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =30
            Width: =30
            X: =1200
            Y: =300

      - Customer1EditButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="📝"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(255, 152, 0, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =30
            Width: =30
            X: =1240
            Y: =300

      - Customer1OrderButton:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="🛒"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(76, 175, 80, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(0, 0, 0, 0)
            Height: =30
            Width: =30
            X: =1280
            Y: =300
            OnSelect: =Navigate(New_Order_Screen, ScreenTransition.Fade)

      # Pagination
      - PaginationPrevious:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="◀ Previous"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(74, 111, 165, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(74, 111, 165, 1)
            Height: =35
            Width: =100
            X: =220
            Y: =680

      - PaginationNext:
          Control: Classic/Button@2.2.0
          Properties:
            Text: ="Next ▶"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(74, 111, 165, 1)
            Fill: =RGBA(0, 0, 0, 0)
            BorderColor: =RGBA(74, 111, 165, 1)
            Height: =35
            Width: =100
            X: =1220
            Y: =680

      - PaginationInfo:
          Control: Label@2.5.1
          Properties:
            Text: ="Showing 1-10 of 24 customers"
            Font: =Font.Arial
            Size: =12
            Color: =RGBA(128, 128, 128, 1)
            Height: =35
            Width: =200
            X: =680
            Y: =680
            Align: =Align.Center
