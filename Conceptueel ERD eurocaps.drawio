<mxfile host="Electron" modified="2024-10-22T14:00:00.000Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.1.1 Chrome/132.0.6834.210 Electron/34.3.3 Safari/537.36" version="26.1.1">
  <diagram name="Page-1" id="ConceptueelERD-EuroCaps">
    <mxGraphModel dx="1434" dy="436" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Titel -->
        <mxCell id="title" value="EuroCaps - Conceptueel ERD" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="160" y="20" width="520" height="40" as="geometry" />
        </mxCell>
        
        <!-- Entiteiten -->
        <mxCell id="entity-soort-partner" value="Soort Partner" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="100" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="entity-partner" value="Partner" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="320" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="entity-partner-contact" value="Partner Contact" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="320" y="240" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="entity-levering" value="Levering" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="580" y="100" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="entity-levering-regel" value="Levering Regel" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="580" y="240" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="entity-soort-product" value="Soort Product" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="100" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="entity-product" value="Product" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="320" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="entity-grinding" value="Grinding" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="580" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="entity-filling" value="Filling" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="580" y="520" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="entity-packaging" value="Packaging" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="580" y="660" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Relaties -->
        <!-- Soort Partner - Partner -->
        <mxCell id="rel-soort-partner-partner" value="heeft type" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;" edge="1" parent="1" source="entity-soort-partner" target="entity-partner">
          <mxGeometry x="-0.0667" y="15" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-soort-partner-partner1" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-soort-partner-partner">
          <mxGeometry x="-0.8" y="1" relative="1" as="geometry">
            <mxPoint x="5" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-soort-partner-partner2" value="n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-soort-partner-partner">
          <mxGeometry x="0.8" y="1" relative="1" as="geometry">
            <mxPoint x="-5" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- Partner - Partner Contact -->
        <mxCell id="rel-partner-contact" value="heeft" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;" edge="1" parent="1" source="entity-partner" target="entity-partner-contact">
          <mxGeometry x="-0.0667" y="15" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-partner-contact1" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-partner-contact">
          <mxGeometry x="-0.8" y="1" relative="1" as="geometry">
            <mxPoint x="15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-partner-contact2" value="n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-partner-contact">
          <mxGeometry x="0.8" y="1" relative="1" as="geometry">
            <mxPoint x="15" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- Partner - Levering -->
        <mxCell id="rel-partner-levering" value="levert aan" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;" edge="1" parent="1" source="entity-partner" target="entity-levering">
          <mxGeometry x="-0.0667" y="15" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-partner-levering1" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-partner-levering">
          <mxGeometry x="-0.8" y="1" relative="1" as="geometry">
            <mxPoint x="5" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-partner-levering2" value="n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-partner-levering">
          <mxGeometry x="0.8" y="1" relative="1" as="geometry">
            <mxPoint x="-5" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- Levering - Levering Regel -->
        <mxCell id="rel-levering-regel" value="bevat" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;" edge="1" parent="1" source="entity-levering" target="entity-levering-regel">
          <mxGeometry x="-0.0667" y="15" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-levering-regel1" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-levering-regel">
          <mxGeometry x="-0.8" y="1" relative="1" as="geometry">
            <mxPoint x="15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-levering-regel2" value="n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-levering-regel">
          <mxGeometry x="0.8" y="1" relative="1" as="geometry">
            <mxPoint x="15" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- Soort Product - Product -->
        <mxCell id="rel-soort-product-product" value="is van type" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;" edge="1" parent="1" source="entity-soort-product" target="entity-product">
          <mxGeometry x="-0.0667" y="15" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-soort-product-product1" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-soort-product-product">
          <mxGeometry x="-0.8" y="1" relative="1" as="geometry">
            <mxPoint x="5" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-soort-product-product2" value="n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-soort-product-product">
          <mxGeometry x="0.8" y="1" relative="1" as="geometry">
            <mxPoint x="-5" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- Product - Grinding -->
        <mxCell id="rel-product-grinding" value="ondergaat" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;" edge="1" parent="1" source="entity-product" target="entity-grinding">
          <mxGeometry x="-0.0667" y="15" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-product-grinding1" value="n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-product-grinding">
          <mxGeometry x="-0.8" y="1" relative="1" as="geometry">
            <mxPoint x="5" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-product-grinding2" value="m" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-product-grinding">
          <mxGeometry x="0.8" y="1" relative="1" as="geometry">
            <mxPoint x="-5" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- Product - Filling -->
        <mxCell id="rel-product-filling" value="ondergaat" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;" edge="1" parent="1" source="entity-product" target="entity-filling">
          <mxGeometry x="-0.0667" y="15" relative="1" as="geometry">
            <mxPoint as="offset" />
            <Array as="points">
              <mxPoint x="500" y="425"/>
              <mxPoint x="500" y="550"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="card-product-filling1" value="n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-product-filling">
          <mxGeometry x="-0.8" y="1" relative="1" as="geometry">
            <mxPoint x="5" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-product-filling2" value="m" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-product-filling">
          <mxGeometry x="0.8" y="1" relative="1" as="geometry">
            <mxPoint x="-5" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- Product - Packaging -->
        <mxCell id="rel-product-packaging" value="ondergaat" style="endArrow=none;html=1;rounded=0;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;" edge="1" parent="1" source="entity-product" target="entity-packaging">
          <mxGeometry x="-0.0667" y="15" relative="1" as="geometry">
            <mxPoint as="offset" />
            <Array as="points">
              <mxPoint x="410" y="690"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="card-product-packaging1" value="n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-product-packaging">
          <mxGeometry x="-0.8" y="1" relative="1" as="geometry">
            <mxPoint x="15" y="5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-product-packaging2" value="m" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-product-packaging">
          <mxGeometry x="0.8" y="1" relative="1" as="geometry">
            <mxPoint x="-5" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- Levering Regel - Product -->
        <mxCell id="rel-levering-regel-product" value="bevat" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;" edge="1" parent="1" source="entity-levering-regel" target="entity-product">
          <mxGeometry x="-0.0667" y="15" relative="1" as="geometry">
            <mxPoint as="offset" />
            <Array as="points">
              <mxPoint x="640" y="330"/>
              <mxPoint x="380" y="330"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="card-levering-regel-product1" value="n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-levering-regel-product">
          <mxGeometry x="-0.9" y="1" relative="1" as="geometry">
            <mxPoint x="15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="card-levering-regel-product2" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;fontStyle=1" vertex="1" connectable="0" parent="rel-levering-regel-product">
          <mxGeometry x="0.9" y="1" relative="1" as="geometry">
            <mxPoint x="15" as="offset" />
          </mxGeometry>
        </mxCell>
        
        <!-- Legenda -->
        <mxCell id="legend-title" value="Legenda" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="780" y="100" width="100" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-partners" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="780" y="140" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-partners-text" value="Partners" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="820" y="140" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-leveringen" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="780" y="170" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-leveringen-text" value="Leveringen" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="820" y="170" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-producten" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="780" y="200" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-producten-text" value="Producten" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="820" y="200" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-processen" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="780" y="230" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-processen-text" value="Processen" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="820" y="230" width="100" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
