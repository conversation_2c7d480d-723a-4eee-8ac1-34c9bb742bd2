#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
EuroCaps CSV Generator Script

Dit script genereert CSV-bestanden met testdata voor de EuroCaps database.
De structuur is gebaseerd op het fysieke ERD voor het EuroCaps productieproces.

Auteur: [<PERSON><PERSON><PERSON>]
Da<PERSON>: [<PERSON><PERSON>]
"""

import csv
import random
import datetime
import os

# Maak een directory voor de CSV-bestanden als deze nog niet bestaat
if not os.path.exists('csv_data'):
    os.makedirs('csv_data')

# Hulpfuncties
def random_date(start_date, end_date):
    """Genereer een willekeurige datum tussen start_date en end_date"""
    time_between_dates = end_date - start_date
    days_between_dates = time_between_dates.days
    random_number_of_days = random.randrange(days_between_dates)
    return start_date + datetime.timedelta(days=random_number_of_days)

def random_datetime(start_date, end_date):
    """<PERSON>reer een willekeurige datum en tijd tussen start_date en end_date"""
    random_date = start_date + datetime.timedelta(
        seconds=random.randint(0, int((end_date - start_date).total_seconds()))
    )
    return random_date.strftime("%Y-%m-%d %H:%M:%S")

# 1. SoortPartner data
def generate_soort_partner_data():
    """Genereer data voor de SoortPartner tabel"""
    soort_partners = [
        [1, "Leverancier"],
        [2, "Klant"],
        [3, "Transporteur"],
        [4, "Dienstverlener"]
    ]
    
    with open('csv_data/soort_partner.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['SoortPartnerId', 'Omschrijving'])
        writer.writerows(soort_partners)
    
    print("SoortPartner data gegenereerd.")
    return soort_partners

# 2. Partner data
def generate_partner_data(soort_partners):
    """Genereer data voor de Partner tabel"""
    partners = []
    bedrijfsnamen = [
        "Koffie Groothandel BV", "Bean Masters", "Capsule Experts", 
        "Koffie Deluxe", "Aroma Supplies", "Packaging Solutions",
        "Transport Express", "Logistiek Partners", "Quality Control Services",
        "Machine Maintenance BV", "Coffee Distributors", "Capsule Innovations"
    ]
    
    for i in range(1, 13):
        soort_partner_id = random.choice([row[0] for row in soort_partners])
        bedrijfsnaam = bedrijfsnamen[i-1]
        straatnaam = f"Industrieweg {i}"
        huisnummer = str(random.randint(1, 100))
        postcode = f"{random.randint(1000, 9999)} {chr(65 + random.randint(0, 25))}{chr(65 + random.randint(0, 25))}"
        plaats = random.choice(["Amsterdam", "Rotterdam", "Utrecht", "Eindhoven", "Groningen", "Den Haag"])
        land = "Nederland"
        email = f"info@{bedrijfsnaam.lower().replace(' ', '')}.nl"
        telnr = f"0{random.randint(10, 99)}-{random.randint(1000000, 9999999)}"
        
        partners.append([i, soort_partner_id, bedrijfsnaam, straatnaam, huisnummer, postcode, plaats, land, email, telnr])
    
    with open('csv_data/partner.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['PartnerId', 'SoortPartnerId', 'Bedrijfsnaam', 'Straatnaam', 'Huisnummer', 
                         'Postcode', 'Plaats', 'Land', 'Email', 'Telnr'])
        writer.writerows(partners)
    
    print("Partner data gegenereerd.")
    return partners

# 3. PartnerContact data
def generate_partner_contact_data(partners):
    """Genereer data voor de PartnerContact tabel"""
    partner_contacts = []
    voornamen = ["Jan", "Piet", "Klaas", "Marie", "Sophie", "Lisa", "Thomas", "Mark", "Emma", "Lotte"]
    achternamen = ["de Vries", "Jansen", "Bakker", "Visser", "Smit", "Meijer", "Bos", "van Dijk", "Mulder", "de Boer"]
    functies = ["Directeur", "Inkoper", "Verkoper", "Logistiek Manager", "Kwaliteitsmanager", "Administratie"]
    
    for i in range(1, 21):
        partner_id = random.choice([row[0] for row in partners])
        voornaam = random.choice(voornamen)
        achternaam = random.choice(achternamen)
        functie = random.choice(functies)
        email = f"{voornaam.lower()}.{achternaam.lower().replace(' ', '')}@{partners[partner_id-1][2].lower().replace(' ', '')}.nl"
        telnr = f"06-{random.randint(10000000, 99999999)}"
        
        partner_contacts.append([i, partner_id, voornaam, achternaam, functie, email, telnr])
    
    with open('csv_data/partner_contact.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['PartnerContactId', 'PartnerId', 'Voornaam', 'Achternaam', 'Functie', 'Email', 'Telnr'])
        writer.writerows(partner_contacts)
    
    print("PartnerContact data gegenereerd.")
    return partner_contacts

# 4. SoortProduct data
def generate_soort_product_data():
    """Genereer data voor de SoortProduct tabel"""
    soort_producten = [
        [1, "Espresso Capsule", "Aluminium", 5.5, "36x22mm"],
        [2, "Lungo Capsule", "Aluminium", 6.0, "36x22mm"],
        [3, "Ristretto Capsule", "Aluminium", 5.0, "36x22mm"],
        [4, "Decaf Capsule", "Aluminium", 5.5, "36x22mm"],
        [5, "Biologische Capsule", "Biologisch afbreekbaar plastic", 4.8, "36x22mm"],
        [6, "Thee Capsule", "Biologisch afbreekbaar plastic", 4.5, "36x22mm"]
    ]
    
    with open('csv_data/soort_product.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['SoortProductId', 'Omschrijving', 'Materiaal', 'Gewicht', 'Afmeting'])
        writer.writerows(soort_producten)
    
    print("SoortProduct data gegenereerd.")
    return soort_producten

# 5. Product data
def generate_product_data(soort_producten):
    """Genereer data voor de Product tabel"""
    products = []
    start_date = datetime.date(2023, 1, 1)
    end_date = datetime.date(2024, 12, 31)
    statussen = ["Geproduceerd", "In productie", "Kwaliteitscontrole", "Gereed voor verzending", "Verzonden"]
    
    for i in range(1, 101):
        soort_product_id = random.choice([row[0] for row in soort_producten])
        tht_datum = random_date(start_date, end_date).strftime("%Y-%m-%d")
        status_product = random.choice(statussen)
        c_status = "C" if random.random() > 0.2 else ""
        f_status = "F" if random.random() > 0.3 else ""
        p_status = "P" if random.random() > 0.4 else ""
        
        products.append([i, soort_product_id, tht_datum, status_product, c_status, f_status, p_status])
    
    with open('csv_data/product.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['ProductId', 'SoortProductId', 'ProductTHTDatum', 'StatusProduct', 
                         'CStatusProduct', 'FStatusProduct', 'PStatusProduct'])
        writer.writerows(products)
    
    print("Product data gegenereerd.")
    return products

# 6. Grinding data
def generate_grinding_data():
    """Genereer data voor de Grinding tabel"""
    grinding_data = []
    start_date = datetime.datetime(2023, 1, 1)
    end_date = datetime.datetime(2023, 12, 31)
    machines = ["Grinder-1000", "Grinder-2000", "Grinder-3000", "Grinder-4000"]
    
    for i in range(1, 31):
        start_datetime = random_datetime(start_date, end_date)
        # Grinding duurt tussen 1 en 4 uur
        end_datetime_obj = datetime.datetime.strptime(start_datetime, "%Y-%m-%d %H:%M:%S") + datetime.timedelta(hours=random.randint(1, 4))
        end_datetime = end_datetime_obj.strftime("%Y-%m-%d %H:%M:%S")
        machine = random.choice(machines)
        
        grinding_data.append([i, start_datetime, end_datetime, machine])
    
    with open('csv_data/grinding.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['GrindingId', 'G_DatumTijdStart', 'G_DatumTijdEind', 'G_Machine'])
        writer.writerows(grinding_data)
    
    print("Grinding data gegenereerd.")
    return grinding_data

# 7. Grinding_Product data
def generate_grinding_product_data(grinding_data, products):
    """Genereer data voor de Grinding_Product koppeltabel"""
    grinding_product_data = []
    
    for grinding_id in range(1, len(grinding_data) + 1):
        # Elke grinding batch verwerkt 3-8 verschillende producten
        num_products = random.randint(3, 8)
        selected_products = random.sample([row[0] for row in products], num_products)
        
        for product_id in selected_products:
            aantal = random.randint(100, 1000)
            grinding_product_data.append([grinding_id, product_id, aantal])
    
    with open('csv_data/grinding_product.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['GrindingId', 'ProductId', 'Aantal'])
        writer.writerows(grinding_product_data)
    
    print("Grinding_Product data gegenereerd.")
    return grinding_product_data

# 8. Filling data
def generate_filling_data():
    """Genereer data voor de Filling tabel"""
    filling_data = []
    start_date = datetime.datetime(2023, 1, 1)
    end_date = datetime.datetime(2023, 12, 31)
    machines = ["Filler-A", "Filler-B", "Filler-C", "Filler-D"]
    
    for i in range(1, 41):
        start_datetime = random_datetime(start_date, end_date)
        # Filling duurt tussen 2 en 6 uur
        end_datetime_obj = datetime.datetime.strptime(start_datetime, "%Y-%m-%d %H:%M:%S") + datetime.timedelta(hours=random.randint(2, 6))
        end_datetime = end_datetime_obj.strftime("%Y-%m-%d %H:%M:%S")
        machine = random.choice(machines)
        
        filling_data.append([i, start_datetime, end_datetime, machine])
    
    with open('csv_data/filling.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['FillingId', 'F_DatumTijdStart', 'F_DatumTijdEind', 'F_Machine'])
        writer.writerows(filling_data)
    
    print("Filling data gegenereerd.")
    return filling_data

# 9. Filling_Product data
def generate_filling_product_data(filling_data, products):
    """Genereer data voor de Filling_Product koppeltabel"""
    filling_product_data = []
    
    for filling_id in range(1, len(filling_data) + 1):
        # Elke filling batch verwerkt 2-5 verschillende producten
        num_products = random.randint(2, 5)
        selected_products = random.sample([row[0] for row in products], num_products)
        
        for product_id in selected_products:
            aantal = random.randint(500, 2000)
            filling_product_data.append([filling_id, product_id, aantal])
    
    with open('csv_data/filling_product.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['FillingId', 'ProductId', 'Aantal'])
        writer.writerows(filling_product_data)
    
    print("Filling_Product data gegenereerd.")
    return filling_product_data

# 10. Packaging data
def generate_packaging_data():
    """Genereer data voor de Packaging tabel"""
    packaging_data = []
    start_date = datetime.datetime(2023, 1, 1)
    end_date = datetime.datetime(2023, 12, 31)
    machines = ["Packager-X1", "Packager-X2", "Packager-Y1", "Packager-Z1"]
    
    for i in range(1, 51):
        start_datetime = random_datetime(start_date, end_date)
        # Packaging duurt tussen 3 en 8 uur
        end_datetime_obj = datetime.datetime.strptime(start_datetime, "%Y-%m-%d %H:%M:%S") + datetime.timedelta(hours=random.randint(3, 8))
        end_datetime = end_datetime_obj.strftime("%Y-%m-%d %H:%M:%S")
        machine = random.choice(machines)
        
        packaging_data.append([i, start_datetime, end_datetime, machine])
    
    with open('csv_data/packaging.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['PackagingId', 'P_DatumTijdStart', 'P_DatumTijdEind', 'P_Machine'])
        writer.writerows(packaging_data)
    
    print("Packaging data gegenereerd.")
    return packaging_data

# 11. Packaging_Product data
def generate_packaging_product_data(packaging_data, products):
    """Genereer data voor de Packaging_Product koppeltabel"""
    packaging_product_data = []
    
    for packaging_id in range(1, len(packaging_data) + 1):
        # Elke packaging batch verwerkt 1-3 verschillende producten
        num_products = random.randint(1, 3)
        selected_products = random.sample([row[0] for row in products], num_products)
        
        for product_id in selected_products:
            aantal = random.randint(1000, 5000)
            aantal_stuks_in_doos = random.choice([10, 20, 30, 40, 50])
            packaging_product_data.append([packaging_id, product_id, aantal, aantal_stuks_in_doos])
    
    with open('csv_data/packaging_product.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['PackagingId', 'ProductId', 'Aantal', 'AantalStuksInDoos'])
        writer.writerows(packaging_product_data)
    
    print("Packaging_Product data gegenereerd.")
    return packaging_product_data

# 12. Levering data
def generate_levering_data(partners):
    """Genereer data voor de Levering tabel"""
    leveringen = []
    start_date = datetime.date(2023, 1, 1)
    end_date = datetime.date(2023, 12, 31)
    
    # Filter alleen partners die klanten zijn (SoortPartnerId = 2)
    klanten = [partner for partner in partners if partner[1] == 2]
    
    for i in range(1, 31):
        partner_id = random.choice([klant[0] for klant in klanten])
        levering_datum = random_date(start_date, end_date).strftime("%Y-%m-%d")
        # Verwachte leverdatum is 1-7 dagen na de leveringsdatum
        verwachte_leverdatum_obj = datetime.datetime.strptime(levering_datum, "%Y-%m-%d") + datetime.timedelta(days=random.randint(1, 7))
        verwachte_leverdatum = verwachte_leverdatum_obj.strftime("%Y-%m-%d")
        
        leveringen.append([i, partner_id, levering_datum, verwachte_leverdatum])
    
    with open('csv_data/levering.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['LeveringId', 'PartnerId', 'LeveringDatum', 'VerwachteLeverdatum'])
        writer.writerows(leveringen)
    
    print("Levering data gegenereerd.")
    return leveringen

# 13. LeveringRegel data
def generate_levering_regel_data(leveringen, products):
    """Genereer data voor de LeveringRegel tabel"""
    levering_regels = []
    
    for levering_id in range(1, len(leveringen) + 1):
        # Elke levering bevat 1-5 verschillende producten
        num_products = random.randint(1, 5)
        selected_products = random.sample([row[0] for row in products], num_products)
        
        for product_id in selected_products:
            aantal = random.randint(100, 1000)
            levering_regels.append([levering_id, product_id, aantal])
    
    with open('csv_data/levering_regel.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['LeveringId', 'ProductId', 'Aantal'])
        writer.writerows(levering_regels)
    
    print("LeveringRegel data gegenereerd.")
    return levering_regels

# Hoofdfunctie om alle data te genereren
def generate_all_data():
    """Genereer alle CSV-bestanden voor de EuroCaps database"""
    print("Start genereren van CSV-bestanden voor EuroCaps database...")
    
    # Genereer data in de juiste volgorde (rekening houdend met afhankelijkheden)
    soort_partners = generate_soort_partner_data()
    partners = generate_partner_data(soort_partners)
    generate_partner_contact_data(partners)
    
    soort_producten = generate_soort_product_data()
    products = generate_product_data(soort_producten)
    
    grinding_data = generate_grinding_data()
    generate_grinding_product_data(grinding_data, products)
    
    filling_data = generate_filling_data()
    generate_filling_product_data(filling_data, products)
    
    packaging_data = generate_packaging_data()
    generate_packaging_product_data(packaging_data, products)
    
    leveringen = generate_levering_data(partners)
    generate_levering_regel_data(leveringen, products)
    
    print("Alle CSV-bestanden zijn succesvol gegenereerd in de map 'csv_data'.")

# Voer het script uit
if __name__ == "__main__":
    generate_all_data()
