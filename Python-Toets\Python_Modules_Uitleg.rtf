{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Courier New;}}
{\colortbl ;\red0\green0\blue255;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs32 PYTHON MODULES UITLEG\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs28 1. MATH MODULE\b0\fs22\par
De math module bevat wiskundige functies en constanten.\par
\b Importeren:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import math\f0\par

\pard\sa200\sl276\slmult1\b Veelgebruikte functies:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Constanten\par
pi_waarde = math.pi      # 3.141592653589793\par
e_waarde = math.e        # 2.718281828459045\par
\par
# Afronden\par
print(math.ceil(4.2))    # 5 (naar boven afronden)\par
print(math.floor(4.8))   # 4 (naar beneden afronden)\par
\par
# Wortels en machten\par
print(math.sqrt(16))     # 4.0 (vierkantswortel)\par
print(math.pow(2, 3))    # 8.0 (2^3)\par
\par
# Goniometrische functies (in radialen)\par
print(math.sin(math.pi/2))  # 1.0\par
print(math.cos(math.pi))    # -1.0\par
print(math.tan(math.pi/4))  # 1.0\par
\par
# Conversie graden/radialen\par
print(math.degrees(math.pi))  # 180.0\par
print(math.radians(180))      # 3.141592653589793\par
\par
# Logaritmen\par
print(math.log(10))      # 2.302585... (natuurlijke logaritme)\par
print(math.log10(100))   # 2.0 (logaritme met grondtal 10)\f0\par

\pard\sa200\sl276\slmult1\b\fs28 2. RANDOM MODULE\b0\fs22\par
De random module wordt gebruikt voor het genereren van willekeurige getallen.\par
\b Importeren:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import random\f0\par

\pard\sa200\sl276\slmult1\b Veelgebruikte functies:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Willekeurig getal tussen 0 en 1\par
print(random.random())  # bijv. 0.7234846783\par
\par
# Willekeurig geheel getal in bereik\par
print(random.randint(1, 10))  # bijv. 7 (inclusief 1 en 10)\par
print(random.randrange(1, 10))  # bijv. 6 (inclusief 1, exclusief 10)\par
\par
# Willekeurig element uit een lijst\par
kleuren = ["rood", "groen", "blauw", "geel"]\par
print(random.choice(kleuren))  # bijv. "blauw"\par
\par
# Willekeurige selectie uit een lijst\par
print(random.sample(kleuren, 2))  # bijv. ["geel", "rood"]\par
\par
# Lijst schudden\par
kaarten = [1, 2, 3, 4, 5]\par
random.shuffle(kaarten)\par
print(kaarten)  # bijv. [3, 1, 5, 2, 4]\f0\par

\pard\sa200\sl276\slmult1\b\fs28 3. DATETIME MODULE\b0\fs22\par
De datetime module wordt gebruikt voor het werken met datums en tijden.\par
\b Importeren:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 from datetime import datetime, date, time, timedelta\f0\par

\pard\sa200\sl276\slmult1\b Veelgebruikte functies:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Huidige datum en tijd\par
nu = datetime.now()\par
print(nu)  # bijv. 2024-05-15 14:32:10.456789\par
\par
# Datum en tijd maken\par
d = date(2024, 5, 15)\par
t = time(14, 30, 0)\par
dt = datetime(2024, 5, 15, 14, 30, 0)\par
\par
# Formatteren van datum/tijd\par
print(nu.strftime("%d-%m-%Y"))  # bijv. "15-05-2024"\par
print(nu.strftime("%H:%M:%S"))  # bijv. "14:32:10"\par
\par
# String naar datetime\par
datum_str = "15-05-2024 14:30:00"\par
datum = datetime.strptime(datum_str, "%d-%m-%Y %H:%M:%S")\par
\par
# Rekenen met datums\par
morgen = nu + timedelta(days=1)\par
volgende_week = nu + timedelta(weeks=1)\par
twee_uur_later = nu + timedelta(hours=2)\f0\par

\pard\sa200\sl276\slmult1\b\fs28 4. OS MODULE\b0\fs22\par
De os module biedt functies voor interactie met het besturingssysteem.\par
\b Importeren:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import os\f0\par

\pard\sa200\sl276\slmult1\b Veelgebruikte functies:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Huidige werkdirectory\par
print(os.getcwd())  # bijv. "C:\\Users\\<USER>\\Documents"\par
\par
# Veranderen van directory\par
os.chdir("C:\\Users\\<USER>\\Downloads")\par
\par
# Lijst van bestanden en mappen\par
print(os.listdir())  # lijst van bestanden in huidige map\par
\par
# Controleren of bestand/map bestaat\par
print(os.path.exists("bestand.txt"))  # True of False\par
\par
# Controleren of het een bestand of map is\par
print(os.path.isfile("bestand.txt"))  # True of False\par
print(os.path.isdir("map"))  # True of False\par
\par
# Pad manipulatie\par
pad = os.path.join("map", "submap", "bestand.txt")\par
print(pad)  # "map/submap/bestand.txt" (of \\ op Windows)\par
\par
# Bestandsinformatie\par
print(os.path.getsize("bestand.txt"))  # grootte in bytes\par
\par
# Map aanmaken\par
os.mkdir("nieuwe_map")\par
os.makedirs("map/submap/subsubmap")  # inclusief tussenmappen\par
\par
# Bestand/map verwijderen\par
os.remove("bestand.txt")  # verwijder bestand\par
os.rmdir("lege_map")  # verwijder lege map\f0\par

\pard\sa200\sl276\slmult1\b\fs28 5. SYS MODULE\b0\fs22\par
De sys module biedt toegang tot variabelen en functies die interactie hebben met de Python interpreter.\par
\b Importeren:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import sys\f0\par

\pard\sa200\sl276\slmult1\b Veelgebruikte functies:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Commandoregel argumenten\par
print(sys.argv)  # lijst van argumenten (sys.argv[0] is scriptnaam)\par
\par
# Python versie\par
print(sys.version)  # bijv. "3.9.5 (default, May 11 2021, ..."\par
\par
# Pad naar modules\par
print(sys.path)  # lijst van mappen waar Python naar modules zoekt\par
\par
# Standaard in/uitvoer\par
sys.stdout.write("Dit is een test\\n")  # print naar stdout\par
invoer = sys.stdin.readline()  # lees van stdin\par
sys.stderr.write("Dit is een fout\\n")  # print naar stderr\par
\par
# Programma be\'ebindigen\par
sys.exit(0)  # be\'ebindig programma met exitcode 0 (succes)\f0\par

\pard\sa200\sl276\slmult1\b\fs28 6. JSON MODULE\b0\fs22\par
De json module wordt gebruikt voor het werken met JSON-gegevens.\par
\b Importeren:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import json\f0\par

\pard\sa200\sl276\slmult1\b Veelgebruikte functies:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Python object naar JSON string\par
data = \{\par
    "naam": "Jan",\par
    "leeftijd": 25,\par
    "hobbies": ["programmeren", "lezen", "fietsen"],\par
    "actief": True\par
\}\par
\par
json_string = json.dumps(data)\par
print(json_string)\par
# \{"naam": "Jan", "leeftijd": 25, "hobbies": ["programmeren", "lezen", "fietsen"], "actief": true\}\par
\par
# Met opmaak (pretty print)\par
json_string_mooi = json.dumps(data, indent=4)\par
print(json_string_mooi)\par
\par
# JSON string naar Python object\par
json_data = '\\{"naam": "Piet", "leeftijd": 30\\}'\par
python_dict = json.loads(json_data)\par
print(python_dict["naam"])  # "Piet"\par
\par
# Schrijven naar JSON bestand\par
with open("data.json", "w") as f:\par
    json.dump(data, f, indent=4)\par
\par
# Lezen van JSON bestand\par
with open("data.json", "r") as f:\par
    geladen_data = json.load(f)\f0\par

\pard\sa200\sl276\slmult1\b\fs28 7. RE MODULE (REGULAR EXPRESSIONS)\b0\fs22\par
De re module wordt gebruikt voor het werken met reguliere expressies.\par
\b Importeren:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import re\f0\par

\pard\sa200\sl276\slmult1\b Veelgebruikte functies:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Zoeken naar een patroon\par
tekst = "Mijn telefoonnummer is 06-12345678"\par
resultaat = re.search(r"\\d\\d-\\d{8}", tekst)\par
if resultaat:\par
    print(resultaat.group())  # "06-12345678"\par
\par
# Alle voorkomens vinden\par
tekst = "De nummers zijn 06-12345678 en 070-9876543"\par
resultaten = re.findall(r"\\d+-\\d+", tekst)\par
print(resultaten)  # ["06-12345678", "070-9876543"]\par
\par
# Vervangen\par
tekst = "Mijn e-<NAME_EMAIL>"\par
nieuw_tekst = re.sub(r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z\{2,\}",\par
                    "EMAIL", tekst)\par
print(nieuw_tekst)  # "Mijn e-mail is EMAIL"\par
\par
# Splitsen op patroon\par
tekst = "appel,peer,banaan;kers:druif"\par
delen = re.split(r"[,;:]", tekst)\par
print(delen)  # ["appel", "peer", "banaan", "kers", "druif"]\par
\par
# Patroon compileren (voor herhaald gebruik)\par
patroon = re.compile(r"\\d+-\\d+")\par
resultaten = patroon.findall("06-12345678 en 070-9876543")\par
print(resultaten)  # ["06-12345678", "070-9876543"]\f0\par

\pard\sa200\sl276\slmult1\b\fs28 8. COLLECTIONS MODULE\b0\fs22\par
De collections module biedt alternatieve datastructuren.\par
\b Importeren:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 from collections import Counter, defaultdict, namedtuple, deque\f0\par

\pard\sa200\sl276\slmult1\b Veelgebruikte klassen:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Counter: telt voorkomens van elementen\par
woorden = ["appel", "peer", "appel", "banaan", "appel", "peer"]\par
teller = Counter(woorden)\par
print(teller)  # Counter(\{'appel': 3, 'peer': 2, 'banaan': 1\})\par
print(teller["appel"])  # 3\par
print(teller.most_common(2))  # [('appel', 3), ('peer', 2)]\par
\par
# defaultdict: dictionary met standaardwaarde voor nieuwe keys\par
d = defaultdict(list)  # standaardwaarde is een lege lijst\par
d["fruit"].append("appel")  # geen KeyError als "fruit" niet bestaat\par
print(d)  # defaultdict(<class 'list'>, \{'fruit': ['appel']\})\par
\par
# namedtuple: tuple met benoemde velden\par
Punt = namedtuple("Punt", ["x", "y"])\par
p = Punt(10, 20)\par
print(p.x, p.y)  # 10 20\par
print(p[0], p[1])  # 10 20\par
\par
# deque: dubbel-eindigende wachtrij (effici\'ebnt toevoegen/verwijderen)\par
d = deque([1, 2, 3])\par
d.append(4)  # toevoegen aan rechterkant\par
d.appendleft(0)  # toevoegen aan linkerkant\par
print(d)  # deque([0, 1, 2, 3, 4])\par
print(d.pop())  # 4 (verwijderen van rechterkant)\par
print(d.popleft())  # 0 (verwijderen van linkerkant)\f0\par

\pard\sa200\sl276\slmult1\b\fs28 9. CSV MODULE\b0\fs22\par
De csv module wordt gebruikt voor het lezen en schrijven van CSV-bestanden.\par
\b Importeren:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import csv\f0\par

\pard\sa200\sl276\slmult1\b Veelgebruikte functies:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # CSV-bestand lezen\par
with open("data.csv", "r", newline="") as f:\par
    reader = csv.reader(f)\par
    for rij in reader:\par
        print(rij)  # lijst van waarden per rij\par
\par
# CSV-bestand lezen met kolomnamen\par
with open("data.csv", "r", newline="") as f:\par
    reader = csv.DictReader(f)\par
    for rij in reader:\par
        print(rij["naam"], rij["leeftijd"])  # toegang via kolomnamen\par
\par
# CSV-bestand schrijven\par
with open("nieuw.csv", "w", newline="") as f:\par
    writer = csv.writer(f)\par
    writer.writerow(["naam", "leeftijd"])  # kolomkoppen\par
    writer.writerow(["Jan", 25])\par
    writer.writerow(["Piet", 30])\par
\par
# CSV-bestand schrijven met kolomnamen\par
with open("nieuw.csv", "w", newline="") as f:\par
    veldnamen = ["naam", "leeftijd"]\par
    writer = csv.DictWriter(f, fieldnames=veldnamen)\par
    writer.writeheader()  # schrijf kolomkoppen\par
    writer.writerow(\{"naam": "Jan", "leeftijd": 25\})\par
    writer.writerow(\{"naam": "Piet", "leeftijd": 30\})\f0\par

\pard\sa200\sl276\slmult1\b\fs28 10. URLLIB MODULE\b0\fs22\par
De urllib module wordt gebruikt voor het werken met URL's en het ophalen van webpagina's.\par
\b Importeren:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 from urllib.request import urlopen\par
from urllib.parse import urlparse, urlencode\f0\par

\pard\sa200\sl276\slmult1\b Veelgebruikte functies:\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Webpagina ophalen\par
with urlopen("https://www.python.org") as response:\par
    html = response.read().decode("utf-8")\par
    print(len(html))  # lengte van de HTML\par
\par
# URL ontleden\par
parsed_url = urlparse("https://www.python.org/docs/?version=3")\par
print(parsed_url.scheme)  # "https"\par
print(parsed_url.netloc)  # "www.python.org"\par
print(parsed_url.path)    # "/docs/"\par
print(parsed_url.query)   # "version=3"\par
\par
# Query parameters maken\par
params = \{"q": "python", "lang": "nl"\}\par
query_string = urlencode(params)\par
print(query_string)  # "q=python&lang=nl"\par
url = f"https://www.google.com/search?\{query_string\}"\par
print(url)  # "https://www.google.com/search?q=python&lang=nl"\f0\par
}
