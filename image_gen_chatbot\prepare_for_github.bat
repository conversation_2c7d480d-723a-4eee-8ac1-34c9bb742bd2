@echo off
echo Preparing AI Image Generator Chatbot for GitHub Pages
echo ===================================================

set /p deploy_dir="Enter the directory where you want to prepare the GitHub repository: "

if not exist "%deploy_dir%" (
    echo Creating directory %deploy_dir%...
    mkdir "%deploy_dir%"
)

echo Copying files to %deploy_dir%...
xcopy /E /I /Y github_pages\* "%deploy_dir%"

echo.
echo Files have been copied to %deploy_dir%
echo.
echo Next steps:
echo 1. Navigate to the directory: cd "%deploy_dir%"
echo 2. Initialize Git: git init
echo 3. Add all files: git add .
echo 4. Commit: git commit -m "Initial commit"
echo 5. Connect to GitHub: git remote add origin https://github.com/YOUR_USERNAME/YOUR_REPOSITORY_NAME.git
echo 6. Push to GitHub: git push -u origin main
echo.
echo For detailed instructions, see the GITHUB_DEPLOYMENT.md file in the directory.

pause
