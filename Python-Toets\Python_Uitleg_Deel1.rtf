{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Courier New;}}
{\colortbl ;\red0\green0\blue255;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 PYTHON UITLEG - DEEL 1\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 1. VARIABELEN & DATATYPES\b0\fs22\par

\pard\sa200\sl276\slmult1\b Integers (gehele getallen)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Integers aanmaken\par
x = 5\par
y = -10\par
groot_getal = 1_000_000  # underscore voor leesbaarheid\par
\par
# Bewerkingen\par
som = x + y          # -5\par
verschil = x - y     # 15\par
product = x * y      # -50\par
quotient = x // y    # 0 (integer deling)\par
rest = x % y         # 5 (modulo/rest)\par
macht = x ** 2       # 25 (5 tot de macht 2)\f0\par

\pard\sa200\sl276\slmult1\b Floats (kommagetallen)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Floats aanmaken\par
x = 3.14\par
y = -0.001\par
wetenschappelijk = 2.5e6  # 2.5 * 10^6 = 2500000.0\par
\par
# Bewerkingen\par
som = x + y          # 3.139\par
product = x * 2      # 6.28\par
deling = x / 2       # 1.57 (float deling)\par
\par
# Afronden\par
afgerond = round(3.14159, 2)  # 3.14 (2 decimalen)\par
naar_beneden = int(3.9)       # 3 (afkappen decimalen)\f0\par

\pard\sa200\sl276\slmult1\b Strings (tekst)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Strings aanmaken\par
naam = "Python"\par
beschrijving = 'Programmeertaal'\par
lange_tekst = """Dit is een tekst\par
die over meerdere regels gaat."""\par
\par
# Concatenatie (samenvoegen)\par
volledige_naam = naam + " " + beschrijving  # "Python Programmeertaal"\par
\par
# Herhaling\par
herhaald = "Ha" * 3  # "HaHaHa"\par
\par
# Indexering (begint bij 0)\par
eerste_letter = naam[0]    # "P"\par
laatste_letter = naam[-1]  # "n"\par
\par
# Slicing [start:stop:stap]\par
deel = naam[1:4]     # "yth" (karakters 1 t/m 3)\par
omkeren = naam[::-1] # "nohtyP" (hele string omgekeerd)\par
\par
# String methoden\par
hoofdletters = naam.upper()         # "PYTHON"\par
kleine_letters = naam.lower()       # "python"\par
vervangen = naam.replace("P", "J")  # "Jython"\par
gesplitst = "a,b,c".split(",")      # ["a", "b", "c"]\par
samengevoegd = "-".join(["a", "b"]) # "a-b"\par
\par
# Formatteren\par
naam = "Alice"\par
leeftijd = 30\par
# f-string (Python 3.6+)\par
bericht1 = f"Hallo, ik ben \{naam\} en ik ben \{leeftijd\} jaar."\par
# .format() methode\par
bericht2 = "Hallo, ik ben \{\} en ik ben \{\} jaar.".format(naam, leeftijd)\par
# %-notatie (ouder)\par
bericht3 = "Hallo, ik ben %s en ik ben %d jaar." % (naam, leeftijd)\f0\par

\pard\sa200\sl276\slmult1\b Booleans (waar/onwaar)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Booleans aanmaken\par
waar = True\par
onwaar = False\par
\par
# Vergelijkingen geven booleans terug\par
is_gelijk = (5 == 5)        # True\par
is_groter = (10 > 5)        # True\par
is_kleiner = (10 < 5)       # False\par
\par
# Logische operatoren\par
en_resultaat = True and False  # False (beide moeten waar zijn)\par
of_resultaat = True or False   # True (minstens één moet waar zijn)\par
niet_resultaat = not True      # False (keert de waarde om)\par
\par
# Boolean conversie\par
# Deze waarden worden als False beschouwd:\par
# False, 0, 0.0, "", [], \{\}, set(), None\par
# Alles anders is True\par
print(bool(0))      # False\par
print(bool(1))      # True\par
print(bool(""))     # False\par
print(bool("abc"))  # True\f0\par

\pard\sa200\sl276\slmult1\b Lists (lijsten)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Lijsten aanmaken\par
getallen = [1, 2, 3, 4, 5]\par
gemengd = [1, "twee", 3.0, [4, 5]]\par
lege_lijst = []\par
via_constructor = list("abc")  # ['a', 'b', 'c']\par
\par
# Indexering en slicing (zoals bij strings)\par
eerste = getallen[0]       # 1\par
laatste = getallen[-1]     # 5\par
deel = getallen[1:4]       # [2, 3, 4]\par
\par
# Lijsten wijzigen\par
getallen[0] = 10           # [10, 2, 3, 4, 5]\par
\par
# Lijst methoden\par
getallen.append(6)         # [10, 2, 3, 4, 5, 6] (toevoegen aan einde)\par
getallen.insert(1, 15)     # [10, 15, 2, 3, 4, 5, 6] (invoegen op index)\par
getallen.extend([7, 8])    # [10, 15, 2, 3, 4, 5, 6, 7, 8] (lijst toevoegen)\par
\par
getallen.remove(15)        # [10, 2, 3, 4, 5, 6, 7, 8] (waarde verwijderen)\par
verwijderd = getallen.pop(1)  # verwijderd = 2, getallen = [10, 3, 4, 5, 6, 7, 8]\par
getallen.clear()           # [] (alle elementen verwijderen)\par
\par
# Andere handige methoden\par
kleuren = ["rood", "groen", "blauw", "rood"]\par
index = kleuren.index("groen")  # 1 (eerste voorkomen)\par
aantal = kleuren.count("rood")  # 2 (aantal voorkomens)\par
kleuren.sort()                  # ["blauw", "groen", "rood", "rood"]\par
kleuren.reverse()               # ["rood", "rood", "groen", "blauw"]\par
\par
# List comprehension (krachtige manier om lijsten te maken)\par
kwadraten = [x**2 for x in range(5)]  # [0, 1, 4, 9, 16]\par
even = [x for x in range(10) if x % 2 == 0]  # [0, 2, 4, 6, 8]\f0\par

\pard\sa200\sl276\slmult1\b Tuples (onveranderbare lijsten)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Tuples aanmaken\par
coordinaten = (10, 20)\par
gemengd = (1, "twee", 3.0)\par
enkele_waarde = (5,)  # Komma nodig voor tuple met één element\par
lege_tuple = ()\par
via_constructor = tuple([1, 2, 3])  # (1, 2, 3)\par
\par
# Indexering en slicing (zoals bij lijsten)\par
x = coordinaten[0]  # 10\par
deel = gemengd[0:2]  # (1, "twee")\par
\par
# Tuples zijn onveranderbaar (immutable)\par
# coordinaten[0] = 5  # Dit geeft een TypeError\par
\par
# Tuple uitpakken\par
x, y = coordinaten  # x = 10, y = 20\par
\par
# Tuple methoden (beperkt omdat ze onveranderbaar zijn)\par
index = gemengd.index("twee")  # 1\par
aantal = (1, 2, 1, 3).count(1)  # 2\par
\par
# Tuples worden vaak gebruikt voor functies die meerdere waarden teruggeven\par
def geef_coordinaten():\par
    return (10, 20)\par
\par
x, y = geef_coordinaten()  # x = 10, y = 20\f0\par

\pard\sa200\sl276\slmult1\b Dictionaries (woordenboeken)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Dictionaries aanmaken\par
persoon = \{"naam": "Jan", "leeftijd": 25\}\par
scores = \{"Alice": 95, "Bob": 87, "Charlie": 92\}\par
lege_dict = \{\}\par
via_constructor = dict(naam="Jan", leeftijd=25)  # {"naam": "Jan", "leeftijd": 25}\par
\par
# Toegang tot waarden\par
naam = persoon["naam"]  # "Jan"\par
# naam = persoon["adres"]  # KeyError als key niet bestaat\par
\par
# Veilige toegang met get() (geen error als key niet bestaat)\par
adres = persoon.get("adres")  # None\par
adres = persoon.get("adres", "Onbekend")  # "Onbekend" (standaardwaarde)\par
\par
# Dictionary wijzigen\par
persoon["naam"] = "Piet"  # {"naam": "Piet", "leeftijd": 25}\par
persoon["adres"] = "Amsterdam"  # {"naam": "Piet", "leeftijd": 25, "adres": "Amsterdam"}\par
\par
# Dictionary methoden\par
keys = persoon.keys()    # dict_keys(['naam', 'leeftijd', 'adres'])\par
values = persoon.values()  # dict_values(['Piet', 25, 'Amsterdam'])\par
items = persoon.items()  # dict_items([('naam', 'Piet'), ('leeftijd', 25), ('adres', 'Amsterdam')])\par
\par
verwijderd = persoon.pop("adres")  # verwijderd = "Amsterdam", persoon = {"naam": "Piet", "leeftijd": 25}\par
persoon.update(\{"email": "<EMAIL>", "leeftijd": 26\})  # {"naam": "Piet", "leeftijd": 26, "email": "<EMAIL>"}\par
persoon.clear()  # \{\}\par
\par
# Dictionary comprehension\par
kwadraten = \{x: x**2 for x in range(5)\}  # \{0: 0, 1: 1, 2: 4, 3: 9, 4: 16\}\f0\par

\pard\sa200\sl276\slmult1\b Sets (verzamelingen)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Sets aanmaken (unieke waarden, ongeordend)\par
kleuren = \{"rood", "groen", "blauw", "rood"\}  # {"rood", "groen", "blauw"} (duplicaten worden verwijderd)\par
getallen = \{1, 2, 3, 4, 5\}\par
lege_set = set()  # \{\} maakt een lege dictionary, niet een lege set\par
via_constructor = set([1, 2, 2, 3])  # \{1, 2, 3\}\par
\par
# Set bewerkingen\par
kleuren.add("geel")  # {"rood", "groen", "blauw", "geel"}\par
kleuren.remove("groen")  # {"rood", "blauw", "geel"} (geeft error als element niet bestaat)\par
kleuren.discard("paars")  # geen error als element niet bestaat\par
verwijderd = kleuren.pop()  # verwijdert en retourneert willekeurig element\par
kleuren.clear()  # set() (alle elementen verwijderen)\par
\par
# Set operaties (verzamelingenleer)\par
a = \{1, 2, 3\}\par
b = \{3, 4, 5\}\par
\par
unie = a | b  # \{1, 2, 3, 4, 5\} (elementen in a OF b)\par
doorsnede = a & b  # \{3\} (elementen in a EN b)\par
verschil = a - b  # \{1, 2\} (elementen in a maar NIET in b)\par
sym_verschil = a ^ b  # \{1, 2, 4, 5\} (elementen in a OF b maar NIET in beide)\par
\par
# Testen op lidmaatschap\par
print(2 in a)  # True\par
print(4 in a)  # False\f0\par
}
