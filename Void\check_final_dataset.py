import pandas as pd

# Laad de definitieve dataset
df = pd.read_csv('Kikker_cleaned_final.csv')

# Controleer Cyclustijd
print('=== CYCLUSTIJD ===')
print('Unieke waarden:')
print(sorted(df['Cyclustijd'].unique()))
print()

# Controleer Klanttevredenheid
print('=== KLANTTEVREDENHEID ===')
print('Statistieken:')
print(df['Klanttevredenheid'].describe())
print('Aantal lege waarden:')
print(df['Klanttevredenheid'].isna().sum())
print()

# Controleer FillingID
print('=== FILLINGID ===')
print('Statistieken:')
print(df['FillingID'].describe())
print('Aantal waarden > 1000:')
print((df['FillingID'] > 1000).sum())
print()

# Controleer Duurzaamheid Score
print('=== DUURZAAMHEID SCORE ===')
print('Statistieken:')
print(df['Duurzaamheid Score'].describe())
print('Aantal lege waarden:')
print(df['Duurzaamheid Score'].isna().sum())
print()

# Controleer Gewichtscontrole
print('=== GEWICHTSCONTROLE ===')
print('Statistieken:')
print(df['Gewichtscontrole'].describe())
print('Unieke waarden:')
print(sorted(df['Gewichtscontrole'].unique()))
print()

# Controleer Klantretourpercentage
print('=== KLANTRETOURPERCENTAGE ===')
print('Statistieken:')
print(df['Klantretourpercentage'].describe())
print('Unieke waarden:')
print(sorted(df['Klantretourpercentage'].unique()))
print('Aantal waarden > 100:')
print((df['Klantretourpercentage'] > 100).sum())
