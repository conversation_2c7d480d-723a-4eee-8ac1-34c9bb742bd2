<!DOCTYPE html>
<html>
<head>
    <title>Input-Throughput-Output (ITO) Model</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: white;
        }
        .diagram {
            width: 800px;
            height: 500px;
            margin: 0 auto;
            position: relative;
            border: 1px solid #ccc;
            background-color: white;
        }
        .title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .box {
            position: absolute;
            border: 2px solid black;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        .input {
            width: 150px;
            height: 80px;
            left: 100px;
            top: 200px;
            background-color: #dae8fc;
            border-color: #6c8ebf;
        }
        .throughput {
            width: 150px;
            height: 80px;
            left: 350px;
            top: 200px;
            background-color: #ffe6cc;
            border-color: #d79b00;
        }
        .output {
            width: 150px;
            height: 80px;
            left: 600px;
            top: 200px;
            background-color: #d5e8d4;
            border-color: #82b366;
        }
        .arrow {
            position: absolute;
            height: 2px;
            background-color: black;
        }
        .arrow-right:after {
            content: '';
            position: absolute;
            right: -10px;
            top: -8px;
            width: 0;
            height: 0;
            border-top: 9px solid transparent;
            border-bottom: 9px solid transparent;
            border-left: 10px solid black;
        }
        .arrow-left:after {
            content: '';
            position: absolute;
            left: -10px;
            top: -8px;
            width: 0;
            height: 0;
            border-top: 9px solid transparent;
            border-bottom: 9px solid transparent;
            border-right: 10px solid black;
        }
        .arrow1 {
            width: 100px;
            left: 250px;
            top: 240px;
        }
        .arrow2 {
            width: 100px;
            left: 500px;
            top: 240px;
        }
        .feedback {
            width: 400px;
            left: 200px;
            top: 350px;
            background-color: transparent;
            border: none;
            transform: rotate(180deg);
        }
        .feedback-text {
            position: absolute;
            left: 370px;
            top: 320px;
            font-weight: bold;
        }
        .input-list, .throughput-list, .output-list {
            position: absolute;
            width: 150px;
            text-align: left;
            font-weight: normal;
            font-size: 14px;
        }
        .input-list {
            left: 100px;
            top: 100px;
        }
        .throughput-list {
            left: 350px;
            top: 100px;
        }
        .output-list {
            left: 600px;
            top: 100px;
        }
        ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="title">Input-Throughput-Output (ITO) Model</div>
    <div class="diagram">
        <div class="box input">INPUT</div>
        <div class="box throughput">THROUGHPUT</div>
        <div class="box output">OUTPUT</div>
        
        <div class="arrow arrow1 arrow-right"></div>
        <div class="arrow arrow2 arrow-right"></div>
        
        <div class="arrow feedback arrow-left"></div>
        <div class="feedback-text">Feedback</div>
        
        <div class="input-list">
            <strong>Input omvat:</strong>
            <ul>
                <li>Grondstoffen</li>
                <li>Arbeid</li>
                <li>Informatie</li>
                <li>Kapitaal</li>
                <li>Energie</li>
            </ul>
        </div>
        
        <div class="throughput-list">
            <strong>Transformatieproces:</strong>
            <ul>
                <li>Productie</li>
                <li>Dienstverlening</li>
                <li>Informatieverwerking</li>
                <li>Waardecreatie</li>
            </ul>
        </div>
        
        <div class="output-list">
            <strong>Output omvat:</strong>
            <ul>
                <li>Producten</li>
                <li>Diensten</li>
                <li>Informatie</li>
                <li>Afval/Emissies</li>
            </ul>
        </div>
    </div>
</body>
</html>
