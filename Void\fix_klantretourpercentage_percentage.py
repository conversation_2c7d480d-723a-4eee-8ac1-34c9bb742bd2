import pandas as pd

# Laad de dataset
df = pd.read_csv('Ki<PERSON>_cleaned_fixed.csv')

# Maak een kopie van de originele dataset voor vergelijking
df.to_csv('Klantretourpercentage_percentage_before.csv', index=False)
print("Originele dataset opgeslagen als 'Klantretourpercentage_percentage_before.csv'")

# Controleer de huidige waarden van Klantretourpercentage
print("\nHuidige unieke waarden in Klantretourpercentage:")
print(sorted(df['Klantretourpercentage'].unique()))

# Converteer de waarden naar percentages zonder decimalen
# Vermenigvuldig met 100 en rond af naar gehele getallen
df['Klantretourpercentage'] = (df['Klantretourpercentage'] * 100).round().astype(int)

# Controleer de nieuwe waarden van Klantretourpercentage
print("\nNieuwe unieke waarden in Klantretourpercentage:")
print(sorted(df['Klantretourpercentage'].unique()))

# Sla de aangepaste dataset op
df.to_csv('Kikker_cleaned_percentage.csv', index=False)
print("\nAangepaste dataset opgeslagen als 'Kikker_cleaned_percentage.csv'")

# Maak een kopie voor vergelijking
df.to_csv('Klantretourpercentage_percentage_after.csv', index=False)
print("Aangepaste dataset opgeslagen als 'Klantretourpercentage_percentage_after.csv'")

# Toon statistieken
print("\nStatistieken van de aangepaste Klantretourpercentage-kolom:")
print(df['Klantretourpercentage'].describe())
