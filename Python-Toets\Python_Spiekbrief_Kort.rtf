{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Courier New;}}
{\colortbl ;\red0\green0\blue255;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 PYTHON SPIEKBRIEF - PAGINA 1\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 1. VARIABELEN & DATATYPES\b0\fs22\par
\pard\li360\sa200\sl276\slmult1 - \b int\b0: x = 5\par
- \b float\b0: y = 3.14\par
- \b str\b0: naam = "Python"\par
- \b bool\b0: waar = True\par
- \b list\b0: lijst = [1, 2, 3]\par
- \b tuple\b0: t = (1, 2, 3)\par
- \b dict\b0: d = \{"naam": "Jan"\}\par
- \b set\b0: s = \{1, 2, 3\}\par

\pard\sa200\sl276\slmult1\b\fs24 2. OPERATOREN\b0\fs22\par
\pard\li360\sa200\sl276\slmult1 - \b Rekenkundig\b0: +, -, *, /, // (integer deling), % (modulo), ** (macht)\par
- \b Vergelijking\b0: ==, !=, <, >, <=, >=\par
- \b Logisch\b0: and, or, not\par
- \b Toewijzing\b0: =, +=, -=, *=, /=\par

\pard\sa200\sl276\slmult1\b\fs24 3. STRINGS\b0\fs22\par
\pard\li360\sa200\sl276\slmult1 - \b Aanmaken\b0: s = "tekst"\par
- \b Indexering\b0: s[0], s[-1]\par
- \b Slicing\b0: s[1:4]\par
- \b Methoden\b0: upper(), lower(), strip(), replace(), split(), join()\par
- \b Formatteren\b0: f"Naam: \{naam\}"\par

\pard\sa200\sl276\slmult1\b\fs24 4. LIJSTEN\b0\fs22\par
\pard\li360\sa200\sl276\slmult1 - \b Aanmaken\b0: lijst = [1, 2, 3]\par
- \b Toevoegen\b0: append(), insert(), extend()\par
- \b Verwijderen\b0: remove(), pop(), clear()\par
- \b Sorteren\b0: sort(), reverse()\par
- \b Comprehension\b0: [x*2 for x in range(5)]\par

\pard\sa200\sl276\slmult1\b\fs24 5. DICTIONARIES\b0\fs22\par
\pard\li360\sa200\sl276\slmult1 - \b Aanmaken\b0: d = \{"key": "value"\}\par
- \b Toegang\b0: d["key"], d.get("key")\par
- \b Methoden\b0: keys(), values(), items(), update(), pop()\par

\pard\sa200\sl276\slmult1\b\fs24 6. CONTROLESTRUCTUREN\b0\fs22\par
\pard\li360\sa200\sl276\slmult1 - \b if-elif-else\b0:\par
\pard\li720\sa200\sl276\slmult1\f1 if x > 0:\par
    print("positief")\par
elif x < 0:\par
    print("negatief")\par
else:\par
    print("nul")\f0\par

\pard\li360\sa200\sl276\slmult1 - \b for-lus\b0:\par
\pard\li720\sa200\sl276\slmult1\f1 for i in range(5):\par
    print(i)\f0\par

\pard\li360\sa200\sl276\slmult1 - \b while-lus\b0:\par
\pard\li720\sa200\sl276\slmult1\f1 while x > 0:\par
    x -= 1\f0\par

\pard\li360\sa200\sl276\slmult1 - \b break\b0, \b continue\b0, \b pass\b0\par

\pard\sa200\sl276\slmult1\qc\b\fs28 PYTHON SPIEKBRIEF - PAGINA 2\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 7. FUNCTIES\b0\fs22\par
\pard\li360\sa200\sl276\slmult1 - \b Definitie\b0:\par
\pard\li720\sa200\sl276\slmult1\f1 def groet(naam):\par
    return f"Hallo \{naam\}"\f0\par

\pard\li360\sa200\sl276\slmult1 - \b Default parameters\b0:\par
\pard\li720\sa200\sl276\slmult1\f1 def groet(naam="wereld"):\par
    return f"Hallo \{naam\}"\f0\par

\pard\li360\sa200\sl276\slmult1 - \b *args, **kwargs\b0:\par
\pard\li720\sa200\sl276\slmult1\f1 def som(*getallen, **opties):\par
    return sum(getallen)\f0\par

\pard\li360\sa200\sl276\slmult1 - \b Lambda\b0:\par
\pard\li720\sa200\sl276\slmult1\f1 f = lambda x, y: x + y\f0\par

\pard\sa200\sl276\slmult1\b\fs24 8. MODULES\b0\fs22\par
\pard\li360\sa200\sl276\slmult1 - \b Import\b0:\par
\pard\li720\sa200\sl276\slmult1\f1 import math\par
from random import randint\par
import os as besturingssysteem\f0\par

\pard\li360\sa200\sl276\slmult1 - \b Standaard modules\b0: math, random, datetime, os, sys, json, re\par

\pard\sa200\sl276\slmult1\b\fs24 9. BESTANDSVERWERKING\b0\fs22\par
\pard\li360\sa200\sl276\slmult1 - \b Context manager\b0:\par
\pard\li720\sa200\sl276\slmult1\f1 with open('bestand.txt', 'r') as f:\par
    inhoud = f.read()\f0\par

\pard\li360\sa200\sl276\slmult1 - \b Modes\b0: 'r' (lezen), 'w' (schrijven), 'a' (toevoegen)\par
- \b Methoden\b0: read(), readline(), readlines(), write(), writelines()\par

\pard\sa200\sl276\slmult1\b\fs24 10. EXCEPTIONS\b0\fs22\par
\pard\li360\sa200\sl276\slmult1 - \b Try-except\b0:\par
\pard\li720\sa200\sl276\slmult1\f1 try:\par
    x = int(input())\par
except ValueError:\par
    print("Geen geldig getal")\f0\par

\pard\li360\sa200\sl276\slmult1 - \b Veelvoorkomende exceptions\b0: ValueError, TypeError, IndexError, KeyError\par

\pard\sa200\sl276\slmult1\b\fs24 11. OBJECT-ORIENTED PROGRAMMING\b0\fs22\par
\pard\li360\sa200\sl276\slmult1 - \b Klasse definitie\b0:\par
\pard\li720\sa200\sl276\slmult1\f1 class Persoon:\par
    def __init__(self, naam):\par
        self.naam = naam\par
    \par
    def groet(self):\par
        return f"Hallo, ik ben \{self.naam\}"\f0\par

\pard\li360\sa200\sl276\slmult1 - \b Instantie aanmaken\b0:\par
\pard\li720\sa200\sl276\slmult1\f1 p = Persoon("Jan")\par
print(p.groet())\f0\par

\pard\li360\sa200\sl276\slmult1 - \b Overerving\b0:\par
\pard\li720\sa200\sl276\slmult1\f1 class Student(Persoon):\par
    def __init__(self, naam, studie):\par
        super().__init__(naam)\par
        self.studie = studie\f0\par

\pard\sa200\sl276\slmult1\b\fs24 12. HANDIGE BUILT-IN FUNCTIES\b0\fs22\par
\pard\li360\sa200\sl276\slmult1 - print(), input(), len(), range(), type()\par
- int(), float(), str(), bool(), list(), dict()\par
- min(), max(), sum(), sorted(), enumerate(), zip()\par
- filter(), map(), any(), all(), abs(), round()\par

\pard\sa200\sl276\slmult1\b\fs24 13. HANDIGE TIPS\b0\fs22\par
\pard\li360\sa200\sl276\slmult1 - Gebruik f-strings voor formattering\par
- Gebruik list comprehensions voor beknopte code\par
- Gebruik enumerate() in plaats van range(len())\par
- Gebruik in voor membership tests\par
- Gebruik is voor identity tests\par
- Gebruik get() voor dictionaries om KeyError te voorkomen\par
}
