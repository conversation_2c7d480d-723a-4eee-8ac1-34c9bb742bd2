CONTROLEKAART (TEKSTVERSIE)
===========================

Waarde |
       |                                UCL (Bovenste Controlelimiet)
  30   |  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
       |
       |
       |                    *
  25   |                   / \
       |                  /   \         *
       |                 /     \       / \
       |                /       \     /   \
  20   |     *         /         \   /     \         *
       |    / \       /           \ /       \       / \
       |   /   \     /             *         \     /   \
       |  /     \   /                         \   /     \
  15   | /       \ /                           \ /       \
       |*         *                             *         *
       |
       |                                                    Gemiddelde
  10   |  -------------------------------------------------
       |
       |
       |
   5   |
       |
       |
       |                                LCL (Onderste Controlelimiet)
   0   |  - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
       +-----------------------------------------------------------
           1    <USER>    <GROUP>    4    5    6    7    8    9    10   11   12
                                  Meting

Uitleg:
- De controlekaart toont de variatie in een proces over tijd
- De middelste lijn is het gemiddelde van de metingen
- UCL (Upper Control Limit): Bovenste controlelimiet, typisch 3 standaarddeviaties boven het gemiddelde
- LCL (Lower Control Limit): Onderste controlelimiet, typisch 3 standaarddeviaties onder het gemiddelde
- Punten binnen de controlelimieten wijzen op normale procesvariatie (common cause variation)
- Punten buiten de controlelimieten wijzen op speciale oorzaken van variatie (special cause variation)
- Patronen in de data (zoals trends of cycli) kunnen ook wijzen op speciale oorzaken, zelfs als alle punten binnen de limieten vallen
