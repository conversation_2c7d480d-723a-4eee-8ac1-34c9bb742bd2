<!DOCTYPE html>
<html>
<head>
    <title>Fysiek ERD</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .diagram {
            width: 800px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: white;
            padding: 20px;
        }
        .entity {
            border: 2px solid #333;
            background-color: #f8f8f8;
            border-radius: 5px;
            text-align: center;
            margin: 10px;
            display: inline-block;
            vertical-align: top;
        }
        .entity-header {
            background-color: #333;
            color: white;
            padding: 8px;
            font-weight: bold;
        }
        .entity-body {
            padding: 10px;
        }
        .attribute {
            text-align: left;
            margin: 5px;
            padding: 3px;
        }
        .pk {
            font-weight: bold;
            text-decoration: underline;
        }
        .fk {
            font-style: italic;
            color: #555;
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .erd-type {
            margin: 30px 0;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .erd-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }
        .arrow {
            display: inline-block;
            margin: 0 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="title">Fysiek ERD</div>
    <div class="subtitle">Laagste abstractieniveau, bevat alle technische details voor implementatie</div>
    
    <div class="diagram">
        <div class="erd-type">
            <div class="erd-title">Fysiek ERD</div>
            <p>Laagste abstractieniveau, bevat alle technische details voor implementatie.</p>
            
            <div style="text-align: center;">
                <div class="entity" style="width: 220px;">
                    <div class="entity-header">Klant</div>
                    <div class="entity-body">
                        <div class="attribute pk">KlantID (INT, PK)</div>
                        <div class="attribute">Naam (VARCHAR(100), NOT NULL)</div>
                        <div class="attribute">Adres (VARCHAR(200))</div>
                        <div class="attribute">Telefoon (VARCHAR(20))</div>
                        <div class="attribute">Email (VARCHAR(100), UNIQUE)</div>
                    </div>
                </div>
                
                <div class="arrow">FK</div>
                
                <div class="entity" style="width: 220px;">
                    <div class="entity-header">Order</div>
                    <div class="entity-body">
                        <div class="attribute pk">OrderID (INT, PK)</div>
                        <div class="attribute fk">KlantID (INT, FK)</div>
                        <div class="attribute">Datum (DATE, NOT NULL)</div>
                        <div class="attribute">Totaal (DECIMAL(10,2))</div>
                        <div class="attribute">Status (VARCHAR(20))</div>
                    </div>
                </div>
                
                <div class="arrow">FK</div>
                
                <div class="entity" style="width: 220px;">
                    <div class="entity-header">OrderDetail</div>
                    <div class="entity-body">
                        <div class="attribute pk fk">OrderID (INT, PK, FK)</div>
                        <div class="attribute pk fk">ProductID (INT, PK, FK)</div>
                        <div class="attribute">Aantal (INT, NOT NULL)</div>
                        <div class="attribute">Prijs (DECIMAL(10,2), NOT NULL)</div>
                    </div>
                </div>
                
                <div class="arrow">FK</div>
                
                <div class="entity" style="width: 220px;">
                    <div class="entity-header">Product</div>
                    <div class="entity-body">
                        <div class="attribute pk">ProductID (INT, PK)</div>
                        <div class="attribute">Naam (VARCHAR(100), NOT NULL)</div>
                        <div class="attribute">Beschrijving (TEXT)</div>
                        <div class="attribute">Prijs (DECIMAL(10,2), NOT NULL)</div>
                        <div class="attribute">Voorraad (INT, DEFAULT 0)</div>
                    </div>
                </div>
            </div>
            
            <p>Kenmerken:</p>
            <ul>
                <li>Bevat alle technische details voor implementatie</li>
                <li>Specificeert datatypen, constraints en defaults</li>
                <li>Toont vreemde sleutels (FK) om relaties te implementeren</li>
                <li>Kan indexen en andere databasespecifieke elementen bevatten</li>
                <li>Klaar voor directe implementatie in een specifiek DBMS</li>
            </ul>
            
            <p>Doel:</p>
            <ul>
                <li>Het specificeren van alle technische details voor implementatie</li>
                <li>Het definiëren van datatypen, constraints en defaults</li>
                <li>Het implementeren van relaties via vreemde sleutels</li>
                <li>Basis voor het genereren van SQL-scripts voor databasecreatie</li>
            </ul>
        </div>
    </div>
</body>
</html>
