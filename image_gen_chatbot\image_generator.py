import os
import requests
import base64
import json
from io import BytesIO
from PIL import Image
import time
import random
import string

# Check which API keys are available
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
STABILITY_API_KEY = os.getenv("STABILITY_API_KEY")
REPLICATE_API_KEY = os.getenv("REPLICATE_API_KEY")

def generate_random_filename():
    """Generate a random filename for the saved image."""
    letters = string.ascii_lowercase
    random_string = ''.join(random.choice(letters) for i in range(10))
    return f"static/images/generated_{random_string}.png"

def ensure_directories():
    """Ensure the necessary directories exist."""
    os.makedirs("static/images", exist_ok=True)

def generate_image(prompt):
    """Generate an image based on the prompt using available API."""
    ensure_directories()
    
    # Try OpenAI's DALL-E if API key is available
    if OPENAI_API_KEY:
        return generate_with_openai(prompt)
    
    # Try Stability AI if API key is available
    elif STABILITY_API_KEY:
        return generate_with_stability(prompt)
    
    # Try Replicate if API key is available
    elif REPLICATE_API_KEY:
        return generate_with_replicate(prompt)
    
    else:
        raise Exception("No API keys found. Please add at least one image generation API key to the .env file.")

def generate_with_openai(prompt):
    """Generate image using OpenAI's DALL-E API."""
    import openai
    
    openai.api_key = OPENAI_API_KEY
    
    try:
        response = openai.Image.create(
            prompt=prompt,
            n=1,
            size="1024x1024"
        )
        
        # OpenAI returns a URL to the generated image
        image_url = response['data'][0]['url']
        
        # Download the image
        image_response = requests.get(image_url)
        image = Image.open(BytesIO(image_response.content))
        
        # Save the image locally
        filename = generate_random_filename()
        image.save(filename)
        
        return filename
    
    except Exception as e:
        raise Exception(f"OpenAI API error: {str(e)}")

def generate_with_stability(prompt):
    """Generate image using Stability AI's API."""
    url = "https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image"
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "Authorization": f"Bearer {STABILITY_API_KEY}"
    }
    
    body = {
        "text_prompts": [{"text": prompt}],
        "cfg_scale": 7,
        "height": 1024,
        "width": 1024,
        "samples": 1,
        "steps": 30,
    }
    
    try:
        response = requests.post(url, headers=headers, json=body)
        
        if response.status_code != 200:
            raise Exception(f"Non-200 response: {response.text}")
        
        data = response.json()
        
        # Get the base64 string of the image
        image_b64 = data["artifacts"][0]["base64"]
        
        # Convert base64 to image
        image = Image.open(BytesIO(base64.b64decode(image_b64)))
        
        # Save the image locally
        filename = generate_random_filename()
        image.save(filename)
        
        return filename
    
    except Exception as e:
        raise Exception(f"Stability AI API error: {str(e)}")

def generate_with_replicate(prompt):
    """Generate image using Replicate API."""
    url = "https://api.replicate.com/v1/predictions"
    
    headers = {
        "Authorization": f"Token {REPLICATE_API_KEY}",
        "Content-Type": "application/json"
    }
    
    body = {
        "version": "a4a8bafd6089e1716b06057c42b19378250d008b80fe87caa5cd36d40c1eda90",
        "input": {
            "prompt": prompt
        }
    }
    
    try:
        # Start the generation
        response = requests.post(url, headers=headers, json=body)
        response.raise_for_status()
        prediction = response.json()
        
        # Get the prediction ID
        prediction_id = prediction["id"]
        
        # Poll for the result
        while prediction["status"] != "succeeded" and prediction["status"] != "failed":
            time.sleep(1)
            response = requests.get(
                f"https://api.replicate.com/v1/predictions/{prediction_id}",
                headers=headers
            )
            response.raise_for_status()
            prediction = response.json()
        
        if prediction["status"] == "failed":
            raise Exception("Image generation failed")
        
        # Get the image URL
        image_url = prediction["output"][0]
        
        # Download the image
        image_response = requests.get(image_url)
        image = Image.open(BytesIO(image_response.content))
        
        # Save the image locally
        filename = generate_random_filename()
        image.save(filename)
        
        return filename
    
    except Exception as e:
        raise Exception(f"Replicate API error: {str(e)}")
