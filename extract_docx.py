import docx
import sys

def extract_text_from_docx(docx_path):
    try:
        doc = docx.Document(docx_path)
        full_text = []
        for para in doc.paragraphs:
            full_text.append(para.text)
        return '\n'.join(full_text)
    except Exception as e:
        return f"Error extracting text: {str(e)}"

if __name__ == "__main__":
    if len(sys.argv) > 1:
        docx_path = sys.argv[1]
        text = extract_text_from_docx(docx_path)
        print(text)
    else:
        print("Please provide the path to a DOCX file as an argument.")
