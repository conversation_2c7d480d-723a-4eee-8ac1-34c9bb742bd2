#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for data_cleaning.py

This script runs the data cleaning process and displays statistics
about the original and cleaned data for comparison.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from data_cleaning import clean_kikker_data

def compare_datasets(original_file='Kikker.csv', cleaned_file='Kikker_cleaned.csv'):
    """
    Compare the original and cleaned datasets
    
    Parameters:
    -----------
    original_file : str
        Path to the original CSV file
    cleaned_file : str
        Path to the cleaned CSV file
    """
    # Run the cleaning process
    clean_kikker_data(original_file, cleaned_file)
    
    # Load both datasets
    print("\nLoading original and cleaned datasets for comparison...")
    df_original = pd.read_csv(original_file)
    df_cleaned = pd.read_csv(cleaned_file)
    
    # Basic information
    print("\n=== BASIC INFORMATION ===")
    print(f"Original data shape: {df_original.shape}")
    print(f"Cleaned data shape: {df_cleaned.shape}")
    
    # Missing values comparison
    print("\n=== MISSING VALUES COMPARISON ===")
    missing_original = df_original.isnull().sum().sum()
    missing_cleaned = df_cleaned.isnull().sum().sum()
    print(f"Original data missing values: {missing_original}")
    print(f"Cleaned data missing values: {missing_cleaned}")
    print(f"Reduction in missing values: {missing_original - missing_cleaned}")
    
    # Data types comparison
    print("\n=== DATA TYPES COMPARISON ===")
    print("Original data types:")
    print(df_original.dtypes.value_counts())
    print("\nCleaned data types:")
    print(df_cleaned.dtypes.value_counts())
    
    # Check for invalid dates in cleaned data
    print("\n=== DATE VALIDATION ===")
    date_columns = [col for col in df_cleaned.columns if 'Datum' in col or 'Audit' in col]
    invalid_dates = 0
    for col in date_columns:
        try:
            # Convert to datetime and check for NaT
            dates = pd.to_datetime(df_cleaned[col], errors='coerce')
            invalid = dates.isna().sum()
            if invalid > 0:
                print(f"Column {col} still has {invalid} invalid dates")
                invalid_dates += invalid
        except:
            print(f"Could not check dates in {col}")
    
    if invalid_dates == 0:
        print("All dates in cleaned data are valid!")
    
    # Check for negative or unrealistic values in cleaned data
    print("\n=== NUMERIC VALIDATION ===")
    numeric_columns = df_cleaned.select_dtypes(include=[np.number]).columns
    unrealistic_values = 0
    for col in numeric_columns:
        # Check for negative values in columns that shouldn't have them
        if col in ['Cost', 'Voorraadniveaus', 'Cyclustijd', 'Energieverbruik']:
            neg_values = (df_cleaned[col] < 0).sum()
            if neg_values > 0:
                print(f"Column {col} still has {neg_values} negative values")
                unrealistic_values += neg_values
    
    if unrealistic_values == 0:
        print("No unrealistic values found in cleaned numeric data!")
    
    # Check for duplicates in cleaned data
    print("\n=== DUPLICATE VALIDATION ===")
    duplicates = df_cleaned.duplicated().sum()
    print(f"Cleaned data has {duplicates} exact duplicate rows")
    
    if 'Batchnr' in df_cleaned.columns:
        batch_duplicates = df_cleaned['Batchnr'].duplicated().sum()
        print(f"Cleaned data has {batch_duplicates} duplicate Batchnr values")
    
    # Summary statistics for key columns
    print("\n=== SUMMARY STATISTICS FOR KEY COLUMNS ===")
    key_columns = ['Klantretourpercentage', 'Defectpercentage', 'Benuttingsgraad']
    for col in key_columns:
        if col in df_cleaned.columns:
            print(f"\n{col} statistics:")
            print(df_cleaned[col].describe())
    
    print("\nComparison complete!")

if __name__ == "__main__":
    compare_datasets()
