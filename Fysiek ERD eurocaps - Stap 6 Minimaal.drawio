<mxfile host="Electron" modified="2024-10-22T15:50:00.000Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.1.1 Chrome/132.0.6834.210 Electron/34.3.3 Safari/537.36" version="26.1.1">
  <diagram name="Page-1" id="FysiekERD-EuroCaps-Stap6">
    <mxGraphModel dx="1434" dy="436" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Titel -->
        <mxCell id="title" value="EuroCaps - Fysiek ERD (Stap 6: Grinding_Product)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="100" y="20" width="600" height="40" as="geometry" />
        </mxCell>
        
        <!-- Grinding_Product koppeltabel -->
        <mxCell id="table-grinding-product" value="Grinding_Product" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=1;fontStyle=1;align=center;resizeLast=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="300" y="100" width="200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="grinding-product-pk1" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-grinding-product">
          <mxGeometry y="30" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="grinding-product-pk1-key" value="PK/FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" vertex="1" parent="grinding-product-pk1">
          <mxGeometry width="60" height="30" as="geometry">
            <mxRectangle width="60" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="grinding-product-pk1-name" value="GrindingId INT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=1;overflow=hidden;" vertex="1" parent="grinding-product-pk1">
          <mxGeometry x="60" width="140" height="30" as="geometry">
            <mxRectangle width="140" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="grinding-product-pk2" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-grinding-product">
          <mxGeometry y="60" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="grinding-product-pk2-key" value="PK/FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" vertex="1" parent="grinding-product-pk2">
          <mxGeometry width="60" height="30" as="geometry">
            <mxRectangle width="60" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="grinding-product-pk2-name" value="ProductId INT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=1;overflow=hidden;" vertex="1" parent="grinding-product-pk2">
          <mxGeometry x="60" width="140" height="30" as="geometry">
            <mxRectangle width="140" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="grinding-product-aantal" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-grinding-product">
          <mxGeometry y="90" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="grinding-product-aantal-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" vertex="1" parent="grinding-product-aantal">
          <mxGeometry width="60" height="30" as="geometry">
            <mxRectangle width="60" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="grinding-product-aantal-name" value="Aantal INT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" vertex="1" parent="grinding-product-aantal">
          <mxGeometry x="60" width="140" height="30" as="geometry">
            <mxRectangle width="140" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        
        <!-- Toelichting -->
        <mxCell id="toelichting-titel" value="Toelichting" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="300" y="240" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="toelichting-text" value="- Koppeltabel Grinding_Product implementeert de many-to-many relatie tussen Product en Grinding&#xa;- Samengestelde primaire sleutel (GrindingId, ProductId)&#xa;- Beide sleutels zijn ook vreemde sleutels die verwijzen naar de respectievelijke tabellen&#xa;- Aantal veld houdt bij hoeveel producten in een grinding batch zijn verwerkt" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="300" y="270" width="200" height="120" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
