<!DOCTYPE html>
<html>
<head>
    <title>Conceptueel ERD Voorbeeld</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: white;
        }
        .container {
            width: 800px;
            height: 600px;
            border: 1px solid #ccc;
            position: relative;
            background-color: white;
        }
        .title {
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            margin-top: 20px;
        }
        .entity {
            width: 120px;
            height: 60px;
            border: 2px solid black;
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            background-color: white;
        }
        .relationship {
            position: absolute;
            font-size: 14px;
        }
        .cardinality {
            position: absolute;
            font-size: 14px;
        }
        .line {
            position: absolute;
            background-color: black;
        }
        .note {
            position: absolute;
            bottom: 80px;
            left: 20px;
            font-size: 14px;
            width: 760px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">Conceptueel ERD Voorbeeld - Webwinkel</div>
        
        <!-- Entities -->
        <div class="entity" style="top: 120px; left: 150px;">KLANT</div>
        <div class="entity" style="top: 120px; left: 500px;">BESTELLING</div>
        <div class="entity" style="top: 320px; left: 150px;">CATEGORIE</div>
        <div class="entity" style="top: 320px; left: 500px;">PRODUCT</div>
        
        <!-- Lines -->
        <div class="line" style="top: 150px; left: 270px; width: 230px; height: 2px;"></div>
        <div class="line" style="top: 150px; left: 560px; width: 2px; height: 170px;"></div>
        <div class="line" style="top: 350px; left: 270px; width: 230px; height: 2px;"></div>
        
        <!-- Relationships -->
        <div class="relationship" style="top: 130px; left: 350px;">plaatst</div>
        <div class="relationship" style="top: 240px; left: 570px;">bevat</div>
        <div class="relationship" style="top: 330px; left: 350px;">behoort tot</div>
        
        <!-- Cardinalities -->
        <div class="cardinality" style="top: 130px; left: 280px;">1</div>
        <div class="cardinality" style="top: 130px; left: 480px;">N</div>
        <div class="cardinality" style="top: 190px; left: 570px;">N</div>
        <div class="cardinality" style="top: 300px; left: 570px;">M</div>
        <div class="cardinality" style="top: 330px; left: 280px;">N</div>
        <div class="cardinality" style="top: 330px; left: 480px;">1</div>
        
        <!-- Note -->
        <div class="note">
            <strong>Opmerking:</strong> Een conceptueel ERD toont alleen entiteiten en hun relaties, zonder attributen of technische details. Het is de eerste stap in het databaseontwerpproces.
        </div>
    </div>
    
    <div style="margin-top: 20px;">
        <h2>Wat is een conceptueel ERD?</h2>
        <p>Een conceptueel ERD (Entity Relationship Diagram) is de meest abstracte vorm van een Entity Relationship Diagram. Het is een visuele weergave van de belangrijkste entiteiten (objecten of concepten) in een systeem en de relaties tussen deze entiteiten.</p>
        
        <h3>Kenmerken van een conceptueel ERD:</h3>
        <ul>
            <li>Bevat alleen entiteiten en hun relaties</li>
            <li>Bevat <strong>geen attributen</strong> (details van entiteiten)</li>
            <li>Toont de cardinaliteit van relaties (één-op-één, één-op-veel, veel-op-veel)</li>
            <li>Gebruikt eenvoudige notaties die begrijpelijk zijn voor niet-technische personen</li>
            <li>Dient als basis voor het latere logische en fysieke ERD</li>
        </ul>
        
        <h3>Doel van een conceptueel ERD:</h3>
        <ul>
            <li>De belangrijkste entiteiten in een domein identificeren</li>
            <li>De relaties tussen deze entiteiten vastleggen</li>
            <li>Een hoog-niveau overzicht geven van de structuur van het systeem</li>
            <li>Communiceren met stakeholders zonder technische details</li>
        </ul>
    </div>
</body>
</html>
