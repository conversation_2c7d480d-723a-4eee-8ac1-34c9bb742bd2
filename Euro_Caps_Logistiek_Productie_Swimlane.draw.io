<mxfile host="app.diagrams.net" modified="2023-11-10T15:30:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="abc123" version="15.7.3" type="device"><diagram id="prtHgNgQTEPvFCAcTncT" name="Euro Caps Logistiek Productie Proces">
  <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
    <root>
      <mxCell id="0" />
      <mxCell id="1" parent="0" />
      
      <!-- Swimlanes -->
      <mxCell id="2" value="Inbound Logistiek" style="swimlane;html=1;startSize=30;horizontal=0;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="80" width="1080" height="160" as="geometry" />
      </mxCell>
      
      <!-- Inbound Logistiek Processen -->
      <mxCell id="3" value="Inkoop" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
        <mxGeometry x="80" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="4" value="Ontvangst" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
        <mxGeometry x="280" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="5" value="Kwaliteitscontrole" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
        <mxGeometry x="480" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="6" value="Opslag Grondstoffen" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
        <mxGeometry x="680" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <!-- Interne Logistiek Swimlane -->
      <mxCell id="7" value="Interne Logistiek" style="swimlane;html=1;startSize=30;horizontal=0;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="240" width="1080" height="160" as="geometry" />
      </mxCell>
      
      <!-- Interne Logistiek Processen -->
      <mxCell id="8" value="Materiaalvoorziening" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="7">
        <mxGeometry x="80" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="9" value="Maalstations" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="7">
        <mxGeometry x="280" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="10" value="Capsule Vulling" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="7">
        <mxGeometry x="480" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="11" value="Intern Transport" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="7">
        <mxGeometry x="680" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="12" value="Productie-ondersteuning" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="7">
        <mxGeometry x="880" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <!-- Outbound Logistiek Swimlane -->
      <mxCell id="13" value="Outbound Logistiek" style="swimlane;html=1;startSize=30;horizontal=0;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="400" width="1080" height="160" as="geometry" />
      </mxCell>
      
      <!-- Outbound Logistiek Processen -->
      <mxCell id="14" value="Opslag Eindproducten" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="13">
        <mxGeometry x="80" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="15" value="Orderverwerking" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="13">
        <mxGeometry x="280" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="16" value="Orderpicking" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="13">
        <mxGeometry x="480" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="17" value="Verzending" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="13">
        <mxGeometry x="680" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="18" value="Retourbeheer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="13">
        <mxGeometry x="880" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <!-- Informatiestromen Swimlane -->
      <mxCell id="19" value="Informatiestromen" style="swimlane;html=1;startSize=30;horizontal=0;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="560" width="1080" height="160" as="geometry" />
      </mxCell>
      
      <!-- Informatiestromen Processen -->
      <mxCell id="20" value="Klantorders &amp; Forecasts" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="19">
        <mxGeometry x="80" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="21" value="Productieplanning" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="19">
        <mxGeometry x="280" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="22" value="Kwaliteitsgegevens" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="19">
        <mxGeometry x="480" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="23" value="Voorraadgegevens" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="19">
        <mxGeometry x="680" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="24" value="Verzendgegevens" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="19">
        <mxGeometry x="880" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <!-- Titel -->
      <mxCell id="25" value="EURO CAPS LOGISTIEK PRODUCTIE PROCES" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="340" y="30" width="480" height="30" as="geometry" />
      </mxCell>
      
      <!-- Verbindingen Inbound Logistiek -->
      <mxCell id="26" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="4">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <mxCell id="27" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="4" target="5">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <mxCell id="28" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="5" target="6">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Verbinding Inbound naar Interne Logistiek -->
      <mxCell id="29" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="6" target="8">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
          <Array as="points">
            <mxPoint x="740" y="220" />
            <mxPoint x="140" y="220" />
          </Array>
        </mxGeometry>
      </mxCell>
      
      <!-- Verbindingen Interne Logistiek -->
      <mxCell id="30" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="8" target="9">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <mxCell id="31" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="9" target="10">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <mxCell id="32" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="10" target="11">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <mxCell id="33" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="11" target="12">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Verbinding Interne naar Outbound Logistiek -->
      <mxCell id="34" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="10" target="14">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
          <Array as="points">
            <mxPoint x="540" y="380" />
            <mxPoint x="140" y="380" />
          </Array>
        </mxGeometry>
      </mxCell>
      
      <!-- Verbindingen Outbound Logistiek -->
      <mxCell id="35" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="14" target="15">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <mxCell id="36" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="15" target="16">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <mxCell id="37" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="16" target="17">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Retouren -->
      <mxCell id="38" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="17" target="18">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
          <Array as="points">
            <mxPoint x="740" y="420" />
            <mxPoint x="940" y="420" />
          </Array>
        </mxGeometry>
      </mxCell>
      
      <!-- Informatiestromen Verbindingen -->
      <mxCell id="39" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="20" target="3">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
          <Array as="points">
            <mxPoint x="140" y="540" />
            <mxPoint x="140" y="540" />
          </Array>
        </mxGeometry>
      </mxCell>
      
      <mxCell id="40" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="21" target="9">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
          <Array as="points">
            <mxPoint x="340" y="520" />
            <mxPoint x="340" y="520" />
          </Array>
        </mxGeometry>
      </mxCell>
      
      <mxCell id="41" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="22" target="5">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
          <Array as="points">
            <mxPoint x="540" y="540" />
            <mxPoint x="540" y="260" />
          </Array>
        </mxGeometry>
      </mxCell>
      
      <mxCell id="42" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="23" target="8">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
          <Array as="points">
            <mxPoint x="740" y="540" />
            <mxPoint x="140" y="540" />
          </Array>
        </mxGeometry>
      </mxCell>
      
      <mxCell id="43" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="24" target="17">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
          <Array as="points">
            <mxPoint x="940" y="540" />
            <mxPoint x="740" y="540" />
          </Array>
        </mxGeometry>
      </mxCell>
      
      <!-- Legenda -->
      <mxCell id="44" value="Legenda:" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="740" width="60" height="20" as="geometry" />
      </mxCell>
      
      <mxCell id="45" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="40" y="780" as="sourcePoint" />
          <mxPoint x="100" y="780" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <mxCell id="46" value="Fysieke stroom" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
        <mxGeometry x="110" y="770" width="100" height="20" as="geometry" />
      </mxCell>
      
      <mxCell id="47" value="" style="endArrow=classic;html=1;dashed=1;strokeColor=#0000FF;" edge="1" parent="1">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="40" y="810" as="sourcePoint" />
          <mxPoint x="100" y="810" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <mxCell id="48" value="Informatiestroom" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
        <mxGeometry x="110" y="800" width="100" height="20" as="geometry" />
      </mxCell>
      
    </root>
  </mxGraphModel>
</diagram></mxfile>
