# Login Screen - EuroCaps Ordering System

## Screen Layout

```
+---------------------------------------------------------------+
|                                                               |
|                      [EuroCaps Logo]                          |
|                                                               |
|                  Coffee Capsule Ordering System               |
|                                                               |
|   +---------------------------------------------------+       |
|   |                                                   |       |
|   |  Username: [                                   ]  |       |
|   |                                                   |       |
|   |  Password: [                                   ]  |       |
|   |                                                   |       |
|   |  [✓] Remember me                                  |       |
|   |                                                   |       |
|   |  [       LOGIN       ]    [Forgot Password?]     |       |
|   |                                                   |       |
|   +---------------------------------------------------+       |
|                                                               |
|                                                               |
|                                                               |
|                       © 2025 EuroCaps                         |
|                                                               |
+---------------------------------------------------------------+
```

## Design Elements

### Colors
- Background: Light gray (#f5f5f5)
- Login panel: White (#ffffff)
- Button: Blue (#4a6fa5)
- Text: Dark gray (#333333)
- Logo: Company colors

### Typography
- Headings: Arial, 18pt, Bold
- Input labels: Arial, 12pt
- Button text: Arial, 14pt, Bold, White
- Footer text: Arial, 10pt

### Components
1. **Header Section**
   - EuroCaps logo (centered, 150x150px)
   - Application title: "Coffee Capsule Ordering System"

2. **Login Panel**
   - White background with subtle shadow
   - Rounded corners (8px radius)
   - 20px padding

3. **Input Fields**
   - Username field (required)
   - Password field (masked, required)
   - Remember me checkbox
   - Forgot password link (smaller text, right-aligned)

4. **Login Button**
   - Blue background (#4a6fa5)
   - White text
   - Full width of the form
   - 12px padding
   - Rounded corners (4px radius)
   - Hover effect: slightly darker blue

5. **Footer**
   - Copyright text
   - Version information

## Interactions

1. **Field Validation**
   - Both username and password are required
   - Visual indication for invalid fields (red outline)
   - Error message appears if fields are empty on submission

2. **Login Button**
   - On click: Validate credentials
   - If valid: Navigate to Dashboard screen
   - If invalid: Show error message "Invalid username or password"
   - Loading indicator while authenticating

3. **Remember Me**
   - When checked: Store username in local storage
   - Pre-fill username on next visit

4. **Forgot Password**
   - On click: Navigate to password reset screen or show dialog

## Accessibility Considerations
- All form fields have proper labels
- Tab order follows logical sequence
- Error messages are clear and descriptive
- Sufficient color contrast for text readability

## Notes for Implementation
- This is the entry point to the application
- No navigation menu is visible until logged in
- Consider adding company branding elements
- For prototype: Use mock authentication with predefined credentials
