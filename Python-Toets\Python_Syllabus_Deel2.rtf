{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Courier New;}}
{\colortbl ;\red0\green0\blue255;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 PYTHON SYLLABUS UITLEG - DEEL 2\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 4. CONTROLESTRUCTUREN\b0\fs22\par

\pard\sa200\sl276\slmult1\b Conditionele statements (if-elif-else)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Basis if-statement\par
x = 10\par
if x > 0:\par
    print("x is positief")\par
\par
# if-else\par
x = -5\par
if x > 0:\par
    print("x is positief")\par
else:\par
    print("x is niet positief")\par
\par
# if-elif-else (me<PERSON><PERSON> voorwaarden)\par
x = 0\par
if x > 0:\par
    print("x is positief")\par
elif x < 0:\par
    print("x is negatief")\par
else:\par
    print("x is nul")\par
\par
# Geneste if-statements\par
x = 10\par
y = 5\par
if x > 0:\par
    if y > 0:\par
        print("Beide zijn positief")\par
    else:\par
        print("Alleen x is positief")\par
\par
# Meerdere voorwaarden combineren\par
leeftijd = 25\par
heeft_id = True\par
\par
if leeftijd >= 18 and heeft_id:\par
    print("Toegang toegestaan")\par
else:\par
    print("Toegang geweigerd")\par
\par
# Voorwaardelijke expressie (ternary operator)\par
x = 10\par
resultaat = "positief" if x > 0 else "niet positief"\par
print(resultaat)  # "positief"\f0\par

\pard\sa200\sl276\slmult1\b For-lus\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Itereren over een reeks getallen\par
for i in range(5):  # 0, 1, 2, 3, 4\par
    print(i)\par
\par
# range() met start, stop, stap\par
for i in range(2, 10, 2):  # 2, 4, 6, 8\par
    print(i)\par
\par
# Itereren over een lijst\par
kleuren = ["rood", "groen", "blauw"]\par
for kleur in kleuren:\par
    print(kleur)\par
\par
# Itereren over een string\par
for letter in "Python":\par
    print(letter)\par
\par
# Itereren over een dictionary\par
persoon = \{"naam": "Jan", "leeftijd": 25\}\par
\par
# Itereren over keys (standaard)\par
for key in persoon:\par
    print(key, persoon[key])\par
\par
# Itereren over key-value pairs\par
for key, value in persoon.items():\par
    print(key, value)\par
\par
# Itereren met index (enumerate)\par
kleuren = ["rood", "groen", "blauw"]\par
for index, kleur in enumerate(kleuren):\par
    print(index, kleur)  # 0 rood, 1 groen, 2 blauw\par
\par
# Itereren over meerdere lijsten tegelijk (zip)\par
namen = ["Alice", "Bob", "Charlie"]\par
scores = [85, 92, 78]\par
for naam, score in zip(namen, scores):\par
    print(naam, score)  # Alice 85, Bob 92, Charlie 78\par
\par
# Geneste lussen\par
for i in range(3):\par
    for j in range(2):\par
        print(i, j)\par
# 0 0\par
# 0 1\par
# 1 0\par
# 1 1\par
# 2 0\par
# 2 1\par
\par
# List comprehension als alternatief voor for-lus\par
kwadraten = [x**2 for x in range(5)]\par
print(kwadraten)  # [0, 1, 4, 9, 16]\f0\par

\pard\sa200\sl276\slmult1\b While-lus\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Basis while-lus\par
i = 0\par
while i < 5:\par
    print(i)  # 0, 1, 2, 3, 4\par
    i += 1\par
\par
# while met else (uitgevoerd als de voorwaarde False wordt)\par
i = 0\par
while i < 5:\par
    print(i)\par
    i += 1\par
else:\par
    print("Lus voltooid")\par
\par
# Oneindige lus met break\par
i = 0\par
while True:\par
    print(i)\par
    i += 1\par
    if i >= 5:\par
        break  # Verlaat de lus\par
\par
# Voorbeeld: gebruikersinvoer valideren\par
while True:\par
    invoer = input("Voer een positief getal in: ")\par
    if invoer.isdigit() and int(invoer) > 0:\par
        print(f"Je hebt \{invoer\} ingevoerd")\par
        break\par
    print("Ongeldige invoer, probeer opnieuw")\f0\par

\pard\sa200\sl276\slmult1\b Break, continue en pass\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # break: verlaat de lus\par
for i in range(10):\par
    if i == 5:\par
        break  # Stopt bij i=5\par
    print(i)  # 0, 1, 2, 3, 4\par
\par
# continue: gaat naar de volgende iteratie\par
for i in range(10):\par
    if i % 2 == 0:  # Als i even is\par
        continue  # Slaat de rest van de code over voor deze iteratie\par
    print(i)  # 1, 3, 5, 7, 9\par
\par
# pass: doet niets (placeholder)\par
for i in range(5):\par
    if i == 2:\par
        pass  # Placeholder voor toekomstige code\par
    print(i)  # 0, 1, 2, 3, 4\par
\par
# Voorbeeld: zoeken in een lijst\par
getallen = [1, 3, 5, 7, 9, 2, 4, 6, 8]\par
\par
# Zoek het eerste even getal\par
for getal in getallen:\par
    if getal % 2 == 0:\par
        print(f"Eerste even getal gevonden: \{getal\}")\par
        break\par
else:  # Wordt uitgevoerd als de lus normaal eindigt (geen break)\par
    print("Geen even getallen gevonden")\f0\par

\pard\sa200\sl276\slmult1\b\fs24 5. FUNCTIES\b0\fs22\par

\pard\sa200\sl276\slmult1\b Functie definitie en aanroep\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Basis functie definitie\par
def groet():\par
    print("Hallo, wereld!")\par
\par
# Functie aanroepen\par
groet()  # Hallo, wereld!\par
\par
# Functie met parameters\par
def groet_persoon(naam):\par
    print(f"Hallo, \{naam\}!")\par
\par
groet_persoon("Alice")  # Hallo, Alice!\par
\par
# Functie met return waarde\par
def som(a, b):\par
    return a + b\par
\par
resultaat = som(5, 3)  # 8\par
print(resultaat)\par
\par
# Functie met meerdere return waarden\par
def bereken(a, b):\par
    som = a + b\par
    verschil = a - b\par
    product = a * b\par
    return som, verschil, product\par
\par
s, v, p = bereken(5, 3)  # s=8, v=2, p=15\par
\par
# Functie documentatie (docstring)\par
def kwadrateer(x):\par
    """Berekent het kwadraat van een getal.\par
    \par
    Args:\par
        x: Het getal om te kwadrateren\par
        \par
    Returns:\par
        Het kwadraat van x\par
    """\par
    return x ** 2\par
\par
# Help over de functie bekijken\par
help(kwadrateer)\par
\par
# Functie als object\par
def zeg_hallo():\par
    return "Hallo"\par
\par
def zeg_doei():\par
    return "Doei"\par
\par
# Functie toewijzen aan variabele\par
mijn_functie = zeg_hallo\par
print(mijn_functie())  # "Hallo"\par
\par
# Functie doorgeven als argument\par
def voer_uit(f):\par
    return f()\par
\par
print(voer_uit(zeg_hallo))  # "Hallo"\par
print(voer_uit(zeg_doei))   # "Doei"\f0\par

\pard\sa200\sl276\slmult1\b Parameters en argumenten\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Verplichte parameters\par
def groet(naam, bericht):\par
    print(f"\{bericht\}, \{naam\}!")\par
\par
groet("Alice", "Hallo")  # Hallo, Alice!\par
\par
# Standaardwaarden (default parameters)\par
def groet(naam, bericht="Hallo"):\par
    print(f"\{bericht\}, \{naam\}!")\par
\par
groet("Alice")  # Hallo, Alice!\par
groet("Bob", "Goedemorgen")  # Goedemorgen, Bob!\par
\par
# Positionele argumenten\par
def beschrijf_huisdier(soort, naam):\par
    print(f"\{naam\} is een \{soort\}")\par
\par
beschrijf_huisdier("kat", "Whiskers")  # Whiskers is een kat\par
\par
# Keyword argumenten\par
beschrijf_huisdier(naam="Rex", soort="hond")  # Rex is een hond\par
\par
# Combinatie van positioneel en keyword\par
beschrijf_huisdier("hamster", naam="Fluffy")  # Fluffy is een hamster\par
\par
# Alleen keyword argumenten (na *)\par
def groet(naam, *, bericht="Hallo", emoji=""):\par
    print(f"\{bericht\}, \{naam\}!\{emoji\}")\par
\par
groet("Alice", bericht="Hoi", emoji=" :)")\par
# groet("Alice", "Hoi")  # TypeError: onverwacht positioneel argument\par
\par
# Alleen positionele argumenten (voor /)\par
def power(base, exponent, /):\par
    return base ** exponent\par
\par
print(power(2, 3))  # 8\par
# print(power(base=2, exponent=3))  # TypeError: onverwacht keyword argument\f0\par

\pard\sa200\sl276\slmult1\b Variabel aantal argumenten\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # *args: variabel aantal positionele argumenten (tuple)\par
def som(*getallen):\par
    resultaat = 0\par
    for getal in getallen:\par
        resultaat += getal\par
    return resultaat\par
\par
print(som(1, 2))  # 3\par
print(som(1, 2, 3, 4, 5))  # 15\par
\par
# **kwargs: variabel aantal keyword argumenten (dictionary)\par
def beschrijf_persoon(**eigenschappen):\par
    for key, value in eigenschappen.items():\par
        print(f"\{key\}: \{value\}")\par
\par
beschrijf_persoon(naam="Alice", leeftijd=30, baan="Ontwikkelaar")\par
# naam: Alice\par
# leeftijd: 30\par
# baan: Ontwikkelaar\par
\par
# Combinatie van normale parameters, *args en **kwargs\par
def voorbeeld(a, b, *args, **kwargs):\par
    print(f"a = \{a\}, b = \{b\}")\par
    print(f"args = \{args\}")\par
    print(f"kwargs = \{kwargs\}")\par
\par
voorbeeld(1, 2, 3, 4, 5, x=10, y=20)\par
# a = 1, b = 2\par
# args = (3, 4, 5)\par
# kwargs = \{'x': 10, 'y': 20\}\par
\par
# Lijst/dictionary uitpakken als argumenten\par
def som(a, b, c):\par
    return a + b + c\par
\par
getallen = [1, 2, 3]\par
print(som(*getallen))  # 6 (uitpakken van lijst)\par
\par
params = \{"a": 1, "b": 2, "c": 3\}\par
print(som(**params))  # 6 (uitpakken van dictionary)\f0\par

\pard\sa200\sl276\slmult1\b Lambda functies\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Lambda functie (anonieme functie)\par
som = lambda a, b: a + b\par
print(som(5, 3))  # 8\par
\par
# Lambda met meerdere parameters\par
berekening = lambda x, y, z: x * y + z\par
print(berekening(2, 3, 4))  # 10\par
\par
# Lambda in combinatie met andere functies\par
getallen = [1, 5, 2, 8, 3]\par
\par
# Sorteren met een aangepaste sleutel\par
gesorteerd = sorted(getallen, key=lambda x: abs(x - 5))\par
print(gesorteerd)  # [5, 3, 8, 2, 1] (gesorteerd op afstand tot 5)\par
\par
# Lambda met filter\par
even = list(filter(lambda x: x % 2 == 0, getallen))\par
print(even)  # [2, 8]\par
\par
# Lambda met map\par
kwadraten = list(map(lambda x: x**2, getallen))\par
print(kwadraten)  # [1, 25, 4, 64, 9]\par
\par
# Wanneer lambda gebruiken vs. gewone functies\par
# - Lambda: voor eenvoudige, eenmalige functies\par
# - Gewone functies: voor complexere logica, hergebruik, documentatie\f0\par

\pard\sa200\sl276\slmult1\b Scope en levensduur van variabelen\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Lokale en globale variabelen\par
x = 10  # Globale variabele\par
\par
def functie():\par
    y = 5  # Lokale variabele\par
    print(x)  # Toegang tot globale variabele\par
    print(y)  # Toegang tot lokale variabele\par
\par
functie()  # 10, 5\par
# print(y)  # NameError: y is niet gedefinieerd buiten de functie\par
\par
# Wijzigen van globale variabelen\par
x = 10\par
\par
def wijzig_x():\par
    global x  # Geeft aan dat we de globale x willen wijzigen\par
    x = 20\par
\par
print(x)  # 10\par
wijzig_x()\par
print(x)  # 20\par
\par
# Geneste functies en nonlocal\par
def outer():\par
    x = 10\par
    \par
    def inner():\par
        nonlocal x  # Geeft aan dat we de x van de omsluitende functie willen wijzigen\par
        x = 20\par
    \par
    print(x)  # 10\par
    inner()\par
    print(x)  # 20\par
\par
outer()\par
\par
# LEGB regel voor scope\par
# L - Local (lokaal): variabelen in de huidige functie\par
# E - Enclosing (omsluitend): variabelen in omsluitende functies\par
# G - Global (globaal): variabelen op het hoogste niveau van het script\par
# B - Built-in (ingebouwd): ingebouwde functies en variabelen (print, len, etc.)\f0\par

\pard\sa200\sl276\slmult1\b Recursie\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Recursie: een functie die zichzelf aanroept\par
def faculteit(n):\par
    if n <= 1:  # Basisgeval\par
        return 1\par
    else:  # Recursief geval\par
        return n * faculteit(n - 1)\par
\par
print(faculteit(5))  # 5 * 4 * 3 * 2 * 1 = 120\par
\par
# Fibonacci reeks met recursie\par
def fibonacci(n):\par
    if n <= 0:\par
        return 0\par
    elif n == 1:\par
        return 1\par
    else:\par
        return fibonacci(n - 1) + fibonacci(n - 2)\par
\par
for i in range(10):\par
    print(fibonacci(i), end=" ")  # 0 1 1 2 3 5 8 13 21 34\par
\par
# Recursie vs. iteratie\par
# - Recursie: eleganter voor sommige problemen, maar kan inefficiënt zijn\par
# - Iteratie: meestal efficiënter, maar soms minder leesbaar\par
\par
# Voorbeeld: iteratieve fibonacci\par
def fibonacci_iteratief(n):\par
    a, b = 0, 1\par
    for _ in range(n):\par
        a, b = b, a + b\par
    return a\par
\par
print(fibonacci_iteratief(9))  # 34\f0\par

\pard\sa200\sl276\slmult1\b\fs24 6. MODULES EN PACKAGES\b0\fs22\par

\pard\sa200\sl276\slmult1\b Modules importeren\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Hele module importeren\par
import math\par
print(math.sqrt(16))  # 4.0\par
\par
# Specifieke functies importeren\par
from math import sqrt, pi\par
print(sqrt(16))  # 4.0\par
print(pi)  # 3.141592653589793\par
\par
# Alles importeren (niet aanbevolen vanwege namespace vervuiling)\par
from math import *\par
print(sqrt(16))  # 4.0\par
\par
# Module importeren met een alias\par
import math as m\par
print(m.sqrt(16))  # 4.0\par
\par
# Functie importeren met een alias\par
from math import sqrt as vierkantswortel\par
print(vierkantswortel(16))  # 4.0\f0\par

\pard\sa200\sl276\slmult1\b Eigen modules maken\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Bestand: mijn_module.py\par
def groet(naam):\par
    return f"Hallo, \{naam\}!"\par
\par
PI = 3.14159\par
\par
class Persoon:\par
    def __init__(self, naam):\par
        self.naam = naam\par
    \par
    def zeg_hallo(self):\par
        return f"Hallo, ik ben \{self.naam\}"\par
\par
# In een ander bestand:\par
import mijn_module\par
\par
print(mijn_module.groet("Alice"))  # Hallo, Alice!\par
print(mijn_module.PI)  # 3.14159\par
p = mijn_module.Persoon("Bob")\par
print(p.zeg_hallo())  # Hallo, ik ben Bob\par
\par
# __name__ variabele\par
# In mijn_module.py:\par
if __name__ == "__main__":\par
    # Code die alleen wordt uitgevoerd als het script direct wordt uitgevoerd\par
    print("Dit script wordt direct uitgevoerd")\par
else:\par
    # Code die wordt uitgevoerd bij import\par
    print("Dit script wordt geïmporteerd")\f0\par

\pard\sa200\sl276\slmult1\b Packages (pakketten)\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Mapstructuur voor een package:\par
# mijn_package/\par
#   __init__.py\par
#   module1.py\par
#   module2.py\par
#   subpackage/\par
#     __init__.py\par
#     module3.py\par
\par
# In module1.py\par
def functie1():\par
    return "Dit is functie1 uit module1"\par
\par
# In module2.py\par
def functie2():\par
    return "Dit is functie2 uit module2"\par
\par
# In subpackage/module3.py\par
def functie3():\par
    return "Dit is functie3 uit module3"\par
\par
# In __init__.py (hoofdpackage)\par
from . import module1, module2\par
from .subpackage import module3\par
\par
# Importeren uit een package\par
import mijn_package.module1\par
print(mijn_package.module1.functie1())\par
\par
from mijn_package import module2\par
print(module2.functie2())\par
\par
from mijn_package.subpackage.module3 import functie3\par
print(functie3())\f0\par

\pard\sa200\sl276\slmult1\b Standaard bibliotheek modules\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # math - wiskundige functies\par
import math\par
print(math.sqrt(16))  # 4.0\par
print(math.sin(math.pi/2))  # 1.0\par
\par
# random - willekeurige getallen\par
import random\par
print(random.randint(1, 10))  # willekeurig getal tussen 1 en 10\par
print(random.choice(["appel", "peer", "banaan"]))  # willekeurig element\par
\par
# datetime - datum en tijd\par
from datetime import datetime, timedelta\par
nu = datetime.now()\par
print(nu)  # huidige datum en tijd\par
morgen = nu + timedelta(days=1)\par
print(morgen)  # datum en tijd van morgen\par
\par
# os - besturingssysteem interface\par
import os\par
print(os.getcwd())  # huidige werkdirectory\par
print(os.listdir())  # bestanden in huidige directory\par
\par
# sys - systeemspecifieke parameters en functies\par
import sys\par
print(sys.version)  # Python versie\par
print(sys.argv)  # commandoregel argumenten\par
\par
# json - JSON data verwerken\par
import json\par
data = \{"naam": "Alice", "leeftijd": 30\}\par
json_str = json.dumps(data)\par
print(json_str)  # {"naam": "Alice", "leeftijd": 30}\par
\par
# re - reguliere expressies\par
import re\par
tekst = "Mijn telefoonnummer is 06-12345678"\par
match = re.search(r"\\d\\d-\\d{8}", tekst)\par
print(match.group())  # 06-12345678\par
\par
# collections - gespecialiseerde container datatypes\par
from collections import Counter, defaultdict\par
teller = Counter(["appel", "peer", "appel", "banaan"])\par
print(teller)  # Counter(\{'appel': 2, 'peer': 1, 'banaan': 1\})\par
\par
# itertools - iteratoren voor efficiënte lussen\par
import itertools\par
for i in itertools.count(10, 2):\par
    if i > 20: break\par
    print(i, end=" ")  # 10 12 14 16 18 20\f0\par

\pard\sa200\sl276\slmult1\b Externe modules installeren en gebruiken\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Installeren met pip (Python package manager)\par
# pip install requests\par
\par
# Requests module gebruiken (HTTP requests)\par
import requests\par
\par
response = requests.get("https://api.github.com")\par
print(response.status_code)  # 200 (OK)\par
print(response.json())  # JSON response als Python dictionary\par
\par
# NumPy (numerieke berekeningen)\par
# pip install numpy\par
import numpy as np\par
\par
arr = np.array([1, 2, 3, 4, 5])\par
print(arr.mean())  # 3.0 (gemiddelde)\par
print(arr * 2)     # [2 4 6 8 10] (vectorisatie)\par
\par
# Pandas (data-analyse)\par
# pip install pandas\par
import pandas as pd\par
\par
df = pd.DataFrame(\{\par
    'Naam': ['Alice', 'Bob', 'Charlie'],\par
    'Leeftijd': [25, 30, 35],\par
    'Stad': ['Amsterdam', 'Rotterdam', 'Utrecht']\par
\})\par
print(df.head())\par
\par
# Matplotlib (visualisatie)\par
# pip install matplotlib\par
import matplotlib.pyplot as plt\par
\par
plt.plot([1, 2, 3, 4], [1, 4, 9, 16])\par
plt.xlabel('x')\par
plt.ylabel('x^2')\par
plt.title('Kwadratische functie')\par
plt.show()\f0\par
}
