<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van ERD Types</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .diagram {
            width: 800px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: white;
            padding: 20px;
        }
        .entity {
            border: 2px solid #333;
            background-color: #f8f8f8;
            border-radius: 5px;
            text-align: center;
            margin: 10px;
            display: inline-block;
            vertical-align: top;
        }
        .entity-header {
            background-color: #333;
            color: white;
            padding: 8px;
            font-weight: bold;
        }
        .entity-body {
            padding: 10px;
        }
        .attribute {
            text-align: left;
            margin: 5px;
            padding: 3px;
        }
        .pk {
            font-weight: bold;
            text-decoration: underline;
        }
        .fk {
            font-style: italic;
            color: #555;
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .erd-type {
            margin: 30px 0;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .erd-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }
        .relationship {
            display: inline-block;
            margin: 10px;
            padding: 5px;
            border: 1px dashed #555;
            border-radius: 5px;
        }
        .arrow {
            display: inline-block;
            margin: 0 10px;
            font-weight: bold;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #333;
            color: white;
        }
    </style>
</head>
<body>
    <div class="title">Vergelijking van ERD Types</div>
    <div class="subtitle">Conceptueel, Logisch en Fysiek ERD</div>
    
    <div class="diagram">
        <div class="erd-type">
            <div class="erd-title">Conceptueel ERD</div>
            <p>Hoogste abstractieniveau, focus op entiteiten en relaties zonder technische details.</p>
            
            <div style="text-align: center;">
                <div class="entity" style="width: 150px;">
                    <div class="entity-header">Klant</div>
                    <div class="entity-body">
                        <div class="attribute">Naam</div>
                        <div class="attribute">Adres</div>
                    </div>
                </div>
                
                <div class="arrow">←→</div>
                
                <div class="relationship">
                    Plaatst
                </div>
                
                <div class="arrow">←→</div>
                
                <div class="entity" style="width: 150px;">
                    <div class="entity-header">Order</div>
                    <div class="entity-body">
                        <div class="attribute">Datum</div>
                        <div class="attribute">Totaal</div>
                    </div>
                </div>
                
                <div class="arrow">←→</div>
                
                <div class="relationship">
                    Bevat
                </div>
                
                <div class="arrow">←→</div>
                
                <div class="entity" style="width: 150px;">
                    <div class="entity-header">Product</div>
                    <div class="entity-body">
                        <div class="attribute">Naam</div>
                        <div class="attribute">Prijs</div>
                    </div>
                </div>
            </div>
            
            <p>Kenmerken:</p>
            <ul>
                <li>Toont alleen entiteiten en hun relaties</li>
                <li>Geen technische details zoals primaire of vreemde sleutels</li>
                <li>Bedoeld voor communicatie met niet-technische stakeholders</li>
                <li>Kardinaliteit kan worden weergegeven (1:1, 1:N, N:M)</li>
            </ul>
        </div>
        
        <div class="erd-type">
            <div class="erd-title">Logisch ERD</div>
            <p>Middenniveau van abstractie, voegt attributen en sleutels toe.</p>
            
            <div style="text-align: center;">
                <div class="entity" style="width: 180px;">
                    <div class="entity-header">Klant</div>
                    <div class="entity-body">
                        <div class="attribute pk">KlantID</div>
                        <div class="attribute">Naam</div>
                        <div class="attribute">Adres</div>
                        <div class="attribute">Telefoon</div>
                        <div class="attribute">Email</div>
                    </div>
                </div>
                
                <div class="arrow">1 —— N</div>
                
                <div class="entity" style="width: 180px;">
                    <div class="entity-header">Order</div>
                    <div class="entity-body">
                        <div class="attribute pk">OrderID</div>
                        <div class="attribute">Datum</div>
                        <div class="attribute">Totaal</div>
                        <div class="attribute">Status</div>
                    </div>
                </div>
                
                <div class="arrow">1 —— N</div>
                
                <div class="entity" style="width: 180px;">
                    <div class="entity-header">OrderDetail</div>
                    <div class="entity-body">
                        <div class="attribute pk">OrderID</div>
                        <div class="attribute pk">ProductID</div>
                        <div class="attribute">Aantal</div>
                        <div class="attribute">Prijs</div>
                    </div>
                </div>
                
                <div class="arrow">N —— 1</div>
                
                <div class="entity" style="width: 180px;">
                    <div class="entity-header">Product</div>
                    <div class="entity-body">
                        <div class="attribute pk">ProductID</div>
                        <div class="attribute">Naam</div>
                        <div class="attribute">Beschrijving</div>
                        <div class="attribute">Prijs</div>
                        <div class="attribute">Voorraad</div>
                    </div>
                </div>
            </div>
            
            <p>Kenmerken:</p>
            <ul>
                <li>Toont alle attributen van entiteiten</li>
                <li>Identificeert primaire sleutels</li>
                <li>Toont kardinaliteit van relaties duidelijk</li>
                <li>Veel-op-veel relaties worden opgesplitst in associatieve entiteiten</li>
                <li>Nog steeds onafhankelijk van specifieke databasetechnologie</li>
            </ul>
        </div>
        
        <div class="erd-type">
            <div class="erd-title">Fysiek ERD</div>
            <p>Laagste abstractieniveau, bevat alle technische details voor implementatie.</p>
            
            <div style="text-align: center;">
                <div class="entity" style="width: 220px;">
                    <div class="entity-header">Klant</div>
                    <div class="entity-body">
                        <div class="attribute pk">KlantID (INT, PK)</div>
                        <div class="attribute">Naam (VARCHAR(100), NOT NULL)</div>
                        <div class="attribute">Adres (VARCHAR(200))</div>
                        <div class="attribute">Telefoon (VARCHAR(20))</div>
                        <div class="attribute">Email (VARCHAR(100), UNIQUE)</div>
                    </div>
                </div>
                
                <div class="arrow">FK</div>
                
                <div class="entity" style="width: 220px;">
                    <div class="entity-header">Order</div>
                    <div class="entity-body">
                        <div class="attribute pk">OrderID (INT, PK)</div>
                        <div class="attribute fk">KlantID (INT, FK)</div>
                        <div class="attribute">Datum (DATE, NOT NULL)</div>
                        <div class="attribute">Totaal (DECIMAL(10,2))</div>
                        <div class="attribute">Status (VARCHAR(20))</div>
                    </div>
                </div>
                
                <div class="arrow">FK</div>
                
                <div class="entity" style="width: 220px;">
                    <div class="entity-header">OrderDetail</div>
                    <div class="entity-body">
                        <div class="attribute pk fk">OrderID (INT, PK, FK)</div>
                        <div class="attribute pk fk">ProductID (INT, PK, FK)</div>
                        <div class="attribute">Aantal (INT, NOT NULL)</div>
                        <div class="attribute">Prijs (DECIMAL(10,2), NOT NULL)</div>
                    </div>
                </div>
                
                <div class="arrow">FK</div>
                
                <div class="entity" style="width: 220px;">
                    <div class="entity-header">Product</div>
                    <div class="entity-body">
                        <div class="attribute pk">ProductID (INT, PK)</div>
                        <div class="attribute">Naam (VARCHAR(100), NOT NULL)</div>
                        <div class="attribute">Beschrijving (TEXT)</div>
                        <div class="attribute">Prijs (DECIMAL(10,2), NOT NULL)</div>
                        <div class="attribute">Voorraad (INT, DEFAULT 0)</div>
                    </div>
                </div>
            </div>
            
            <p>Kenmerken:</p>
            <ul>
                <li>Bevat alle technische details voor implementatie</li>
                <li>Specificeert datatypen, constraints en defaults</li>
                <li>Toont vreemde sleutels (FK) om relaties te implementeren</li>
                <li>Kan indexen en andere databasespecifieke elementen bevatten</li>
                <li>Klaar voor directe implementatie in een specifiek DBMS</li>
            </ul>
        </div>
        
        <table class="comparison-table">
            <tr>
                <th>Kenmerk</th>
                <th>Conceptueel ERD</th>
                <th>Logisch ERD</th>
                <th>Fysiek ERD</th>
            </tr>
            <tr>
                <td>Abstractieniveau</td>
                <td>Hoog</td>
                <td>Midden</td>
                <td>Laag</td>
            </tr>
            <tr>
                <td>Doelgroep</td>
                <td>Niet-technische stakeholders</td>
                <td>Systeemanalisten, ontwerpers</td>
                <td>Databaseontwikkelaars</td>
            </tr>
            <tr>
                <td>Primaire sleutels</td>
                <td>Niet gespecificeerd</td>
                <td>Geïdentificeerd</td>
                <td>Volledig gespecificeerd met datatypen</td>
            </tr>
            <tr>
                <td>Vreemde sleutels</td>
                <td>Niet aanwezig</td>
                <td>Niet expliciet getoond</td>
                <td>Expliciet getoond</td>
            </tr>
            <tr>
                <td>Datatypen</td>
                <td>Niet gespecificeerd</td>
                <td>Niet gespecificeerd</td>
                <td>Volledig gespecificeerd</td>
            </tr>
            <tr>
                <td>Kardinaliteit</td>
                <td>Kan worden weergegeven</td>
                <td>Duidelijk weergegeven</td>
                <td>Geïmplementeerd via vreemde sleutels</td>
            </tr>
            <tr>
                <td>Veel-op-veel relaties</td>
                <td>Direct weergegeven</td>
                <td>Opgesplitst in associatieve entiteiten</td>
                <td>Geïmplementeerd via koppeltabellen</td>
            </tr>
        </table>
    </div>
</body>
</html>
