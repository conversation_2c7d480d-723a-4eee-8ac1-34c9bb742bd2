<!DOCTYPE html>
<html>
<head>
    <title>Recursieve Relatie - Logisch ERD</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .diagram {
            width: 600px;
            height: 400px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: white;
        }
        .entity {
            position: absolute;
            width: 200px;
            border: 2px solid #333;
            background-color: #f8f8f8;
            border-radius: 5px;
            text-align: center;
        }
        .entity-header {
            background-color: #333;
            color: white;
            padding: 8px;
            font-weight: bold;
        }
        .entity-body {
            padding: 10px;
        }
        .attribute {
            text-align: left;
            margin: 5px;
            padding: 3px;
        }
        .pk {
            font-weight: bold;
            text-decoration: underline;
        }
        .fk {
            font-style: italic;
            color: #555;
        }
        .cardinality {
            position: absolute;
            font-weight: bold;
            color: #333;
        }
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 20px;
        }
        .subtitle {
            text-align: center;
            font-size: 18px;
            margin-bottom: 30px;
            color: #555;
        }
        .explanation {
            margin: 20px;
            padding: 15px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="title">Recursieve Relatie</div>
    <div class="subtitle">Logisch ERD</div>
    
    <div class="diagram">
        <div style="position: absolute; top: 20px; left: 200px; font-weight: bold;">
            Logisch ERD
        </div>
        
        <div class="entity" style="top: 80px; left: 200px;">
            <div class="entity-header">Verkoper</div>
            <div class="entity-body">
                <div class="attribute pk">VerkoperID</div>
                <div class="attribute">Naam</div>
                <div class="attribute">Attribuut1</div>
                <div class="attribute">Attribuut2</div>
                <div class="attribute fk">ManagerID</div>
            </div>
        </div>
        
        <!-- Recursieve relatie -->
        <div style="position: absolute; top: 200px; left: 350px; width: 100px; height: 100px; border: 2px solid #555; border-radius: 0; border-style: solid;"></div>
        <div style="position: absolute; top: 240px; left: 360px; font-weight: bold;">Managet</div>
        
        <!-- Cardinaliteit -->
        <div class="cardinality" style="top: 180px; left: 360px;">1</div>
        <div class="cardinality" style="top: 280px; left: 360px;">N</div>
        
        <!-- Pijl -->
        <div style="position: absolute; top: 160px; left: 350px; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-bottom: 15px solid #555;"></div>
    </div>
    
    <div class="explanation">
        <p><strong>Toelichting:</strong></p>
        <p>Dit is een logisch ERD van een recursieve relatie, gebaseerd op het voorbeeld uit de cursus:</p>
        <ul>
            <li>Een Manager kan veel andere verkopers managen</li>
            <li>Een verkoper wordt beheerd door slechts één manager</li>
        </ul>
        <p>In het logische ERD wordt de recursieve relatie geïmplementeerd door een vreemde sleutel (ManagerID) toe te voegen aan de Verkoper entiteit, die verwijst naar de primaire sleutel (VerkoperID) van dezelfde tabel.</p>
        <p>Kenmerken van het logische ERD:</p>
        <ul>
            <li>Primaire sleutel (VerkoperID) is geïdentificeerd</li>
            <li>Vreemde sleutel (ManagerID) is toegevoegd om de relatie te implementeren</li>
            <li>Alle attributen zijn gespecificeerd</li>
            <li>De kardinaliteit is duidelijk weergegeven (1:N)</li>
        </ul>
        <p>In een fysiek ERD zou dit worden geïmplementeerd als een tabel met een vreemde sleutel die naar zichzelf verwijst, inclusief datatypen en constraints.</p>
    </div>
</body>
</html>
