<mxfile host="app.diagrams.net" modified="2023-11-09T15:30:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="abc123" version="15.7.3" type="device"><diagram id="C5RBs43oDa-KdzZeNtuy" name="Euro Caps Supply Chain">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="LEVERANCIERS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="40" y="280" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="3" value="KLANTEN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="960" y="280" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="4" value="EURO CAPS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="480" y="280" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="9" value="INBOUND LOGISTIEK" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="360" y="400" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="10" value="INTERNE LOGISTIEK" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="510" y="400" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="11" value="OUTBOUND LOGISTIEK" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="660" y="400" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="12" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="9" target="4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="13" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="10" target="4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="14" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="11" target="4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- Inbound Logistiek Details -->
        <mxCell id="15" value="Inkoop" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="280" y="500" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="16" value="Ontvangst" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="370" y="500" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="17" value="Kwaliteits-controle" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="460" y="500" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="18" value="Opslag" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="460" y="550" width="80" height="40" as="geometry" />
        </mxCell>

        <!-- Inbound Logistiek Verbindingen -->
        <mxCell id="110" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="15" target="16">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="111" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="16" target="17">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="112" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="17" target="18">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="113" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.25;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="15" target="9">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- Interne Logistiek Details -->
        <mxCell id="19" value="Materiaal-voorziening" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="510" y="500" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="20" value="Intern transport" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="600" y="500" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="21" value="Productie-ondersteuning" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="550" y="550" width="90" height="40" as="geometry" />
        </mxCell>
        <mxCell id="120" value="Maalstations" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="510" y="600" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="121" value="Capsule-vulling" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="600" y="600" width="80" height="40" as="geometry" />
        </mxCell>

        <!-- Interne Logistiek Verbindingen -->
        <mxCell id="122" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="18" target="19">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="123" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="19" target="20">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="124" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="19" target="120">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="125" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="120" target="121">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="126" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="21" target="20">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- Outbound Logistiek Details -->
        <mxCell id="22" value="Opslag eindproducten" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="690" y="500" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="23" value="Order-verwerking" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="780" y="500" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="24" value="Order-picking" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="690" y="550" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="25" value="Verzending" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="780" y="550" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="26" value="Retourbeheer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="735" y="600" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="130" value="Verpakking" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="735" y="500" width="80" height="40" as="geometry" />
        </mxCell>

        <!-- Outbound Logistiek Verbindingen -->
        <mxCell id="131" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="121" target="22">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="132" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="22" target="23">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="133" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="22" target="24">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="134" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="24" target="25">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="135" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="25" target="3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="880" y="570" />
              <mxPoint x="880" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="136" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.75;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="26" target="4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="630" y="620" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- Connect to main components -->
        <mxCell id="27" value="" style="endArrow=none;dashed=1;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="15" target="9">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="28" value="" style="endArrow=none;dashed=1;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="19" target="10">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="29" value="" style="endArrow=none;dashed=1;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="22" target="11">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- Informatiestromen -->
        <mxCell id="30" value="INFORMATIESTROMEN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="510" y="160" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="31" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="4" target="30">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="32" value="Klantorders &amp; forecasts" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="370" y="110" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="33" value="Productieplanning" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="480" y="110" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="34" value="Kwaliteitsgegevens" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="590" y="110" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="35" value="Voorraadgegevens" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="700" y="110" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="150" value="Verzendgegevens" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="810" y="110" width="100" height="40" as="geometry" />
        </mxCell>

        <!-- Informatiestromen Verbindingen -->
        <mxCell id="151" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="32" target="4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="152" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="33" target="4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="153" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="33" target="15">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="480" y="200" />
              <mxPoint x="320" y="200" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="154" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="33" target="10">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="530" y="200" />
              <mxPoint x="580" y="200" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="155" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="34" target="17">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="640" y="200" />
              <mxPoint x="500" y="200" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="156" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="35" target="19">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="750" y="200" />
              <mxPoint x="550" y="200" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="157" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="35" target="15">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="750" y="180" />
              <mxPoint x="320" y="180" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="158" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="150" target="25">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="860" y="200" />
              <mxPoint x="820" y="200" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="159" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;dashed=1;strokeColor=#0000FF;" edge="1" parent="1" source="150" target="3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="930" y="130" />
              <mxPoint x="930" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- Knelpunten -->
        <mxCell id="36" value="KNELPUNTEN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="510" y="650" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="37" value="Ontvangstvertragingen" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="370" y="700" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="38" value="Opslagbeperkingen" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="500" y="700" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="39" value="Voorraadbeheer-uitdagingen" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="630" y="700" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="40" value="Orderverwerking vertragingen" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="430" y="750" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="41" value="Transportproblemen" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="580" y="750" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="140" value="Documentatiefouten" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="500" y="800" width="120" height="40" as="geometry" />
        </mxCell>

        <!-- Knelpunten Verbindingen -->
        <mxCell id="141" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;strokeColor=#FF0000;" edge="1" parent="1" source="37" target="16">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="142" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;strokeColor=#FF0000;" edge="1" parent="1" source="38" target="18">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="143" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;strokeColor=#FF0000;" edge="1" parent="1" source="39" target="22">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="144" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;strokeColor=#FF0000;" edge="1" parent="1" source="40" target="23">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="145" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;strokeColor=#FF0000;" edge="1" parent="1" source="41" target="25">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="146" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;strokeColor=#FF0000;" edge="1" parent="1" source="140" target="17">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
            <Array as="points">
              <mxPoint x="500" y="780" />
              <mxPoint x="460" y="780" />
              <mxPoint x="460" y="560" />
              <mxPoint x="500" y="560" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- Verbindingen -->
        <mxCell id="42" value="" style="endArrow=none;dashed=1;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="36" target="26">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="43" value="" style="endArrow=none;dashed=1;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="32" target="30">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="560" y="450" as="sourcePoint" />
            <mxPoint x="610" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- Titel -->
        <mxCell id="44" value="EURO CAPS SUPPLY CHAIN" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="430" y="40" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="5" value="Grondstoffen" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#ffffff;labelBackgroundColor=#FFFFFF;" edge="1" parent="1" source="2" target="4">
          <mxGeometry x="-0.1429" y="-10" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="6" value="Verpakte koffiecapsules" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;fillColor=#ffffff;labelBackgroundColor=#FFFFFF;" edge="1" parent="1" source="4" target="3">
          <mxGeometry x="-0.1429" y="-10" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="100" value="Leveranciersportaal / VMI / Forecasting" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;dashed=1;dashPattern=1 2;strokeWidth=1;labelBackgroundColor=#FFFFFF;fontSize=10;" edge="1" parent="1" source="2" target="4">
          <mxGeometry x="-0.1429" y="-20" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="101" value="Klantportaal / Feedback / SLA" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;dashed=1;dashPattern=1 2;strokeWidth=1;labelBackgroundColor=#FFFFFF;fontSize=10;" edge="1" parent="1" source="4" target="3">
          <mxGeometry x="-0.1429" y="-20" relative="1" as="geometry">
            <mxPoint x="560" y="410" as="sourcePoint" />
            <mxPoint x="610" y="360" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="7" value="Grondstoffen:&lt;br&gt;- Koffiebonen&lt;br&gt;- Capsules&lt;br&gt;- Aluminiumfolie&lt;br&gt;- Verpakkingsmaterialen" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="250" y="220" width="150" height="90" as="geometry" />
        </mxCell>
        <mxCell id="8" value="Eindproducten:&lt;br&gt;- Verpakte koffiecapsules" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="760" y="245" width="150" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram></mxfile>
