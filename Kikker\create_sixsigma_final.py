from openpyxl import Workbook
from openpyxl.chart import <PERSON><PERSON><PERSON>, Reference
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.chart.label import DataLabelList
import os

def create_sixsigma_excel():
    # Verwijder het bestaande bestand als het bestaat
    output_file = 'SixSigma_Final.xlsx'
    if os.path.exists(output_file):
        try:
            os.remove(output_file)
            print(f"Bestaand bestand {output_file} verwijderd")
        except:
            print(f"Kon bestaand bestand {output_file} niet verwijderen")

    # Maak een Excel workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Six Sigma Analyse"

    # Definieer stijlen
    header_font = Font(bold=True, size=12)
    normal_font = Font(size=11)

    # Definieer borders
    thin_border = Border(left=Side(style='thin'),
                         right=Side(style='thin'),
                         top=Side(style='thin'),
                         bottom=Side(style='thin'))

    # Definieer vulkleuren
    header_fill = PatternFill(start_color="8EAADB", end_color="8EAADB", fill_type="solid")
    subheader_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
    data_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")

    # Maak de tabelheaders
    ws.cell(row=1, column=1, value="Column1").font = header_font
    ws.cell(row=1, column=1).fill = header_fill
    ws.cell(row=1, column=1).alignment = Alignment(horizontal='left')
    ws.cell(row=1, column=1).border = thin_border

    ws.cell(row=1, column=2, value="Column2").font = header_font
    ws.cell(row=1, column=2).fill = header_fill
    ws.cell(row=1, column=2).alignment = Alignment(horizontal='left')
    ws.cell(row=1, column=2).border = thin_border

    # Titel
    ws.cell(row=2, column=1, value="SIX SIGMA ANALYSE").font = header_font
    ws.cell(row=2, column=1).fill = subheader_fill
    ws.cell(row=2, column=1).border = thin_border
    ws.cell(row=2, column=2).fill = subheader_fill
    ws.cell(row=2, column=2).border = thin_border

    # Subtitel
    ws.cell(row=3, column=1, value="Defectpercentage per batch").font = Font(italic=True)
    ws.cell(row=3, column=1).fill = subheader_fill
    ws.cell(row=3, column=1).border = thin_border
    ws.cell(row=3, column=2).fill = subheader_fill
    ws.cell(row=3, column=2).border = thin_border

    # Lege rij
    ws.cell(row=4, column=1).fill = subheader_fill
    ws.cell(row=4, column=1).border = thin_border
    ws.cell(row=4, column=2).fill = subheader_fill
    ws.cell(row=4, column=2).border = thin_border

    # Top 10 batches met hoogste defectpercentage
    ws.cell(row=5, column=1, value="Top 10 batches met hoogste defectpercentage:").font = header_font
    ws.cell(row=5, column=1).fill = subheader_fill
    ws.cell(row=5, column=1).border = thin_border
    ws.cell(row=5, column=2).fill = subheader_fill
    ws.cell(row=5, column=2).border = thin_border

    # Batch data
    batch_data = [
        ("- Batch 7078", 5.00),
        ("- Batch 7569", 5.00),
        ("- Batch 8636", 5.00),
        ("- Batch 2329", 5.00),
        ("- Batch 7886", 4.99),
        ("- Batch 8788", 4.98),
        ("- Batch 2443", 4.97),
        ("- Batch 9787", 4.96),
        ("- Batch 9695", 4.94),
        ("- Batch 6534", 4.78)
    ]

    for i, (batch, defect) in enumerate(batch_data):
        row = 6 + i
        ws.cell(row=row, column=1, value=batch).font = normal_font
        ws.cell(row=row, column=1).border = thin_border
        # Voeg het percentage toe aan de waarde voor de visualisatie
        ws.cell(row=row, column=2, value=f"{defect}%").font = normal_font
        ws.cell(row=row, column=2).fill = data_fill
        ws.cell(row=row, column=2).border = thin_border

    # Lege rij
    ws.cell(row=16, column=1).fill = subheader_fill
    ws.cell(row=16, column=1).border = thin_border
    ws.cell(row=16, column=2).fill = subheader_fill
    ws.cell(row=16, column=2).border = thin_border

    # Proces Capability Analyse
    ws.cell(row=17, column=1, value="Proces Capability Analyse voor Gewichtscontrole:").font = header_font
    ws.cell(row=17, column=1).fill = subheader_fill
    ws.cell(row=17, column=1).border = thin_border
    ws.cell(row=17, column=2).fill = subheader_fill
    ws.cell(row=17, column=2).border = thin_border

    # Proces Capability data
    capability_data = [
        ("- Target gewicht (kg)", 1.00),
        ("- Ondergrens (kg)", 0.90),
        ("- Bovengrens (kg)", 1.10),
        ("- Process capability (Cp)", 0.16),
        ("- Process capability index (Cpk)", 0.16)
    ]

    for i, (label, value) in enumerate(capability_data):
        row = 18 + i
        ws.cell(row=row, column=1, value=label).font = normal_font
        ws.cell(row=row, column=1).border = thin_border
        ws.cell(row=row, column=2, value=value).font = normal_font
        ws.cell(row=row, column=2).fill = data_fill
        ws.cell(row=row, column=2).border = thin_border

    # Pas kolombreedtes aan
    ws.column_dimensions['A'].width = 40
    ws.column_dimensions['B'].width = 15
    ws.column_dimensions['C'].width = 15

    # 1. Maak een staafdiagram voor defectpercentage per batch
    defect_chart = BarChart()
    defect_chart.title = "Defectpercentage per Batch"
    defect_chart.y_axis.title = "Defectpercentage (%)"
    defect_chart.x_axis.title = "Batch"
    defect_chart.height = 15  # Hoogte van het diagram
    defect_chart.width = 20   # Breedte van het diagram

    # Definieer de data voor het staafdiagram direct vanuit de tabel
    # Gebruik de waarden uit kolom B (rij 6-15)
    defect_data = Reference(ws, min_col=2, min_row=6, max_row=15, max_col=2)

    # Gebruik de labels uit kolom A (rij 6-15)
    defect_cats = Reference(ws, min_col=1, min_row=6, max_row=15)

    # Voeg de data toe aan het staafdiagram
    defect_chart.add_data(defect_data)
    defect_chart.set_categories(defect_cats)

    # Voeg data labels toe om de exacte waarden te tonen
    defect_chart.dataLabels = DataLabelList()
    defect_chart.dataLabels.showVal = True  # Toon de waarden
    defect_chart.dataLabels.showCatName = False  # Verberg categorienamen
    defect_chart.dataLabels.showSerName = False  # Verberg serienamen
    defect_chart.dataLabels.showPercent = False  # Verberg percentages
    defect_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels

    # Pas de stijl aan
    defect_chart.style = 10  # Kies een stijl

    # Voeg het staafdiagram toe aan het Six Sigma Analyse werkblad naast de tabel
    ws.add_chart(defect_chart, "D5")

    # 2. Maak een visualisatie voor Process Capability
    cp_chart = BarChart()
    cp_chart.title = "Process Capability Analyse"
    cp_chart.y_axis.title = "Waarde (kg voor Target en Specificatiegrenzen)"
    cp_chart.x_axis.title = "Metric"
    cp_chart.height = 10  # Hoogte van het diagram
    cp_chart.width = 15   # Breedte van het diagram

    # Definieer de data voor het staafdiagram direct vanuit de tabel
    # Gebruik de waarden uit kolom B (rij 18-22)
    cp_data = Reference(ws, min_col=2, min_row=18, max_row=22, max_col=2)

    # Gebruik de labels uit kolom A (rij 18-22)
    cp_cats = Reference(ws, min_col=1, min_row=18, max_row=22)

    # Voeg de data toe aan het staafdiagram
    cp_chart.add_data(cp_data)
    cp_chart.set_categories(cp_cats)

    # Voeg data labels toe om de exacte waarden te tonen
    cp_chart.dataLabels = DataLabelList()
    cp_chart.dataLabels.showVal = True  # Toon de waarden
    cp_chart.dataLabels.showCatName = False  # Verberg categorienamen
    cp_chart.dataLabels.showSerName = False  # Verberg serienamen
    cp_chart.dataLabels.showPercent = False  # Verberg percentages
    cp_chart.dataLabels.showLegendKey = False  # Verberg legendasleutels

    # Pas de stijl aan
    cp_chart.style = 11  # Kies een andere stijl

    # Voeg het staafdiagram toe aan het Six Sigma Analyse werkblad naast de tabel
    ws.add_chart(cp_chart, "D17")

    # Sla het Excel bestand op
    try:
        wb.save(output_file)
        print(f"Excel bestand met visualisaties is opgeslagen als {output_file}")
    except Exception as e:
        print(f"Fout bij opslaan van bestand: {e}")
        # Als er een fout is, probeer op te slaan in een tijdelijke map
        import tempfile
        temp_dir = tempfile.gettempdir()
        temp_file = os.path.join(temp_dir, 'SixSigma_Final.xlsx')
        wb.save(temp_file)
        print(f"Excel bestand met visualisaties is opgeslagen als {temp_file}")

if __name__ == "__main__":
    create_sixsigma_excel()
