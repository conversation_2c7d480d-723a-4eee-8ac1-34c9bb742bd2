# Complete Quiz over Entity Relationship Diagrams (ERD) en Derde Normaalvorm (3NF)

## Deel 1: Basisconcepten ERD

1. Wat zijn de drie hoofdcomponenten van een Entity Relationship Diagram?
   a) Entiteiten, attributen en relaties
   b) <PERSON><PERSON>en, rijen en kolommen
   c) Databases, schema's en views
   d) Primaire sleutels, vreemde sleutels en indexen

2. Welk symbool wordt meestal gebruikt om een entiteit weer te geven in een ERD?
   a) Ovaal
   b) Rechthoek
   c) Ruit
   d) Cirkel

3. Wat geeft een "crow's foot" (kraai<PERSON>poot) notatie aan in een ERD?
   a) Een één-op-één relatie
   b) Een één-op-veel relatie
   c) Een veel-op-veel relatie
   d) Een recursieve relatie

4. Wat is een zwakke entiteit in een ERD?
   a) Een entiteit die niet vaak wordt gebruikt
   b) Een entiteit die afhankelijk is van een andere entiteit voor zijn bestaan
   c) E<PERSON> entiteit met weinig attributen
   d) Een entiteit zonder relaties

5. <PERSON><PERSON><PERSON> van de volgende relatie-types bestaat NIET in een ERD?
   a) Één-op-één (1:1)
   b) Één-op-veel (1:N)
   c) Veel-op-veel (M:N)
   d) Nul-op-nul (0:0)

6. Wat is het doel van een associatieve entiteit (junction table) in een ERD?
   a) Om een één-op-één relatie te implementeren
   b) Om een veel-op-veel relatie om te zetten naar twee één-op-veel relaties
   c) Om attributen van een zwakke entiteit op te slaan
   d) Om primaire sleutels te genereren

7. In welke fase van databaseontwerp wordt een ERD meestal gemaakt?
   a) Implementatiefase
   b) Conceptueel en logisch ontwerp
   c) Fysiek ontwerp
   d) Testfase

8. Wat is een samengestelde sleutel in een ERD?
   a) Een sleutel die uit meerdere attributen bestaat
   b) Een sleutel die gedeeld wordt tussen meerdere entiteiten
   c) Een sleutel die automatisch wordt gegenereerd
   d) Een sleutel die alleen numerieke waarden bevat

9. Wat is het verschil tussen een conceptueel ERD en een logisch ERD?
   a) Een conceptueel ERD bevat meer technische details dan een logisch ERD
   b) Een logisch ERD bevat meer technische details dan een conceptueel ERD
   c) Een conceptueel ERD is specifiek voor één databasemanagementsysteem
   d) Er is geen verschil tussen deze twee types

10. Welke van de volgende is GEEN voordeel van het gebruik van ERD's?
    a) Ze helpen bij het visualiseren van de databasestructuur
    b) Ze verbeteren de communicatie tussen stakeholders
    c) Ze optimaliseren automatisch database queries
    d) Ze documenteren de relaties tussen entiteiten

11. Wat is een veel-op-veel (many-to-many of N:M) relatie in een ERD?
    a) Een relatie waarbij één record in tabel A gerelateerd is aan één record in tabel B
    b) Een relatie waarbij één record in tabel A gerelateerd is aan meerdere records in tabel B
    c) Een relatie waarbij meerdere records in tabel A gerelateerd zijn aan meerdere records in tabel B
    d) Een relatie waarbij geen records in tabel A gerelateerd zijn aan records in tabel B

12. In het voorbeeld uit de cursus: "Een Superheld behoort tot 0 of meerdere groepen, Een Groep kan 1 of meerdere superhelden bevatten", wat voor soort relatie is dit?
    a) Eén-op-één (1:1)
    b) Eén-op-veel (1:N)
    c) Veel-op-veel (N:M)
    d) Nul-op-veel (0:N)

13. Waarom is de volgende tabel niet correct voor het opslaan van de relatie tussen superhelden en groepen?
    | SuperheldID | Naam | GroepID | GroepNaam |
    |-------------|------|---------|-----------|
    | 1 | Thor | 1 | Avengers |
    | 1 | Thor | 2 | Guardians of the Galaxy |
    | 2 | Iron Man | 1 | Avengers |
    | 3 | Gamora | 2 | Guardians of the Galaxy |
    
    a) Omdat de primaire sleutel niet uniek is
    b) Omdat er geen vreemde sleutel is
    c) Omdat de tabel niet in 3NF is
    d) Omdat er geen datum is opgenomen

14. Wat is een kenmerk van een fysiek ERD?
    a) Het bevat alleen entiteiten en relaties, geen attributen
    b) Het bevat vreemde sleutels (FK) om relaties te implementeren
    c) Het is onafhankelijk van het gekozen databasemanagementsysteem
    d) Het bevat geen informatie over kardinaliteit

15. Welke van de volgende elementen zou je NIET vinden in een conceptueel ERD?
    a) Entiteiten
    b) Relaties
    c) Vreemde sleutels
    d) Kardinaliteit

16. Hoe implementeer je een veel-op-veel (N:M) relatie in een fysiek ERD?
    a) Door een directe relatie tussen de twee entiteiten
    b) Door een associatieve entiteit (junction table) te creëren
    c) Door een vreemde sleutel toe te voegen aan beide entiteiten
    d) Door de relatie te negeren

17. In een situatie waar "Een Manager kan veel andere verkopers managen. Een verkoper wordt beheerd door slechts één manager", wat voor soort relatie is dit?
    a) Eén-op-één (1:1)
    b) Eén-op-veel (1:N)
    c) Veel-op-veel (N:M)
    d) Recursieve relatie

18. Wat is een recursieve relatie in een ERD?
    a) Een relatie tussen twee verschillende entiteiten
    b) Een relatie van een entiteit met zichzelf
    c) Een relatie die niet kan worden geïmplementeerd
    d) Een relatie die alleen in conceptuele ERD's voorkomt

19. In het voorbeeld van de cursus: "Een Manager kan veel andere verkopers managen. Een verkoper wordt beheerd door slechts één manager", wat voor soort relatie is dit?
    a) Een standaard één-op-veel relatie
    b) Een veel-op-veel relatie
    c) Een recursieve relatie
    d) Een zwakke entiteit relatie

20. Welke tool wordt in de cursus aanbevolen voor het maken van ERD-diagrammen?
    a) Microsoft Visio
    b) draw.io
    c) MySQL Workbench
    d) ERDPlus

21. Wat is een belangrijke stap bij het maken van een fysiek ERD op basis van een logisch ERD?
    a) Het verwijderen van alle attributen
    b) Het toevoegen van vreemde sleutels om relaties te implementeren
    c) Het verwijderen van alle relaties
    d) Het veranderen van alle kardinaliteiten

## Deel 2: Normalisatie en 3NF

22. Wat is normalisatie in de context van databaseontwerp?
    a) Het proces van het optimaliseren van databaseprestaties
    b) Het proces van het organiseren van data om redundantie te verminderen
    c) Het proces van het beveiligen van een database
    d) Het proces van het maken van backups van een database

23. Welke normaalvorm moet een database bereiken voordat deze in 3NF kan zijn?
    a) 0NF
    b) 1NF
    c) 2NF
    d) 4NF

24. Een tabel is in de Eerste Normaalvorm (1NF) als:
    a) Alle attributen atomair (ondeelbaar) zijn
    b) Er geen partiële afhankelijkheden zijn
    c) Er geen transitieve afhankelijkheden zijn
    d) Alle niet-sleutel attributen functioneel afhankelijk zijn van de primaire sleutel

25. Een tabel is in de Tweede Normaalvorm (2NF) als:
    a) Alle attributen atomair zijn
    b) De tabel in 1NF is en er geen partiële afhankelijkheden zijn
    c) De tabel in 1NF is en er geen transitieve afhankelijkheden zijn
    d) De tabel in 1NF is en alle attributen functioneel onafhankelijk zijn

26. Een tabel is in de Derde Normaalvorm (3NF) als:
    a) De tabel in 2NF is en er geen transitieve afhankelijkheden zijn
    b) De tabel in 2NF is en alle attributen atomair zijn
    c) De tabel in 1NF is en er geen partiële afhankelijkheden zijn
    d) De tabel in 1NF is en alle attributen functioneel onafhankelijk zijn

27. Wat is een transitieve afhankelijkheid?
    a) Wanneer een niet-sleutel attribuut afhankelijk is van een deel van een samengestelde sleutel
    b) Wanneer een niet-sleutel attribuut afhankelijk is van een ander niet-sleutel attribuut
    c) Wanneer een sleutel attribuut afhankelijk is van een niet-sleutel attribuut
    d) Wanneer twee tabellen een relatie hebben

28. Welk probleem lost de Derde Normaalvorm (3NF) op?
    a) Duplicatie van gegevens door herhalende groepen
    b) Partiële afhankelijkheden van een samengestelde sleutel
    c) Transitieve afhankelijkheden tussen niet-sleutel attributen
    d) Problemen met multi-waarde attributen

29. Gegeven de tabel: BESTELLING(OrderID, KlantID, KlantNaam, KlantAdres, ProductID, ProductNaam, Aantal, Prijs)
    Welke van de volgende afhankelijkheden zorgt ervoor dat deze tabel NIET in 3NF is?
    a) OrderID → KlantID
    b) KlantID → KlantNaam, KlantAdres
    c) ProductID → ProductNaam
    d) Zowel b als c

30. Wat is een potentieel nadeel van het normaliseren van een database tot 3NF?
    a) Het verhoogt de kans op data-inconsistentie
    b) Het kan leiden tot complexere queries met meer joins
    c) Het vermindert de mogelijkheden voor indexering
    d) Het maakt het moeilijker om de database te beveiligen

31. Welke uitspraak over 3NF is WAAR?
    a) Een tabel in 3NF heeft altijd minder rijen dan dezelfde gegevens in een niet-genormaliseerde tabel
    b) Een tabel in 3NF heeft meestal meer tabellen dan een database in 1NF
    c) 3NF garandeert optimale queryprestaties in alle gevallen
    d) 3NF elimineert alle vormen van redundantie in een database

32. Wat is de definitie van de nulde normaalvorm (0NF)?
    a) Een tabel waarin alle attributen atomair zijn
    b) Een tabel waarin geen partiële afhankelijkheden zijn
    c) Een tabel waarin geen transitieve afhankelijkheden zijn
    d) Gegevens die op geen enkele manier genormaliseerd zijn

33. Welke van de volgende handelingen moet je NIET uitvoeren om tot de eerste normaalvorm (1NF) te komen?
    a) Zorgen dat een attribuut geen dubbele betekenis heeft
    b) Attributen die berekend kunnen worden uit andere attributen weglaten
    c) De identifier bepalen (attributen die de entiteit uniek maken)
    d) Zoeken naar niet-sleutelattributen die afhankelijk zijn van een ander niet-sleutelattribuut

34. Wat is een voorwaarde voor de tweede normaalvorm (2NF)?
    a) De tabel moet in 1NF zijn
    b) De tabel moet in 3NF zijn
    c) Er mogen geen transitieve afhankelijkheden zijn
    d) Alle attributen moeten deel uitmaken van de primaire sleutel

35. Wat moet je doen om een tabel naar de derde normaalvorm (3NF) te brengen?
    a) Repeterende groepen verwijderen
    b) Zorgen dat alle attributen atomair zijn
    c) Zoeken naar niet-sleutelattributen die afhankelijk zijn van een ander niet-sleutelattribuut
    d) Zorgen dat alle attributen direct afhankelijk zijn van de primaire sleutel

36. Welke van de volgende vragen moet je NIET stellen bij het normaliseren van een tabel volgens de cursus?
    a) Zie je meerdere entiteiten?
    b) Zijn er herhalende gegevens?
    c) Zijn er meerdere waardes in 1 kolom?
    d) Is de tabel in Boyce-Codd normaalvorm?

37. Wat is het doel van normalisatie in databaseontwerp?
    a) Het versnellen van database queries
    b) Het verminderen van redundantie en het voorkomen van anomalieën
    c) Het vereenvoudigen van het databaseschema
    d) Het verminderen van het aantal tabellen

38. Welke stap in het normalisatieproces is het belangrijkst volgens de cursus?
    a) Het identificeren van de primaire sleutel
    b) Het verwijderen van transitieve afhankelijkheden
    c) Het identificeren van meerdere entiteiten
    d) Het verwijderen van berekende velden

## Deel 3: Praktische Toepassingen

39. Je hebt een tabel MEDEWERKER(MedewerkerID, Naam, AfdelingID, AfdelingNaam, LocatieID, LocatieAdres).
    Hoe zou je deze tabel normaliseren naar 3NF?
    a) Eén tabel is voldoende omdat alle attributen afhankelijk zijn van MedewerkerID
    b) Twee tabellen: MEDEWERKER(MedewerkerID, Naam, AfdelingID) en AFDELING(AfdelingID, AfdelingNaam, LocatieID, LocatieAdres)
    c) Drie tabellen: MEDEWERKER(MedewerkerID, Naam, AfdelingID), AFDELING(AfdelingID, AfdelingNaam, LocatieID) en LOCATIE(LocatieID, LocatieAdres)
    d) Vier tabellen zijn nodig om 3NF te bereiken

40. Welke van de volgende relaties zou je in een ERD modelleren als een veel-op-veel relatie?
    a) Student en StudentID
    b) Student en Klas (elke student zit in één klas, elke klas heeft meerdere studenten)
    c) Student en Cursus (studenten volgen meerdere cursussen, cursussen hebben meerdere studenten)
    d) Student en Adres (elke student heeft één adres)

41. Je moet een database ontwerpen voor een bibliotheek. Welke van de volgende zou een geschikte set entiteiten zijn voor een ERD?
    a) Boeken, Auteurs, Leden, Uitleningen
    b) Boektitels, Boeknummers, Ledennamen, Uitleendatums
    c) Bibliotheek, Collectie, Lidmaatschap, Boekenkast
    d) ISBN, Auteursnaam, Ledennummer, Retourneren

42. Wat is de beste manier om een één-op-één relatie te implementeren in een relationele database?
    a) Gebruik dezelfde primaire sleutel in beide tabellen
    b) Creëer een derde tabel om de relatie te beheren
    c) Voeg een vreemde sleutel toe aan één van de tabellen die verwijst naar de andere tabel
    d) Combineer beide tabellen tot één tabel

43. Je hebt een tabel FACTUUR(FactuurID, KlantID, KlantNaam, Datum, ProductID, ProductNaam, Prijs, Aantal, Subtotaal).
    Welke functionele afhankelijkheden bestaan er in deze tabel?
    a) FactuurID → KlantID, Datum
    b) KlantID → KlantNaam
    c) ProductID → ProductNaam, Prijs
    d) Alle bovenstaande

44. Je hebt een tabel BESTELLING(OrderID, KlantID, KlantNaam, KlantAdres, ProductID, ProductNaam, Aantal, Prijs). Welke stap moet je als eerste nemen om deze tabel te normaliseren?
    a) Zoeken naar transitieve afhankelijkheden
    b) Controleren of er meerdere entiteiten zijn
    c) De primaire sleutel bepalen
    d) Berekende velden verwijderen

45. Gegeven de volgende functionele afhankelijkheden in een tabel:
    - OrderID → KlantID, Datum
    - KlantID → KlantNaam, KlantAdres
    - ProductID → ProductNaam, Prijs
    Waarom is deze tabel niet in 3NF?
    a) Omdat er geen primaire sleutel is
    b) Omdat er partiële afhankelijkheden zijn
    c) Omdat er transitieve afhankelijkheden zijn
    d) Omdat er repeterende groepen zijn

## Antwoorden

### Deel 1: Basisconcepten ERD
1. a) Entiteiten, attributen en relaties
2. b) Rechthoek
3. b) Een één-op-veel relatie
4. b) Een entiteit die afhankelijk is van een andere entiteit voor zijn bestaan
5. d) Nul-op-nul (0:0)
6. b) Om een veel-op-veel relatie om te zetten naar twee één-op-veel relaties
7. b) Conceptueel en logisch ontwerp
8. a) Een sleutel die uit meerdere attributen bestaat
9. b) Een logisch ERD bevat meer technische details dan een conceptueel ERD
10. c) Ze optimaliseren automatisch database queries
11. c) Een relatie waarbij meerdere records in tabel A gerelateerd zijn aan meerdere records in tabel B
12. c) Veel-op-veel (N:M)
13. a) Omdat de primaire sleutel niet uniek is
14. b) Het bevat vreemde sleutels (FK) om relaties te implementeren
15. c) Vreemde sleutels
16. b) Door een associatieve entiteit (junction table) te creëren
17. d) Recursieve relatie
18. b) Een relatie van een entiteit met zichzelf
19. c) Een recursieve relatie
20. b) draw.io
21. b) Het toevoegen van vreemde sleutels om relaties te implementeren

### Deel 2: Normalisatie en 3NF
22. b) Het proces van het organiseren van data om redundantie te verminderen
23. c) 2NF
24. a) Alle attributen atomair (ondeelbaar) zijn
25. b) De tabel in 1NF is en er geen partiële afhankelijkheden zijn
26. a) De tabel in 2NF is en er geen transitieve afhankelijkheden zijn
27. b) Wanneer een niet-sleutel attribuut afhankelijk is van een ander niet-sleutel attribuut
28. c) Transitieve afhankelijkheden tussen niet-sleutel attributen
29. d) Zowel b als c
30. b) Het kan leiden tot complexere queries met meer joins
31. b) Een tabel in 3NF heeft meestal meer tabellen dan een database in 1NF
32. d) Gegevens die op geen enkele manier genormaliseerd zijn
33. d) Zoeken naar niet-sleutelattributen die afhankelijk zijn van een ander niet-sleutelattribuut
34. a) De tabel moet in 1NF zijn
35. c) Zoeken naar niet-sleutelattributen die afhankelijk zijn van een ander niet-sleutelattribuut
36. d) Is de tabel in Boyce-Codd normaalvorm?
37. b) Het verminderen van redundantie en het voorkomen van anomalieën
38. c) Het identificeren van meerdere entiteiten

### Deel 3: Praktische Toepassingen
39. c) Drie tabellen: MEDEWERKER(MedewerkerID, Naam, AfdelingID), AFDELING(AfdelingID, AfdelingNaam, LocatieID) en LOCATIE(LocatieID, LocatieAdres)
40. c) Student en Cursus (studenten volgen meerdere cursussen, cursussen hebben meerdere studenten)
41. a) Boeken, Auteurs, Leden, Uitleningen
42. c) Voeg een vreemde sleutel toe aan één van de tabellen die verwijst naar de andere tabel
43. d) Alle bovenstaande
44. b) Controleren of er meerdere entiteiten zijn
45. c) Omdat er transitieve afhankelijkheden zijn
