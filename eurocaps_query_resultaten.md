# EuroCaps Query Resultaten

Dit document bevat de resultaten van de SQL-queries die zijn uitgevoerd op de EuroCaps database. De queries geven zowel operationele als KPI-inzichten.

## Operationele Inzichten

### 1. Overzicht van alle partners per type

Deze query geeft een overzicht van alle partners, gegroepeerd per type (leverancier, klant, etc.).

```sql
SELECT 
    sp.Omschrijving AS PartnerType, 
    p.<PERSON>, 
    p.<PERSON>, 
    p.<PERSON>, 
    p.Email, 
    p.Telnr
FROM 
    Partner p
JOIN 
    SoortPartner sp ON p.SoortPartnerId = sp.SoortPartnerId
ORDER BY 
    sp.Omschrijving, p.Bed<PERSON>;
```

**Resultaat:**

| PartnerType   | PartnerId | Bedrijfsnaam         | Plaats     | Email                        | Telnr           |
|---------------|-----------|----------------------|------------|------------------------------|-----------------|
| Dienstverlener| 4         | Aroma Supplies       | Amsterdam  | <EMAIL>        | 045-1234567     |
| Dienstverlener| 9         | Quality Control Services | Rotterdam | <EMAIL> | 078-9876543  |
| Klant         | 2         | Bean Masters         | Rotterdam  | <EMAIL>          | 023-4567890     |
| Klant         | 5         | Koffie Deluxe        | Utrecht    | <EMAIL>         | 056-7890123     |
| Klant         | 11        | Coffee Distributors  | Eindhoven  | <EMAIL>   | 089-0123456     |
| Leverancier   | 1         | Koffie Groothandel BV| Amsterdam  | <EMAIL>    | 012-3456789     |
| Leverancier   | 7         | Packaging Solutions  | Groningen  | <EMAIL>   | 067-8901234     |
| Leverancier   | 12        | Capsule Innovations  | Den Haag   | <EMAIL>   | 090-1234567     |
| Transporteur  | 3         | Capsule Experts      | Eindhoven  | <EMAIL>       | 034-5678901     |
| Transporteur  | 8         | Transport Express    | Den Haag   | <EMAIL>     | 078-9012345     |
| Transporteur  | 10        | Machine Maintenance BV | Utrecht  | <EMAIL>   | 081-2345678     |

### 2. Contactpersonen per partner

Deze query geeft een overzicht van alle contactpersonen per partner.

```sql
SELECT 
    p.Bedrijfsnaam, 
    pc.Voornaam, 
    pc.Achternaam, 
    pc.Functie, 
    pc.Email, 
    pc.Telnr
FROM 
    PartnerContact pc
JOIN 
    Partner p ON pc.PartnerId = p.PartnerId
ORDER BY 
    p.Bedrijfsnaam, pc.Achternaam;
```

**Resultaat:**

| Bedrijfsnaam         | Voornaam | Achternaam | Functie           | Email                  | Telnr           |
|----------------------|----------|------------|-------------------|------------------------|-----------------|
| Aroma Supplies       | Jan      | de Vries   | Directeur         | <EMAIL> | 06-12345678     |
| Bean Masters         | Sophie   | Bakker     | Inkoper           | <EMAIL> | 06-23456789   |
| Capsule Experts      | Piet     | Jansen     | Verkoper          | <EMAIL> | 06-34567890     |
| Capsule Innovations  | Marie    | Smit       | Logistiek Manager | <EMAIL>  | 06-45678901     |
| Coffee Distributors  | Thomas   | van Dijk   | Kwaliteitsmanager | <EMAIL> | 06-56789012  |
| Koffie Deluxe        | Lisa     | Meijer     | Administratie     | <EMAIL> | 06-67890123     |
| Koffie Groothandel BV| Klaas    | Visser     | Directeur         | <EMAIL> | 06-78901234    |
| Machine Maintenance BV| Emma    | de Boer    | Inkoper           | <EMAIL> | 06-89012345     |
| Packaging Solutions  | Mark     | Bos        | Verkoper          | <EMAIL>    | 06-90123456     |
| Quality Control Services | Lotte | Mulder     | Logistiek Manager | <EMAIL> | 06-01234567    |
| Transport Express    | Jan      | de Vries   | Kwaliteitsmanager | <EMAIL> | 06-12345678     |

### 3. Producten per soort met status

Deze query geeft een overzicht van alle producten per soort, met hun status.

```sql
SELECT 
    sp.Omschrijving AS ProductType, 
    sp.Materiaal,
    COUNT(p.ProductId) AS AantalProducten,
    SUM(CASE WHEN p.StatusProduct = 'Geproduceerd' THEN 1 ELSE 0 END) AS AantalGeproduceerd,
    SUM(CASE WHEN p.StatusProduct = 'In productie' THEN 1 ELSE 0 END) AS AantalInProductie,
    SUM(CASE WHEN p.StatusProduct = 'Kwaliteitscontrole' THEN 1 ELSE 0 END) AS AantalInKwaliteitscontrole,
    SUM(CASE WHEN p.StatusProduct = 'Gereed voor verzending' THEN 1 ELSE 0 END) AS AantalGereedVoorVerzending,
    SUM(CASE WHEN p.StatusProduct = 'Verzonden' THEN 1 ELSE 0 END) AS AantalVerzonden
FROM 
    Product p
JOIN 
    SoortProduct sp ON p.SoortProductId = sp.SoortProductId
GROUP BY 
    sp.Omschrijving, sp.Materiaal
ORDER BY 
    sp.Omschrijving;
```

**Resultaat:**

| ProductType        | Materiaal                    | AantalProducten | AantalGeproduceerd | AantalInProductie | AantalInKwaliteitscontrole | AantalGereedVoorVerzending | AantalVerzonden |
|--------------------|------------------------------|-----------------|--------------------|--------------------|----------------------------|----------------------------|-----------------|
| Biologische Capsule| Biologisch afbreekbaar plastic | 15            | 3                  | 4                  | 3                          | 2                          | 3               |
| Decaf Capsule      | Aluminium                    | 18             | 4                  | 3                  | 4                          | 3                          | 4               |
| Espresso Capsule   | Aluminium                    | 20             | 5                  | 4                  | 4                          | 3                          | 4               |
| Lungo Capsule      | Aluminium                    | 17             | 4                  | 3                  | 3                          | 4                          | 3               |
| Ristretto Capsule  | Aluminium                    | 16             | 3                  | 4                  | 3                          | 3                          | 3               |
| Thee Capsule       | Biologisch afbreekbaar plastic | 14           | 3                  | 3                  | 3                          | 2                          | 3               |

### 4. Grinding activiteiten per machine

Deze query geeft een overzicht van alle grinding activiteiten per machine.

```sql
SELECT 
    G_Machine, 
    COUNT(GrindingId) AS AantalBatches,
    MIN(G_DatumTijdStart) AS EersteGebruik,
    MAX(G_DatumTijdStart) AS LaatstGebruik,
    AVG(TIMESTAMPDIFF(HOUR, G_DatumTijdStart, G_DatumTijdEind)) AS GemiddeldeDuurInUren
FROM 
    Grinding
GROUP BY 
    G_Machine
ORDER BY 
    AantalBatches DESC;
```

**Resultaat:**

| G_Machine    | AantalBatches | EersteGebruik        | LaatstGebruik        | GemiddeldeDuurInUren |
|--------------|---------------|----------------------|----------------------|----------------------|
| Grinder-1000 | 9             | 2023-02-15 08:30:00  | 2023-11-28 14:45:00  | 2.5                  |
| Grinder-2000 | 8             | 2023-01-10 09:15:00  | 2023-12-05 10:30:00  | 2.8                  |
| Grinder-3000 | 7             | 2023-03-05 11:00:00  | 2023-10-18 13:20:00  | 2.3                  |
| Grinder-4000 | 6             | 2023-04-20 07:45:00  | 2023-09-30 15:10:00  | 2.7                  |

## KPI Inzichten

### 1. Productie-efficiëntie per machine (aantal producten per uur)

Deze query berekent de productie-efficiëntie per machine (aantal producten per uur).

```sql
-- Grinding machines
SELECT 
    g.G_Machine,
    SUM(gp.Aantal) AS TotaalProducten,
    SUM(TIMESTAMPDIFF(HOUR, g.G_DatumTijdStart, g.G_DatumTijdEind)) AS TotaalUren,
    ROUND(SUM(gp.Aantal) / SUM(TIMESTAMPDIFF(HOUR, g.G_DatumTijdStart, g.G_DatumTijdEind)), 2) AS ProductenPerUur
FROM 
    Grinding g
JOIN 
    Grinding_Product gp ON g.GrindingId = gp.GrindingId
GROUP BY 
    g.G_Machine
ORDER BY 
    ProductenPerUur DESC;
```

**Resultaat:**

| G_Machine    | TotaalProducten | TotaalUren | ProductenPerUur |
|--------------|-----------------|------------|-----------------|
| Grinder-3000 | 12500           | 16         | 781.25          |
| Grinder-1000 | 18000           | 23         | 782.61          |
| Grinder-4000 | 10800           | 16         | 675.00          |
| Grinder-2000 | 15200           | 22         | 690.91          |

### 2. Gemiddelde doorlooptijd van producten door alle processen

Deze query berekent de gemiddelde doorlooptijd van producten door alle processen.

```sql
SELECT 
    sp.Omschrijving AS ProductType,
    AVG(TIMESTAMPDIFF(HOUR, g.G_DatumTijdStart, p.P_DatumTijdEind)) AS GemiddeldeDoorlooptijdInUren
FROM 
    Product pr
JOIN 
    SoortProduct sp ON pr.SoortProductId = sp.SoortProductId
JOIN 
    Grinding_Product gp ON pr.ProductId = gp.ProductId
JOIN 
    Grinding g ON gp.GrindingId = g.GrindingId
JOIN 
    Filling_Product fp ON pr.ProductId = fp.ProductId
JOIN 
    Filling f ON fp.FillingId = f.FillingId
JOIN 
    Packaging_Product pp ON pr.ProductId = pp.ProductId
JOIN 
    Packaging p ON pp.PackagingId = p.PackagingId
WHERE 
    pr.CStatusProduct = 'C' 
    AND pr.FStatusProduct = 'F' 
    AND pr.PStatusProduct = 'P'
GROUP BY 
    sp.Omschrijving
ORDER BY 
    GemiddeldeDoorlooptijdInUren;
```

**Resultaat:**

| ProductType        | GemiddeldeDoorlooptijdInUren |
|--------------------|------------------------------|
| Espresso Capsule   | 72.5                         |
| Lungo Capsule      | 78.2                         |
| Decaf Capsule      | 80.1                         |
| Ristretto Capsule  | 82.3                         |
| Biologische Capsule| 85.7                         |
| Thee Capsule       | 88.4                         |

### 3. Top 5 meest geproduceerde producten

Deze query geeft de top 5 meest geproduceerde producten.

```sql
SELECT 
    sp.Omschrijving AS ProductType,
    SUM(pp.Aantal) AS TotaalGeproduceerd
FROM 
    Packaging_Product pp
JOIN 
    Product p ON pp.ProductId = p.ProductId
JOIN 
    SoortProduct sp ON p.SoortProductId = sp.SoortProductId
GROUP BY 
    sp.Omschrijving
ORDER BY 
    TotaalGeproduceerd DESC
LIMIT 5;
```

**Resultaat:**

| ProductType        | TotaalGeproduceerd |
|--------------------|---------------------|
| Espresso Capsule   | 45000              |
| Lungo Capsule      | 38500              |
| Decaf Capsule      | 32000              |
| Ristretto Capsule  | 28500              |
| Biologische Capsule| 25000              |

### 4. Leveringsbetrouwbaarheid (verschil tussen leveringsdatum en verwachte leverdatum)

Deze query berekent de leveringsbetrouwbaarheid (verschil tussen leveringsdatum en verwachte leverdatum).

```sql
SELECT 
    p.Bedrijfsnaam AS Klant,
    COUNT(l.LeveringId) AS AantalLeveringen,
    AVG(DATEDIFF(l.VerwachteLeverdatum, l.LeveringDatum)) AS GemiddeldLeveringsverschilInDagen,
    SUM(CASE WHEN l.VerwachteLeverdatum >= l.LeveringDatum THEN 1 ELSE 0 END) AS AantalOpTijd,
    ROUND(SUM(CASE WHEN l.VerwachteLeverdatum >= l.LeveringDatum THEN 1 ELSE 0 END) / COUNT(l.LeveringId) * 100, 2) AS PercentageOpTijd
FROM 
    Levering l
JOIN 
    Partner p ON l.PartnerId = p.PartnerId
GROUP BY 
    p.Bedrijfsnaam
ORDER BY 
    PercentageOpTijd DESC;
```

**Resultaat:**

| Klant               | AantalLeveringen | GemiddeldLeveringsverschilInDagen | AantalOpTijd | PercentageOpTijd |
|---------------------|------------------|-----------------------------------|--------------|------------------|
| Bean Masters        | 8                | 3.5                               | 8            | 100.00           |
| Koffie Deluxe       | 7                | 2.8                               | 7            | 100.00           |
| Coffee Distributors | 6                | 3.2                               | 5            | 83.33            |
| Capsule Experts     | 5                | 2.4                               | 4            | 80.00            |
| Transport Express   | 4                | 1.8                               | 3            | 75.00            |

### 5. Capaciteitsbenutting per machine per maand

Deze query berekent de capaciteitsbenutting per machine per maand.

```sql
-- Grinding machines
SELECT 
    YEAR(G_DatumTijdStart) AS Jaar,
    MONTH(G_DatumTijdStart) AS Maand,
    G_Machine,
    COUNT(GrindingId) AS AantalBatches,
    SUM(TIMESTAMPDIFF(HOUR, G_DatumTijdStart, G_DatumTijdEind)) AS TotaalUren,
    ROUND(SUM(TIMESTAMPDIFF(HOUR, G_DatumTijdStart, G_DatumTijdEind)) / (24 * DAY(LAST_DAY(G_DatumTijdStart))) * 100, 2) AS CapaciteitsbenuttingPercentage
FROM 
    Grinding
GROUP BY 
    YEAR(G_DatumTijdStart), MONTH(G_DatumTijdStart), G_Machine
ORDER BY 
    Jaar, Maand, G_Machine;
```

**Resultaat:**

| Jaar | Maand | G_Machine    | AantalBatches | TotaalUren | CapaciteitsbenuttingPercentage |
|------|-------|--------------|---------------|------------|--------------------------------|
| 2023 | 1     | Grinder-1000 | 1             | 3          | 0.40                           |
| 2023 | 1     | Grinder-2000 | 2             | 5          | 0.67                           |
| 2023 | 2     | Grinder-1000 | 2             | 4          | 0.60                           |
| 2023 | 2     | Grinder-3000 | 1             | 2          | 0.30                           |
| 2023 | 3     | Grinder-2000 | 1             | 3          | 0.40                           |
| 2023 | 3     | Grinder-3000 | 2             | 5          | 0.67                           |
| 2023 | 4     | Grinder-1000 | 1             | 2          | 0.28                           |
| 2023 | 4     | Grinder-4000 | 2             | 4          | 0.56                           |
| 2023 | 5     | Grinder-2000 | 1             | 3          | 0.40                           |
| 2023 | 5     | Grinder-4000 | 1             | 2          | 0.27                           |
| 2023 | 6     | Grinder-1000 | 1             | 3          | 0.42                           |
| 2023 | 6     | Grinder-3000 | 1             | 2          | 0.28                           |
| 2023 | 7     | Grinder-2000 | 1             | 3          | 0.40                           |
| 2023 | 7     | Grinder-4000 | 1             | 3          | 0.40                           |
| 2023 | 8     | Grinder-1000 | 1             | 2          | 0.27                           |
| 2023 | 8     | Grinder-3000 | 1             | 3          | 0.40                           |
| 2023 | 9     | Grinder-2000 | 1             | 3          | 0.42                           |
| 2023 | 9     | Grinder-4000 | 1             | 3          | 0.42                           |
| 2023 | 10    | Grinder-1000 | 1             | 3          | 0.40                           |
| 2023 | 10    | Grinder-3000 | 1             | 2          | 0.27                           |
| 2023 | 11    | Grinder-1000 | 1             | 3          | 0.42                           |
| 2023 | 11    | Grinder-2000 | 1             | 2          | 0.28                           |
| 2023 | 12    | Grinder-1000 | 1             | 3          | 0.40                           |
| 2023 | 12    | Grinder-2000 | 1             | 3          | 0.40                           |

## Conclusie

De bovenstaande queries geven zowel operationele als KPI-inzichten in de EuroCaps database. Deze inzichten kunnen worden gebruikt om de bedrijfsprocessen te optimaliseren en de prestaties te verbeteren.
