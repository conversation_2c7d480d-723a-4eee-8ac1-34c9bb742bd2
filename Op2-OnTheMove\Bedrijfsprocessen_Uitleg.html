<!DOCTYPE html>
<html>
<head>
    <title>Bedrijfsprocessen Uitleg</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #0066cc;
            margin-top: 30px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        h3 {
            color: #009933;
        }
        .diagram {
            background-color: #f5f5f5;
            padding: 15px;
            border: 1px solid #ddd;
            margin: 15px 0;
            font-family: monospace;
            white-space: pre;
        }
        .explanation {
            margin-left: 20px;
        }
        .note {
            background-color: #ffffcc;
            padding: 10px;
            border-left: 4px solid #ffcc00;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>Bedrijfsprocessen: Uitleg en Visualisaties</h1>
    <p>Gebaseerd op het boek van Peter Thuis & <PERSON><PERSON><PERSON>uive (3e druk) en de slides van Bedrijfskunde Lesweek 2.1 t/m 2.7.</p>

    <h2>1. Basisconcepten Processen</h2>
    <p>Een proces is een reeks van logisch samenhangende activiteiten om een bepaald doel te bereiken. Processen transformeren inputs naar outputs die waarde hebben voor klanten.</p>

    <h3>Input-Throughput-Output (ITO) model</h3>
    <div class="diagram">
[INPUT] -----> [THROUGHPUT] -----> [OUTPUT]
   ^                                  |
   |                                  |
   +----------------------------------+
              Feedback
    </div>
    <div class="explanation">
        <p><strong>Input:</strong> Grondstoffen, arbeid, informatie, kapitaal, energie die het proces ingaan</p>
        <p><strong>Throughput:</strong> Het transformatieproces waarin de inputs worden omgezet in outputs</p>
        <p><strong>Output:</strong> De resultaten van het proces, zoals producten, diensten of informatie</p>
        <p><strong>Feedback:</strong> Informatie over de output die wordt gebruikt om het proces te verbeteren</p>
    </div>
    <p>Het ITO-model is fundamenteel voor het begrijpen van processen. Elk proces, of het nu een productieproces, een dienstverleningsproces of een informatieverwerkingsproces is, kan worden beschreven in termen van inputs, throughput en outputs.</p>

    <h2>2. Types Bedrijfsprocessen</h2>
    <p>Bedrijfsprocessen worden onderverdeeld in drie hoofdcategorieën:</p>

    <h3>Visualisatie als piramide</h3>
    <div class="diagram">
                /\
               /  \
              /    \
             /      \
            /Primaire\
           /Processen \
          /------------\
         /Ondersteunende\
        /   Processen    \
       /------------------\
      /  Managementprocessen \
     /------------------------\
    </div>

    <h3>Visualisatie als concentrische cirkels</h3>
    <div class="diagram">
    +------------------------------+
    |      Managementprocessen     |
    |  +------------------------+  |
    |  |  Ondersteunende        |  |
    |  |  Processen             |  |
    |  |  +------------------+  |  |
    |  |  |                  |  |  |
    |  |  |     Primaire     |  |  |
    |  |  |    Processen     |  |  |
    |  |  |                  |  |  |
    |  |  +------------------+  |  |
    |  +------------------------+  |
    +------------------------------+
    </div>

    <div class="explanation">
        <p><strong>Primaire processen:</strong> Deze processen zijn direct gericht op het creëren van producten of diensten voor externe klanten.</p>
        <ul>
            <li>Inkoop</li>
            <li>Productie</li>
            <li>Verkoop</li>
            <li>Distributie</li>
            <li>Klantenservice</li>
        </ul>

        <p><strong>Ondersteunende processen:</strong> Deze processen ondersteunen de primaire processen maar dragen niet direct bij aan de waardecreatie voor externe klanten.</p>
        <ul>
            <li>Personeelsbeheer (HRM)</li>
            <li>Financiële administratie</li>
            <li>IT-ondersteuning</li>
            <li>Facilitaire diensten</li>
            <li>Juridische zaken</li>
        </ul>

        <p><strong>Managementprocessen:</strong> Deze processen zijn gericht op het plannen, organiseren, leiden en controleren van de organisatie.</p>
        <ul>
            <li>Strategische planning</li>
            <li>Budgettering</li>
            <li>Prestatiemanagement</li>
            <li>Risicomanagement</li>
            <li>Kwaliteitsmanagement</li>
        </ul>
    </div>

    <h2>3. Primaire Processen</h2>

    <h3>Inkoopproces</h3>
    <div class="diagram">
(Start) --> [Specificeren van behoeften] --> [Selecteren van leveranciers] --> 
[Onderhandelen en contracteren] --> [Bestellen] --> [Bewaken van leveringen] --> 
[Nazorg en evaluatie] --> (Einde)
    </div>
    <div class="explanation">
        <ol>
            <li><strong>Specificeren van behoeften:</strong> Bepalen wat er nodig is, in welke hoeveelheden en met welke specificaties</li>
            <li><strong>Selecteren van leveranciers:</strong> Zoeken naar geschikte leveranciers en deze evalueren</li>
            <li><strong>Onderhandelen en contracteren:</strong> Afspraken maken over prijs, kwaliteit, levertijd, etc.</li>
            <li><strong>Bestellen:</strong> Plaatsen van de bestelling bij de geselecteerde leverancier</li>
            <li><strong>Bewaken van leveringen:</strong> Monitoren van de levering en controleren of deze voldoet aan de afspraken</li>
            <li><strong>Nazorg en evaluatie:</strong> Evalueren van de leverancier en het inkoopproces</li>
        </ol>
    </div>

    <h3>Productieproces</h3>
    <div class="diagram">
(Start) --> [Productieplanning] --> [Werkvoorbereiding] --> [Productie-uitvoering] --> 
[Kwaliteitscontrole] --> [Verpakking] --> [Opslag] --> (Einde)
    </div>
    <div class="explanation">
        <ol>
            <li><strong>Productieplanning:</strong> Plannen van de productie op basis van vraag en capaciteit</li>
            <li><strong>Werkvoorbereiding:</strong> Voorbereiden van de productie, zoals het klaarzetten van materialen en instellen van machines</li>
            <li><strong>Productie-uitvoering:</strong> Uitvoeren van de productieactiviteiten</li>
            <li><strong>Kwaliteitscontrole:</strong> Controleren of de producten voldoen aan de kwaliteitseisen</li>
            <li><strong>Verpakking:</strong> Verpakken van de producten voor opslag of verzending</li>
            <li><strong>Opslag:</strong> Opslaan van de producten in het magazijn</li>
        </ol>
    </div>

    <h3>Verkoopproces</h3>
    <div class="diagram">
(Start) --> [Prospectie] --> [Voorbereiding] --> [Benadering] --> [Presentatie] --> 
[Behandelen bezwaren] --> [Afsluiting] --> [Follow-up] --> (Einde)
    </div>
    <div class="explanation">
        <ol>
            <li><strong>Prospectie:</strong> Zoeken naar potentiële klanten</li>
            <li><strong>Voorbereiding:</strong> Verzamelen van informatie over de klant en voorbereiden van de verkooppresentatie</li>
            <li><strong>Benadering:</strong> Eerste contact met de potentiële klant</li>
            <li><strong>Presentatie:</strong> Demonstratie van het product of de dienst</li>
            <li><strong>Behandelen bezwaren:</strong> Ingaan op vragen en bezwaren van de klant</li>
            <li><strong>Afsluiting:</strong> Afsluiten van de verkoop</li>
            <li><strong>Follow-up:</strong> Nazorg en onderhouden van de klantrelatie</li>
        </ol>
    </div>

    <h2>4. Procesvisualisatie</h2>

    <h3>Processtroomdiagram (Flowchart)</h3>
    <p>Een processtroomdiagram gebruikt verschillende symbolen om de stappen in een proces weer te geven:</p>
    <div class="explanation">
        <ul>
            <li><strong>Ovaal:</strong> Start/Einde</li>
            <li><strong>Rechthoek:</strong> Activiteit/Taak</li>
            <li><strong>Ruit:</strong> Beslissing</li>
            <li><strong>Pijlen:</strong> Stroomrichting</li>
        </ul>
    </div>
    <div class="diagram">
[Controleer voorraad] --> <Is product op voorraad?> 
                           /       \
                         Ja        Nee
                         /           \
                        v             v
               [Verzend product]  [Plaats bestelling]
    </div>

    <h3>Swimlane-diagram</h3>
    <p>Een swimlane-diagram is een type processtroomdiagram dat laat zien welke afdeling of persoon verantwoordelijk is voor elke stap in het proces.</p>
    <div class="diagram">
+----------------+----------------+----------------+----------------+
|    Verkoop     |    Magazijn    |    Logistiek   |    Financiën   |
+----------------+----------------+----------------+----------------+
| [Ontvang       |                |                |                |
|  bestelling]   |                |                |                |
|       |        |                |                |                |
|       v        |                |                |                |
| [Controleer    |                |                |                |
|  bestelling]   |                |                |                |
|       |        |                |                |                |
|       v        |                |                |                |
| [Bevestig      |                |                |                |
|  bestelling]   |                |                |                |
|       |        |                |                |                |
|       v        |                |                |                |
|                | [Controleer    |                |                |
|                |  voorraad]     |                |                |
|                |       |        |                |                |
|                |       v        |                |                |
|                | [Verzamel      |                |                |
|                |  producten]    |                |                |
|                |       |        |                |                |
|                |       v        |                |                |
|                |                | [Verpak        |                |
|                |                |  producten]    |                |
|                |                |       |        |                |
|                |                |       v        |                |
|                |                | [Verzend       |                |
|                |                |  pakket]       |                |
|                |                |       |        |                |
|                |                |       v        |                |
|                |                |                | [Verwerk       |
|                |                |                |  betaling]     |
|                |                |                |       |        |
|                |                |                |       v        |
|                |                |                | [Maak factuur] |
+----------------+----------------+----------------+----------------+
    </div>

    <h2>5. Procesmanagement</h2>

    <h3>Plan-Do-Check-Act (PDCA) cyclus</h3>
    <div class="diagram">
        +-------+
        |       |
        | PLAN  |
        |       |
+-------+-------+-------+
|       |               |
| ACT   |       DO      |
|       |               |
+-------+-------+-------+
        |       |
        | CHECK |
        |       |
        +-------+
    </div>
    <div class="explanation">
        <p><strong>Plan:</strong> Doelen stellen en processen plannen</p>
        <ul>
            <li>Identificeer het probleem of de verbetermogelijkheid</li>
            <li>Analyseer de huidige situatie</li>
            <li>Bepaal de oorzaken van het probleem</li>
            <li>Ontwikkel een verbeterplan</li>
        </ul>

        <p><strong>Do:</strong> Processen uitvoeren</p>
        <ul>
            <li>Implementeer het plan op kleine schaal</li>
            <li>Verzamel data over de resultaten</li>
            <li>Documenteer problemen en onverwachte gebeurtenissen</li>
        </ul>

        <p><strong>Check:</strong> Resultaten meten en analyseren</p>
        <ul>
            <li>Vergelijk de resultaten met de doelen</li>
            <li>Bepaal of het plan succesvol was</li>
            <li>Identificeer wat goed ging en wat niet</li>
        </ul>

        <p><strong>Act:</strong> Processen verbeteren</p>
        <ul>
            <li>Standaardiseer succesvolle verbeteringen</li>
            <li>Pas het plan aan op basis van de resultaten</li>
            <li>Bepaal volgende stappen</li>
            <li>Begin een nieuwe PDCA-cyclus</li>
        </ul>
    </div>

    <h3>Regelkring</h3>
    <div class="diagram">
[Norm/Standaard] <---- [Vergelijken] <---- [Meten]
      ^                     |                ^
      |                     v                |
      |                [Bijsturen] --------> |
      |                     |                |
      v                     v                |
[Proces input] --------> [Proces] --------> [Proces output]
    </div>
    <div class="explanation">
        <p><strong>Meten:</strong> Het meten van de procesoutput of prestaties</p>
        <p><strong>Vergelijken:</strong> Het vergelijken van de gemeten waarden met een norm of standaard</p>
        <p><strong>Bijsturen:</strong> Het aanpassen van de procesinput of -parameters als er afwijkingen zijn</p>
        <p><strong>Proces:</strong> Het uitvoeren van het proces met de aangepaste parameters</p>
    </div>

    <h2>6. Organisatiestructuren</h2>

    <h3>Functionele organisatie (hiërarchisch)</h3>
    <div class="diagram">
                  +-------+
                  |  CEO  |
                  +-------+
                      |
        +-------------+-------------+
        |             |             |
    +-------+     +-------+     +-------+
    |Productie|   |Verkoop |   |Financiën|
    +-------+     +-------+     +-------+
    </div>

    <h3>Procesgerichte organisatie</h3>
    <div class="diagram">
                  +-------+
                  |  CEO  |
                  +-------+
                      |
        +-------------+-------------+
        |             |             |
    +-------+     +-------+     +-------+
    |Proces A|    |Proces B|    |Proces C|
    +-------+     +-------+     +-------+
    </div>

    <h3>Matrixorganisatie</h3>
    <div class="diagram">
                  +-------+
                  |  CEO  |
                  +-------+
                      |
        +-------------+-------------+
        |             |             |
    +-------+     +-------+     +-------+
    |Functie A|   |Functie B|   |Functie C|
    +-------+     +-------+     +-------+
        |             |             |
    +-------+     +-------+     +-------+
    |Proces 1 |   |Proces 2 |   |Proces 3 |
    +-------+     +-------+     +-------+
    </div>
    <div class="explanation">
        <p><strong>Functionele organisatie:</strong> Georganiseerd rond functies/afdelingen. Processen lopen door verschillende afdelingen, wat coördinatie tussen afdelingen vereist.</p>
        <p><strong>Procesgerichte organisatie:</strong> Georganiseerd rond processen. Teams zijn verantwoordelijk voor complete processen, wat leidt tot betere afstemming op klantbehoeften.</p>
        <p><strong>Matrixorganisatie:</strong> Combinatie van functionele en procesgerichte structuur. Medewerkers rapporteren aan zowel een functionele manager als een procesmanager, wat kan leiden tot dubbele aansturing.</p>
    </div>

    <h2>7. Informatiesystemen</h2>
    <div class="diagram">
                  +-------+
                  |  ERP  |
                  +-------+
                      |
        +-------------+-------------+
        |             |             |
    +-------+     +-------+     +-------+
    |  CRM  |     |  SCM  |     |  BI   |
    +-------+     +-------+     +-------+
    </div>
    <div class="explanation">
        <p><strong>Enterprise Resource Planning (ERP):</strong> Geïntegreerd systeem voor bedrijfsprocessen, met modules voor verschillende functionele gebieden en een gemeenschappelijke database.</p>
        <p><strong>Customer Relationship Management (CRM):</strong> Systeem voor het beheer van klantinteracties, ondersteuning van verkoopprocessen, en klantservice.</p>
        <p><strong>Supply Chain Management (SCM):</strong> Systeem voor het beheer van de goederenstroom, planning en forecasting, en leveranciersmanagement.</p>
        <p><strong>Business Intelligence (BI):</strong> Systeem voor de analyse van bedrijfsgegevens, dashboards en rapportages, en ondersteuning van besluitvorming.</p>
    </div>

    <div class="note">
        <p><strong>Opmerking:</strong> Om dit document als PDF op te slaan, open het in een browser en gebruik de functie "Afdrukken" (Ctrl+P). Kies vervolgens "Opslaan als PDF" als printer.</p>
    </div>

    <h2>Bronnen</h2>
    <ul>
        <li>Thuis, P., & Stuive, R. (2020). Bedrijfskunde Integraal (3e druk). Noordhoff Uitgevers.</li>
        <li>Bedrijfskunde Lesweek 2.1 t/m 2.7 (PDF documenten)</li>
    </ul>
</body>
</html>
