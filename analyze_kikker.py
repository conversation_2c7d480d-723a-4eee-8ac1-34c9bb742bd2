import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

def load_and_analyze_kikker():
    """
    Function to load, clean, and analyze the Kikker.csv dataset for Americaps coffee capsule production.
    This function performs data exploration and quality checks.
    """
    print("=" * 80)
    print("AMERICAPS DATA ANALYSIS - KIKKER.CSV")
    print("=" * 80)
    
    # 1. Import the CSV file
    print("\n1. Importing CSV file...")
    try:
        df = pd.read_csv('Kikker.csv', sep=',')
        print(f"Successfully loaded dataset with {df.shape[0]} rows and {df.shape[1]} columns.")
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        return
    
    # 2. Display basic information about the dataset
    print("\n2. Basic dataset information:")
    print(f"\nDataset dimensions: {df.shape[0]} rows x {df.shape[1]} columns")
    
    print("\nColumn names:")
    for i, col in enumerate(df.columns, 1):
        print(f"{i:2d}. {col}")
    
    # 3. Data quality assessment
    print("\n3. Data quality assessment:")
    
    # Check for missing values
    missing_values = df.isnull().sum()
    missing_percent = (missing_values / len(df)) * 100
    
    print("\nColumns with missing values:")
    missing_info = pd.DataFrame({
        'Missing Values': missing_values[missing_values > 0],
        'Percentage': missing_percent[missing_values > 0].round(2)
    }).sort_values('Missing Values', ascending=False)
    
    print(missing_info)
    
    # Check for unusual values in key columns
    print("\nUnusual values in key columns:")
    
    # Check date columns
    date_columns = [col for col in df.columns if 'Datum' in col or 'datum' in col]
    for col in date_columns:
        invalid_dates = df[~pd.to_datetime(df[col], errors='coerce').notna()][col].unique()
        if len(invalid_dates) > 0:
            print(f"\n- Column '{col}' contains invalid date formats:")
            for i, val in enumerate(invalid_dates[:5], 1):
                print(f"  {i}. '{val}'")
            if len(invalid_dates) > 5:
                print(f"  ... and {len(invalid_dates) - 5} more")
    
    # Check percentage columns
    percentage_cols = [col for col in df.columns if 'percentage' in col.lower()]
    for col in percentage_cols:
        print(f"\n- Column '{col}' values:")
        # Extract unique values that don't follow the expected pattern
        unusual = [val for val in df[col].unique() if not (isinstance(val, str) and val.endswith('%') and val.replace('%', '').replace('.', '').isdigit())]
        if unusual:
            print(f"  Contains {len(unusual)} unusual values (examples: {unusual[:3]})")
    
    # Check numeric columns with potential issues
    numeric_issue_cols = ['Leveranciersbeoordeling', 'Fair-Trade Score', 'Voorraadniveaus', 'Cost', 'Energieverbruik']
    for col in numeric_issue_cols:
        print(f"\n- Column '{col}' values:")
        try:
            if col in ['Voorraadniveaus', 'Cost', 'Energieverbruik']:
                # These are stored as strings but should be numeric
                unusual = [val for val in df[col].unique() if isinstance(val, str) and not val.replace('.', '').replace(' ', '').replace('-', '').isdigit()]
                if unusual:
                    print(f"  Contains non-numeric values (examples: {unusual[:3]})")
            else:
                # These are already numeric
                if df[col].min() < 0:
                    print(f"  Contains negative values (min: {df[col].min()})")
                if df[col].max() > 100 and 'Score' in col:
                    print(f"  Contains unusually high scores (max: {df[col].max()})")
        except:
            print(f"  Could not analyze column due to data type issues")
    
    # 4. Column meaning and data types
    print("\n4. Column meaning and data types:")
    
    column_meanings = {
        'PackagingApparaat': 'Packaging machine used',
        'Herkomst': 'Origin of coffee beans',
        'Risicokwalificatie': 'Risk qualification',
        'Koffieboon': 'Type of coffee bean',
        'Panel Test': 'Result of panel testing',
        'Klantretourpercentage': 'Customer return percentage',
        'Registratiedatum': 'Registration date',
        'FillingDatumTijdEind': 'End date/time of filling process',
        'PackagingDatumTijdEind': 'End date/time of packaging process',
        'FillingApparaat': 'Filling machine used',
        'Cost': 'Production cost',
        'Voorraadniveaus': 'Inventory levels',
        'FillingDatumTijdStart': 'Start date/time of filling process',
        'Benuttingsgraad': 'Utilization rate',
        'GrindingDatumTijdEind': 'End date/time of grinding process',
        'Cyclustijd': 'Cycle time',
        'GrindingDatumTijdStart': 'Start date/time of grinding process',
        'LeverancierLevertijd': 'Supplier delivery time',
        'Leveranciersbeoordeling': 'Supplier rating',
        'Roosterprofiel': 'Roasting profile',
        'Klanttevredenheid': 'Customer satisfaction',
        'Gewichtscontrole': 'Weight control',
        'FillingID': 'Filling process ID',
        'Duurzaamheid Score': 'Sustainability score',
        'PackagingDatumTijdStart': 'Start date/time of packaging process',
        'Audit van Leverancier': 'Supplier audit result',
        'Batchnr': 'Batch number',
        'CO2-Footprint': 'CO2 footprint',
        'Opmerkingen': 'Comments',
        'ProcessTime': 'Total process time',
        'Fair-Trade Score': 'Fair trade score',
        'Defectpercentage': 'Defect percentage',
        'Energieverbruik': 'Energy consumption',
        'GrindingApparaat': 'Grinding machine used',
        'PackagingID': 'Packaging process ID',
        'Laatste Audit': 'Date of last audit',
        'GrindingID': 'Grinding process ID'
    }
    
    for col, meaning in column_meanings.items():
        if col in df.columns:
            dtype = str(df[col].dtype)
            unique_count = df[col].nunique()
            print(f"- {col}: {meaning} (Type: {dtype}, Unique values: {unique_count})")
    
    # 5. Key statistics for important columns
    print("\n5. Key statistics for important columns:")
    
    # Machine usage statistics
    print("\nMachine usage statistics:")
    for machine_col in ['PackagingApparaat', 'FillingApparaat', 'GrindingApparaat']:
        if machine_col in df.columns:
            print(f"\n{machine_col} distribution:")
            machine_counts = df[machine_col].value_counts().reset_index()
            machine_counts.columns = [machine_col, 'Count']
            print(machine_counts)
    
    # Coffee bean types
    if 'Koffieboon' in df.columns:
        print("\nCoffee bean type distribution:")
        bean_counts = df['Koffieboon'].value_counts().reset_index()
        bean_counts.columns = ['Bean Type', 'Count']
        print(bean_counts)
    
    # Quality indicators
    quality_cols = ['Panel Test', 'Defectpercentage', 'Klantretourpercentage', 'Klanttevredenheid']
    print("\nQuality indicators:")
    for col in quality_cols:
        if col in df.columns:
            print(f"\n{col} distribution:")
            if col in ['Defectpercentage', 'Klantretourpercentage']:
                # These are percentages stored as strings
                print(df[col].value_counts().head(10).reset_index())
            else:
                print(df[col].value_counts().head(10).reset_index())
    
    # 6. Summary of findings
    print("\n6. Summary of findings:")
    print(f"- Dataset contains {df.shape[0]} rows and {df.shape[1]} columns")
    print(f"- {missing_values.sum()} total missing values across all columns")
    print(f"- Several date columns contain invalid formats like 'onbekend', '31-02-2025 25:61:61'")
    print(f"- Some numeric columns contain negative values or unusually high values")
    print(f"- The dataset includes information about coffee production processes including grinding, filling, and packaging")
    print(f"- Quality indicators like defect percentage and customer return percentage are available for analysis")
    
    return df

if __name__ == "__main__":
    df = load_and_analyze_kikker()
    print("\nData analysis complete.")
