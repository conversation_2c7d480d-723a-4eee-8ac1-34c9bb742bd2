<mxfile host="app.diagrams.net" modified="2023-11-15T14:23:12.742Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" etag="8jLHDJZYYYYYYYYYYYYY" version="22.1.1" type="device">
  <diagram name="Page-1" id="c7558073-3199-34d8-9f00-42111426c3f3">
    <mxGraphModel dx="1050" dy="542" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="826" pageHeight="1169" background="none" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="&lt;b&gt;<PERSON>te<PERSON><PERSON> van HACCP-principes in Six Sigma voor Euro Caps&lt;/b&gt;" style="swimlane;whiteSpace=wrap;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="760" height="500" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Grondstoffen" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1" parent="2" vertex="1">
          <mxGeometry x="40" y="60" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="4" value="Maalproces" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1" parent="2" vertex="1">
          <mxGeometry x="200" y="60" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="5" value="Vulproces" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1" parent="2" vertex="1">
          <mxGeometry x="360" y="60" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6" value="Verpakkingsproces" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1" parent="2" vertex="1">
          <mxGeometry x="520" y="60" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="7" value="Eindproduct" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1" parent="2" vertex="1">
          <mxGeometry x="680" y="60" width="40" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#82b366;fillColor=#d5e8d4;" parent="2" source="3" target="4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="170" y="90" as="sourcePoint" />
            <mxPoint x="220" y="40" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="9" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#82b366;fillColor=#d5e8d4;" parent="2" source="4" target="5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="330" y="90" as="sourcePoint" />
            <mxPoint x="380" y="40" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="10" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#82b366;fillColor=#d5e8d4;" parent="2" source="5" target="6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="490" y="90" as="sourcePoint" />
            <mxPoint x="540" y="40" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="11" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#82b366;fillColor=#d5e8d4;" parent="2" source="6" target="7" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="650" y="90" as="sourcePoint" />
            <mxPoint x="700" y="40" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="12" value="CCP1: Temperatuurcontrole" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;fontStyle=1" parent="2" vertex="1">
          <mxGeometry x="200" y="140" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="13" value="CCP2: Metaaldetectie" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;fontStyle=1" parent="2" vertex="1">
          <mxGeometry x="200" y="190" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="14" value="CCP3: Afdichtingsintegriteit" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;fontStyle=1" parent="2" vertex="1">
          <mxGeometry x="520" y="140" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="15" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#d79b00;fillColor=#ffe6cc;" parent="2" source="4" target="12" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="260" y="130" as="sourcePoint" />
            <mxPoint x="310" y="80" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="16" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#d79b00;fillColor=#ffe6cc;" parent="2" source="12" target="13" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="260" y="190" as="sourcePoint" />
            <mxPoint x="310" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="17" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=2;strokeColor=#d79b00;fillColor=#ffe6cc;" parent="2" source="6" target="14" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="580" y="130" as="sourcePoint" />
            <mxPoint x="630" y="80" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="18" value="Six Sigma DMAIC" style="swimlane;whiteSpace=wrap;fillColor=#f5f5f5;strokeColor=#666666;fontSize=14;fontStyle=1;fontColor=#333333" parent="2" vertex="1">
          <mxGeometry x="40" y="260" width="320" height="220" as="geometry" />
        </mxCell>
        <mxCell id="19" value="Define" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontStyle=1" parent="18" vertex="1">
          <mxGeometry x="20" y="40" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="20" value="Measure" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontStyle=1" parent="18" vertex="1">
          <mxGeometry x="20" y="80" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="21" value="Analyze" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontStyle=1" parent="18" vertex="1">
          <mxGeometry x="20" y="120" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="22" value="Improve" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontStyle=1" parent="18" vertex="1">
          <mxGeometry x="20" y="160" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="23" value="Control" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontStyle=1" parent="18" vertex="1">
          <mxGeometry x="20" y="200" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="24" value="Projectdefinitie, scope, doelstellingen" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="18" vertex="1">
          <mxGeometry x="150" y="40" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="25" value="Dataverzameling, baseline prestatie" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="18" vertex="1">
          <mxGeometry x="150" y="80" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="26" value="Root cause analyse, Pareto-analyse" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="18" vertex="1">
          <mxGeometry x="150" y="120" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="27" value="Procesaanpassingen, optimalisatie" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="18" vertex="1">
          <mxGeometry x="150" y="160" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="28" value="SPC, standaardisatie, monitoring" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="18" vertex="1">
          <mxGeometry x="150" y="200" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="29" value="HACCP Principes" style="swimlane;whiteSpace=wrap;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1" parent="2" vertex="1">
          <mxGeometry x="400" y="260" width="320" height="220" as="geometry" />
        </mxCell>
        <mxCell id="30" value="1. Gevarenanalyse" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;fontStyle=1" parent="29" vertex="1">
          <mxGeometry x="20" y="40" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="31" value="2. Kritische controlepunten (CCPs)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;fontStyle=1" parent="29" vertex="1">
          <mxGeometry x="20" y="80" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="32" value="3. Kritische grenzen" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;fontStyle=1" parent="29" vertex="1">
          <mxGeometry x="20" y="120" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="33" value="4. Monitoring" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;fontStyle=1" parent="29" vertex="1">
          <mxGeometry x="20" y="160" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="34" value="5. Corrigerende maatregelen" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=12;fontStyle=1" parent="29" vertex="1">
          <mxGeometry x="20" y="200" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="35" value="Fysieke, chemische, biologische gevaren" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="29" vertex="1">
          <mxGeometry x="150" y="40" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="36" value="CCP1: Temperatuur, CCP2: Metaaldetectie, CCP3: Afdichting" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="29" vertex="1">
          <mxGeometry x="150" y="80" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="37" value="Temperatuur &lt; 60°C, Geen metaaldeeltjes &gt; 0.5mm, 100% intacte afdichting" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="29" vertex="1">
          <mxGeometry x="150" y="120" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="38" value="Continue temperatuurregistratie, metaaldetectie, steekproeven afdichting" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="29" vertex="1">
          <mxGeometry x="150" y="160" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="39" value="Productie stoppen, product isoleren, apparatuur aanpassen" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="29" vertex="1">
          <mxGeometry x="150" y="200" width="160" height="30" as="geometry" />
        </mxCell>
        <mxCell id="40" value="" style="shape=flexArrow;endArrow=classic;startArrow=classic;html=1;rounded=0;width=10;startSize=4.67;endSize=4.67;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="2" edge="1">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="370" y="370" as="sourcePoint" />
            <mxPoint x="390" y="370" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="41" value="Kritische Grenzen voor CCPs" style="swimlane;whiteSpace=wrap;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1" parent="2" vertex="1">
          <mxGeometry x="40" y="160" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="42" value="- Temperatuur &lt; 60°C&#xa;- Geen metaal &gt; 0.5mm&#xa;- 100% intacte afdichting" style="text;spacingTop=-5;whiteSpace=wrap;align=left;fontSize=10" parent="41" vertex="1">
          <mxGeometry x="10" y="30" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="43" value="Monitoring Procedures" style="swimlane;whiteSpace=wrap;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1" parent="2" vertex="1">
          <mxGeometry x="360" y="160" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="44" value="- Continue registratie&#xa;- 100% metaaldetectie&#xa;- Steekproeven afdichting" style="text;spacingTop=-5;whiteSpace=wrap;align=left;fontSize=10" parent="43" vertex="1">
          <mxGeometry x="10" y="30" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="45" value="Corrigerende Maatregelen" style="swimlane;whiteSpace=wrap;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1" parent="2" vertex="1">
          <mxGeometry x="680" y="160" width="40" height="80" as="geometry" />
        </mxCell>
        <mxCell id="46" value="Gedefinieerde acties bij afwijkingen" style="text;spacingTop=-5;whiteSpace=wrap;align=left;fontSize=8" parent="45" vertex="1">
          <mxGeometry x="5" y="30" width="30" height="40" as="geometry" />
        </mxCell>
        <mxCell id="47" value="&quot;Quality. Every Single Time.&quot;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=3" parent="2" vertex="1">
          <mxGeometry x="280" y="230" width="200" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
