====================== PYTHON SPIEKBRIEF (PAGINA 1) ======================

# PYTHON BASICS
## Variabelen en Datatypes
- Variabelen: naam = waarde (geen declaratie nodig)
- Naamgeving: letters, cijfers, underscore (_), start niet met cijfer
- Hoofdlettergevoelig: naam ≠ Naam
- Datatypes:
  * int: gehele getallen (42, -7)
  * float: kommagetallen (3.14, -0.001)
  * str: tekst ("Hallo", 'Python')
  * bool: True of False
  * list: [1, 2, 3] (geordend, muteerbaar)
  * tuple: (1, 2, 3) (geordend, niet muteerbaar)
  * dict: {"naam": "Jan", "leeftijd": 25} (key-value pairs)
  * set: {1, 2, 3} (unieke waarden, ongeordend)

## Operatoren
- Rekenkundig: +, -, *, /, // (integer deling), % (modulo), ** (macht)
- Vergelijking: ==, !=, <, >, <=, >=
- Logisch: and, or, not
- Toewijzing: =, +=, -=, *=, /=, //=, %=, **=
- Membership: in, not in
- Identity: is, is not

## Strings
- Aanmaken: "tekst" of 'tekst'
- Meerdere regels: """tekst""" of '''tekst'''
- Indexering: tekst[0], tekst[-1] (laatste karakter)
- Slicing: tekst[2:5], tekst[:5], tekst[2:], tekst[::2]
- Methoden:
  * upper(), lower(), capitalize(), title()
  * strip(), lstrip(), rstrip()
  * replace(oud, nieuw)
  * split(separator), join(lijst)
  * find(substr), count(substr)
  * startswith(prefix), endswith(suffix)
  * isalpha(), isdigit(), isalnum(), isspace()
- Formatteren:
  * f-strings: f"Naam: {naam}, Leeftijd: {leeftijd}"
  * format(): "Naam: {}, Leeftijd: {}".format(naam, leeftijd)
  * %-notatie: "Naam: %s, Leeftijd: %d" % (naam, leeftijd)

## Lijsten (Lists)
- Aanmaken: lijst = [1, 2, 3] of lijst = list()
- Indexering: lijst[0], lijst[-1]
- Slicing: lijst[2:5], lijst[:5], lijst[2:], lijst[::2]
- Methoden:
  * append(item): voegt item toe aan einde
  * insert(index, item): voegt item toe op index
  * extend(iterable): voegt meerdere items toe
  * remove(item): verwijdert eerste voorkomen van item
  * pop(index): verwijdert en retourneert item op index
  * clear(): verwijdert alle items
  * index(item): geeft index van eerste voorkomen
  * count(item): telt aantal voorkomens
  * sort(): sorteert lijst (in-place)
  * reverse(): keert volgorde om (in-place)
- List comprehension: [x*2 for x in range(10) if x % 2 == 0]

## Dictionaries
- Aanmaken: d = {"key": "value"} of d = dict()
- Toegang: d["key"] of d.get("key", default)
- Methoden:
  * keys(): geeft alle keys
  * values(): geeft alle values
  * items(): geeft alle key-value pairs als tuples
  * update(dict2): voegt dict2 toe aan dict
  * pop(key): verwijdert en retourneert value
  * clear(): verwijdert alle items
- Dict comprehension: {x: x**2 for x in range(5)}

## Controlestructuren
- if-elif-else:
  ```python
  if voorwaarde:
      # code
  elif andere_voorwaarde:
      # code
  else:
      # code
  ```

- for-lus:
  ```python
  for item in iterable:
      # code
  ```

- while-lus:
  ```python
  while voorwaarde:
      # code
  ```

- break: verlaat de lus
- continue: gaat naar volgende iteratie
- pass: doet niets (placeholder)

====================== PYTHON SPIEKBRIEF (PAGINA 2) ======================

## Functies
- Definitie:
  ```python
  def functie_naam(param1, param2=default):
      # code
      return waarde
  ```

- Argumenten:
  * Positioneel: functie(1, 2)
  * Keyword: functie(param2=2, param1=1)
  * Variabel aantal: *args (tuple), **kwargs (dict)
  ```python
  def functie(*args, **kwargs):
      print(args)    # (1, 2, 3)
      print(kwargs)  # {'a': 4, 'b': 5}
  
  functie(1, 2, 3, a=4, b=5)
  ```

- Lambda functies (anonieme functies):
  ```python
  f = lambda x, y: x + y
  print(f(2, 3))  # 5
  ```

## Modules en Packages
- Import:
  ```python
  import module
  from module import functie
  from module import functie as f
  from module import *
  import module as m
  ```

- Standaard modules:
  * math: math.sqrt(), math.pi, math.sin()
  * random: random.randint(), random.choice()
  * datetime: datetime.now(), datetime.strptime()
  * os: os.path.join(), os.listdir()
  * sys: sys.argv, sys.exit()
  * json: json.loads(), json.dumps()
  * re: re.search(), re.match(), re.findall()

## Bestandsverwerking
- Openen en sluiten:
  ```python
  # Met context manager (aanbevolen)
  with open('bestand.txt', 'r') as f:
      inhoud = f.read()
  
  # Handmatig
  f = open('bestand.txt', 'r')
  inhoud = f.read()
  f.close()
  ```

- Modes:
  * 'r': lezen (default)
  * 'w': schrijven (overschrijft bestand)
  * 'a': toevoegen (append)
  * 'b': binaire mode (rb, wb)
  * 't': tekst mode (default)

- Methoden:
  * read(): leest hele bestand
  * readline(): leest één regel
  * readlines(): leest alle regels in lijst
  * write(str): schrijft string
  * writelines(list): schrijft lijst van strings

## Exceptions
- Try-except:
  ```python
  try:
      # code die exception kan veroorzaken
  except ExceptionType as e:
      # code bij exception
  else:
      # code als geen exception
  finally:
      # code die altijd uitgevoerd wordt
  ```

- Veelvoorkomende exceptions:
  * ValueError: ongeldige waarde
  * TypeError: ongeldige type
  * IndexError: index buiten bereik
  * KeyError: key niet in dictionary
  * FileNotFoundError: bestand niet gevonden
  * ZeroDivisionError: deling door nul
  * ImportError: module niet gevonden

- Eigen exceptions:
  ```python
  class MijnException(Exception):
      pass
  
  raise MijnException("Foutmelding")
  ```

## Object-Oriented Programming (OOP)
- Klasse definitie:
  ```python
  class Persoon:
      # Class variabele (gedeeld door alle instanties)
      soort = "mens"
      
      # Constructor
      def __init__(self, naam, leeftijd):
          # Instance variabelen
          self.naam = naam
          self.leeftijd = leeftijd
      
      # Instance methode
      def groet(self):
          return f"Hallo, ik ben {self.naam}"
      
      # Class methode
      @classmethod
      def van_geboortejaar(cls, naam, geboortejaar):
          leeftijd = 2024 - geboortejaar
          return cls(naam, leeftijd)
      
      # Static methode
      @staticmethod
      def is_volwassen(leeftijd):
          return leeftijd >= 18
  ```

- Instantie aanmaken:
  ```python
  p = Persoon("Jan", 25)
  print(p.naam)        # Jan
  print(p.groet())     # Hallo, ik ben Jan
  ```

- Overerving:
  ```python
  class Student(Persoon):
      def __init__(self, naam, leeftijd, studie):
          super().__init__(naam, leeftijd)
          self.studie = studie
      
      def groet(self):
          return f"{super().groet()} en ik studeer {self.studie}"
  ```

## Handige Built-in Functies
- print(): output naar console
- input(): input van gebruiker
- len(): lengte van object
- range(): reeks getallen
- type(): type van object
- int(), float(), str(), bool(): type conversie
- list(), tuple(), dict(), set(): type conversie
- min(), max(), sum(): minimum, maximum, som
- sorted(): gesorteerde kopie
- enumerate(): index en waarde
- zip(): combineert iterables
- filter(), map(): functioneel programmeren
- any(), all(): controleert voorwaarden
- abs(): absolute waarde
- round(): afronden
- dir(): attributen van object
- help(): documentatie

## Handige Tips
- PEP 8: Python stijlgids (indentatie, naamgeving)
- Docstrings: documentatie in code
- Virtual environments: isolatie van dependencies
- pip: package manager
- List/dict/set comprehensions voor beknopte code
- f-strings voor leesbare string formatting
- Context managers (with) voor resource management
- Gebruik enumerate() in plaats van range(len())
- Gebruik in voor membership tests
- Gebruik is voor identity tests
- Gebruik get() voor dictionaries om KeyError te voorkomen
