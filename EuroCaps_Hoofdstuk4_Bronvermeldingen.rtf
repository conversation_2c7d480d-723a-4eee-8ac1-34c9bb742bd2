{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 HOOFDSTUK 4: IT OPLOSSING: DATABASE ONTWERP EN REALISATIE\b0\fs22\par

\pard\sa200\sl276\slmult1\b 4.1 Informatiebehoeften Analyse\b0\par
Op basis van de analyse van de huidige situatie en de ge\'efdentificeerde knelpunten, zijn de informatiebehoeften voor het databasesysteem in kaart gebracht. Deze informatiebehoeften zijn gegroepeerd per kernproces. \b Bron: (Elmasri & Navathe, 2017)\b0\par

\b Informatiebehoeften Grinding (Malen)\b0\par
Voor het grinding proces zijn de volgende informatiebehoeften ge\'efdentificeerd:\par
- Tracking van koffiebonen (leverancier, batch, kwaliteit)\par
- Registratie van maalparameters (maalgraad, temperatuur, tijd)\par
- Monitoring van machinegegevens (machine-ID, onderhoudsstatus)\par
- Kwaliteitscontrole metingen (deeltjesgrootte, vochtigheid)\par
Deze informatie is essentieel voor het waarborgen van de kwaliteit van het maalproces en het optimaliseren van de maalparameters. \b Bron: (Hoffer et al., 2016)\b0\par

\b Informatiebehoeften Filling (Vullen)\b0\par
Voor het filling proces zijn de volgende informatiebehoeften ge\'efdentificeerd:\par
- Tracking van gemalen koffie (batch, maalgraad)\par
- Registratie van vulparameters (vulgewicht, druk, tijd)\par
- Monitoring van machinegegevens (machine-ID, onderhoudsstatus)\par
- Kwaliteitscontrole metingen (vulgewicht, dichtheid)\par
Deze informatie is essentieel voor het waarborgen van de consistentie van het vulproces en het minimaliseren van variatie in vulgewichten. \b Bron: (Teorey et al., 2011)\b0\par

\b Informatiebehoeften Packaging (Verpakken)\b0\par
Voor het packaging proces zijn de volgende informatiebehoeften ge\'efdentificeerd:\par
- Tracking van gevulde capsules (batch, vulgewicht)\par
- Registratie van verpakkingsparameters (sealingtemperatuur, druk, tijd)\par
- Monitoring van machinegegevens (machine-ID, onderhoudsstatus)\par
- Kwaliteitscontrole metingen (sealingkwaliteit, lektest)\par
Deze informatie is essentieel voor het waarborgen van de integriteit van de verpakking en het voorkomen van lekkage of contaminatie. \b Bron: (Date, 2019)\b0\par

\b Informatiebehoeften Kwaliteitscontrole\b0\par
Voor het kwaliteitscontrole proces zijn de volgende informatiebehoeften ge\'efdentificeerd:\par
- Registratie van kwaliteitscontrole metingen voor alle processen\par
- Tracking van afwijkingen en corrigerende maatregelen\par
- Monitoring van kwaliteitstrends over tijd\par
- Genereren van kwaliteitsrapporten voor management en klanten\par
Deze informatie is essentieel voor het waarborgen van de algehele productkwaliteit en het voldoen aan kwaliteitsnormen en klanteneisen. \b Bron: (Silberschatz et al., 2019)\b0\par

\b Informatiebehoeften Logistiek\b0\par
Voor het logistieke proces zijn de volgende informatiebehoeften ge\'efdentificeerd:\par
- Tracking van grondstoffen en eindproducten\par
- Registratie van voorraadniveaus en -bewegingen\par
- Monitoring van leveringen en verzendingen\par
- Genereren van logistieke rapporten en prognoses\par
Deze informatie is essentieel voor het optimaliseren van de supply chain en het waarborgen van tijdige leveringen aan klanten. \b Bron: (Oppel, 2011)\b0\par

\b 4.2 Database Ontwerp\b0\par
Op basis van de informatiebehoeften is een databaseontwerp ontwikkeld dat alle kernprocessen van Euro Caps ondersteunt. Het ontwerp volgt een gestructureerde aanpak van conceptueel naar logisch naar fysiek niveau. \b Bron: (Connolly & Begg, 2020)\b0\par

\b Conceptueel Ontwerp\b0\par
Het conceptuele ontwerp is gebaseerd op een Entity-Relationship Diagram (ERD) dat de belangrijkste entiteiten en hun relaties weergeeft. De kernentiteiten in het model zijn:\par
- Partner (leveranciers, klanten, transporteurs)\par
- Product (koffiecapsules in verschillende varianten)\par
- Grinding (maalproces)\par
- Filling (vulproces)\par
- Packaging (verpakkingsproces)\par
- Kwaliteitscontrole\par
- Levering\par
Deze entiteiten zijn met elkaar verbonden via verschillende soorten relaties, zoals one-to-many en many-to-many, om de bedrijfsprocessen accuraat weer te geven. \b Bron: (Garcia-Molina et al., 2020)\b0\par

\b Logisch Ontwerp\b0\par
In het logische ontwerp zijn de conceptuele entiteiten vertaald naar tabellen met attributen, primaire sleutels en vreemde sleutels. Het logische model bevat de volgende hoofdtabellen:\par
- SoortPartner en Partner\par
- SoortProduct en Product\par
- Grinding en Grinding_Product (koppeltabel)\par
- Filling en Filling_Product (koppeltabel)\par
- Packaging en Packaging_Product (koppeltabel)\par
- Levering en Levering_Regel (koppeltabel)\par
Deze structuur maakt het mogelijk om complexe relaties tussen entiteiten te modelleren en tegelijkertijd de principes van normalisatie te respecteren. \b Bron: (Elmasri & Navathe, 2017)\b0\par

\b Fysiek Ontwerp\b0\par
Het fysieke ontwerp specificeert de implementatie van het logische model in een MySQL-database. Het omvat de definitie van tabellen, kolommen, datatypen, constraints en indexen. De belangrijkste aspecten van het fysieke ontwerp zijn:\par
- Keuze van geschikte datatypen voor elk attribuut\par
- Definitie van primaire en vreemde sleutels\par
- Implementatie van constraints (NOT NULL, UNIQUE, CHECK)\par
- Creatie van indexen voor veelgebruikte zoekvelden\par
- Optimalisatie van de databasestructuur voor prestaties\par
Deze fysieke implementatie vormt de basis voor het opslaan en beheren van alle gegevens die worden gegenereerd door de productieprocessen van Euro Caps. \b Bron: (MySQL, 2023)\b0\par

\b 4.3 Database Implementatie\b0\par
De implementatie van de database omvat de creatie van de databasestructuur in MySQL en het ontwikkelen van scripts voor het genereren en importeren van testdata. \b Bron: (McKinney, 2017)\b0\par

\b MySQL Structuur en Constraints\b0\par
De database is ge\'efmplementeerd in MySQL met de volgende structuur en constraints:\par
- Tabellen voor alle entiteiten in het logische model\par
- Primaire sleutels voor unieke identificatie van records\par
- Vreemde sleutels voor het waarborgen van referenti\'eble integriteit\par
- NOT NULL constraints voor verplichte velden\par
- UNIQUE constraints voor unieke waarden\par
- CHECK constraints voor validatie van gegevens\par
Deze structuur waarborgt de integriteit en consistentie van de gegevens in de database. \b Bron: (Date, 2019)\b0\par

\b Python Script + CSV Structuur\b0\par
Voor het genereren en importeren van testdata zijn Python-scripts ontwikkeld die gebruik maken van de NumPy en Pandas bibliotheken. Deze scripts maken het mogelijk om:\par
- Realistische testdata te genereren voor alle tabellen\par
- De data op te slaan in CSV-bestanden\par
- De CSV-bestanden te importeren in de MySQL-database\par
- De data te valideren en te controleren op consistentie\par
Deze scripts vergemakkelijken het testen en valideren van de databasestructuur en de bijbehorende queries. \b Bron: (VanderPlas, 2016)\b0\par

\b 4.4 Data Analyse en Inzichten\b0\par
De database maakt het mogelijk om verschillende soorten analyses uit te voeren en inzichten te verkrijgen in de productieprocessen van Euro Caps. \b Bron: (Kimball & Ross, 2013)\b0\par

\b Operationele Inzichten\b0\par
De database biedt operationele inzichten zoals:\par
- Overzicht van alle partners per type\par
- Producten per soort met status\par
- Grinding activiteiten per machine\par
- Filling activiteiten per machine\par
- Packaging activiteiten per machine\par
- Leveringen per klant\par
Deze inzichten helpen bij het dagelijkse beheer van de productieprocessen en het identificeren van operationele knelpunten. \b Bron: (Hoffer et al., 2016)\b0\par

\b KPI Inzichten\b0\par
De database biedt ook KPI-inzichten zoals:\par
- Productie-effici\'ebntie per machine (aantal producten per uur)\par
- Gemiddelde doorlooptijd van producten door alle processen\par
- Leveringsbetrouwbaarheid (verschil tussen leveringsdatum en verwachte leverdatum)\par
- Kwaliteitspercentage (percentage producten dat voldoet aan kwaliteitseisen)\par
- Voorraadrotatie (hoe snel producten door het magazijn gaan)\par
Deze inzichten helpen bij het meten van de prestaties van de productieprocessen en het identificeren van verbetermogelijkheden. \b Bron: (Wambler, 2015)\b0\par
}
