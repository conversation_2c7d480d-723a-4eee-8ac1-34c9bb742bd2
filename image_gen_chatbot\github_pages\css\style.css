* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f5f5;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.chat-container {
    width: 90%;
    max-width: 800px;
    height: 90vh;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

.chat-header {
    background-color: #4a6fa5;
    color: white;
    padding: 15px;
    text-align: center;
}

.welcome-container {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.welcome-container h2 {
    margin-bottom: 15px;
    color: #4a6fa5;
    font-size: 28px;
}

.welcome-container p {
    margin-bottom: 20px;
    text-align: center;
    color: #666;
    max-width: 80%;
    line-height: 1.5;
}

.generation-options {
    width: 80%;
    max-width: 500px;
    margin: 20px 0;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #e0e0e0;
}

.generation-options h3 {
    color: #4a6fa5;
    margin-bottom: 10px;
    text-align: center;
}

.generation-options ul {
    list-style: disc;
    padding-left: 20px;
    margin-bottom: 20px;
}

.generation-options li {
    margin: 10px 0;
    line-height: 1.4;
}

.image-settings {
    background-color: #f0f4f8;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
}

.image-settings h4 {
    color: #4a6fa5;
    margin-bottom: 12px;
    text-align: center;
    font-size: 16px;
}

.setting-group {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.setting-group label {
    flex: 0 0 100px;
    font-weight: bold;
    color: #555;
}

.setting-group select {
    flex: 1;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
}

.start-button-container {
    margin: 20px 0;
}

#start-chat-button {
    padding: 12px 30px;
    background-color: #4a6fa5;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 18px;
    transition: background-color 0.3s;
    position: relative;
    z-index: 100; /* Ensure button is above other elements */
    display: inline-block; /* Ensure proper sizing */
    user-select: none; /* Prevent text selection */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

#start-chat-button:hover {
    background-color: #3a5a8f;
}

#start-chat-button:active {
    background-color: #2a4a7f; /* Darker color when clicked */
    transform: scale(0.98); /* Slight scale down effect */
}

.disclaimer {
    margin-top: 20px;
    width: 80%;
    max-width: 600px;
    padding: 10px;
    background-color: #fff8e1;
    border-radius: 5px;
    border-left: 4px solid #ffc107;
}

.disclaimer p {
    margin: 0;
    font-size: 14px;
    color: #856404;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.message {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

.user {
    align-items: flex-end;
}

.bot {
    align-items: flex-start;
}

.message-content {
    padding: 10px 15px;
    border-radius: 20px;
    max-width: 80%;
    word-wrap: break-word;
}

.user .message-content {
    background-color: #4a6fa5;
    color: white;
}

.bot .message-content {
    background-color: #e9e9eb;
    color: #333;
}

.chat-input {
    display: flex;
    padding: 15px;
    border-top: 1px solid #e9e9eb;
}

#user-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ccc;
    border-radius: 20px;
    outline: none;
    font-size: 16px;
}

#send-button, #send-button-fallback {
    margin-left: 10px;
    padding: 10px 20px;
    background-color: #4a6fa5;
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 16px;
    position: relative;
    z-index: 100; /* Ensure button is above other elements */
    display: inline-block; /* Ensure proper sizing */
    user-select: none; /* Prevent text selection */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    font-weight: bold; /* Make text more visible */
    transition: all 0.2s ease; /* Smooth transition for hover/active states */
}

#send-button:hover, #send-button-fallback:hover {
    background-color: #3a5a8f;
    transform: scale(1.05); /* Slight scale up on hover */
}

#send-button:active, #send-button-fallback:active {
    background-color: #2a4a7f; /* Darker color when clicked */
    transform: scale(0.98); /* Slight scale down effect */
}

/* Add a special style for the fallback button */
#send-button-fallback {
    background-color: #5a7fb5; /* Slightly different color */
    border: 1px solid #3a5a8f; /* Add border for distinction */
}

.image-container {
    width: 100%;
    margin-top: 10px;
    position: relative;
    min-height: 50px;
}

.generated-image {
    max-width: 100%;
    max-height: 300px;
    border-radius: 10px;
    display: block;
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.image-controls {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    padding: 5px;
    background-color: #f5f5f5;
    border-radius: 5px;
}

.image-info {
    font-size: 12px;
    color: #666;
    display: flex;
    align-items: center;
}

.image-actions {
    display: flex;
    gap: 10px;
}

.download-button, .new-image-button {
    background-color: #4a6fa5;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.download-button:hover, .new-image-button:hover {
    background-color: #3a5a8f;
}

.download-button:before {
    content: "↓";
    font-weight: bold;
}

.new-image-button:before {
    content: "↻";
    font-weight: bold;
}

.image-loading {
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 5px;
    text-align: center;
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.image-error {
    padding: 10px;
    background-color: #fff8e1;
    border-radius: 5px;
    text-align: center;
    color: #856404;
    font-size: 14px;
    margin-bottom: 10px;
    border-left: 4px solid #ffc107;
}

.image-error a {
    color: #4a6fa5;
    text-decoration: underline;
    font-weight: bold;
}

.loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4a6fa5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
