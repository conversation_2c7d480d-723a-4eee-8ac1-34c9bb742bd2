# 4.3 Database Implementatie

## 4.3.1 MySQL Structuur en Constraints

De database voor EuroCaps is geïmplementeerd in MySQL en bevat de volgende tabellen en constraints:

```sql
-- SoortPartner tabel
CREATE TABLE SoortPartner (
    SoortPartnerId INT PRIMARY KEY,
    Omschrijving VARCHAR(50) NOT NULL
);

-- Partner tabel
CREATE TABLE Partner (
    PartnerId INT PRIMARY KEY,
    SoortPartnerId INT NOT NULL,
    Bedrijfsnaam VARCHAR(100) NOT NULL,
    Straatnaam VARCHAR(100),
    Huisnummer VARCHAR(10),
    Postcode VARCHAR(10),
    Plaats VARCHAR(50),
    Land VARCHAR(50),
    Email VARCHAR(100),
    Telnr VARCHAR(20),
    FOREIGN KEY (SoortPartnerId) REFERENCES SoortPartner(SoortPartnerId)
);

-- PartnerContact tabel
CREATE TABLE PartnerContact (
    PartnerContactId INT PRIMARY KEY,
    PartnerId INT NOT NULL,
    Voornaam VARCHAR(50) NOT NULL,
    Achternaam VARCHAR(50) NOT NULL,
    Functie VARCHAR(50),
    Email VARCHAR(100),
    Telnr VARCHAR(20),
    FOREIGN KEY (PartnerId) REFERENCES Partner(PartnerId)
);

-- SoortProduct tabel
CREATE TABLE SoortProduct (
    SoortProductId INT PRIMARY KEY,
    Omschrijving VARCHAR(100) NOT NULL,
    Materiaal VARCHAR(50),
    Gewicht DECIMAL(5,2),
    Afmeting VARCHAR(20)
);

-- Product tabel
CREATE TABLE Product (
    ProductId INT PRIMARY KEY,
    SoortProductId INT NOT NULL,
    ProductTHTDatum DATE,
    StatusProduct VARCHAR(20),
    CStatusProduct CHAR(1),
    FStatusProduct CHAR(1),
    PStatusProduct CHAR(1),
    FOREIGN KEY (SoortProductId) REFERENCES SoortProduct(SoortProductId)
);

-- Grinding tabel
CREATE TABLE Grinding (
    GrindingId INT PRIMARY KEY,
    G_DatumTijdStart DATETIME NOT NULL,
    G_DatumTijdEind DATETIME,
    G_Machine VARCHAR(50)
);

-- Grinding_Product koppeltabel
CREATE TABLE Grinding_Product (
    GrindingId INT,
    ProductId INT,
    Aantal INT,
    PRIMARY KEY (GrindingId, ProductId),
    FOREIGN KEY (GrindingId) REFERENCES Grinding(GrindingId),
    FOREIGN KEY (ProductId) REFERENCES Product(ProductId)
);

-- Filling tabel
CREATE TABLE Filling (
    FillingId INT PRIMARY KEY,
    F_DatumTijdStart DATETIME NOT NULL,
    F_DatumTijdEind DATETIME,
    F_Machine VARCHAR(50)
);

-- Filling_Product koppeltabel
CREATE TABLE Filling_Product (
    FillingId INT,
    ProductId INT,
    Aantal INT,
    PRIMARY KEY (FillingId, ProductId),
    FOREIGN KEY (FillingId) REFERENCES Filling(FillingId),
    FOREIGN KEY (ProductId) REFERENCES Product(ProductId)
);

-- Packaging tabel
CREATE TABLE Packaging (
    PackagingId INT PRIMARY KEY,
    P_DatumTijdStart DATETIME NOT NULL,
    P_DatumTijdEind DATETIME,
    P_Machine VARCHAR(50)
);

-- Packaging_Product koppeltabel
CREATE TABLE Packaging_Product (
    PackagingId INT,
    ProductId INT,
    Aantal INT,
    AantalStuksInDoos INT,
    PRIMARY KEY (PackagingId, ProductId),
    FOREIGN KEY (PackagingId) REFERENCES Packaging(PackagingId),
    FOREIGN KEY (ProductId) REFERENCES Product(ProductId)
);

-- Levering tabel
CREATE TABLE Levering (
    LeveringId INT PRIMARY KEY,
    PartnerId INT NOT NULL,
    LeveringDatum DATE NOT NULL,
    VerwachteLeverdatum DATE,
    FOREIGN KEY (PartnerId) REFERENCES Partner(PartnerId)
);

-- Levering_Regel tabel
CREATE TABLE Levering_Regel (
    LeveringId INT,
    ProductId INT,
    Aantal INT,
    PRIMARY KEY (LeveringId, ProductId),
    FOREIGN KEY (LeveringId) REFERENCES Levering(LeveringId),
    FOREIGN KEY (ProductId) REFERENCES Product(ProductId)
);
```

De database structuur is ontworpen om de bedrijfsprocessen van EuroCaps te ondersteunen, met name de drie kernprocessen: grinding (malen), filling (vullen) en packaging (verpakken). De structuur bevat de volgende belangrijke constraints:

1. **Primaire sleutels**: Elke tabel heeft een primaire sleutel om unieke records te garanderen.
2. **Vreemde sleutels**: Relaties tussen tabellen worden gedefinieerd met vreemde sleutels om referentiële integriteit te waarborgen.
3. **NOT NULL constraints**: Belangrijke velden zoals Omschrijving, Bedrijfsnaam, Voornaam, Achternaam en datumvelden zijn gemarkeerd als NOT NULL.
4. **Samengestelde primaire sleutels**: De koppeltabellen (Grinding_Product, Filling_Product, Packaging_Product, Levering_Regel) hebben samengestelde primaire sleutels om de many-to-many relaties te implementeren.

## 4.3.2 Python Script + CSV Structuur

Voor het genereren en importeren van testdata zijn twee Python scripts ontwikkeld:

1. **generate_eurocaps_csv_pandas.py**: Dit script genereert CSV-bestanden met testdata voor alle tabellen in de database.
2. **import_csv_to_mysql.py**: Dit script importeert de gegenereerde CSV-bestanden in de MySQL database.

### CSV Structuur

De CSV-bestanden hebben de volgende structuur:

1. **soort_partner.csv**:
   - SoortPartnerId
   - Omschrijving

2. **partner.csv**:
   - PartnerId
   - SoortPartnerId
   - Bedrijfsnaam
   - Straatnaam
   - Huisnummer
   - Postcode
   - Plaats
   - Land
   - Email
   - Telnr

3. **partner_contact.csv**:
   - PartnerContactId
   - PartnerId
   - Voornaam
   - Achternaam
   - Functie
   - Email
   - Telnr

4. **soort_product.csv**:
   - SoortProductId
   - Omschrijving
   - Materiaal
   - Gewicht
   - Afmeting

5. **product.csv**:
   - ProductId
   - SoortProductId
   - ProductTHTDatum
   - StatusProduct
   - CStatusProduct
   - FStatusProduct
   - PStatusProduct

6. **grinding.csv**:
   - GrindingId
   - G_DatumTijdStart
   - G_DatumTijdEind
   - G_Machine

7. **grinding_product.csv**:
   - GrindingId
   - ProductId
   - Aantal

8. **filling.csv**:
   - FillingId
   - F_DatumTijdStart
   - F_DatumTijdEind
   - F_Machine

9. **filling_product.csv**:
   - FillingId
   - ProductId
   - Aantal

10. **packaging.csv**:
    - PackagingId
    - P_DatumTijdStart
    - P_DatumTijdEind
    - P_Machine

11. **packaging_product.csv**:
    - PackagingId
    - ProductId
    - Aantal
    - AantalStuksInDoos

12. **levering.csv**:
    - LeveringId
    - PartnerId
    - LeveringDatum
    - VerwachteLeverdatum

13. **levering_regel.csv**:
    - LeveringId
    - ProductId
    - Aantal

### Python Script voor CSV Generatie

Het script `generate_eurocaps_csv_pandas.py` gebruikt NumPy en Pandas voor het genereren van de CSV-bestanden. Hier is een voorbeeld van hoe het script een CSV-bestand genereert:

```python
def generate_soort_partner_data():
    """Genereer data voor de SoortPartner tabel met Pandas"""
    # Maak een DataFrame
    data = {
        'SoortPartnerId': [1, 2, 3, 4],
        'Omschrijving': ["Leverancier", "Klant", "Transporteur", "Dienstverlener"]
    }
    
    df = pd.DataFrame(data)
    
    # Sla op als CSV
    df.to_csv('csv_data/soort_partner.csv', index=False)
    
    print("SoortPartner data gegenereerd.")
    return df
```

### Python Script voor CSV Import

Het script `import_csv_to_mysql.py` gebruikt mysql.connector en Pandas voor het importeren van de CSV-bestanden in de MySQL database. Hier is een voorbeeld van hoe het script een CSV-bestand importeert:

```python
def import_csv_to_table(connection, csv_file, table_name):
    """Importeer een CSV-bestand in een MySQL tabel"""
    try:
        # Lees het CSV-bestand
        df = pd.read_csv(f"csv_data/{csv_file}")
        
        # Maak een cursor
        cursor = connection.cursor()
        
        # Verwijder bestaande data uit de tabel
        cursor.execute(f"DELETE FROM {table_name}")
        
        # Genereer de INSERT query
        columns = ", ".join(df.columns)
        placeholders = ", ".join(["%s"] * len(df.columns))
        query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
        
        # Voer de query uit voor elke rij in het DataFrame
        for _, row in df.iterrows():
            cursor.execute(query, tuple(row))
        
        # Commit de wijzigingen
        connection.commit()
        
        print(f"{len(df)} rijen geïmporteerd in tabel {table_name}")
    except Error as e:
        print(f"Fout bij importeren van {csv_file} in {table_name}: {e}")
    except Exception as e:
        print(f"Algemene fout bij importeren van {csv_file}: {e}")
```

# 4.4 Data Analyse en Inzichten

## 4.4.1 Operationele Inzichten

De volgende SQL-queries geven operationele inzichten in de EuroCaps database:

### 1. Overzicht van alle partners per type

```sql
SELECT 
    sp.Omschrijving AS PartnerType, 
    p.PartnerId, 
    p.Bedrijfsnaam, 
    p.Plaats, 
    p.Email, 
    p.Telnr
FROM 
    Partner p
JOIN 
    SoortPartner sp ON p.SoortPartnerId = sp.SoortPartnerId
ORDER BY 
    sp.Omschrijving, p.Bedrijfsnaam;
```

Deze query geeft een overzicht van alle partners, gegroepeerd per type (leverancier, klant, etc.). Dit is nuttig voor het beheren van de relaties met partners en het identificeren van de verschillende soorten partners waarmee EuroCaps samenwerkt.

### 2. Producten per soort met status

```sql
SELECT 
    sp.Omschrijving AS ProductType, 
    sp.Materiaal,
    COUNT(p.ProductId) AS AantalProducten,
    SUM(CASE WHEN p.StatusProduct = 'Geproduceerd' THEN 1 ELSE 0 END) AS AantalGeproduceerd,
    SUM(CASE WHEN p.StatusProduct = 'In productie' THEN 1 ELSE 0 END) AS AantalInProductie,
    SUM(CASE WHEN p.StatusProduct = 'Kwaliteitscontrole' THEN 1 ELSE 0 END) AS AantalInKwaliteitscontrole,
    SUM(CASE WHEN p.StatusProduct = 'Gereed voor verzending' THEN 1 ELSE 0 END) AS AantalGereedVoorVerzending,
    SUM(CASE WHEN p.StatusProduct = 'Verzonden' THEN 1 ELSE 0 END) AS AantalVerzonden
FROM 
    Product p
JOIN 
    SoortProduct sp ON p.SoortProductId = sp.SoortProductId
GROUP BY 
    sp.Omschrijving, sp.Materiaal
ORDER BY 
    sp.Omschrijving;
```

Deze query geeft een overzicht van alle producten per soort, met hun status. Dit is nuttig voor het monitoren van de productie en het identificeren van knelpunten in het productieproces.

### 3. Grinding activiteiten per machine

```sql
SELECT 
    G_Machine, 
    COUNT(GrindingId) AS AantalBatches,
    MIN(G_DatumTijdStart) AS EersteGebruik,
    MAX(G_DatumTijdStart) AS LaatstGebruik,
    AVG(TIMESTAMPDIFF(HOUR, G_DatumTijdStart, G_DatumTijdEind)) AS GemiddeldeDuurInUren
FROM 
    Grinding
GROUP BY 
    G_Machine
ORDER BY 
    AantalBatches DESC;
```

Deze query geeft een overzicht van alle grinding activiteiten per machine. Dit is nuttig voor het monitoren van de prestaties van de machines en het plannen van onderhoud.

## 4.4.2 KPI Inzichten

De volgende SQL-queries geven KPI-inzichten in de EuroCaps database:

### 1. Productie-efficiëntie per machine (aantal producten per uur)

```sql
-- Grinding machines
SELECT 
    g.G_Machine,
    SUM(gp.Aantal) AS TotaalProducten,
    SUM(TIMESTAMPDIFF(HOUR, g.G_DatumTijdStart, g.G_DatumTijdEind)) AS TotaalUren,
    ROUND(SUM(gp.Aantal) / SUM(TIMESTAMPDIFF(HOUR, g.G_DatumTijdStart, g.G_DatumTijdEind)), 2) AS ProductenPerUur
FROM 
    Grinding g
JOIN 
    Grinding_Product gp ON g.GrindingId = gp.GrindingId
GROUP BY 
    g.G_Machine
ORDER BY 
    ProductenPerUur DESC;
```

Deze query berekent de productie-efficiëntie per machine (aantal producten per uur). Dit is een belangrijke KPI voor het meten van de efficiëntie van de productie en het identificeren van verbetermogelijkheden.

### 2. Gemiddelde doorlooptijd van producten door alle processen

```sql
SELECT 
    sp.Omschrijving AS ProductType,
    AVG(TIMESTAMPDIFF(HOUR, g.G_DatumTijdStart, p.P_DatumTijdEind)) AS GemiddeldeDoorlooptijdInUren
FROM 
    Product pr
JOIN 
    SoortProduct sp ON pr.SoortProductId = sp.SoortProductId
JOIN 
    Grinding_Product gp ON pr.ProductId = gp.ProductId
JOIN 
    Grinding g ON gp.GrindingId = g.GrindingId
JOIN 
    Filling_Product fp ON pr.ProductId = fp.ProductId
JOIN 
    Filling f ON fp.FillingId = f.FillingId
JOIN 
    Packaging_Product pp ON pr.ProductId = pp.ProductId
JOIN 
    Packaging p ON pp.PackagingId = p.PackagingId
WHERE 
    pr.CStatusProduct = 'C' 
    AND pr.FStatusProduct = 'F' 
    AND pr.PStatusProduct = 'P'
GROUP BY 
    sp.Omschrijving
ORDER BY 
    GemiddeldeDoorlooptijdInUren;
```

Deze query berekent de gemiddelde doorlooptijd van producten door alle processen. Dit is een belangrijke KPI voor het meten van de efficiëntie van het productieproces en het identificeren van knelpunten.

### 3. Leveringsbetrouwbaarheid (verschil tussen leveringsdatum en verwachte leverdatum)

```sql
SELECT 
    p.Bedrijfsnaam AS Klant,
    COUNT(l.LeveringId) AS AantalLeveringen,
    AVG(DATEDIFF(l.VerwachteLeverdatum, l.LeveringDatum)) AS GemiddeldLeveringsverschilInDagen,
    SUM(CASE WHEN l.VerwachteLeverdatum >= l.LeveringDatum THEN 1 ELSE 0 END) AS AantalOpTijd,
    ROUND(SUM(CASE WHEN l.VerwachteLeverdatum >= l.LeveringDatum THEN 1 ELSE 0 END) / COUNT(l.LeveringId) * 100, 2) AS PercentageOpTijd
FROM 
    Levering l
JOIN 
    Partner p ON l.PartnerId = p.PartnerId
GROUP BY 
    p.Bedrijfsnaam
ORDER BY 
    PercentageOpTijd DESC;
```

Deze query berekent de leveringsbetrouwbaarheid (verschil tussen leveringsdatum en verwachte leverdatum). Dit is een belangrijke KPI voor het meten van de kwaliteit van de dienstverlening en het identificeren van verbetermogelijkheden in het leveringsproces.

## 4.4.3 Conclusie

De bovenstaande queries geven zowel operationele als KPI-inzichten in de EuroCaps database. Deze inzichten kunnen worden gebruikt om de bedrijfsprocessen te optimaliseren en de prestaties te verbeteren. De database structuur en de SQL-queries zijn ontworpen om de bedrijfsprocessen van EuroCaps te ondersteunen, met name de drie kernprocessen: grinding (malen), filling (vullen) en packaging (verpakken).
