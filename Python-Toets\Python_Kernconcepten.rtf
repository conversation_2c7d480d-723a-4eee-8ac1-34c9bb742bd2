{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}{\f1\fnil\fcharset0 Courier New;}}
{\colortbl ;\red0\green0\blue255;}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 PYTHON KERNCONCEPTEN\b0\fs22\par

\pard\sa200\sl276\slmult1\b\fs24 1. VARIABELEN & DATATYPES\b0\fs22\par

\pard\sa200\sl276\slmult1\b Numerieke types\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Integer (gehele getallen)\par
x = 5\par
y = -10\par
\par
# Float (kommagetallen)\par
pi = 3.14\par
e = 2.718\par
\par
# Bewerkingen\par
som = x + y          # -5\par
verschil = x - y     # 15\par
product = x * y      # -50\par
deling = x / y       # -0.5 (float deling)\par
int_deling = x // y  # 0 (integer deling)\par
rest = x % y         # -5 (modulo/rest)\par
macht = x ** 2       # 25 (5 tot de macht 2)\f0\par

\pard\sa200\sl276\slmult1\b Strings\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # String aanmaken\par
naam = "Python"\par
tekst = 'Programmeren'\par
\par
# Concatenatie\par
volledig = naam + " " + tekst  # "Python Programmeren"\par
\par
# Indexering\par
eerste = naam[0]   # "P"\par
laatste = naam[-1] # "n"\par
\par
# Slicing\par
deel = naam[1:4]   # "yth"\par
\par
# Methoden\par
upper_case = naam.upper()  # "PYTHON"\par
vervangen = naam.replace("P", "J")  # "Jython"\par
woorden = "a,b,c".split(",")  # ["a", "b", "c"]\par
\par
# Formatteren\par
leeftijd = 30\par
bericht = f"\{naam\} is \{leeftijd\} jaar oud"  # "Python is 30 jaar oud"\f0\par

\pard\sa200\sl276\slmult1\b Boolean & None\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Boolean waarden\par
waar = True\par
onwaar = False\par
\par
# Vergelijkingen\par
is_gelijk = (5 == 5)  # True\par
is_groter = (10 > 5)  # True\par
\par
# Logische operatoren\par
resultaat1 = True and False  # False\par
resultaat2 = True or False   # True\par
resultaat3 = not True        # False\par
\par
# None (afwezigheid van waarde)\par
x = None\par
if x is None:\par
    print("x heeft geen waarde")\f0\par

\pard\sa200\sl276\slmult1\b\fs24 2. COLLECTIES\b0\fs22\par

\pard\sa200\sl276\slmult1\b Lijsten\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Lijst aanmaken\par
getallen = [1, 2, 3, 4, 5]\par
kleuren = ["rood", "groen", "blauw"]\par
\par
# Indexering en slicing\par
eerste = getallen[0]  # 1\par
deel = getallen[1:3]  # [2, 3]\par
\par
# Wijzigen\par
getallen[0] = 10  # [10, 2, 3, 4, 5]\par
\par
# Methoden\par
getallen.append(6)  # [10, 2, 3, 4, 5, 6]\par
getallen.insert(1, 15)  # [10, 15, 2, 3, 4, 5, 6]\par
getallen.remove(15)  # [10, 2, 3, 4, 5, 6]\par
verwijderd = getallen.pop()  # verwijderd = 6, getallen = [10, 2, 3, 4, 5]\par
\par
# List comprehension\par
kwadraten = [x**2 for x in range(5)]  # [0, 1, 4, 9, 16]\f0\par

\pard\sa200\sl276\slmult1\b Tuples\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Tuple aanmaken (onveranderbaar)\par
punt = (10, 20)\par
kleuren = ("rood", "groen", "blauw")\par
\par
# Indexering\par
x = punt[0]  # 10\par
\par
# Uitpakken\par
x, y = punt  # x = 10, y = 20\par
\par
# Tuple vs. lijst\par
# - Tuple is onveranderbaar (kan niet worden gewijzigd)\par
# - Tuple is meestal sneller dan lijst\par
# - Tuple kan worden gebruikt als dictionary key\f0\par

\pard\sa200\sl276\slmult1\b Dictionaries\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Dictionary aanmaken\par
persoon = \{"naam": "Jan", "leeftijd": 25\}\par
\par
# Toegang tot waarden\par
naam = persoon["naam"]  # "Jan"\par
\par
# Veilige toegang\par
adres = persoon.get("adres", "Onbekend")  # "Onbekend"\par
\par
# Wijzigen\par
persoon["naam"] = "Piet"  # \{"naam": "Piet", "leeftijd": 25\}\par
persoon["adres"] = "Amsterdam"  # Nieuwe key-value pair\par
\par
# Methoden\par
keys = persoon.keys()  # dict_keys(['naam', 'leeftijd', 'adres'])\par
values = persoon.values()  # dict_values(['Piet', 25, 'Amsterdam'])\par
\par
# Itereren\par
for key, value in persoon.items():\par
    print(key, value)\f0\par

\pard\sa200\sl276\slmult1\b Sets\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Set aanmaken (unieke waarden)\par
kleuren = \{"rood", "groen", "blauw", "rood"\}  # \{"rood", "groen", "blauw"\}\par
\par
# Methoden\par
kleuren.add("geel")  # \{"rood", "groen", "blauw", "geel"\}\par
kleuren.remove("groen")  # \{"rood", "blauw", "geel"\}\par
\par
# Set operaties\par
a = \{1, 2, 3\}\par
b = \{3, 4, 5\}\par
\par
unie = a | b  # \{1, 2, 3, 4, 5\}\par
doorsnede = a & b  # \{3\}\par
verschil = a - b  # \{1, 2\}\f0\par

\pard\sa200\sl276\slmult1\b\fs24 3. CONTROLESTRUCTUREN\b0\fs22\par

\pard\sa200\sl276\slmult1\b Conditionele statements\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # if-elif-else\par
x = 10\par
\par
if x > 0:\par
    print("Positief")\par
elif x < 0:\par
    print("Negatief")\par
else:\par
    print("Nul")\par
\par
# Ternary operator\par
resultaat = "Positief" if x > 0 else "Niet positief"\f0\par

\pard\sa200\sl276\slmult1\b Loops\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # for-loop\par
for i in range(5):\par
    print(i)  # 0, 1, 2, 3, 4\par
\par
# Itereren over collectie\par
kleuren = ["rood", "groen", "blauw"]\par
for kleur in kleuren:\par
    print(kleur)\par
\par
# Enumerate (met index)\par
for index, kleur in enumerate(kleuren):\par
    print(index, kleur)  # 0 rood, 1 groen, 2 blauw\par
\par
# while-loop\par
i = 0\par
while i < 5:\par
    print(i)  # 0, 1, 2, 3, 4\par
    i += 1\par
\par
# break en continue\par
for i in range(10):\par
    if i == 3:\par
        continue  # Sla 3 over\par
    if i == 7:\par
        break  # Stop bij 7\par
    print(i)  # 0, 1, 2, 4, 5, 6\f0\par

\pard\sa200\sl276\slmult1\b\fs24 4. FUNCTIES\b0\fs22\par

\pard\sa200\sl276\slmult1\b Functie definitie\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Basis functie\par
def groet(naam):\par
    return f"Hallo, \{naam\}!"\par
\par
print(groet("Alice"))  # "Hallo, Alice!"\par
\par
# Functie met standaardwaarde\par
def groet(naam, bericht="Hallo"):\par
    return f"\{bericht\}, \{naam\}!"\par
\par
print(groet("Bob"))  # "Hallo, Bob!"\par
print(groet("Charlie", "Goedemorgen"))  # "Goedemorgen, Charlie!"\par
\par
# Functie met meerdere return waarden\par
def bereken(a, b):\par
    som = a + b\par
    verschil = a - b\par
    return som, verschil\par
\par
s, v = bereken(10, 5)  # s = 15, v = 5\f0\par

\pard\sa200\sl276\slmult1\b Argumenten\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Positionele argumenten\par
def beschrijf(naam, leeftijd):\par
    return f"\{naam\} is \{leeftijd\} jaar oud"\par
\par
print(beschrijf("Alice", 30))  # "Alice is 30 jaar oud"\par
\par
# Keyword argumenten\par
print(beschrijf(leeftijd=25, naam="Bob"))  # "Bob is 25 jaar oud"\par
\par
# *args (variabel aantal positionele argumenten)\par
def som(*getallen):\par
    return sum(getallen)\par
\par
print(som(1, 2, 3, 4))  # 10\par
\par
# **kwargs (variabel aantal keyword argumenten)\par
def toon_info(**info):\par
    for key, value in info.items():\par
        print(f"\{key\}: \{value\}")\par
\par
toon_info(naam="Alice", leeftijd=30, stad="Amsterdam")\f0\par

\pard\sa200\sl276\slmult1\b Lambda functies\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Lambda (anonieme functie)\par
kwadraat = lambda x: x**2\par
print(kwadraat(5))  # 25\par
\par
# Lambda met meerdere parameters\par
som = lambda a, b: a + b\par
print(som(3, 4))  # 7\par
\par
# Lambda met filter\par
getallen = [1, 2, 3, 4, 5, 6]\par
even = list(filter(lambda x: x % 2 == 0, getallen))  # [2, 4, 6]\par
\par
# Lambda met map\par
kwadraten = list(map(lambda x: x**2, getallen))  # [1, 4, 9, 16, 25, 36]\f0\par

\pard\sa200\sl276\slmult1\b\fs24 5. MODULES\b0\fs22\par

\pard\sa200\sl276\slmult1\b Importeren\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Hele module importeren\par
import math\par
print(math.sqrt(16))  # 4.0\par
\par
# Specifieke functies importeren\par
from math import sqrt, pi\par
print(sqrt(16))  # 4.0\par
\par
# Module met alias\par
import math as m\par
print(m.sqrt(16))  # 4.0\f0\par

\pard\sa200\sl276\slmult1\b Standaard modules\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # math - wiskundige functies\par
import math\par
print(math.sqrt(16))  # 4.0\par
print(math.sin(math.pi/2))  # 1.0\par
\par
# random - willekeurige getallen\par
import random\par
print(random.randint(1, 10))  # willekeurig getal tussen 1 en 10\par
print(random.choice(["appel", "peer", "banaan"]))  # willekeurig element\par
\par
# datetime - datum en tijd\par
from datetime import datetime, timedelta\par
nu = datetime.now()  # huidige datum en tijd\par
morgen = nu + timedelta(days=1)  # datum en tijd van morgen\par
\par
# os - besturingssysteem interface\par
import os\par
print(os.getcwd())  # huidige werkdirectory\par
print(os.listdir())  # bestanden in huidige directory\f0\par

\pard\sa200\sl276\slmult1\b\fs24 6. BESTANDSVERWERKING\b0\fs22\par

\pard\sa200\sl276\slmult1\b Bestanden lezen en schrijven\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Bestand lezen\par
with open("bestand.txt", "r") as f:\par
    inhoud = f.read()  # hele bestand lezen\par
    print(inhoud)\par
\par
# Regel voor regel lezen\par
with open("bestand.txt", "r") as f:\par
    for regel in f:\par
        print(regel.strip())\par
\par
# Bestand schrijven\par
with open("output.txt", "w") as f:\par
    f.write("Hallo, wereld!\\n")\par
    f.write("Dit is een test.")\par
\par
# Toevoegen aan bestand\par
with open("output.txt", "a") as f:\par
    f.write("\\nDeze regel wordt toegevoegd.")\f0\par

\pard\sa200\sl276\slmult1\b CSV en JSON\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # CSV lezen\par
import csv\par
\par
with open("data.csv", "r", newline="") as f:\par
    reader = csv.reader(f)\par
    for rij in reader:\par
        print(rij)\par
\par
# CSV met headers\par
with open("data.csv", "r", newline="") as f:\par
    reader = csv.DictReader(f)\par
    for rij in reader:\par
        print(rij["naam"], rij["leeftijd"])\par
\par
# JSON verwerken\par
import json\par
\par
data = \{"naam": "Alice", "leeftijd": 30\}\par
\par
# Naar JSON string\par
json_str = json.dumps(data)\par
print(json_str)  # {"naam": "Alice", "leeftijd": 30}\par
\par
# Van JSON string\par
parsed = json.loads(json_str)\par
print(parsed["naam"])  # "Alice"\par
\par
# Naar bestand\par
with open("data.json", "w") as f:\par
    json.dump(data, f, indent=4)\par
\par
# Van bestand\par
with open("data.json", "r") as f:\par
    loaded = json.load(f)\f0\par

\pard\sa200\sl276\slmult1\b\fs24 7. EXCEPTIONS\b0\fs22\par

\pard\sa200\sl276\slmult1\b Try-except\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Basis try-except\par
try:\par
    x = int(input("Voer een getal in: "))\par
    print(f"Je hebt \{x\} ingevoerd")\par
except ValueError:\par
    print("Dat is geen geldig getal!")\par
\par
# Meerdere exceptions\par
try:\par
    x = int(input("Voer een getal in: "))\par
    resultaat = 10 / x\par
    print(f"10 / \{x\} = \{resultaat\}")\par
except ValueError:\par
    print("Dat is geen geldig getal!")\par
except ZeroDivisionError:\par
    print("Delen door nul is niet toegestaan!")\par
\par
# Else en finally\par
try:\par
    bestand = open("data.txt", "r")\par
    inhoud = bestand.read()\par
except FileNotFoundError:\par
    print("Bestand niet gevonden")\par
else:\par
    print(f"Bestand gelezen: \{len(inhoud)\} tekens")\par
finally:\par
    bestand.close() if 'bestand' in locals() else None\f0\par

\pard\sa200\sl276\slmult1\b\fs24 8. OBJECT-ORIENTED PROGRAMMING\b0\fs22\par

\pard\sa200\sl276\slmult1\b Klassen en objecten\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Klasse definitie\par
class Persoon:\par
    def __init__(self, naam, leeftijd):\par
        self.naam = naam\par
        self.leeftijd = leeftijd\par
    \par
    def groet(self):\par
        return f"Hallo, ik ben \{self.naam\}"\par
    \par
    def verjaar(self):\par
        self.leeftijd += 1\par
        return f"\{self.naam\} is nu \{self.leeftijd\} jaar"\par
\par
# Object aanmaken\par
p1 = Persoon("Alice", 30)\par
p2 = Persoon("Bob", 25)\par
\par
# Methoden aanroepen\par
print(p1.groet())  # "Hallo, ik ben Alice"\par
print(p2.verjaar())  # "Bob is nu 26 jaar"\f0\par

\pard\sa200\sl276\slmult1\b Overerving\b0\par
\pard\li360\sa200\sl276\slmult1\f1 # Basis klasse\par
class Dier:\par
    def __init__(self, naam):\par
        self.naam = naam\par
    \par
    def geluid(self):\par
        return "Onbekend geluid"\par
\par
# Afgeleide klasse\par
class Hond(Dier):\par
    def geluid(self):\par
        return "Woef!"\par
\par
class Kat(Dier):\par
    def geluid(self):\par
        return "Miauw!"\par
\par
# Objecten aanmaken\par
dier = Dier("Dier")\par
hond = Hond("Rex")\par
kat = Kat("Felix")\par
\par
# Methoden aanroepen\par
print(dier.geluid())  # "Onbekend geluid"\par
print(hond.geluid())  # "Woef!"\par
print(kat.geluid())   # "Miauw!"\f0\par

\pard\sa200\sl276\slmult1\b\fs24 9. REGULIERE EXPRESSIES\b0\fs22\par

\pard\sa200\sl276\slmult1\b Basis regex\b0\par
\pard\li360\sa200\sl276\slmult1\f1 import re\par
\par
tekst = "Mijn telefoonnummer is 06-12345678 en mijn e-<NAME_EMAIL>"\par
\par
# Zoeken naar patroon\par
telefoon = re.search(r"\\d\\d-\\d{8}", tekst)\par
print(telefoon.group())  # "06-12345678"\par
\par
# Alle matches vinden\par
emails = re.findall(r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]\{2,\}", tekst)\par
print(emails)  # ["<EMAIL>"]\par
\par
# Vervangen\par
nieuw_tekst = re.sub(r"\\d\\d-\\d{8}", "TELEFOONNUMMER", tekst)\par
print(nieuw_tekst)  # "Mijn telefoonnummer is TELEFOONNUMMER en mijn e-<NAME_EMAIL>"\f0\par
}
