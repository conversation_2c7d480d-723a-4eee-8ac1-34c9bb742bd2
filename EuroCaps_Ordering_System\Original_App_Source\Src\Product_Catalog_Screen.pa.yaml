# ************************************************************************************************
# Warning: YAML source code for Canvas Apps should only be used to review changes made within Power Apps Studio and for minor edits (Preview).
# Use the maker portal to create and edit your Power Apps.
#
# The schema file for Canvas Apps is available at https://go.microsoft.com/fwlink/?linkid=2304907
#
# For more information, visit https://go.microsoft.com/fwlink/?linkid=2292623
# ************************************************************************************************
Screens:
  Product_Catalog_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(56, 96, 178, 1)
    Children:
      - HeaderBar:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(56, 96, 178, 1)
            Height: =60
            Width: =1366
            X: =0
            Y: =0
      - PageTitle:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="Products"
            Size: =18
            Color: =RGBA(51, 51, 51, 1)
            Height: =40
            Width: =200
            X: =20
            Y: =80
      - SearchInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            Font: =Font.'Open Sans'
            HintText: ="Search Products..."
            BorderColor: =RGBA(56, 96, 178, 1)
            Height: =40
            Width: =400
            X: =20
            Y: =140
      - Product1Card:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            Height: =180
            Width: =250
            X: =20
            Y: =200
      - Product1Image:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(93, 64, 55, 1)
            Height: =80
            Width: =200
            X: =45
            Y: =220
      - Product1Name:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="Espresso"
            Size: =14
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =200
            X: =45
            Y: =310
      - Product1Type:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            Text: ="Classic"
            Size: =12
            Color: =RGBA(128, 128, 128, 1)
            Height: =20
            Width: =100
            X: =45
            Y: =330
      - Product1AddButton:
          Control: Classic/Button@2.2.0
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="+ Add"
            Fill: =RGBA(76, 175, 80, 1)
            Color: =RGBA(255, 255, 255, 1)
            Height: =30
            Width: =80
            X: =165
            Y: =345
      - Product2Card:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            Height: =180
            Width: =250
            X: =290
            Y: =200
      - Product2Image:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(141, 110, 99, 1)
            Height: =80
            Width: =200
            X: =315
            Y: =220
      - Product2Name:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="Lungo"
            Size: =14
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =200
            X: =315
            Y: =310
      - Product2Type:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            Text: ="Intense"
            Size: =12
            Color: =RGBA(128, 128, 128, 1)
            Height: =20
            Width: =100
            X: =315
            Y: =330
      - Product2AddButton:
          Control: Classic/Button@2.2.0
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="+ Add"
            Fill: =RGBA(76, 175, 80, 1)
            Color: =RGBA(255, 255, 255, 1)
            Height: =30
            Width: =80
            X: =435
            Y: =345
      - Product3Card:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            Height: =180
            Width: =250
            X: =560
            Y: =200
      - Product3Image:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(33, 33, 33, 1)
            Height: =80
            Width: =200
            X: =585
            Y: =220
      - Product3Name:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="Ristretto"
            Size: =14
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =200
            X: =585
            Y: =310
      - Product3Type:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            Text: ="Strong"
            Size: =12
            Color: =RGBA(128, 128, 128, 1)
            Height: =20
            Width: =100
            X: =585
            Y: =330
      - Product3AddButton:
          Control: Classic/Button@2.2.0
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="+ Add"
            Fill: =RGBA(76, 175, 80, 1)
            Color: =RGBA(255, 255, 255, 1)
            Height: =30
            Width: =80
            X: =705
            Y: =345
