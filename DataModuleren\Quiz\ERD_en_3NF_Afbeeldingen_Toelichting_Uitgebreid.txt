# Toelichting bij de ERD en 3NF Afbeeldingen

Deze afbeeldingen zijn gemaakt als visuele ondersteuning bij de quiz over Entity Relationship Diagrams (ERD) en de Derde Normaalvorm (3NF). Ze illustreren de belangrijkste concepten die in de quiz worden behandeld.

## 1. Many-to-Many (Veel-op-veel) Relaties

### many_to_many_incorrect.jpg
Deze afbeelding toont een incorrecte implementatie van een veel-op-veel relatie tussen Superhelden en Groepen. Het probleem is dat de primaire sleutel (SuperheldID) niet uniek is, omdat een superheld zoals Thor tot meerdere groepen kan behoren.

### many_to_many_correct.jpg
Deze afbeelding toont de correcte implementatie van een veel-op-veel relatie met drie tabellen: Superheld, Groep, en een koppeltabel Superheld_Groep. De koppeltabel heeft een samengestelde primaire sleutel van SuperheldID en GroepID.

### superhelden_many_to_many.jpg
Deze afbeelding combineert beide bovenstaande concepten in één overzicht, met zowel de incorrecte als de correcte implementatie van een veel-op-veel relatie.

## 2. Normalisatie

### normalisatie_0nf_1nf.jpg
Deze afbeelding toont de eerste stap in het normalisatieproces: van 0NF (niet-genormaliseerd) naar 1NF (eerste normaalvorm). Het laat zien hoe berekende velden worden verwijderd en een samengestelde primaire sleutel wordt gedefinieerd.

### normalisatie_2nf.jpg
Deze afbeelding toont de tweede normaalvorm (2NF), waarin partiële afhankelijkheden worden verwijderd door de tabel te splitsen in een Order tabel en een OrderDetail tabel.

### normalisatie_3nf.jpg
Deze afbeelding toont de derde normaalvorm (3NF), waarin transitieve afhankelijkheden worden verwijderd door aparte tabellen te creëren voor Klant en Product.

### normalisatie_3nf_voorbeeld.jpg
Deze afbeelding combineert alle stappen van het normalisatieproces in één overzicht, van 0NF tot 3NF.

## 3. Recursieve Relaties

### recursieve_relatie_conceptueel.jpg
Deze afbeelding toont een conceptueel ERD van een recursieve relatie, waarbij een entiteit (Verkoper) een relatie heeft met zichzelf (Managet). Het voorbeeld is gebaseerd op de relatie tussen managers en verkopers.

### recursieve_relatie_logisch.jpg
Deze afbeelding toont een logisch ERD van dezelfde recursieve relatie, met een vreemde sleutel (ManagerID) die verwijst naar de primaire sleutel (VerkoperID) van dezelfde tabel.

### recursieve_relatie_voorbeeld.jpg
Deze afbeelding combineert beide bovenstaande concepten in één overzicht, met zowel het conceptuele als het logische ERD van een recursieve relatie.

## 4. ERD Types

### erd_conceptueel.jpg
Deze afbeelding toont een conceptueel ERD, het hoogste abstractieniveau dat zich richt op entiteiten en hun relaties zonder technische details. Het is bedoeld voor communicatie met niet-technische stakeholders.

### erd_logisch.jpg
Deze afbeelding toont een logisch ERD, het middenniveau van abstractie dat attributen en primaire sleutels toevoegt. Veel-op-veel relaties worden opgesplitst in associatieve entiteiten.

### erd_fysiek.jpg
Deze afbeelding toont een fysiek ERD, het laagste abstractieniveau dat alle technische details bevat voor implementatie, inclusief datatypen, constraints en vreemde sleutels.

### erd_types_vergelijking.jpg
Deze afbeelding combineert alle drie de ERD types in één overzicht, met een vergelijkingstabel van hun kenmerken.

## Gebruik van de afbeeldingen

Deze afbeeldingen kunnen worden gebruikt als:
- Visuele ondersteuning bij het bestuderen van de concepten in de quiz
- Referentiemateriaal tijdens het beantwoorden van de quizvragen
- Hulpmiddel om de relatie tussen verschillende concepten beter te begrijpen

De afbeeldingen zijn specifiek ontworpen om de concepten uit de "IT Datamodelleren 2.7.pdf" cursus te illustreren en sluiten direct aan bij de vragen in de quiz.
