Login_Screen As screen:
    Fill: =RGBA(245, 245, 245, 1)
    LoadingSpinnerColor: =RGBA(74, 111, 165, 1)

    Background As rectangle:
        Fill: =RGBA(245, 245, 245, 1)
        Height: =Parent.Height
        Width: =Parent.Width
        X: =0
        Y: =0

    Logo As image:
        Height: =150
        Image: ="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjNGE2ZmE1Ii8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+RXVyb0NhcHM8L3RleHQ+Cjwvc3ZnPg=="
        Width: =150
        X: =(Parent.Width - Self.Width) / 2
        Y: =100

    AppTitle As label:
        Align: =Align.Center
        Color: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =18
        Text: ="Order Management Pro"
        Width: =400
        X: =(Parent.Width - Self.Width) / 2
        Y: =270

    LoginPanel As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =300
        RadiusBottomLeft: =8
        RadiusBottomRight: =8
        RadiusTopLeft: =8
        RadiusTopRight: =8
        Width: =400
        X: =(Parent.Width - Self.Width) / 2
        Y: =330

    UsernameLabel As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =25
        Size: =12
        Text: ="Username:"
        Width: =100
        X: =LoginPanel.X + 20
        Y: =LoginPanel.Y + 30

    UsernameInput As text:
        BorderColor: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        Height: =40
        HintText: ="Enter your username"
        HoverBorderColor: =RGBA(74, 111, 165, 1)
        Size: =12
        Width: =360
        X: =LoginPanel.X + 20
        Y: =LoginPanel.Y + 55

    PasswordLabel As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =25
        Size: =12
        Text: ="Password:"
        Width: =100
        X: =LoginPanel.X + 20
        Y: =LoginPanel.Y + 110

    PasswordInput As text:
        BorderColor: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        Height: =40
        HintText: ="Enter your password"
        HoverBorderColor: =RGBA(74, 111, 165, 1)
        Mode: =TextMode.Password
        Size: =12
        Width: =360
        X: =LoginPanel.X + 20
        Y: =LoginPanel.Y + 135

    RememberMeCheckbox As checkbox:
        CheckboxBorderColor: =RGBA(74, 111, 165, 1)
        CheckmarkFill: =RGBA(74, 111, 165, 1)
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =30
        Size: =12
        Text: ="Remember me"
        Width: =150
        X: =LoginPanel.X + 20
        Y: =LoginPanel.Y + 190

    LoginButton As button:
        BorderColor: =RGBA(74, 111, 165, 1)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =45
        HoverFill: =RGBA(58, 95, 149, 1)
        OnSelect: |-
            =If(
                !IsBlank(UsernameInput.Text) && !IsBlank(PasswordInput.Text),
                Navigate(Dashboard_Screen, ScreenTransition.Fade),
                Notify("Please enter both username and password", NotificationType.Error)
            )
        PressedFill: =RGBA(42, 79, 133, 1)
        Size: =14
        Text: ="LOGIN"
        Width: =200
        X: =LoginPanel.X + 20
        Y: =LoginPanel.Y + 235

    ForgotPasswordLink As button:
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(74, 111, 165, 1)
        Fill: =RGBA(0, 0, 0, 0)
        Font: =Font.Arial
        Height: =30
        HoverFill: =RGBA(0, 0, 0, 0)
        Size: =12
        Text: ="Forgot Password?"
        Width: =120
        X: =LoginPanel.X + 260
        Y: =LoginPanel.Y + 245

    Footer As label:
        Align: =Align.Center
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =20
        Size: =10
        Text: ="© 2025 EuroCaps"
        Width: =200
        X: =(Parent.Width - Self.Width) / 2
        Y: =720
