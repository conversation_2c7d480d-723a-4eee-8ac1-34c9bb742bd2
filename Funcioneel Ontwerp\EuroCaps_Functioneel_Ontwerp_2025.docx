# EuroCaps Functioneel Ontwerp 2025

## Inhoudsopgave

1. [Inleiding](#1-inleiding)
2. [Projectomschrijving](#2-projectomschrijving)
3. [Stakeholders](#3-stakeholders)
4. [Functionele Requirements](#4-functionele-requirements)
5. [User Stories](#5-user-stories)
6. [Systeemarchitectuur](#6-systeemarchitectuur)
7. [Procesdiagrammen](#7-procesdiagrammen)
8. [Database Ontwerp](#8-database-ontwerp)
9. [Implementatieplan](#9-implementatieplan)
10. [Bijlagen](#10-bijlagen)

---

## 1. Inleiding

### 1.1 Doel van het document
Dit functioneel ontwerp beschrijft de requirements en specificaties voor het EuroCaps productiemanagement systeem. Het systeem ondersteunt de kernprocessen van koffiecapsule productie, kwaliteitscontrole en logistiek.

### 1.2 Scope
Het systeem omvat:
- Productieprocessen (grinding, filling, packaging)
- Kwaliteitsmanagement
- Voorraadbeheersing
- Orderverwerking
- Rapportage en analytics

### 1.3 Doelgroep
- Productiemanagers
- Kwaliteitscontroleurs
- Logistiek medewerkers
- IT-beheerders
- Management

---

## 2. Projectomschrijving

### 2.1 Achtergrond
EuroCaps is een toonaangevende producent van koffiecapsules in Nederland. Het bedrijf produceert hoogwaardige koffiecapsules voor verschillende merken en retailers. Om de groeiende vraag te kunnen ondersteunen en de operationele efficiëntie te verbeteren, heeft EuroCaps behoefte aan een geïntegreerd productiemanagement systeem.

### 2.2 Huidige situatie
- Handmatige registratie van productiegegevens
- Beperkte traceerbaarheid van producten
- Inefficiënte kwaliteitscontrole processen
- Gebrek aan real-time inzicht in productiestatistieken

### 2.3 Gewenste situatie
- Geautomatiseerde registratie van productiegegevens
- Volledige traceerbaarheid van grondstof tot eindproduct
- Geïntegreerde kwaliteitscontrole met real-time monitoring
- Dashboard met KPI's en productiestatistieken

---

## 3. Stakeholders

### 3.1 Primaire Stakeholders

#### Productiemanager
- **Rol**: Verantwoordelijk voor de dagelijkse productieoperaties
- **Belangen**: Efficiënte productie, minimale downtime, kwaliteitsborging
- **Requirements**: Real-time productieoverzicht, capaciteitsplanning, performance monitoring

#### Kwaliteitscontroleur
- **Rol**: Bewaakt productkwaliteit en compliance
- **Belangen**: Kwaliteitsstandaarden, traceerbaarheid, compliance
- **Requirements**: Kwaliteitsregistratie, afwijkingsrapportage, trend analyses

#### Logistiek Coördinator
- **Rol**: Beheert voorraad en distributie
- **Belangen**: Voorraadoptimalisatie, leverbetrouwbaarheid
- **Requirements**: Voorraadoverzicht, ordertracking, leveringsplanning

### 3.2 Secundaire Stakeholders

#### IT-beheerder
- **Rol**: Technisch beheer van het systeem
- **Belangen**: Systeemstabiliteit, beveiliging, onderhoudbaarheid
- **Requirements**: Monitoring tools, backup procedures, gebruikersbeheer

#### Management
- **Rol**: Strategische besluitvorming
- **Belangen**: ROI, operationele excellentie, groei
- **Requirements**: Executive dashboards, KPI rapportage, trend analyses

---

## 4. Functionele Requirements

### 4.1 Productiemanagement
- **FR-001**: Het systeem moet productieorders kunnen aanmaken en beheren
- **FR-002**: Het systeem moet real-time productiestatistieken tonen
- **FR-003**: Het systeem moet machine status en performance bijhouden
- **FR-004**: Het systeem moet productiecapaciteit kunnen plannen

### 4.2 Kwaliteitscontrole
- **FR-005**: Het systeem moet kwaliteitsmetingen kunnen registreren
- **FR-006**: Het systeem moet afwijkingen automatisch detecteren
- **FR-007**: Het systeem moet kwaliteitsrapporten genereren
- **FR-008**: Het systeem moet traceerbaarheid van batch tot eindproduct bieden

### 4.3 Voorraadbeheersing
- **FR-009**: Het systeem moet voorraadniveaus real-time bijhouden
- **FR-010**: Het systeem moet automatische herbestelpunten ondersteunen
- **FR-011**: Het systeem moet grondstofverbruik per product tracken
- **FR-012**: Het systeem moet FIFO principe ondersteunen

### 4.4 Orderverwerking
- **FR-013**: Het systeem moet klantorders kunnen verwerken
- **FR-014**: Het systeem moet leveringsplanning ondersteunen
- **FR-015**: Het systeem moet orderstatussen bijhouden
- **FR-016**: Het systeem moet factuurgegevens kunnen exporteren

---

## 5. User Stories

### 5.1 Productiemanager Stories

#### US-001: Productieorder aanmaken
**Als** productiemanager
**Wil ik** een nieuwe productieorder kunnen aanmaken
**Zodat** ik de productie kan plannen en starten

**Acceptatiecriteria:**
- Ik kan producttype selecteren
- Ik kan gewenste hoeveelheid invoeren
- Ik kan prioriteit instellen
- Ik kan geplande startdatum invoeren
- Het systeem controleert beschikbaarheid van grondstoffen

#### US-002: Productieoverzicht bekijken
**Als** productiemanager
**Wil ik** een real-time overzicht van alle lopende productieorders
**Zodat** ik de voortgang kan monitoren en bijsturen waar nodig

**Acceptatiecriteria:**
- Ik zie alle actieve productieorders
- Ik zie de status van elke order
- Ik zie verwachte voltooiingstijd
- Ik kan orders filteren op status/prioriteit

#### US-003: Machine performance monitoren
**Als** productiemanager
**Wil ik** de performance van productiemachines kunnen monitoren
**Zodat** ik proactief onderhoud kan plannen

**Acceptatiecriteria:**
- Ik zie machine status (actief/inactief/onderhoud)
- Ik zie productiviteitsmetrics per machine
- Ik zie historische performance data
- Ik krijg alerts bij afwijkende performance

### 5.2 Kwaliteitscontroleur Stories

#### US-004: Kwaliteitsmetingen registreren
**Als** kwaliteitscontroleur
**Wil ik** kwaliteitsmetingen kunnen invoeren en opslaan
**Zodat** ik de productkwaliteit kan bewaken

**Acceptatiecriteria:**
- Ik kan metingen invoeren per productiebatch
- Ik kan verschillende kwaliteitsparameters meten
- Het systeem valideert ingevoerde waarden
- Afwijkingen worden automatisch gemarkeerd

#### US-005: Afwijkingsrapport genereren
**Als** kwaliteitscontroleur
**Wil ik** rapporten kunnen genereren over kwaliteitsafwijkingen
**Zodat** ik trends kan identificeren en verbeteracties kan voorstellen

**Acceptatiecriteria:**
- Ik kan rapportperiode selecteren
- Ik kan filteren op producttype
- Rapport toont afwijkingspercentages
- Rapport bevat trend analyses

### 5.3 Logistiek Coördinator Stories

#### US-006: Voorraadniveaus controleren
**Als** logistiek coördinator
**Wil ik** actuele voorraadniveaus kunnen bekijken
**Zodat** ik tijdig kan herbestellen

**Acceptatiecriteria:**
- Ik zie voorraad per grondstof/product
- Ik zie herbestelpunten en -hoeveelheden
- Ik krijg alerts bij lage voorraad
- Ik kan voorraadmutaties bekijken

#### US-007: Leveringsplanning maken
**Als** logistiek coördinator
**Wil ik** leveringen kunnen plannen en tracken
**Zodat** ik klanten tijdig kan informeren

**Acceptatiecriteria:**
- Ik kan leveringsdatums plannen
- Ik kan transporteurs toewijzen
- Ik kan leveringsstatus bijhouden
- Klanten ontvangen automatische updates

### 5.4 Management Stories

#### US-008: KPI Dashboard bekijken
**Als** manager
**Wil ik** een overzicht van belangrijke KPI's kunnen bekijken
**Zodat** ik de bedrijfsprestaties kan monitoren

**Acceptatiecriteria:**
- Ik zie productie-efficiëntie metrics
- Ik zie kwaliteitspercentages
- Ik zie financiële indicatoren
- Ik kan tijdsperiodes selecteren

#### US-009: Trend analyses uitvoeren
**Als** manager
**Wil ik** trend analyses kunnen bekijken
**Zodat** ik strategische beslissingen kan nemen

**Acceptatiecriteria:**
- Ik zie historische trends
- Ik kan verschillende metrics vergelijken
- Ik kan prognoses bekijken
- Ik kan rapporten exporteren

### 5.5 IT-beheerder Stories

#### US-010: Gebruikers beheren
**Als** IT-beheerder
**Wil ik** gebruikersaccounts kunnen beheren
**Zodat** ik toegang kan controleren

**Acceptatiecriteria:**
- Ik kan nieuwe gebruikers aanmaken
- Ik kan rollen en rechten toewijzen
- Ik kan accounts deactiveren
- Ik kan inlogactiviteit monitoren

#### US-011: Systeem monitoring
**Als** IT-beheerder
**Wil ik** de systeemprestaties kunnen monitoren
**Zodat** ik problemen proactief kan oplossen

**Acceptatiecriteria:**
- Ik zie server performance metrics
- Ik krijg alerts bij problemen
- Ik kan logs bekijken
- Ik kan backup status controleren

---

## 6. Systeemarchitectuur

### 6.1 Architectuuroverzicht
Het EuroCaps systeem volgt een gelaagde architectuur:

1. **Presentatielaag**: Web-based dashboard en mobile apps
2. **Applicatielaag**: Business logic en API's
3. **Datalaag**: Relationele database met real-time data processing
4. **Integratielaag**: Koppelingen met machines en externe systemen

### 6.2 Technische Componenten
- **Frontend**: React.js dashboard, Progressive Web App
- **Backend**: Node.js/Express API server
- **Database**: MySQL/PostgreSQL relationele database
- **Real-time**: WebSocket verbindingen voor live updates
- **Integratie**: REST API's en MQTT voor machine communicatie

### 6.3 Deployment
- **Cloud hosting**: AWS/Azure cloud platform
- **Containerization**: Docker containers met Kubernetes orchestration
- **Load balancing**: Voor high availability en performance
- **Backup**: Automatische dagelijkse backups met point-in-time recovery
- **Security**: SSL/TLS encryptie, VPN toegang, multi-factor authenticatie
- **Monitoring**: Application Performance Monitoring (APM) met alerting

### 6.4 Integraties
- **ERP Systeem**: Koppeling met bestaande ERP voor financiële data
- **Machine Interfaces**: MQTT/OPC-UA voor real-time machine data
- **Email/SMS**: Notificaties en alerts
- **Barcode/RFID**: Voor product tracking en identificatie
- **Weegschalen**: Automatische gewichtsregistratie
- **Temperatuursensoren**: Monitoring van productieomgeving

---

## 7. Procesdiagrammen

### 7.1 Hoofdproces Overzicht
[Diagram wordt toegevoegd via draw.io]

Het hoofdproces van EuroCaps omvat:
1. Orderontvangst
2. Productieplanningen
3. Grondstofbeheer
4. Productieuitvoering (Grinding → Filling → Packaging)
5. Kwaliteitscontrole
6. Voorraadopslag
7. Verzending

### 7.2 Productieproces Detail
[Diagram wordt toegevoegd via draw.io]

Gedetailleerd productieproces:
1. **Grinding**: Koffiebonen malen tot gewenste maalgraad
2. **Filling**: Capsules vullen met 4.0-6.0g koffie
3. **Sealing**: Capsules afsluiten met aluminiumfolie
4. **Packaging**: Verpakken in dozen van 10, 20 of 44 stuks
5. **Quality Check**: Kwaliteitscontrole op elk niveau

### 7.3 Kwaliteitscontrole Proces
[Diagram wordt toegevoegd via draw.io]

Kwaliteitscontrole stappen:
1. Inkomende grondstofcontrole
2. In-process controles tijdens productie
3. Eindproduct controle
4. Afwijkingsregistratie en -behandeling
5. Rapportage en trend analyse

---

## 8. Database Ontwerp

### 8.1 Conceptueel Model
Het database model ondersteunt:
- Productieorders en -batches
- Grondstof- en productvoorraad
- Kwaliteitsmetingen en -afwijkingen
- Klant- en leveranciergegevens
- Machine- en procesdata

### 8.2 Belangrijkste Entiteiten
- **ProductionOrder**: Productieorders
- **Batch**: Productiebatches
- **Product**: Eindproducten en grondstoffen
- **QualityCheck**: Kwaliteitsmetingen
- **Inventory**: Voorraadregistratie
- **Customer**: Klantgegevens
- **Supplier**: Leveranciergegevens
- **Machine**: Machine informatie

### 8.3 Relaties
- Customer → ProductionOrder (1:N): Een klant kan meerdere orders plaatsen
- ProductionOrder → OrderItem (1:N): Een order bevat meerdere producten
- Product → OrderItem (1:N): Een product kan in meerdere orders voorkomen
- ProductionOrder → Batch (1:N): Een order wordt geproduceerd in batches
- Batch → QualityCheck (1:N): Elke batch ondergaat meerdere kwaliteitscontroles
- Batch → ProductionStep (1:N): Een batch doorloopt meerdere productiestappen
- Machine → ProductionStep (1:N): Een machine voert meerdere stappen uit
- Product → Inventory (1:1): Elk product heeft een voorraadregistratie
- Supplier → RawMaterial (1:N): Een leverancier levert meerdere grondstoffen

### 8.4 Database Performance
- **Indexering**: Primaire en vreemde sleutels, veelgebruikte zoekvelden
- **Partitionering**: Grote tabellen gepartitioneerd op datum
- **Archivering**: Automatische archivering van oude data
- **Backup strategie**: Dagelijkse full backup, continue transaction log backup
- **Replicatie**: Read-only replica's voor rapportage

### 8.5 Data Governance
- **Data kwaliteit**: Validatieregels en constraints
- **Audit trail**: Logging van alle wijzigingen
- **Retention policy**: Bewaarperiodes per data type
- **Privacy**: GDPR compliance voor persoonlijke data
- **Security**: Role-based access control, data encryptie

---

## 9. Implementatieplan

### 9.1 Fasen
**Fase 1: Basis functionaliteit (3 maanden)**
- Productieorder management
- Basis voorraadbeheersing
- Gebruikersbeheer

**Fase 2: Kwaliteitsmodule (2 maanden)**
- Kwaliteitsregistratie
- Afwijkingsbeheer
- Basis rapportage

**Fase 3: Integratie en Analytics (3 maanden)**
- Machine integraties
- Advanced analytics
- Mobile applicaties

### 9.2 Resources
- 1 Project Manager
- 2 Backend Developers
- 1 Frontend Developer
- 1 Database Specialist
- 1 QA Tester

### 9.3 Risico's en Mitigaties

| Risico | Impact | Waarschijnlijkheid | Mitigatie |
|--------|--------|-------------------|-----------|
| Machine integratie complexiteit | Hoog | Gemiddeld | Proof of concept, pilot implementatie |
| Data migratie problemen | Hoog | Laag | Uitgebreide testing, parallelle systemen |
| Gebruikersacceptatie | Gemiddeld | Gemiddeld | Training, change management |
| Performance problemen | Gemiddeld | Laag | Load testing, performance monitoring |
| Security vulnerabilities | Hoog | Laag | Security audits, penetration testing |
| Budget overschrijding | Gemiddeld | Gemiddeld | Strakke projectcontrole, contingency |

### 9.4 Success Criteria
- **Functioneel**: Alle user stories geïmplementeerd en getest
- **Performance**: Response tijd < 2 seconden voor 95% van requests
- **Availability**: 99.5% uptime tijdens kantooruren
- **User Adoption**: 90% van gebruikers actief binnen 3 maanden
- **ROI**: Positieve return on investment binnen 18 maanden

### 9.5 Training en Change Management
- **Gebruikerstraining**: 2 dagen training per gebruikersgroep
- **Train-the-trainer**: Interne super users opleiden
- **Documentatie**: Gebruikershandleidingen en video tutorials
- **Support**: Helpdesk tijdens go-live periode
- **Feedback loops**: Regelmatige evaluatie en verbetering

---

## 10. Bijlagen

### 10.1 Stakeholder Matrix

| Stakeholder | Invloed | Belang | Strategie | Communicatie |
|-------------|---------|--------|-----------|--------------|
| Productiemanager | Hoog | Hoog | Manage closely | Wekelijks |
| Kwaliteitscontroleur | Hoog | Hoog | Manage closely | Wekelijks |
| Logistiek Coördinator | Gemiddeld | Hoog | Keep satisfied | Tweewekelijks |
| IT-beheerder | Hoog | Gemiddeld | Keep informed | Maandelijks |
| Management | Hoog | Gemiddeld | Keep informed | Maandelijks |
| Productie Operators | Gemiddeld | Hoog | Keep satisfied | Tweewekelijks |

### 10.2 Technische Specificaties

#### 10.2.1 Hardware Requirements
- **Server**: Minimum 16GB RAM, 8 CPU cores, 500GB SSD
- **Database**: Dedicated database server met 32GB RAM
- **Network**: Gigabit ethernet, redundante verbindingen
- **Backup**: NAS systeem voor lokale backups

#### 10.2.2 Software Requirements
- **Operating System**: Linux Ubuntu 20.04 LTS of Windows Server 2019
- **Database**: MySQL 8.0 of PostgreSQL 13
- **Web Server**: Nginx of Apache
- **Application Server**: Node.js 16+ of .NET Core 6
- **Monitoring**: Prometheus + Grafana

#### 10.2.3 Security Requirements
- **Authentication**: Multi-factor authentication verplicht
- **Authorization**: Role-based access control (RBAC)
- **Encryption**: TLS 1.3 voor data in transit, AES-256 voor data at rest
- **Audit**: Volledige audit trail van alle acties
- **Compliance**: GDPR, ISO 27001 compliance

### 10.3 Test Scenarios

#### 10.3.1 Functionele Tests
- **User Story US-001**: Productieorder aanmaken
  - Test 1: Succesvolle order aanmaak
  - Test 2: Validatie bij ontbrekende gegevens
  - Test 3: Grondstof beschikbaarheidscheck

- **User Story US-004**: Kwaliteitsmetingen registreren
  - Test 1: Normale waarden invoeren
  - Test 2: Afwijkende waarden detectie
  - Test 3: Batch koppeling validatie

#### 10.3.2 Performance Tests
- **Load Test**: 100 gelijktijdige gebruikers
- **Stress Test**: Piek belasting simulatie
- **Volume Test**: Grote datasets verwerking
- **Endurance Test**: 24-uur continue belasting

#### 10.3.3 Security Tests
- **Penetration Testing**: Externe security audit
- **Vulnerability Scanning**: Automatische scans
- **Access Control Testing**: Autorisatie verificatie
- **Data Protection Testing**: Encryptie validatie

### 10.4 Glossary

| Term | Definitie |
|------|-----------|
| Batch | Een productielot van koffiecapsules |
| Grinding | Het maalproces van koffiebonen |
| Filling | Het vulproces van capsules met koffie |
| Sealing | Het afsluiten van capsules met folie |
| Packaging | Het verpakken van capsules in dozen |
| NOK | Not OK - product dat niet voldoet aan kwaliteitseisen |
| KPI | Key Performance Indicator |
| ERP | Enterprise Resource Planning |
| API | Application Programming Interface |
| MQTT | Message Queuing Telemetry Transport |

### 10.5 Referenties

- ISO 9001:2015 - Quality Management Systems
- ISO 22000:2018 - Food Safety Management
- HACCP Guidelines - Hazard Analysis Critical Control Points
- GDPR - General Data Protection Regulation
- EuroCaps Internal Process Documentation (2024)
- Industry Best Practices for Food Manufacturing IT Systems

---

**Document Versie**: 1.0
**Datum**: Januari 2025
**Auteur**: EuroCaps Project Team
**Goedgekeurd door**: [Management]
**Status**: Definitief Concept
**Volgende Review**: Februari 2025
