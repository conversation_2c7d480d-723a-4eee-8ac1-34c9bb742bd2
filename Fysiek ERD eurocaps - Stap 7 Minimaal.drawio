<mxfile host="Electron" modified="2024-10-22T16:00:00.000Z" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.1.1 Chrome/132.0.6834.210 Electron/34.3.3 Safari/537.36" version="26.1.1">
  <diagram name="Page-1" id="FysiekERD-EuroCaps-Stap7">
    <mxGraphModel dx="1434" dy="436" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Titel -->
        <mxCell id="title" value="EuroCaps - Fysiek ERD (Stap 7: Filling)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="100" y="20" width="600" height="40" as="geometry" />
        </mxCell>
        
        <!-- Filling tabel -->
        <mxCell id="table-filling" value="Filling" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=1;fontStyle=1;align=center;resizeLast=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="100" y="100" width="200" height="150" as="geometry" />
        </mxCell>
        <mxCell id="filling-pk" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-filling">
          <mxGeometry y="30" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="filling-pk-key" value="PK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" vertex="1" parent="filling-pk">
          <mxGeometry width="40" height="30" as="geometry">
            <mxRectangle width="40" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="filling-pk-name" value="FillingId INT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=1;overflow=hidden;" vertex="1" parent="filling-pk">
          <mxGeometry x="40" width="160" height="30" as="geometry">
            <mxRectangle width="160" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="filling-start" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-filling">
          <mxGeometry y="60" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="filling-start-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" vertex="1" parent="filling-start">
          <mxGeometry width="40" height="30" as="geometry">
            <mxRectangle width="40" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="filling-start-name" value="F_DatumTijdStart DATETIME" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" vertex="1" parent="filling-start">
          <mxGeometry x="40" width="160" height="30" as="geometry">
            <mxRectangle width="160" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="filling-eind" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-filling">
          <mxGeometry y="90" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="filling-eind-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" vertex="1" parent="filling-eind">
          <mxGeometry width="40" height="30" as="geometry">
            <mxRectangle width="40" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="filling-eind-name" value="F_DatumTijdEind DATETIME" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" vertex="1" parent="filling-eind">
          <mxGeometry x="40" width="160" height="30" as="geometry">
            <mxRectangle width="160" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="filling-machine" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-filling">
          <mxGeometry y="120" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="filling-machine-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" vertex="1" parent="filling-machine">
          <mxGeometry width="40" height="30" as="geometry">
            <mxRectangle width="40" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="filling-machine-name" value="F_Machine VARCHAR(50)" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" vertex="1" parent="filling-machine">
          <mxGeometry x="40" width="160" height="30" as="geometry">
            <mxRectangle width="160" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        
        <!-- Filling_Product koppeltabel -->
        <mxCell id="table-filling-product" value="Filling_Product" style="shape=table;startSize=30;container=1;collapsible=1;childLayout=tableLayout;fixedRows=1;rowLines=1;fontStyle=1;align=center;resizeLast=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="400" y="100" width="200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="filling-product-pk1" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-filling-product">
          <mxGeometry y="30" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="filling-product-pk1-key" value="PK/FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" vertex="1" parent="filling-product-pk1">
          <mxGeometry width="60" height="30" as="geometry">
            <mxRectangle width="60" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="filling-product-pk1-name" value="FillingId INT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=1;overflow=hidden;" vertex="1" parent="filling-product-pk1">
          <mxGeometry x="60" width="140" height="30" as="geometry">
            <mxRectangle width="140" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="filling-product-pk2" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-filling-product">
          <mxGeometry y="60" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="filling-product-pk2-key" value="PK/FK" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;fontStyle=1;overflow=hidden;" vertex="1" parent="filling-product-pk2">
          <mxGeometry width="60" height="30" as="geometry">
            <mxRectangle width="60" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="filling-product-pk2-name" value="ProductId INT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;fontStyle=1;overflow=hidden;" vertex="1" parent="filling-product-pk2">
          <mxGeometry x="60" width="140" height="30" as="geometry">
            <mxRectangle width="140" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="filling-product-aantal" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;fillColor=none;collapsible=0;dropTarget=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;top=0;left=0;right=0;bottom=0;" vertex="1" parent="table-filling-product">
          <mxGeometry y="90" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="filling-product-aantal-key" value="" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;editable=1;overflow=hidden;" vertex="1" parent="filling-product-aantal">
          <mxGeometry width="60" height="30" as="geometry">
            <mxRectangle width="60" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="filling-product-aantal-name" value="Aantal INT" style="shape=partialRectangle;connectable=0;fillColor=none;top=0;left=0;bottom=0;right=0;align=left;spacingLeft=6;overflow=hidden;" vertex="1" parent="filling-product-aantal">
          <mxGeometry x="60" width="140" height="30" as="geometry">
            <mxRectangle width="140" height="30" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        
        <!-- Relatie -->
        <mxCell id="rel-filling-filling-product" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERoneToMany;startArrow=ERmandOne;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="filling-pk" target="filling-product-pk1">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="350" y="400" as="sourcePoint" />
            <mxPoint x="450" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
