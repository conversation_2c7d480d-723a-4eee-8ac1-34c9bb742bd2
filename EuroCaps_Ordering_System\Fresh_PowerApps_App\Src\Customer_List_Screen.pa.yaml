Customer_List_Screen As screen:
    Fill: =RGBA(245, 245, 245, 1)
    LoadingSpinnerColor: =RGBA(74, 111, 165, 1)

    HeaderBar As rectangle:
        Fill: =RGBA(74, 111, 165, 1)
        Height: =60
        Width: =Parent.Width
        X: =0
        Y: =0

    HeaderLogo As image:
        Height: =40
        Image: ="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSJ3aGl0ZSIvPgo8dGV4dCB4PSIyMCIgeT0iMjUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI4IiBmaWxsPSIjNGE2ZmE1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5FQzwvdGV4dD4KPC9zdmc+"
        Width: =40
        X: =20
        Y: =10

    HeaderTitle As label:
        Color: =RGBA(255, 255, 255, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =16
        Text: ="EuroCaps Order Management Pro"
        Width: =300
        X: =70
        Y: =10

    NavigationMenu As rectangle:
        Fill: =RGBA(58, 90, 128, 1)
        Height: =Parent.Height - 60
        Width: =200
        X: =0
        Y: =60

    MenuDashboard As button:
        Align: =Align.Left
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(58, 90, 128, 1)
        Font: =Font.Arial
        Height: =50
        OnSelect: =Navigate(Dashboard_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="≡ Dashboard"
        Width: =200
        X: =0
        Y: =60

    MenuCustomers As button:
        Align: =Align.Left
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        Height: =50
        Size: =14
        Text: ="👥 Customers"
        Width: =200
        X: =0
        Y: =110

    MenuProducts As button:
        Align: =Align.Left
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(58, 90, 128, 1)
        Font: =Font.Arial
        Height: =50
        OnSelect: =Navigate(Product_Catalog_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="📦 Products"
        Width: =200
        X: =0
        Y: =160

    MenuOrders As button:
        Align: =Align.Left
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(58, 90, 128, 1)
        Font: =Font.Arial
        Height: =50
        OnSelect: =Navigate(Order_History_Screen, ScreenTransition.Fade)
        Size: =14
        Text: ="📋 Orders"
        Width: =200
        X: =0
        Y: =210

    PageTitle As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        Size: =18
        Text: ="Customers"
        Width: =200
        X: =220
        Y: =80

    NewCustomerButton As button:
        Color: =RGBA(255, 255, 255, 1)
        Fill: =RGBA(76, 175, 80, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =40
        HoverFill: =RGBA(60, 159, 64, 1)
        Size: =14
        Text: ="+ NEW CUSTOMER"
        Width: =150
        X: =Parent.Width - 170
        Y: =80

    SearchInput As text:
        BorderColor: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        Height: =40
        HintText: ="Search Customers..."
        Size: =12
        Width: =400
        X: =220
        Y: =140

    SearchIcon As label:
        Color: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        Height: =40
        Size: =14
        Text: ="🔍"
        Width: =40
        X: =630
        Y: =140

    FiltersLabel As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =25
        Size: =12
        Text: ="FILTERS:"
        Width: =80
        X: =220
        Y: =200

    FilterAllDropdown As dropdown:
        BorderColor: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        Height: =35
        Items: =["All", "Active", "Inactive"]
        Size: =12
        Width: =100
        X: =310
        Y: =195

    SortDropdown As dropdown:
        BorderColor: =RGBA(74, 111, 165, 1)
        Font: =Font.Arial
        Height: =35
        Items: =["Sort: Name", "Sort: Recent", "Sort: Email"]
        Size: =12
        Width: =120
        X: =420
        Y: =195

    ResetFiltersButton As button:
        BorderColor: =RGBA(74, 111, 165, 1)
        Color: =RGBA(74, 111, 165, 1)
        Fill: =RGBA(0, 0, 0, 0)
        Font: =Font.Arial
        Height: =35
        Size: =12
        Text: ="Reset Filters"
        Width: =100
        X: =550
        Y: =195

    CustomerTableBackground As rectangle:
        BorderColor: =RGBA(200, 200, 200, 1)
        BorderThickness: =1
        Fill: =RGBA(255, 255, 255, 1)
        Height: =400
        Width: =Parent.Width - 240
        X: =220
        Y: =250

    HeaderName As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =12
        Text: ="Name"
        Width: =200
        X: =240
        Y: =260

    HeaderContact As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =12
        Text: ="Contact"
        Width: =200
        X: =460
        Y: =260

    HeaderEmail As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =12
        Text: ="Email"
        Width: =200
        X: =680
        Y: =260

    HeaderActions As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        FontWeight: =FontWeight.Bold
        Height: =30
        Size: =12
        Text: ="Actions"
        Width: =100
        X: =900
        Y: =260

    Customer1Name As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =30
        Size: =12
        Text: ="Bean Lovers"
        Width: =200
        X: =240
        Y: =300

    Customer1Contact As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =30
        Size: =12
        Text: ="John Smith"
        Width: =200
        X: =460
        Y: =300

    Customer1Email As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =30
        Size: =12
        Text: ="<EMAIL>"
        Width: =200
        X: =680
        Y: =300

    Customer1ViewButton As button:
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(74, 111, 165, 1)
        Fill: =RGBA(0, 0, 0, 0)
        Font: =Font.Arial
        Height: =30
        Size: =12
        Text: ="👁️"
        Width: =30
        X: =900
        Y: =300

    Customer1EditButton As button:
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 152, 0, 1)
        Fill: =RGBA(0, 0, 0, 0)
        Font: =Font.Arial
        Height: =30
        Size: =12
        Text: ="📝"
        Width: =30
        X: =940
        Y: =300

    Customer1OrderButton As button:
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(76, 175, 80, 1)
        Fill: =RGBA(0, 0, 0, 0)
        Font: =Font.Arial
        Height: =30
        OnSelect: =Navigate(New_Order_Screen, ScreenTransition.Fade)
        Size: =12
        Text: ="🛒"
        Width: =30
        X: =980
        Y: =300

    Customer2Name As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =30
        Size: =12
        Text: ="Café Express"
        Width: =200
        X: =240
        Y: =340

    Customer2Contact As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =30
        Size: =12
        Text: ="Maria Garcia"
        Width: =200
        X: =460
        Y: =340

    Customer2Email As label:
        Color: =RGBA(51, 51, 51, 1)
        Font: =Font.Arial
        Height: =30
        Size: =12
        Text: ="<EMAIL>"
        Width: =200
        X: =680
        Y: =340

    Customer2ViewButton As button:
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(74, 111, 165, 1)
        Fill: =RGBA(0, 0, 0, 0)
        Font: =Font.Arial
        Height: =30
        Size: =12
        Text: ="👁️"
        Width: =30
        X: =900
        Y: =340

    Customer2EditButton As button:
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(255, 152, 0, 1)
        Fill: =RGBA(0, 0, 0, 0)
        Font: =Font.Arial
        Height: =30
        Size: =12
        Text: ="📝"
        Width: =30
        X: =940
        Y: =340

    Customer2OrderButton As button:
        BorderColor: =RGBA(0, 0, 0, 0)
        Color: =RGBA(76, 175, 80, 1)
        Fill: =RGBA(0, 0, 0, 0)
        Font: =Font.Arial
        Height: =30
        OnSelect: =Navigate(New_Order_Screen, ScreenTransition.Fade)
        Size: =12
        Text: ="🛒"
        Width: =30
        X: =980
        Y: =340

    PaginationPrevious As button:
        BorderColor: =RGBA(74, 111, 165, 1)
        Color: =RGBA(74, 111, 165, 1)
        Fill: =RGBA(0, 0, 0, 0)
        Font: =Font.Arial
        Height: =35
        Size: =12
        Text: ="◀ Previous"
        Width: =100
        X: =220
        Y: =680

    PaginationNext As button:
        BorderColor: =RGBA(74, 111, 165, 1)
        Color: =RGBA(74, 111, 165, 1)
        Fill: =RGBA(0, 0, 0, 0)
        Font: =Font.Arial
        Height: =35
        Size: =12
        Text: ="Next ▶"
        Width: =100
        X: =Parent.Width - 120
        Y: =680

    PaginationInfo As label:
        Align: =Align.Center
        Color: =RGBA(128, 128, 128, 1)
        Font: =Font.Arial
        Height: =35
        Size: =12
        Text: ="Showing 1-10 of 24 customers"
        Width: =200
        X: =(Parent.Width - 200) / 2
        Y: =680
