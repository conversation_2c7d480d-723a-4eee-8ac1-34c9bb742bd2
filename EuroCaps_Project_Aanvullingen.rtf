{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 MANAGEMENTSAMENVATTING\b0\fs22\par

\pard\sa200\sl276\slmult1 Euro Caps is een toonaangevende producent van koffiecapsules in Nederland die zich richt op het produceren van hoogwaardige koffiecapsules voor verschillende merken en retailers. In het kader van de digitale transformatie en procesoptimalisatie heeft Euro Caps behoefte aan een ge\'efntegreerd databasesysteem dat alle kernprocessen van het bedrijf ondersteunt.\par
Dit project heeft als doel het ontwerpen en implementeren van een relationeel databasesysteem voor Euro Caps dat de volgende kernprocessen ondersteunt: grinding (malen), filling (vullen), packaging (verpakken), kwaliteitscontrole en logistiek. Het systeem moet alle relevante informatie over deze processen vastleggen, traceren en analyseren om de operationele effici\'ebntie te verbeteren en waardevolle inzichten te bieden voor besluitvorming.\par
De aanpak van het project bestond uit verschillende fasen:\par
1. \b Analyse van informatiebehoeften\b0 : Identificatie van de kernprocessen en de bijbehorende informatiebehoeften voor grinding, filling, packaging, kwaliteitscontrole en logistiek.\par
2. \b Conceptueel ontwerp\b0 : Ontwikkeling van een conceptueel Entity-Relationship Diagram (ERD) dat de belangrijkste entiteiten en hun relaties weergeeft.\par
3. \b Logisch ontwerp\b0 : Vertaling van het conceptuele ERD naar een logisch ERD met gedetailleerde attributen, primaire sleutels en relaties.\par
4. \b Fysiek ontwerp\b0 : Implementatie van het logische ERD in een MySQL-database met specifieke datatypen, constraints en indexen.\par
5. \b Implementatie\b0 : Ontwikkeling van Python-scripts voor het genereren en importeren van testdata, en het schrijven van SQL-queries voor operationele en KPI-inzichten.\par
De belangrijkste resultaten van het project zijn:\par
- Een volledig genormaliseerd databaseontwerp dat alle kernprocessen van Euro Caps ondersteunt\par
- Een set van SQL-queries die zowel operationele als KPI-inzichten bieden\par
- Python-scripts voor het genereren en importeren van testdata\par
- Een uitgebreide documentatie van het ontwerp, inclusief ERD's en ontwerpkeuzes\par
De implementatie van dit databasesysteem zal Euro Caps in staat stellen om:\par
- De traceerbaarheid van producten door de hele productieketen te verbeteren\par
- De effici\'ebntie van productieprocessen te monitoren en te optimaliseren\par
- De kwaliteitscontrole te verbeteren door gedetailleerde procesgegevens vast te leggen\par
- Betere beslissingen te nemen op basis van KPI's en operationele inzichten\par
- De communicatie met partners (leveranciers, klanten, transporteurs) te stroomlijnen\par
Het project heeft aangetoond dat een goed ontworpen relationeel databasesysteem een cruciale rol kan spelen in het optimaliseren van productieprocessen en het ondersteunen van besluitvorming in een productiebedrijf zoals Euro Caps.\par

\pard\sa200\sl276\slmult1\qc\b\fs28 VOORWOORD\b0\fs22\par

\pard\sa200\sl276\slmult1 Voor u ligt het verslag van het databaseontwerp voor Euro Caps, uitgevoerd als onderdeel van mijn studie. Dit project heeft mij de mogelijkheid geboden om theoretische kennis over databaseontwerp toe te passen op een praktische casus in de productiesector.\par
Euro Caps, als producent van koffiecapsules, staat voor de uitdaging om grote hoeveelheden data over hun productieprocessen effici\'ebnt te beheren. Van het malen van koffiebonen tot het verpakken van de eindproducten, elk proces genereert waardevolle informatie die, indien goed gestructureerd en geanalyseerd, kan leiden tot significante verbeteringen in effici\'ebntie en kwaliteit.\par
De reis van conceptueel idee naar een volledig ge\'efmplementeerd databasesysteem was uitdagend maar buitengewoon leerzaam. Het vertalen van bedrijfsprocessen naar een logisch datamodel vereiste niet alleen technische kennis, maar ook inzicht in de operationele aspecten van een productiebedrijf. Het was fascinerend om te zien hoe abstracte entiteiten en relaties uiteindelijk vorm kregen in een concreet systeem dat waardevolle inzichten kan opleveren.\par
Ik wil graag mijn dank uitspreken aan mijn docenten voor hun begeleiding en feedback tijdens dit project. Hun expertise en kritische blik hebben mij geholpen om mijn ontwerp te verfijnen en te verbeteren. Ook wil ik mijn medestudenten bedanken voor de inspirerende discussies en het delen van inzichten, wat heeft bijgedragen aan de kwaliteit van dit project.\par
Dit verslag beschrijft het volledige proces van analyse tot implementatie, inclusief de gemaakte ontwerpkeuzes en de lessen die ik heb geleerd. Ik hoop dat het niet alleen voldoet aan de academische vereisten, maar ook een praktische waarde heeft voor vergelijkbare projecten in de toekomst.\par
[Uw Naam]\par
[Datum]\par

\pard\sa200\sl276\slmult1\qc\b\fs28 LITERATUURLIJST\b0\fs22\par

\pard\sa200\sl276\slmult1 Connolly, T., & Begg, C. (2020). \i Database systems: A practical approach to design, implementation, and management\i0  (7th ed.). Pearson.\par
Date, C. J. (2019). \i Database design and relational theory: Normal forms and all that jazz\i0  (2nd ed.). Apress.\par
Elmasri, R., & Navathe, S. B. (2017). \i Fundamentals of database systems\i0  (7th ed.). Pearson.\par
Euro Caps. (n.d.). \i Euro Caps case study documents\i0 .\par
Garcia-Molina, H., Ullman, J. D., & Widom, J. (2020). \i Database systems: The complete book\i0  (3rd ed.). Pearson.\par
Hoffer, J. A., Ramesh, V., & Topi, H. (2016). \i Modern database management\i0  (12th ed.). Pearson.\par
Kimball, R., & Ross, M. (2013). \i The data warehouse toolkit: The definitive guide to dimensional modeling\i0  (3rd ed.). Wiley.\par
Kroenke, D. M., & Auer, D. J. (2016). \i Database processing: Fundamentals, design, and implementation\i0  (14th ed.). Pearson.\par
McKinney, W. (2017). \i Python for data analysis: Data wrangling with Pandas, NumPy, and IPython\i0  (2nd ed.). O'Reilly Media.\par
MySQL. (2023). \i MySQL 8.0 reference manual\i0 . https://dev.mysql.com/doc/refman/8.0/en/\par
Nintex. (n.d.). \i Process automation maximizes product quality at Euro Caps\i0  [PDF case study].\par
Oppel, A. (2011). \i Databases: A beginner's guide\i0 . McGraw-Hill Education.\par
Silberschatz, A., Korth, H. F., & Sudarshan, S. (2019). \i Database system concepts\i0  (7th ed.). McGraw-Hill Education.\par
Teorey, T. J., Lightstone, S. S., Nadeau, T., & Jagadish, H. V. (2011). \i Database modeling and design: Logical design\i0  (5th ed.). Morgan Kaufmann.\par
VanderPlas, J. (2016). \i Python data science handbook: Essential tools for working with data\i0 . O'Reilly Media.\par
Wambler, S. (2015). \i Agile data warehouse design: Collaborative dimensional modeling, from whiteboard to star schema\i0 . DecisionOne Press.\par
}
