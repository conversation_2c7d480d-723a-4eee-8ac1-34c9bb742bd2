#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Americaps CSV Data Analysis Script

Dit script importeert de CSV-bestanden en analyseert de data voor het Americaps productieproces.
Het script controleert de datakwaliteit en visualiseert belangrijke aspecten van de data.

Auteur: Augment Agent
Datum: 2023-11-01
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

def analyze_americaps_data():
    """Importeer en analyseer de Americaps CSV-bestanden"""
    print("=== Americaps Data Analyse ===\n")
    
    # Controleer of de CSV-bestanden bestaan
    csv_files = [
        'csv_data/product_types.csv',
        'csv_data/products.csv',
        'csv_data/machines.csv',
        'csv_data/production_batches.csv',
        'csv_data/production_steps.csv',
        'csv_data/quality_control.csv'
    ]
    
    for file_path in csv_files:
        if not os.path.exists(file_path):
            print(f"Bestand {file_path} bestaat niet. Genereer eerst de data.")
            return
    
    # Maak een map voor de analyse resultaten als deze nog niet bestaat
    if not os.path.exists('analysis_results'):
        os.makedirs('analysis_results')
    
    # 1. Importeer de CSV-bestanden
    print("1. Importeren van CSV-bestanden...")
    
    product_types_df = pd.read_csv('csv_data/product_types.csv')
    products_df = pd.read_csv('csv_data/products.csv')
    machines_df = pd.read_csv('csv_data/machines.csv')
    batches_df = pd.read_csv('csv_data/production_batches.csv')
    steps_df = pd.read_csv('csv_data/production_steps.csv')
    qc_df = pd.read_csv('csv_data/quality_control.csv')
    
    # 2. Bekijk de structuur van de data
    print("\n2. Bekijken van de datastructuur...")
    
    print(f"\nProductTypes: {len(product_types_df)} rijen, {len(product_types_df.columns)} kolommen")
    print(f"Kolommen: {', '.join(product_types_df.columns)}")
    print(product_types_df.head())
    
    print(f"\nProducts: {len(products_df)} rijen, {len(products_df.columns)} kolommen")
    print(f"Kolommen: {', '.join(products_df.columns)}")
    print(products_df.head())
    
    print(f"\nMachines: {len(machines_df)} rijen, {len(machines_df.columns)} kolommen")
    print(f"Kolommen: {', '.join(machines_df.columns)}")
    print(machines_df.head())
    
    print(f"\nProductionBatches: {len(batches_df)} rijen, {len(batches_df.columns)} kolommen")
    print(f"Kolommen: {', '.join(batches_df.columns)}")
    print(batches_df.head())
    
    print(f"\nProductionSteps: {len(steps_df)} rijen, {len(steps_df.columns)} kolommen")
    print(f"Kolommen: {', '.join(steps_df.columns)}")
    print(steps_df.head())
    
    print(f"\nQualityControl: {len(qc_df)} rijen, {len(qc_df.columns)} kolommen")
    print(f"Kolommen: {', '.join(qc_df.columns)}")
    print(qc_df.head())
    
    # 3. Controleer de datakwaliteit
    print("\n3. Controleren van de datakwaliteit...")
    
    # 3.1 Controleer op ontbrekende waarden
    print("\nOntbrekende waarden per tabel:")
    for df_name, df in [
        ("ProductTypes", product_types_df),
        ("Products", products_df),
        ("Machines", machines_df),
        ("ProductionBatches", batches_df),
        ("ProductionSteps", steps_df),
        ("QualityControl", qc_df)
    ]:
        missing_values = df.isnull().sum()
        print(f"\n{df_name}:")
        print(missing_values[missing_values > 0] if any(missing_values > 0) else "Geen ontbrekende waarden")
    
    # 3.2 Controleer op duplicaten
    print("\nDuplicaten per tabel:")
    for df_name, df in [
        ("ProductTypes", product_types_df),
        ("Products", products_df),
        ("Machines", machines_df),
        ("ProductionBatches", batches_df),
        ("ProductionSteps", steps_df),
        ("QualityControl", qc_df)
    ]:
        duplicates = df.duplicated().sum()
        print(f"{df_name}: {duplicates} duplicaten")
    
    # 3.3 Controleer op ongewone waarden in numerieke kolommen
    print("\nStatistieken voor numerieke kolommen:")
    
    print("\nProducts:")
    print(products_df.describe())
    
    print("\nProductionSteps (QuantityProcessed en QuantityRejected):")
    print(steps_df[['QuantityProcessed', 'QuantityRejected']].describe())
    
    print("\nQualityControl (SampleSize en DefectCount):")
    print(qc_df[['SampleSize', 'DefectCount']].describe())
    
    # 3.4 Controleer op ongewone waarden in categorische kolommen
    print("\nUnieke waarden in categorische kolommen:")
    
    print("\nProducts (ProductStatus):")
    print(products_df['ProductStatus'].value_counts())
    
    print("\nProductionSteps (StepType):")
    print(steps_df['StepType'].value_counts())
    
    print("\nQualityControl (Result):")
    print(qc_df['Result'].value_counts())
    
    # 4. Analyseer de data
    print("\n4. Analyseren van de data...")
    
    # 4.1 Verdeling van producttypes
    plt.figure(figsize=(10, 6))
    product_type_counts = products_df['ProductTypeId'].value_counts().sort_index()
    product_type_labels = [f"{row['ProductTypeId']}: {row['Description']}" 
                          for _, row in product_types_df.iterrows()]
    
    ax = product_type_counts.plot(kind='bar')
    ax.set_xticklabels(product_type_labels, rotation=45, ha='right')
    plt.title('Verdeling van ProductTypes')
    plt.xlabel('ProductType')
    plt.ylabel('Aantal Producten')
    plt.tight_layout()
    plt.savefig('analysis_results/product_type_distribution.png')
    
    # 4.2 Verdeling van productstatus
    plt.figure(figsize=(10, 6))
    status_counts = products_df['ProductStatus'].value_counts()
    plt.pie(status_counts, labels=status_counts.index, autopct='%1.1f%%', startangle=90)
    plt.axis('equal')
    plt.title('Verdeling van ProductStatus')
    plt.savefig('analysis_results/product_status_distribution.png')
    
    # 4.3 Afkeurpercentages per stap type
    plt.figure(figsize=(12, 6))
    steps_df['RejectionRate'] = steps_df['QuantityRejected'] / steps_df['QuantityProcessed'] * 100
    sns.boxplot(x='StepType', y='RejectionRate', data=steps_df)
    plt.title('Afkeurpercentages per Stap Type')
    plt.xlabel('Stap Type')
    plt.ylabel('Afkeurpercentage (%)')
    plt.savefig('analysis_results/rejection_rates.png')
    
    # 4.4 QC resultaten per stap type
    # Voeg StepType toe aan QC data door te joinen met steps_df
    qc_with_step_type = qc_df.merge(steps_df[['StepId', 'StepType']], on='StepId')
    
    plt.figure(figsize=(12, 6))
    qc_result_by_step = pd.crosstab(qc_with_step_type['Result'], qc_with_step_type['StepType'])
    qc_result_by_step.plot(kind='bar', stacked=True)
    plt.title('QC Resultaten per Stap Type')
    plt.xlabel('QC Resultaat')
    plt.ylabel('Aantal')
    plt.legend(title='Stap Type')
    plt.savefig('analysis_results/qc_results_by_step.png')
    
    # 4.5 Defect types per stap type
    plt.figure(figsize=(14, 8))
    # Filter alleen rijen met defecten
    defects_df = qc_with_step_type[qc_with_step_type['DefectCount'] > 0]
    defect_by_step = pd.crosstab(defects_df['DefectType'], defects_df['StepType'])
    defect_by_step.plot(kind='bar', stacked=False)
    plt.title('Defect Types per Stap Type')
    plt.xlabel('Defect Type')
    plt.ylabel('Aantal')
    plt.xticks(rotation=45, ha='right')
    plt.legend(title='Stap Type')
    plt.tight_layout()
    plt.savefig('analysis_results/defect_types_by_step.png')
    
    # 4.6 Productie-efficiëntie per batch
    plt.figure(figsize=(12, 6))
    batches_df['EfficiencyRate'] = batches_df['ActualQuantity'] / batches_df['PlannedQuantity'] * 100
    completed_batches = batches_df[batches_df['Status'] == 'completed']
    
    plt.hist(completed_batches['EfficiencyRate'], bins=20, alpha=0.7)
    plt.axvline(completed_batches['EfficiencyRate'].mean(), color='red', linestyle='dashed', linewidth=1)
    plt.text(completed_batches['EfficiencyRate'].mean() - 1, 
             plt.ylim()[1] * 0.9, 
             f'Gemiddelde: {completed_batches["EfficiencyRate"].mean():.2f}%', 
             color='red')
    
    plt.title('Productie-efficiëntie van Voltooide Batches')
    plt.xlabel('Efficiëntie (%)')
    plt.ylabel('Aantal Batches')
    plt.savefig('analysis_results/production_efficiency.png')
    
    # 4.7 Tijdsduur per stap type
    plt.figure(figsize=(12, 6))
    
    # Bereken de tijdsduur voor elke stap
    steps_df['StartTime'] = pd.to_datetime(steps_df['StartTime'])
    steps_df['EndTime'] = pd.to_datetime(steps_df['EndTime'])
    steps_df['Duration'] = (steps_df['EndTime'] - steps_df['StartTime']).dt.total_seconds() / 3600  # in uren
    
    sns.boxplot(x='StepType', y='Duration', data=steps_df)
    plt.title('Tijdsduur per Stap Type')
    plt.xlabel('Stap Type')
    plt.ylabel('Duur (uren)')
    plt.savefig('analysis_results/step_duration.png')
    
    # 4.8 Correlatie tussen steekproefgrootte en aantal defecten
    plt.figure(figsize=(10, 6))
    plt.scatter(qc_df['SampleSize'], qc_df['DefectCount'], alpha=0.5)
    plt.title('Correlatie tussen Steekproefgrootte en Aantal Defecten')
    plt.xlabel('Steekproefgrootte')
    plt.ylabel('Aantal Defecten')
    
    # Voeg trendlijn toe
    z = np.polyfit(qc_df['SampleSize'], qc_df['DefectCount'], 1)
    p = np.poly1d(z)
    plt.plot(qc_df['SampleSize'], p(qc_df['SampleSize']), "r--")
    
    # Voeg correlatiecoëfficiënt toe
    corr = qc_df['SampleSize'].corr(qc_df['DefectCount'])
    plt.text(qc_df['SampleSize'].max() * 0.7, 
             qc_df['DefectCount'].max() * 0.9, 
             f'Correlatie: {corr:.2f}', 
             fontsize=12)
    
    plt.savefig('analysis_results/sample_defect_correlation.png')
    
    # 5. Samenvatting van de analyse
    print("\n5. Samenvatting van de analyse:")
    
    print("\n5.1 Datakwaliteit:")
    print(f"- Totaal aantal producten: {len(products_df)}")
    print(f"- Totaal aantal producttypes: {len(product_types_df)}")
    print(f"- Totaal aantal machines: {len(machines_df)}")
    print(f"- Totaal aantal productiebatches: {len(batches_df)}")
    print(f"- Totaal aantal productiestappen: {len(steps_df)}")
    print(f"- Totaal aantal kwaliteitscontroles: {len(qc_df)}")
    
    print("\n5.2 Productiestappen:")
    print(f"- Gemiddelde afkeurpercentage bij mixing: {steps_df[steps_df['StepType'] == 'mixing']['RejectionRate'].mean():.2f}%")
    print(f"- Gemiddelde afkeurpercentage bij filling: {steps_df[steps_df['StepType'] == 'filling']['RejectionRate'].mean():.2f}%")
    print(f"- Gemiddelde afkeurpercentage bij packing: {steps_df[steps_df['StepType'] == 'packing']['RejectionRate'].mean():.2f}%")
    
    print("\n5.3 Kwaliteitscontrole:")
    print(f"- Percentage geslaagde QC checks: {(qc_df['Result'] == 'Pass').mean() * 100:.2f}%")
    print(f"- Meest voorkomende defect type bij mixing: {defects_df[defects_df['StepType'] == 'mixing']['DefectType'].value_counts().index[0] if not defects_df[defects_df['StepType'] == 'mixing'].empty else 'Geen defecten'}")
    print(f"- Meest voorkomende defect type bij filling: {defects_df[defects_df['StepType'] == 'filling']['DefectType'].value_counts().index[0] if not defects_df[defects_df['StepType'] == 'filling'].empty else 'Geen defecten'}")
    print(f"- Meest voorkomende defect type bij packing: {defects_df[defects_df['StepType'] == 'packing']['DefectType'].value_counts().index[0] if not defects_df[defects_df['StepType'] == 'packing'].empty else 'Geen defecten'}")
    
    print("\n5.4 Productie-efficiëntie:")
    print(f"- Gemiddelde productie-efficiëntie: {completed_batches['EfficiencyRate'].mean():.2f}%")
    print(f"- Minimum productie-efficiëntie: {completed_batches['EfficiencyRate'].min():.2f}%")
    print(f"- Maximum productie-efficiëntie: {completed_batches['EfficiencyRate'].max():.2f}%")
    
    print("\nAnalyse voltooid. Visualisaties opgeslagen in de map 'analysis_results'.")

if __name__ == "__main__":
    analyze_americaps_data()
