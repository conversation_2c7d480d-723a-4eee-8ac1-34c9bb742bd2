# 2.3 Huidig IT-Landschap

Euro Caps heeft sinds de oprichting in 2012 een geleidelijke ontwikkeling doorgemaakt in haar IT-infrastructuur. Het huidige IT-landschap bestaat uit drie kerncomponenten die in verschillende fasen zijn geïmplementeerd.

Microsoft Dynamics NAV (Navision) werd in 2014 geïmplementeerd als het centrale ERP-systeem. Dit systeem vormt de ruggengraat van de bedrijfsvoering en wordt gebruikt voor financiële administratie, voorraadbeheer, inkoopprocessen, productieplanning en orderverwerking. Het NAV-systeem is aangepast aan de specifieke behoeften van Euro Caps, maar is niet volledig geïntegreerd met de productievloer.

In 2016 implementeerde Euro Caps Microsoft SharePoint als documentbeheersysteem voor centraal beheer van bedrijfsdocumentatie, kwaliteitshandboeken, projectdocumentatie en versiebeheer. Het systeem wordt echter niet optimaal benut, met inconsistent gebruik tussen afdelingen.

Het meest recente onderdeel is K2/Nintex, geïmplementeerd in 2017 voor Business Process Management. Dit systeem wordt gebruikt voor automatisering van werkstromen, digitalisering van goedkeuringsprocessen en procesmonitoring. De volledige potentie wordt nog niet benut door beperkte integratie met productieprocessen.

De IT-infrastructuur wordt ondersteund door een IT-afdeling bestaande uit 5 FTE met verschillende specialisaties (IT-manager, NAV-specialist, SharePoint/K2-specialist, netwerkbeheerder en helpdesk). Deze relatief kleine afdeling is verantwoordelijk voor alle IT-systemen, wat soms leidt tot capaciteitsproblemen.

# 2.4 Geïdentificeerde Knelpunten

Uit analyse van de huidige situatie bij Euro Caps komen verschillende knelpunten naar voren die de efficiëntie en effectiviteit van de bedrijfsprocessen beïnvloeden.

Systeemintegratie vormt een prominent knelpunt. NAV, SharePoint en K2/Nintex functioneren grotendeels als losstaande systemen. Handmatige gegevensinvoer is nodig om informatie tussen systemen te synchroniseren. Productiegegevens worden niet real-time verwerkt in het ERP-systeem. Deze gebrekkige integratie leidt tot inefficiënties en potentiële fouten.

Door de gefragmenteerde systeemarchitectuur zijn er verschillende data-silo's ontstaan. Productiegegevens zijn niet direct toegankelijk voor verkoop- en planningsafdelingen. Kwaliteitscontrolegegevens worden niet systematisch gekoppeld aan productiebatches. Voorraadgegevens in NAV komen niet altijd overeen met de werkelijke situatie op de productievloer.

Ondanks de implementatie van SharePoint blijven er uitdagingen bestaan op het gebied van versiebeheer. Inconsistent gebruik van het documentbeheersysteem tussen afdelingen leidt tot parallelle documentstromen. Productiespecificaties worden soms lokaal aangepast zonder centrale registratie. Dit verhoogt het risico op fouten.

Het huidige systeem voor kwaliteitsregistratie is onvoldoende geautomatiseerd. De OK/NOK-classificatie wordt vaak handmatig geregistreerd. Trendanalyse van kwaliteitsgegevens is arbeidsintensief. Traceerbaarheid van kwaliteitsproblemen naar specifieke productieparameters is beperkt.

De huidige IT-systemen hebben inherente beperkingen. NAV is niet optimaal ingericht voor gedetailleerde productieplanning. SharePoint biedt beperkte mogelijkheden voor procesautomatisering. K2/Nintex is niet volledig geïntegreerd met productiemachines. Rapportagemogelijkheden zijn beperkt door gefragmenteerde gegevensbronnen.

De IT-infrastructuur brengt aanzienlijke kosten met zich mee door licenties voor drie verschillende systemen, maatwerk voor integratie, personeelskosten voor handmatige gegevensverwerking en onderhoudskosten voor verouderde systeemcomponenten.

Deze knelpunten onderstrepen de noodzaak voor een meer geïntegreerde IT-oplossing die de productie- en kwaliteitsprocessen beter ondersteunt.
