import os
import subprocess

def create_all_analyses():
    # Verwijder bestaande bestanden als ze bestaan
    output_file = 'Alle_Analyses.xlsx'
    if os.path.exists(output_file):
        try:
            os.remove(output_file)
            print(f"Bestaand bestand {output_file} verwijderd")
        except:
            print(f"Kon bestaand bestand {output_file} niet verwijderen")
    
    # Voer alle scripts uit om de individuele analyses te maken
    print("TOC Analyse maken...")
    subprocess.run(["python", "create_toc_excel_final.py"])
    
    print("Six Sigma Analyse maken...")
    subprocess.run(["python", "create_sixsigma_final.py"])
    
    print("Lean Analyse maken...")
    subprocess.run(["python", "create_lean_excel.py"])
    
    print("Kaizen Analyse maken...")
    subprocess.run(["python", "create_kaizen_correct.py"])
    
    print("Alle analyses zijn succesvol gemaakt!")
    print("U kunt de volgende bestanden openen:")
    print("- TOC_Analyse_Final.xlsx")
    print("- SixSigma_Final.xlsx")
    print("- Lean_Analyse.xlsx")
    print("- Kaizen_Correct.xlsx")

if __name__ == "__main__":
    create_all_analyses()
