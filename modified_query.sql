-- Modified query with train_ID changed from 6679 to 3070
WITH 
frequent_travelers AS (
    -- Identify customers who traveled 3-5 times per week between Jan-Apr 2024
    SELECT 
        frequent.klant_id
    FROM (
        SELECT 
            ov_data.klant_id, 
            ov_data.weeknr, 
            COUNT(DISTINCT ov_data.datum) AS travel_frequency
        FROM 
            ov_info ov_data
        WHERE 
            ov_data.jaar = 2024
            AND ov_data.datum >= '2024-01-17' 
            AND ov_data.datum <= '2024-04-24'
        GROUP BY 
            ov_data.klant_id, ov_data.weeknr
        HAVING 
            COUNT(DISTINCT ov_data.datum) >= 3 
            AND COUNT(DISTINCT ov_data.datum) <= 5
    ) AS frequent
    LEFT JOIN 
        ov_info recent ON frequent.klant_id = recent.klant_id AND recent.datum > '2024-04-24'
    WHERE 
        recent.klant_id IS NULL
),

unchecked_customers AS (
    -- Find customers who checked in but never checked out
    SELECT DISTINCT 
        check_in.klant_id
    FROM 
        ov_info check_in
    WHERE 
        check_in.status = 'inchecken'
        AND NOT EXISTS (
            SELECT 1 
            FROM ov_info check_out 
            WHERE check_out.klant_id = check_in.klant_id 
              AND check_out.reis_id = check_in.reis_id 
              AND check_out.status = 'uitchecken'
        )
),

train_passengers AS (
    -- Passengers on train 3070 on April 24, 2024
    SELECT DISTINCT 
        passenger.klant_id, 
        schedule.trein_id, 
        passenger.station_id, 
        passenger.datum
    FROM 
        ov_info passenger
    JOIN 
        dienstregeling schedule ON passenger.station_id = schedule.station_id
    WHERE 
        schedule.trein_id = 3070
        AND passenger.datum = '2024-04-24'
),

suspicious_activity AS (
    -- Customers with suspicious activity (disguise)
    SELECT DISTINCT 
        detection.klant_id, 
        detection.activiteit_id
    FROM 
        activiteit_detectie detection
    WHERE 
        detection.activiteit_id = 3
)

-- Main query combining all conditions
SELECT DISTINCT 
    ft.klant_id,
    customer.voornaam,
    customer.achternaam,
    customer.geboortedatum,
    train.trein_id,
    suspect.activiteit_id,
    train.station_id,
    train.datum
FROM 
    frequent_travelers ft
INNER JOIN 
    unchecked_customers uc ON ft.klant_id = uc.klant_id
INNER JOIN 
    train_passengers train ON ft.klant_id = train.klant_id
INNER JOIN 
    suspicious_activity suspect ON ft.klant_id = suspect.klant_id
INNER JOIN 
    klant customer ON ft.klant_id = customer.klant_id
ORDER BY 
    ft.klant_id;
