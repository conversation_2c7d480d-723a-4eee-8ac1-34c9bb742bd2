# ************************************************************************************************
# Warning: YAML source code for Canvas Apps should only be used to review changes made within Power Apps Studio and for minor edits (Preview).
# Use the maker portal to create and edit your Power Apps.
#
# The schema file for Canvas Apps is available at https://go.microsoft.com/fwlink/?linkid=2304907
#
# For more information, visit https://go.microsoft.com/fwlink/?linkid=2292623
# ************************************************************************************************
Screens:
  New_Order_Screen:
    Properties:
      LoadingSpinnerColor: =RGBA(56, 96, 178, 1)
    Children:
      - HeaderBar:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(56, 96, 178, 1)
            Height: =60
            Width: =1366
            X: =0
            Y: =0
      - PageTitle:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="New Order"
            Size: =18
            Color: =RGBA(51, 51, 51, 1)
            Height: =40
            Width: =200
            X: =20
            Y: =80
      - OrderInfoPanel:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            Height: =200
            Width: =600
            X: =20
            Y: =140
      - OrderInfoTitle:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="ORDER INFORMATION"
            Size: =16
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =300
            X: =40
            Y: =160
      - CustomerLabel:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="Customer:"
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =100
            X: =40
            Y: =200
      - CustomerInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            Font: =Font.'Open Sans'
            HintText: ="Select Customer..."
            BorderColor: =RGBA(56, 96, 178, 1)
            Height: =35
            Width: =300
            X: =150
            Y: =195
      - OrderDateLabel:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="Order Date:"
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =100
            X: =40
            Y: =240
      - OrderDateInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            Font: =Font.'Open Sans'
            Default: ="Today"
            BorderColor: =RGBA(56, 96, 178, 1)
            Height: =35
            Width: =150
            X: =150
            Y: =235
      - DeliveryDateLabel:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="Delivery Date:"
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =120
            X: =40
            Y: =280
      - DeliveryDateInput:
          Control: Classic/TextInput@2.3.2
          Properties:
            Font: =Font.'Open Sans'
            HintText: ="Select Date..."
            BorderColor: =RGBA(56, 96, 178, 1)
            Height: =35
            Width: =150
            X: =170
            Y: =275
      - NotesLabel:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="Notes:"
            Size: =12
            Color: =RGBA(51, 51, 51, 1)
            Height: =25
            Width: =100
            X: =40
            Y: =320
      - OrderItemsTitle:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="ORDER ITEMS"
            Size: =16
            Color: =RGBA(51, 51, 51, 1)
            Height: =30
            Width: =300
            X: =20
            Y: =360
      - OrderItemsPanel:
          Control: Rectangle@2.3.0
          Properties:
            Fill: =RGBA(255, 255, 255, 1)
            BorderColor: =RGBA(200, 200, 200, 1)
            BorderThickness: =1
            Height: =120
            Width: =600
            X: =20
            Y: =400
      - NoItemsMessage:
          Control: Label@2.5.1
          Properties:
            Font: =Font.'Open Sans'
            Text: ="No items added yet. Click 'Add Products' to add items."
            Size: =14
            Color: =RGBA(128, 128, 128, 1)
            Height: =30
            Width: =400
            X: =120
            Y: =440
      - AddProductsButton:
          Control: Classic/Button@2.2.0
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="+ ADD PRODUCTS"
            Fill: =RGBA(76, 175, 80, 1)
            Color: =RGBA(255, 255, 255, 1)
            Height: =40
            Width: =150
            X: =220
            Y: =470
      - SaveDraftButton:
          Control: Classic/Button@2.2.0
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="SAVE AS DRAFT"
            Fill: =RGBA(56, 96, 178, 1)
            Color: =RGBA(255, 255, 255, 1)
            Height: =45
            Width: =150
            X: =20
            Y: =550
      - SubmitOrderButton:
          Control: Classic/Button@2.2.0
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="SUBMIT ORDER"
            Fill: =RGBA(76, 175, 80, 1)
            Color: =RGBA(255, 255, 255, 1)
            Height: =45
            Width: =150
            X: =190
            Y: =550
      - CancelButton:
          Control: Classic/Button@2.2.0
          Properties:
            Font: =Font.'Open Sans'
            FontWeight: =FontWeight.Bold
            Text: ="CANCEL"
            Fill: =RGBA(158, 158, 158, 1)
            Color: =RGBA(255, 255, 255, 1)
            Height: =45
            Width: =100
            X: =360
            Y: =550
