{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 HOOFDSTUK 1: INLEIDING\b0\fs22\par

\pard\sa200\sl276\slmult1\b 1.1 Achtergrond\b0\par
Euro Caps is een toonaangevende producent van koffiecapsules in Nederland, opgericht in 2012 in Rotterdam. Het bedrijf produceert hoogwaardige koffiecapsules voor verschillende merken en retailers. \b Bron: (Euro Caps, n.d.)\b0\par
In een steeds competitievere markt is het voor Euro Caps essentieel om de effici\'ebntie van hun productieprocessen te optimaliseren en de kwaliteit van hun producten te waarborgen. Een ge\'efntegreerd databasesysteem kan hierbij een cruciale rol spelen. \b Bron: (Connolly & <PERSON>, 2020)\b0\par

\b 1.2 Probleemstelling / Doelstelling\b0\par
Euro Caps kampt met verschillende uitdagingen in hun huidige bedrijfsprocessen, waaronder beperkte traceerbaarheid van producten, ineffici\'ebnte gegevensverzameling voor kwaliteitscontrole, en gebrek aan real-time inzicht in productieparameters. \b Bron: (Nintex, n.d.)\b0\par
De doelstelling van dit project is het ontwerpen en implementeren van een relationeel databasesysteem dat alle kernprocessen van Euro Caps ondersteunt: grinding (malen), filling (vullen), packaging (verpakken), kwaliteitscontrole en logistiek. \b Bron: (Date, 2019)\b0\par

\b 1.3 Onderzoeksvragen\b0\par
Om de doelstelling te bereiken, zijn de volgende onderzoeksvragen geformuleerd:\par
1. Welke knelpunten zijn er in de huidige bedrijfsprocessen van Euro Caps, met name in het productie- en logistieke proces? \b Bron: (Hoffer et al., 2016)\b0\par
2. Welke informatiebehoeften hebben de verschillende afdelingen binnen Euro Caps? \b Bron: (Elmasri & Navathe, 2017)\b0\par
3. Hoe kan een relationeel databasesysteem worden ontworpen dat voldoet aan de specifieke eisen van Euro Caps? \b Bron: (Teorey et al., 2011)\b0\par
4. Welke operationele en KPI-inzichten kunnen worden afgeleid uit het databasesysteem? \b Bron: (Kimball & Ross, 2013)\b0\par

\b 1.4 Methodologie\b0\par
Voor dit project wordt een combinatie van methoden gebruikt:\par
- Literatuuronderzoek naar best practices in databaseontwerp voor productiebedrijven \b Bron: (Silberschatz et al., 2019)\b0\par
- Procesanalyse met behulp van swimlane diagrammen om de huidige bedrijfsprocessen in kaart te brengen \b Bron: (Kroenke & Auer, 2016)\b0\par
- Entity-Relationship Modeling voor het ontwerpen van de databasestructuur \b Bron: (Garcia-Molina et al., 2020)\b0\par
- Implementatie in MySQL met Python-scripts voor data-analyse \b Bron: (McKinney, 2017)\b0\par

\b 1.5 Leeswijzer\b0\par
Dit verslag is als volgt opgebouwd:\par
- Hoofdstuk 2 analyseert de huidige situatie (as-is) bij Euro Caps\par
- Hoofdstuk 3 presenteert een kwaliteitsmanagement analyse\par
- Hoofdstuk 4 beschrijft het databaseontwerp en de implementatie\par
- Hoofdstuk 5 toont de resultaten en inzichten\par
- Hoofdstuk 6 bevat de conclusie en aanbevelingen\par
Deze structuur zorgt voor een logische opbouw van het project, van analyse tot implementatie en evaluatie. \b Bron: (Oppel, 2011)\b0\par
}
