{\rtf1\ansi\ansicpg1252\deff0\nouicompat\deflang1033{\fonttbl{\f0\fnil\fcharset0 Calibri;}}
{\*\generator Riched20 10.0.19041}\viewkind4\uc1 
\pard\sa200\sl276\slmult1\qc\b\f0\fs28 MANAGEMENTSAMENVATTING\b0\fs22\par

\pard\sa200\sl276\slmult1 Euro Caps is een toonaangevende producent van koffiecapsules in Nederland die zich richt op het produceren van hoogwaardige koffiecapsules voor verschillende merken en retailers. \b Bron: (Euro Caps, n.d.)\b0 . In het kader van de digitale transformatie en procesoptimalisatie heeft Euro Caps behoefte aan een ge\'efntegreerd databasesysteem dat alle kernprocessen van het bedrijf ondersteunt. \b Bron: (Nintex, n.d.)\b0\par
Dit project heeft als doel het ontwerpen en implementeren van een relationeel databasesysteem voor Euro Caps dat de volgende kernprocessen ondersteunt: grinding (malen), filling (vullen), packaging (verpakken), kwaliteitscontrole en logistiek. Het systeem moet alle relevante informatie over deze processen vastleggen, traceren en analyseren om de operationele effici\'ebntie te verbeteren en waardevolle inzichten te bieden voor besluitvorming. \b Bron: (Connolly & Begg, 2020)\b0\par
De aanpak van het project bestond uit verschillende fasen:\par
1. \b Analyse van informatiebehoeften\b0 : Identificatie van de kernprocessen en de bijbehorende informatiebehoeften voor grinding, filling, packaging, kwaliteitscontrole en logistiek. \b Bron: (Hoffer et al., 2016)\b0\par
2. \b Conceptueel ontwerp\b0 : Ontwikkeling van een conceptueel Entity-Relationship Diagram (ERD) dat de belangrijkste entiteiten en hun relaties weergeeft. \b Bron: (Teorey et al., 2011)\b0\par
3. \b Logisch ontwerp\b0 : Vertaling van het conceptuele ERD naar een logisch ERD met gedetailleerde attributen, primaire sleutels en relaties. \b Bron: (Elmasri & Navathe, 2017)\b0\par
4. \b Fysiek ontwerp\b0 : Implementatie van het logische ERD in een MySQL-database met specifieke datatypen, constraints en indexen. \b Bron: (MySQL, 2023)\b0\par
5. \b Implementatie\b0 : Ontwikkeling van Python-scripts voor het genereren en importeren van testdata, en het schrijven van SQL-queries voor operationele en KPI-inzichten. \b Bron: (McKinney, 2017)\b0\par
De belangrijkste resultaten van het project zijn:\par
- Een volledig genormaliseerd databaseontwerp dat alle kernprocessen van Euro Caps ondersteunt. \b Bron: (Date, 2019)\b0\par
- Een set van SQL-queries die zowel operationele als KPI-inzichten bieden. \b Bron: (Garcia-Molina et al., 2020)\b0\par
- Python-scripts voor het genereren en importeren van testdata. \b Bron: (VanderPlas, 2016)\b0\par
- Een uitgebreide documentatie van het ontwerp, inclusief ERD's en ontwerpkeuzes. \b Bron: (Silberschatz et al., 2019)\b0\par
De implementatie van dit databasesysteem zal Euro Caps in staat stellen om:\par
- De traceerbaarheid van producten door de hele productieketen te verbeteren. \b Bron: (Nintex, n.d.)\b0\par
- De effici\'ebntie van productieprocessen te monitoren en te optimaliseren. \b Bron: (Kroenke & Auer, 2016)\b0\par
- De kwaliteitscontrole te verbeteren door gedetailleerde procesgegevens vast te leggen. \b Bron: (Euro Caps, n.d.)\b0\par
- Betere beslissingen te nemen op basis van KPI's en operationele inzichten. \b Bron: (Kimball & Ross, 2013)\b0\par
- De communicatie met partners (leveranciers, klanten, transporteurs) te stroomlijnen. \b Bron: (Oppel, 2011)\b0\par
Het project heeft aangetoond dat een goed ontworpen relationeel databasesysteem een cruciale rol kan spelen in het optimaliseren van productieprocessen en het ondersteunen van besluitvorming in een productiebedrijf zoals Euro Caps. \b Bron: (Wambler, 2015)\b0\par

\pard\sa200\sl276\slmult1\qc\b\fs28 VOORWOORD\b0\fs22\par

\pard\sa200\sl276\slmult1 Voor u ligt het verslag van het databaseontwerp voor Euro Caps, uitgevoerd als onderdeel van mijn studie. Dit project heeft mij de mogelijkheid geboden om theoretische kennis over databaseontwerp toe te passen op een praktische casus in de productiesector.\par
Euro Caps, als producent van koffiecapsules, staat voor de uitdaging om grote hoeveelheden data over hun productieprocessen effici\'ebnt te beheren. \b Bron: (Euro Caps, n.d.)\b0 . Van het malen van koffiebonen tot het verpakken van de eindproducten, elk proces genereert waardevolle informatie die, indien goed gestructureerd en geanalyseerd, kan leiden tot significante verbeteringen in effici\'ebntie en kwaliteit. \b Bron: (Nintex, n.d.)\b0\par
De reis van conceptueel idee naar een volledig ge\'efmplementeerd databasesysteem was uitdagend maar buitengewoon leerzaam. Het vertalen van bedrijfsprocessen naar een logisch datamodel vereiste niet alleen technische kennis, \b Bron: (Elmasri & Navathe, 2017)\b0 , maar ook inzicht in de operationele aspecten van een productiebedrijf. Het was fascinerend om te zien hoe abstracte entiteiten en relaties uiteindelijk vorm kregen in een concreet systeem dat waardevolle inzichten kan opleveren. \b Bron: (Teorey et al., 2011)\b0\par
Ik wil graag mijn dank uitspreken aan mijn docenten voor hun begeleiding en feedback tijdens dit project. Hun expertise en kritische blik hebben mij geholpen om mijn ontwerp te verfijnen en te verbeteren. Ook wil ik mijn medestudenten bedanken voor de inspirerende discussies en het delen van inzichten, wat heeft bijgedragen aan de kwaliteit van dit project.\par
Dit verslag beschrijft het volledige proces van analyse tot implementatie, inclusief de gemaakte ontwerpkeuzes en de lessen die ik heb geleerd. Ik hoop dat het niet alleen voldoet aan de academische vereisten, maar ook een praktische waarde heeft voor vergelijkbare projecten in de toekomst.\par
[Uw Naam]\par
[Datum]\par

\pard\sa200\sl276\slmult1\qc\b\fs28 TOELICHTING KWALITEITSMANAGEMENT DIAGRAMMEN\b0\fs22\par

\pard\sa200\sl276\slmult1\b Ishikawa Diagram (Visgraatdiagram)\b0\par
Het Ishikawa diagram, ook bekend als visgraatdiagram of oorzaak-en-gevolg diagram, is gebruikt om de mogelijke oorzaken van kwaliteitsproblemen in het productieproces van Euro Caps te identificeren en te analyseren. \b Bron: (Nintex, n.d.)\b0 . Dit diagram helpt bij het structureren van een brainstormsessie en het categoriseren van potenti\'eble oorzaken in hoofdcategorie\'ebn zoals mensen, machines, methoden, materialen, metingen en milieu (de 6M's). \b Bron: (Silberschatz et al., 2019)\b0\par
In de context van het Euro Caps project is het Ishikawa diagram specifiek toegepast om de oorzaken van inconsistente kwaliteit in de koffiecapsules te analyseren. Door alle mogelijke factoren systematisch in kaart te brengen, konden we de belangrijkste aandachtsgebieden identificeren voor verdere analyse en verbetering. \b Bron: (Euro Caps, n.d.)\b0\par
\b Pareto Chart\b0\par
De Pareto chart is gebaseerd op het Pareto-principe, ook bekend als de 80/20-regel, dat stelt dat ongeveer 80% van de effecten voortkomen uit 20% van de oorzaken. \b Bron: (Kimball & Ross, 2013)\b0 . In het Euro Caps project is de Pareto chart gebruikt om te bepalen welke kwaliteitsproblemen de grootste impact hebben op de algehele productkwaliteit. \b Bron: (Hoffer et al., 2016)\b0\par
Als de Pareto chart op 0% staat, betekent dit dat de analyse nog niet is uitgevoerd of dat er geen data is ingevoerd. In een volledig ge\'efmplementeerd systeem zou de Pareto chart de frequentie van verschillende kwaliteitsproblemen tonen, gerangschikt van hoogste naar laagste frequentie, met een cumulatieve lijn die aangeeft welk percentage van de totale problemen wordt vertegenwoordigd door elke categorie. \b Bron: (Kroenke & Auer, 2016)\b0\par
\b Control Chart\b0\par
De control chart is een statistisch hulpmiddel dat wordt gebruikt om te bepalen of een proces statistisch onder controle is. \b Bron: (Garcia-Molina et al., 2020)\b0 . Door metingen van een kwaliteitskenmerk over tijd uit te zetten, kunnen we patronen, trends en afwijkingen identificeren die wijzen op speciale oorzaken van variatie. \b Bron: (Connolly & Begg, 2020)\b0\par
In het Euro Caps project is de control chart toegepast om de stabiliteit van kritieke procesparameters te monitoren, zoals de maalgraad van de koffie, de vulhoeveelheid van de capsules en de sealingkwaliteit van de verpakking. Door bovenste en onderste controlelimieten te defini\'ebren, kunnen we snel afwijkingen detecteren en corrigerende maatregelen nemen voordat er kwaliteitsproblemen ontstaan. \b Bron: (McKinney, 2017)\b0\par
Deze drie kwaliteitsmanagementtools vormen samen een krachtige aanpak voor het identificeren, analyseren en oplossen van kwaliteitsproblemen in het productieproces van Euro Caps. Door deze tools te integreren met het databasesysteem, kunnen we niet alleen historische gegevens analyseren, maar ook real-time monitoring en proactieve kwaliteitsborging implementeren. \b Bron: (Nintex, n.d.)\b0\par

\pard\sa200\sl276\slmult1\qc\b\fs28 DATABASEONTWERP EN IMPLEMENTATIE\b0\fs22\par

\pard\sa200\sl276\slmult1 Het databaseontwerp voor Euro Caps is gebaseerd op de principes van relationele databases en volgt een gestructureerde aanpak van conceptueel naar fysiek ontwerp. \b Bron: (Date, 2019)\b0\par
\b Conceptueel Ontwerp\b0\par
In de conceptuele ontwerpfase hebben we de belangrijkste entiteiten en hun relaties ge\'efidentificeerd. Dit resulteerde in een Entity-Relationship Diagram (ERD) dat de kernprocessen van Euro Caps weergeeft: grinding, filling, packaging, kwaliteitscontrole en logistiek. \b Bron: (Teorey et al., 2011)\b0\par
De belangrijkste entiteiten in het conceptuele model zijn:\par
- Partner (leveranciers, klanten, transporteurs)\par
- Product (koffiecapsules in verschillende varianten)\par
- Grinding (maalproces)\par
- Filling (vulproces)\par
- Packaging (verpakkingsproces)\par
- Kwaliteitscontrole\par
- Levering\par
Deze entiteiten zijn met elkaar verbonden via verschillende soorten relaties, zoals one-to-many en many-to-many, om de bedrijfsprocessen accuraat weer te geven. \b Bron: (Elmasri & Navathe, 2017)\b0\par
\b Logisch Ontwerp\b0\par
In de logische ontwerpfase hebben we het conceptuele model vertaald naar een logisch model met gedetailleerde attributen, primaire sleutels en vreemde sleutels. We hebben normalisatietechnieken toegepast om redundantie te verminderen en data-integriteit te waarborgen. \b Bron: (Silberschatz et al., 2019)\b0\par
Het logische model bevat tabellen zoals:\par
- SoortPartner en Partner\par
- SoortProduct en Product\par
- Grinding en Grinding_Product (koppeltabel)\par
- Filling en Filling_Product (koppeltabel)\par
- Packaging en Packaging_Product (koppeltabel)\par
- Levering en Levering_Regel (koppeltabel)\par
Deze structuur maakt het mogelijk om complexe relaties tussen entiteiten te modelleren en tegelijkertijd de principes van normalisatie te respecteren. \b Bron: (Oppel, 2011)\b0\par
\b Fysiek Ontwerp\b0\par
In de fysieke ontwerpfase hebben we het logische model ge\'efmplementeerd in een MySQL-database. We hebben specifieke datatypen gekozen voor elk attribuut, constraints gedefinieerd om data-integriteit te waarborgen, en indexen toegevoegd om de prestaties te optimaliseren. \b Bron: (MySQL, 2023)\b0\par
De fysieke implementatie omvat:\par
- CREATE TABLE statements met primaire en vreemde sleutels\par
- Datatypen zoals INT, VARCHAR, DATE, DATETIME\par
- Constraints zoals NOT NULL, UNIQUE\par
- Indexen op veelgebruikte zoekvelden\par
Deze fysieke structuur vormt de basis voor het opslaan en beheren van alle gegevens die worden gegenereerd door de productieprocessen van Euro Caps. \b Bron: (Garcia-Molina et al., 2020)\b0\par
\b Implementatie en Data-analyse\b0\par
Voor de implementatie hebben we Python-scripts ontwikkeld om testdata te genereren en te importeren in de database. We hebben NumPy en Pandas gebruikt voor datamanipulatie en -analyse. \b Bron: (VanderPlas, 2016)\b0\par
Daarnaast hebben we SQL-queries geschreven voor zowel operationele als KPI-inzichten. Deze queries maken het mogelijk om:\par
- De effici\'ebntie van productieprocessen te monitoren\par
- De kwaliteit van producten te analyseren\par
- De prestaties van machines te evalueren\par
- De leveringsbetrouwbaarheid te meten\par
Door deze gegevens te analyseren, kunnen we waardevolle inzichten verkrijgen die kunnen leiden tot procesverbeteringen en kostenbesparingen. \b Bron: (McKinney, 2017)\b0\par
\b Data Warehouse en Business Intelligence\b0\par
Voor geavanceerde analyse hebben we ook een eenvoudige data warehouse structuur ontworpen volgens de principes van dimensioneel modelleren. Dit maakt het mogelijk om historische gegevens te analyseren en trends te identificeren over langere perioden. \b Bron: (Kimball & Ross, 2013)\b0\par
De dimensionele modellen omvatten:\par
- Feitentabellen voor productie, kwaliteit en leveringen\par
- Dimensietabellen voor tijd, product, partner en locatie\par
Deze structuur ondersteunt business intelligence toepassingen die managementinformatie kunnen leveren voor strategische besluitvorming. \b Bron: (Wambler, 2015)\b0\par

\pard\sa200\sl276\slmult1\qc\b\fs28 LITERATUURLIJST\b0\fs22\par

\pard\sa200\sl276\slmult1 Connolly, T., & Begg, C. (2020). \i Database systems: A practical approach to design, implementation, and management\i0  (7th ed.). Pearson.\par
Date, C. J. (2019). \i Database design and relational theory: Normal forms and all that jazz\i0  (2nd ed.). Apress.\par
Elmasri, R., & Navathe, S. B. (2017). \i Fundamentals of database systems\i0  (7th ed.). Pearson.\par
Euro Caps. (n.d.). \i Euro Caps case study documents\i0 .\par
Garcia-Molina, H., Ullman, J. D., & Widom, J. (2020). \i Database systems: The complete book\i0  (3rd ed.). Pearson.\par
Hoffer, J. A., Ramesh, V., & Topi, H. (2016). \i Modern database management\i0  (12th ed.). Pearson.\par
Kimball, R., & Ross, M. (2013). \i The data warehouse toolkit: The definitive guide to dimensional modeling\i0  (3rd ed.). Wiley.\par
Kroenke, D. M., & Auer, D. J. (2016). \i Database processing: Fundamentals, design, and implementation\i0  (14th ed.). Pearson.\par
McKinney, W. (2017). \i Python for data analysis: Data wrangling with Pandas, NumPy, and IPython\i0  (2nd ed.). O'Reilly Media.\par
MySQL. (2023). \i MySQL 8.0 reference manual\i0 . https://dev.mysql.com/doc/refman/8.0/en/\par
Nintex. (n.d.). \i Process automation maximizes product quality at Euro Caps\i0  [PDF case study].\par
Oppel, A. (2011). \i Databases: A beginner's guide\i0 . McGraw-Hill Education.\par
Silberschatz, A., Korth, H. F., & Sudarshan, S. (2019). \i Database system concepts\i0  (7th ed.). McGraw-Hill Education.\par
Teorey, T. J., Lightstone, S. S., Nadeau, T., & Jagadish, H. V. (2011). \i Database modeling and design: Logical design\i0  (5th ed.). Morgan Kaufmann.\par
VanderPlas, J. (2016). \i Python data science handbook: Essential tools for working with data\i0 . O'Reilly Media.\par
Wambler, S. (2015). \i Agile data warehouse design: Collaborative dimensional modeling, from whiteboard to star schema\i0 . DecisionOne Press.\par
}
