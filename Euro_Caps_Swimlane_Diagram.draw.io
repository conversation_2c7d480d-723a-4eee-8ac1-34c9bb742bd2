<mxfile host="app.diagrams.net" modified="2023-11-10T15:30:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="abc123" version="15.7.3" type="device"><diagram id="prtHgNgQTEPvFCAcTncT" name="Euro Caps Supply Chain Process">
  <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
    <root>
      <mxCell id="0" />
      <mxCell id="1" parent="0" />
      
      <!-- Swimlanes -->
      <mxCell id="2" value="Leveranciers" style="swimlane;html=1;startSize=30;horizontal=0;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="40" width="1080" height="100" as="geometry" />
      </mxCell>
      
      <mxCell id="3" value="Verkoop/Planning" style="swimlane;html=1;startSize=30;horizontal=0;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="140" width="1080" height="100" as="geometry" />
      </mxCell>
      
      <mxCell id="4" value="Inkoop" style="swimlane;html=1;startSize=30;horizontal=0;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="240" width="1080" height="100" as="geometry" />
      </mxCell>
      
      <mxCell id="5" value="Ontvangst &amp; Kwaliteitscontrole" style="swimlane;html=1;startSize=30;horizontal=0;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="340" width="1080" height="100" as="geometry" />
      </mxCell>
      
      <mxCell id="6" value="Magazijn" style="swimlane;html=1;startSize=30;horizontal=0;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="440" width="1080" height="100" as="geometry" />
      </mxCell>
      
      <mxCell id="7" value="Productie" style="swimlane;html=1;startSize=30;horizontal=0;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=14;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="540" width="1080" height="100" as="geometry" />
      </mxCell>
      
      <mxCell id="8" value="Verzending" style="swimlane;html=1;startSize=30;horizontal=0;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="640" width="1080" height="100" as="geometry" />
      </mxCell>
      
      <mxCell id="9" value="Klanten" style="swimlane;html=1;startSize=30;horizontal=0;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="740" width="1080" height="100" as="geometry" />
      </mxCell>
      
      <!-- Proces Stappen -->
      <!-- Leveranciers -->
      <mxCell id="10" value="Leveren grondstoffen" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="2">
        <mxGeometry x="280" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <!-- Verkoop/Planning -->
      <mxCell id="11" value="Ontvangen klantorders" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="3">
        <mxGeometry x="80" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="12" value="Maken productieplanning" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="3">
        <mxGeometry x="280" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <!-- Inkoop -->
      <mxCell id="13" value="Controleren voorraad" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="4">
        <mxGeometry x="180" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="14" value="Plaatsen inkooporders" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="4">
        <mxGeometry x="380" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <!-- Ontvangst & Kwaliteitscontrole -->
      <mxCell id="15" value="Ontvangen materialen" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="5">
        <mxGeometry x="280" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="16" value="Uitvoeren kwaliteitscontrole" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="5">
        <mxGeometry x="480" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <!-- Magazijn -->
      <mxCell id="17" value="Opslaan grondstoffen" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="6">
        <mxGeometry x="480" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="18" value="Leveren materialen aan productie" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="6">
        <mxGeometry x="680" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="19" value="Opslaan eindproducten" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="6">
        <mxGeometry x="880" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <!-- Productie -->
      <mxCell id="20" value="Malen koffiebonen" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="7">
        <mxGeometry x="680" y="20" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="21" value="Vullen capsules" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="7">
        <mxGeometry x="780" y="60" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="22" value="Verpakken producten" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="7">
        <mxGeometry x="880" y="20" width="120" height="40" as="geometry" />
      </mxCell>
      
      <!-- Verzending -->
      <mxCell id="23" value="Verwerken orders" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="8">
        <mxGeometry x="780" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="24" value="Picken en verzenden" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="8">
        <mxGeometry x="980" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <!-- Klanten -->
      <mxCell id="25" value="Plaatsen orders" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="9">
        <mxGeometry x="80" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="26" value="Ontvangen producten" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="9">
        <mxGeometry x="980" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <!-- Verbindingen -->
      <!-- Klant naar Verkoop -->
      <mxCell id="27" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="25" target="11">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Verkoop naar Planning -->
      <mxCell id="28" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="11" target="12">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Planning naar Inkoop -->
      <mxCell id="29" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="12" target="13">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Inkoop naar Inkoop -->
      <mxCell id="30" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="13" target="14">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Inkoop naar Leverancier -->
      <mxCell id="31" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="14" target="10">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Leverancier naar Ontvangst -->
      <mxCell id="32" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="10" target="15">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Ontvangst naar Kwaliteitscontrole -->
      <mxCell id="33" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="15" target="16">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Kwaliteitscontrole naar Magazijn -->
      <mxCell id="34" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="16" target="17">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Magazijn naar Magazijn -->
      <mxCell id="35" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="17" target="18">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Magazijn naar Productie -->
      <mxCell id="36" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="18" target="20">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Productie stappen -->
      <mxCell id="37" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="20" target="21">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <mxCell id="38" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="21" target="22">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Productie naar Magazijn -->
      <mxCell id="39" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="22" target="19">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Magazijn naar Verzending -->
      <mxCell id="40" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="19" target="23">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Verzending stappen -->
      <mxCell id="41" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="23" target="24">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Verzending naar Klant -->
      <mxCell id="42" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="24" target="26">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <!-- Planning naar Productie -->
      <mxCell id="43" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.25;entryY=0;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="12" target="20">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
          <Array as="points">
            <mxPoint x="710" y="190" />
          </Array>
        </mxGeometry>
      </mxCell>
      
      <!-- Verkoop naar Verzending -->
      <mxCell id="44" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="12" target="23">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
          <Array as="points">
            <mxPoint x="780" y="190" />
          </Array>
        </mxGeometry>
      </mxCell>
      
      <!-- Retouren -->
      <mxCell id="45" value="Verwerken retouren" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="8">
        <mxGeometry x="580" y="30" width="120" height="40" as="geometry" />
      </mxCell>
      
      <mxCell id="46" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="26" target="45">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="560" y="410" as="sourcePoint" />
          <mxPoint x="610" y="360" as="targetPoint" />
          <Array as="points">
            <mxPoint x="980" y="690" />
            <mxPoint x="740" y="690" />
          </Array>
        </mxGeometry>
      </mxCell>
      
      <!-- Titel -->
      <mxCell id="47" value="EURO CAPS SUPPLY CHAIN PROCES" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="430" y="10" width="300" height="30" as="geometry" />
      </mxCell>
      
      <!-- Legenda -->
      <mxCell id="48" value="Legenda:" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1" vertex="1" parent="1">
        <mxGeometry x="40" y="850" width="60" height="20" as="geometry" />
      </mxCell>
      
      <mxCell id="49" value="" style="endArrow=classic;html=1;" edge="1" parent="1">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="40" y="890" as="sourcePoint" />
          <mxPoint x="100" y="890" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <mxCell id="50" value="Fysieke stroom" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
        <mxGeometry x="110" y="880" width="100" height="20" as="geometry" />
      </mxCell>
      
      <mxCell id="51" value="" style="endArrow=classic;html=1;dashed=1;" edge="1" parent="1">
        <mxGeometry width="50" height="50" relative="1" as="geometry">
          <mxPoint x="40" y="920" as="sourcePoint" />
          <mxPoint x="100" y="920" as="targetPoint" />
        </mxGeometry>
      </mxCell>
      
      <mxCell id="52" value="Informatiestroom" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
        <mxGeometry x="110" y="910" width="100" height="20" as="geometry" />
      </mxCell>
      
    </root>
  </mxGraphModel>
</diagram></mxfile>
