{"UsedTemplates": [{"Name": "rectangle", "Version": "2.3.0", "Template": "<widget xmlns=\"http://openajax.org/metadata\" spec=\"1.0\" id=\"http://microsoft.com/appmagic/shapes/rectangle\" name=\"rectangle\" jsClass=\"AppMagic.Controls.Shapes.Rectangle\" version=\"2.3.0\" styleable=\"true\" runtimeCost=\"1\" xmlns:appMagic=\"http://schemas.microsoft.com/appMagic\"><author name=\"Microsoft AppMagic\" /><license type=\"text/html\"><![CDATA[<p>TODO:  Need license text here.</p>]]></license><description><![CDATA[Rectangle\r\n      Control description here.]]></description><requires><require type=\"css\" src=\"/ctrllib/shapes/css/shape.css\" /><require type=\"javascript\" src=\"/ctrllib/shapes/js/shape.js\" /></requires><appMagic:capabilities contextualViewsEnabled=\"true\" autoPointerViewState=\"true\" autoDisabledViewState=\"true\" isVersionFlexible=\"true\" /><content><![CDATA[\r\n    <div class=\"appmagic-svg no-focus-outline\" data-bind=\"\r\n      shortcut: {\r\n        provider: shortcutProvider,\r\n        capture: false\r\n      },\r\n      click: onClickHandler,\r\n      css: {\r\n        readonly: viewState.displayMode() !== AppMagic.Constants.DisplayMode.Edit\r\n      },\r\n      attr: {\r\n        title: properties.Tooltip() || null,\r\n        role: (properties.TabIndex() < 0) ? (!!properties.AccessibleLabel() && 'img') : 'button',\r\n        'aria-label': properties.AccessibleLabel() || null,\r\n        'aria-disabled': (properties.TabIndex() >= 0) && (viewState.displayMode() !== AppMagic.Constants.DisplayMode.Edit),\r\n        'aria-hidden': properties.TabIndex() < 0 && !properties.AccessibleLabel()\r\n      }\">\r\n      <svg version=\"1.1\" baseProfile=\"tiny\" preserveAspectRatio=\"none\" xmlns=\"http://www.w3.org/2000/svg\"\r\n        focusable=\"false\"\r\n        aria-hidden=\"true\"\r\n        touch-action=\"pan-x pan-y\"\r\n        data-bind=\"\r\n          attr: {\r\n            preserveAspectRatio: properties.PreserveAspectRatio() ? 'xMidYMid meet' : 'none'\r\n          },\r\n          style: {\r\n            width: AppMagic.Controls.converters.pxHorizontalConverter.view(properties.Width()),\r\n            height: AppMagic.Controls.converters.pxVerticalConverter.view(properties.Height())\r\n          }\">\r\n        <g>\r\n          <polygon data-bind=\"attr: {\r\n            fill: fill,\r\n            points: innerPoints\r\n          }\" />\r\n\r\n          <!-- Border polygon -->\r\n          <polygon data-bind=\"attr: {\r\n            fill: 'transparent',\r\n            stroke: borderColor,\r\n            'stroke-width': borderThickness,\r\n            'stroke-dasharray': properties.BorderStyle,\r\n            points: outerPoints\r\n          }\" />\r\n        </g>\r\n      </svg>\r\n    </div>\r\n    ]]></content><appMagic:includeProperties><appMagic:includeProperty name=\"AccessibleLabel\" /><appMagic:includeProperty name=\"Tooltip\" /><appMagic:includeProperty name=\"ContentLanguage\" /><appMagic:includeProperty name=\"Fill\" defaultValue=\"RGBA(0, 176, 240, 1)\" isPrimaryOutputProperty=\"true\" /><appMagic:includeProperty name=\"DisabledFill\" defaultValue=\"Self.Fill\" /><appMagic:includeProperty name=\"PressedFill\" defaultValue=\"Self.Fill\" /><appMagic:includeProperty name=\"HoverFill\" defaultValue=\"Self.Fill\" /><appMagic:includeProperty name=\"BorderColor\" defaultValue=\"RGBA(0, 0, 255, 1)\" /><appMagic:includeProperty name=\"BorderStyle\" converter=\"strokeConverter\" /><appMagic:includeProperty name=\"BorderThickness\" /><appMagic:includeProperty name=\"FocusedBorderColor\" defaultValue=\"Self.BorderColor\" isExpr=\"true\" /><appMagic:includeProperty name=\"FocusedBorderThickness\" /><appMagic:includeProperty name=\"Visible\" /><appMagic:includeProperty name=\"DisplayMode\" /><appMagic:includeProperty name=\"TabIndex\" defaultValue=\"-1\" /><appMagic:includeProperty name=\"X\" /><appMagic:includeProperty name=\"Y\" /><appMagic:includeProperty name=\"Width\" defaultValue=\"150\" /><appMagic:includeProperty name=\"Height\" defaultValue=\"100\" /><!-- Behavior Properties --><appMagic:includeProperty name=\"OnSelect\" direction=\"in\" isPrimaryInputProperty=\"true\" /><!-- Hidden properties --><appMagic:includeProperty name=\"minimumWidth\" defaultValue=\"1\" /><appMagic:includeProperty name=\"minimumHeight\" defaultValue=\"1\" /><appMagic:includeProperty name=\"maximumWidth\" defaultValue=\"1366\" /><appMagic:includeProperty name=\"maximumHeight\" defaultValue=\"768\" /><appMagic:includeProperty name=\"PreserveAspectRatio\" defaultValue=\"false\" /></appMagic:includeProperties><appMagic:insertMetadata isDeviceOptimized=\"true\"><appMagic:category name=\"Popular\" priority=\"60\" /><appMagic:category name=\"Shapes\" priority=\"70\" /></appMagic:insertMetadata><!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) --><appMagic:displayMetadata><appMagic:section><appMagic:property name=\"DisplayMode\" /></appMagic:section><appMagic:section><appMagic:property name=\"Visible\" /><appMagic:propertyGroup name=\"Position\"><appMagic:property name=\"X\" /><appMagic:property name=\"Y\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Size\"><appMagic:property name=\"Width\" /><appMagic:property name=\"Height\" /></appMagic:propertyGroup></appMagic:section><appMagic:section><appMagic:propertyGroup name=\"Color\"><appMagic:property name=\"Fill\" showInFloatie=\"true\" showInCommandBar=\"true\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Border\"><appMagic:property name=\"BorderStyle\" showInCommandBar=\"true\" /><appMagic:property name=\"BorderThickness\" showInCommandBar=\"true\" /><appMagic:property name=\"BorderColor\" showInCommandBar=\"true\" /></appMagic:propertyGroup></appMagic:section><appMagic:section><appMagic:propertyGroup name=\"DisabledColor\"><appMagic:property name=\"DisabledFill\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"HoverColor\"><appMagic:property name=\"HoverFill\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"PressedColor\"><appMagic:property name=\"PressedFill\" /></appMagic:propertyGroup><appMagic:property name=\"Tooltip\" /><appMagic:property name=\"TabIndex\" /></appMagic:section></appMagic:displayMetadata><appMagic:conversion from=\"2.0.0\" to=\"2.1.0\"><!-- Changed to access Width and Height on measuredProperties --><appMagic:conversionAction type=\"block\" newDocVersion=\"1.295\" /></appMagic:conversion><appMagic:conversion from=\"2.1.0\" to=\"2.1.1\"><!-- Reverted previous change (Width and Height properties now return actual values) --></appMagic:conversion><appMagic:conversion from=\"2.1.1\" to=\"2.1.2\"><!-- KO template changes for accessibility fixes --></appMagic:conversion><appMagic:conversion from=\"2.1.2\" to=\"2.2.0\"><appMagic:conversionAction type=\"add\" name=\"ContentLanguage\" /></appMagic:conversion><appMagic:conversion from=\"2.2.0\" to=\"2.3.0\"><!-- Adding showInCommandBar flag --></appMagic:conversion></widget>"}, {"Name": "label", "Version": "2.5.1", "Template": "<widget xmlns=\"http://openajax.org/metadata\" spec=\"1.0\" id=\"http://microsoft.com/appmagic/label\" name=\"label\" jsClass=\"AppMagic.Controls.Label\" version=\"2.5.1\" styleable=\"true\" runtimeCost=\"1\" xmlns:appMagic=\"http://schemas.microsoft.com/appMagic\"><author name=\"Microsoft AppMagic\" /><license type=\"text/html\"><![CDATA[<p>TODO:  Need license text here.</p>]]></license><description><![CDATA[LABEL\r\n      Control description here.]]></description><requires><require type=\"css\" src=\"css/label.css\" /><require type=\"javascript\" src=\"js/label.js\" /></requires><appMagic:capabilities contextualViewsEnabled=\"true\" allowsPerCharacterFormatting=\"true\" autoBorders=\"true\" autoFill=\"true\" autoFocusedBorders=\"true\" autoPointerViewState=\"true\" autoDisabledViewState=\"true\" screenActiveAware=\"true\" isVersionFlexible=\"true\" supportsSetFocus=\"true\" /><content><![CDATA[\r\n  <div\r\n    class=\"appmagic-label no-focus-outline\"\r\n    touch-action=\"pan-x pan-y\"\r\n    tabIndex=\"-1\"\r\n    data-bind=\"\r\n      style: {\r\n        fontFamily: properties.Font,\r\n        fontSize: properties.Size,\r\n        color: autoProperties.Color,\r\n        fontWeight: properties.FontWeight,\r\n        fontStyle: properties.Italic,\r\n        textAlign: properties.Align,\r\n        paddingTop: properties.PaddingTop,\r\n        paddingRight: properties.PaddingRight,\r\n        paddingBottom: properties.PaddingBottom,\r\n        paddingLeft: properties.PaddingLeft,\r\n        lineHeight: properties.LineHeight,\r\n        overflowY: properties.Overflow,\r\n        display:  properties.Overflow() === 'auto' ? 'block' : ''\r\n      },\r\n      css: {\r\n        top: properties.AutoHeight() || properties.VerticalAlign() === 'top',\r\n        middle: !properties.AutoHeight() && properties.VerticalAlign() === 'middle',\r\n        bottom: !properties.AutoHeight() && properties.VerticalAlign() === 'bottom',\r\n        disabled: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled,\r\n        readOnly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View,\r\n        underline: properties.Underline,\r\n        strikethrough: properties.Strikethrough\r\n      },\r\n      attr: {\r\n        title: properties.Tooltip,\r\n        role: properties.TabIndex() >= 0 ? 'button' : 'presentation',\r\n        'aria-disabled': properties.TabIndex() >= 0 && viewState.displayMode() !== AppMagic.Constants.DisplayMode.Edit\r\n      },\r\n      event: {\r\n        click: handleClick\r\n      },\r\n      shortcut: {\r\n        provider: shortcutProvider,\r\n        enabled: shortcutsEnabled\r\n      }\"\r\n    >\r\n    <!-- ko if: properties.Role() !== 'heading1' && properties.Role() !== 'heading2' && properties.Role() !== 'heading3' && properties.Role() !== 'heading4' -->\r\n        <div\r\n          spellcheck=\"false\"\r\n          unselectable=\"off\"\r\n          class=\"appmagic-label-text\"\r\n          data-control-part=\"text\"\r\n          data-bind=\"{\r\n            inlineEditText: properties.Text,\r\n            css: {\r\n              'appmagic-label-single-line': !properties.Wrap()\r\n            },\r\n            attr: {\r\n              'aria-live': live,\r\n              'aria-atomic': live() ? 'true' : null\r\n            }\r\n          }\">\r\n        </div>\r\n    <!-- /ko -->\r\n    <!-- ko if: properties.Role() === 'heading1' -->\r\n        <h1\r\n          spellcheck=\"false\"\r\n          unselectable=\"off\"\r\n          class=\"appmagic-label-text\"\r\n          data-control-part=\"text\"\r\n          data-bind=\"{\r\n            inlineEditText: properties.Text,\r\n            css: {\r\n              'appmagic-label-single-line': !properties.Wrap()\r\n            },\r\n            attr: {\r\n              'aria-live': live,\r\n              'aria-atomic': live() ? 'true' : null\r\n            }\r\n          }\">\r\n         </h1>\r\n    <!-- /ko -->\r\n    <!-- ko if: properties.Role() === 'heading2' -->\r\n        <h2\r\n          spellcheck=\"false\"\r\n          unselectable=\"off\"\r\n          class=\"appmagic-label-text\"\r\n          data-control-part=\"text\"\r\n          data-bind=\"{\r\n            inlineEditText: properties.Text,\r\n            css: {\r\n              'appmagic-label-single-line': !properties.Wrap()\r\n            },\r\n            attr: {\r\n              'aria-live': live,\r\n              'aria-atomic': live() ? 'true' : null\r\n            }\r\n          }\">\r\n         </h2>\r\n    <!-- /ko -->\r\n    <!-- ko if: properties.Role() === 'heading3' -->\r\n        <h3\r\n          spellcheck=\"false\"\r\n          unselectable=\"off\"\r\n          class=\"appmagic-label-text\"\r\n          data-control-part=\"text\"\r\n          data-bind=\"{\r\n            inlineEditText: properties.Text,\r\n            css: {\r\n              'appmagic-label-single-line': !properties.Wrap()\r\n            },\r\n            attr: {\r\n              'aria-live': live,\r\n              'aria-atomic': live() ? 'true' : null\r\n            }\r\n          }\">\r\n         </h3>\r\n    <!-- /ko -->\r\n    <!-- ko if: properties.Role() === 'heading4' -->\r\n        <h4\r\n          spellcheck=\"false\"\r\n          unselectable=\"off\"\r\n          class=\"appmagic-label-text\"\r\n          data-control-part=\"text\"\r\n          data-bind=\"{\r\n            inlineEditText: properties.Text,\r\n            css: {\r\n              'appmagic-label-single-line': !properties.Wrap()\r\n            },\r\n            attr: {\r\n              'aria-live': live,\r\n              'aria-atomic': live() ? 'true' : null\r\n            }\r\n          }\">\r\n         </h4>\r\n    <!-- /ko -->\r\n  </div>\r\n  ]]></content><properties><!-- Data --><property name=\"Live\" localizedName=\"##label_Live##\" datatype=\"Live\" defaultValue=\"%Live.RESERVED%.Off\" isExpr=\"true\"><title>Live</title><appMagic:category>data</appMagic:category><appMagic:displayName>##label_Live_DisplayName##</appMagic:displayName><appMagic:tooltip>##label_Live_Tooltip##</appMagic:tooltip></property><!-- Design --><property name=\"LineHeight\" localizedName=\"##label_LineHeight##\" datatype=\"Number\" defaultValue=\"1.2\"><title>Line Height</title><appMagic:category>design</appMagic:category><appMagic:displayName>##label_LineHeight_DisplayName##</appMagic:displayName><appMagic:tooltip>##label_LineHeight_Tooltip##</appMagic:tooltip><appMagic:helperUI>lineWidth</appMagic:helperUI></property><property name=\"Overflow\" localizedName=\"##label_Overflow##\" datatype=\"Overflow\" defaultValue=\"%Overflow.RESERVED%.Hidden\" isExpr=\"true\" converter=\"overflowConverter\"><title>Overflow</title><appMagic:category>design</appMagic:category><appMagic:displayName>##label_Overflow_DisplayName##</appMagic:displayName><appMagic:tooltip>##label_Overflow_Tooltip##</appMagic:tooltip><appMagic:helperUI>overflow</appMagic:helperUI></property><!-- This is not an 'Auto' property. This is for allowing the label to grow vertically to display the content --><property name=\"AutoHeight\" localizedName=\"##CommonProperties_AutoHeight##\" datatype=\"Boolean\" defaultValue=\"false\"><title>AutoHeight</title><appMagic:category>design</appMagic:category><appMagic:displayName>##CommonProperties_AutoHeight_DisplayName##</appMagic:displayName><appMagic:tooltip>##CommonProperties_AutoHeight_Tooltip##</appMagic:tooltip></property><property name=\"Wrap\" localizedName=\"##label_Wrap##\" datatype=\"Boolean\" defaultValue=\"true\" canBeCompressed=\"false\"><title>Wrap</title><appMagic:category>design</appMagic:category><appMagic:displayName>##label_Wrap_DisplayName##</appMagic:displayName><appMagic:tooltip>##label_Wrap_Tooltip##</appMagic:tooltip></property><!-- IsErrorMessage is deprecated. Delete once a document converter is written to migrate IsErrorMessage=true -> Live=Live.Assertive --><property name=\"IsErrorMessage\" localizedName=\"##label_IsErrorMessage##\" datatype=\"Boolean\" defaultValue=\"false\" hidden=\"true\"><appMagic:category>design</appMagic:category></property></properties><appMagic:includeProperties><!-- Data --><appMagic:includeProperty name=\"Text\" defaultValue=\"##Label_DefaultValue_Text##\" isExpr=\"true\" isPrimaryInputProperty=\"true\" isPrimaryOutputProperty=\"true\"><appMagic:autoBind>true</appMagic:autoBind></appMagic:includeProperty><appMagic:includeProperty name=\"Tooltip\" /><appMagic:includeProperty name=\"Role\" /><appMagic:includeProperty name=\"ContentLanguage\" /><!-- Design --><appMagic:includeProperty name=\"Color\" allowsPerCharacterFormatting=\"true\" defaultValue=\"RGBA(71, 69, 64, 1)\" /><appMagic:includeProperty name=\"DisabledColor\" defaultValue=\"RGBA(186, 186, 186, 1)\" /><appMagic:includeProperty name=\"PressedColor\" defaultValue=\"Self.Color\" /><appMagic:includeProperty name=\"HoverColor\" defaultValue=\"Self.Color\" /><appMagic:includeProperty name=\"BorderColor\" /><appMagic:includeProperty name=\"DisabledBorderColor\" defaultValue=\"RGBA(56, 56, 56, 1)\" /><appMagic:includeProperty name=\"PressedBorderColor\" defaultValue=\"Self.BorderColor\" /><appMagic:includeProperty name=\"HoverBorderColor\" defaultValue=\"Self.BorderColor\" /><appMagic:includeProperty name=\"BorderStyle\" /><appMagic:includeProperty name=\"BorderThickness\" defaultValue=\"0\" /><appMagic:includeProperty name=\"FocusedBorderColor\" defaultValue=\"Self.BorderColor\" isExpr=\"true\" /><appMagic:includeProperty name=\"FocusedBorderThickness\" defaultValue=\"0\" /><appMagic:includeProperty name=\"Fill\" allowsPerCharacterFormatting=\"true\" /><appMagic:includeProperty name=\"DisabledFill\" defaultValue=\"RGBA(0, 0, 0, 0)\" /><appMagic:includeProperty name=\"PressedFill\" defaultValue=\"Self.Fill\" /><appMagic:includeProperty name=\"HoverFill\" defaultValue=\"Self.Fill\" /><appMagic:includeProperty name=\"Font\" allowsPerCharacterFormatting=\"true\" /><appMagic:includeProperty name=\"Size\" allowsPerCharacterFormatting=\"true\" defaultValue=\"14\" phoneDefaultValue=\"24\" /><appMagic:includeProperty name=\"FontWeight\" allowsPerCharacterFormatting=\"true\" /><appMagic:includeProperty name=\"Italic\" allowsPerCharacterFormatting=\"true\" /><appMagic:includeProperty name=\"Underline\" allowsPerCharacterFormatting=\"true\" /><appMagic:includeProperty name=\"Strikethrough\" allowsPerCharacterFormatting=\"true\" /><appMagic:includeProperty name=\"PaddingTop\" defaultValue=\"5\" /><appMagic:includeProperty name=\"PaddingRight\" defaultValue=\"5\" /><appMagic:includeProperty name=\"PaddingBottom\" defaultValue=\"5\" /><appMagic:includeProperty name=\"PaddingLeft\" defaultValue=\"5\" /><appMagic:includeProperty name=\"Align\" /><appMagic:includeProperty name=\"VerticalAlign\" defaultValue=\"%VerticalAlign.RESERVED%.Middle\" /><appMagic:includeProperty name=\"X\" /><appMagic:includeProperty name=\"Y\" /><appMagic:includeProperty name=\"Width\" defaultValue=\"150\" phoneDefaultValue=\"560\" webDefaultValue=\"260\" /><appMagic:includeProperty name=\"Height\" defaultValue=\"40\" phoneDefaultValue=\"70\" webDefaultValue=\"40\" /><appMagic:includeProperty name=\"Visible\" /><appMagic:includeProperty name=\"DisplayMode\" /><!-- TabIndex should be -1 to *prevent* making the control a tabstop unless explicitly changed --><appMagic:includeProperty name=\"TabIndex\" defaultValue=\"-1\" /><!-- Behavior Properties --><!-- TASK: 85476: Do Behavior properties make sense as input only? --><appMagic:includeProperty name=\"OnSelect\" direction=\"in\" /><!-- Hidden properties --><appMagic:includeProperty name=\"minimumWidth\" defaultValue=\"1\" /><appMagic:includeProperty name=\"minimumHeight\" defaultValue=\"1\" /><appMagic:includeProperty name=\"maximumWidth\" defaultValue=\"1366\" /><appMagic:includeProperty name=\"maximumHeight\" defaultValue=\"7680\" /></appMagic:includeProperties><appMagic:propertyDependencies><appMagic:propertyDependency input=\"AutoHeight\" output=\"Height\" /></appMagic:propertyDependencies><appMagic:insertMetadata isDeviceOptimized=\"true\"><appMagic:category name=\"Popular\" priority=\"10\" /><appMagic:category name=\"Display\" priority=\"10\" /><appMagic:category name=\"ClassicControls\" priority=\"10\" /></appMagic:insertMetadata><!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) --><appMagic:displayMetadata><appMagic:section><appMagic:property name=\"Text\" /><appMagic:property name=\"Font\" displayType=\"FontEnum\" showInFloatie=\"true\" showInCommandBar=\"true\" /><appMagic:property name=\"Size\" labelOverride=\"##FontSize_Property##\" showInFloatie=\"true\" showInCommandBar=\"true\" /><appMagic:property name=\"FontWeight\" displayType=\"EnumIcon\" itemsOrder=\"Bold;Semibold;Normal;Lighter\" showInCommandBar=\"true\" /><appMagic:propertyGroup name=\"Style\"><appMagic:property name=\"Italic\" displayType=\"ToggleButton\" /><appMagic:property name=\"Underline\" displayType=\"ToggleButton\" /><appMagic:property name=\"Strikethrough\" displayType=\"ToggleButton\" /></appMagic:propertyGroup><appMagic:property name=\"Align\" displayType=\"EnumButtons\" itemsOrder=\"Left;Center;Right;Justify\" labelOverride=\"##FontAlign_Property##\" showInFloatie=\"true\" showInCommandBar=\"true\" floatieDisplayType=\"FaceplateIconEnum\" /><appMagic:property name=\"AutoHeight\" /><appMagic:property name=\"LineHeight\" /><appMagic:property name=\"Overflow\" /><appMagic:property name=\"DisplayMode\" /></appMagic:section><appMagic:section><appMagic:property name=\"Visible\" /><appMagic:propertyGroup name=\"Position\"><appMagic:property name=\"X\" /><appMagic:property name=\"Y\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Size\"><appMagic:property name=\"Width\" /><appMagic:property name=\"Height\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Padding\"><appMagic:property name=\"PaddingTop\" labelOverride=\"##Padding_Top_Title##\" /><appMagic:property name=\"PaddingBottom\" labelOverride=\"##Padding_Bottom_Title##\" /><appMagic:property name=\"PaddingLeft\" labelOverride=\"##Padding_Left_Title##\" /><appMagic:property name=\"PaddingRight\" labelOverride=\"##Padding_Right_Title##\" /></appMagic:propertyGroup></appMagic:section><appMagic:section><appMagic:propertyGroup name=\"Color\"><appMagic:property name=\"Color\" showInFloatie=\"true\" showInCommandBar=\"true\" /><appMagic:property name=\"Fill\" showInFloatie=\"true\" showInCommandBar=\"true\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Border\"><appMagic:property name=\"BorderStyle\" /><appMagic:property name=\"BorderThickness\" /><appMagic:property name=\"BorderColor\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"FocusedBorder\"><appMagic:property name=\"FocusedBorderThickness\" /><appMagic:property name=\"FocusedBorderColor\" /></appMagic:propertyGroup><appMagic:property name=\"Wrap\" /><appMagic:property name=\"VerticalAlign\" displayType=\"EnumIcon\" itemsOrder=\"Top;Middle;Bottom\" /></appMagic:section><appMagic:section><appMagic:propertyGroup name=\"DisabledColor\"><appMagic:property name=\"DisabledColor\" /><appMagic:property name=\"DisabledFill\" /><appMagic:property name=\"DisabledBorderColor\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"HoverColor\"><appMagic:property name=\"HoverColor\" /><appMagic:property name=\"HoverFill\" /><appMagic:property name=\"HoverBorderColor\" /></appMagic:propertyGroup><appMagic:property name=\"Tooltip\" /><appMagic:property name=\"TabIndex\" /></appMagic:section></appMagic:displayMetadata><appMagic:conversion from=\"2.0.0\" to=\"2.1.0\"><appMagic:conversionAction type=\"add\" name=\"IsErrorMessage\" /></appMagic:conversion><appMagic:conversion from=\"2.1.0\" to=\"2.2.0\"><appMagic:conversionAction type=\"add\" name=\"Live\" /></appMagic:conversion><appMagic:conversion from=\"2.2.0\" to=\"2.3.0\"><appMagic:conversionAction type=\"add\" name=\"Role\" /></appMagic:conversion><appMagic:conversion from=\"2.3.0\" to=\"2.3.1\"><!-- updated template for auto-height --></appMagic:conversion><appMagic:conversion from=\"2.3.1\" to=\"2.3.2\"><!-- KO template changes for accessibility fixes --></appMagic:conversion><appMagic:conversion from=\"2.3.2\" to=\"2.4.0\"><appMagic:conversionAction type=\"add\" name=\"ContentLanguage\" /></appMagic:conversion><appMagic:conversion from=\"2.4.0\" to=\"2.5.0\"><!-- Adding showInCommandBar flag --></appMagic:conversion><appMagic:conversion from=\"2.5.0\" to=\"2.5.1\"><!-- Adding role=presentation for TabIndex > -1 --></appMagic:conversion></widget>"}, {"Name": "text", "Version": "2.3.2", "Template": "<widget xmlns=\"http://openajax.org/metadata\" spec=\"1.0\" id=\"http://microsoft.com/appmagic/text\" name=\"text\" jsClass=\"AppMagic.Controls.Text\" version=\"2.3.2\" styleable=\"true\" runtimeCost=\"1\" xmlns:appMagic=\"http://schemas.microsoft.com/appMagic\"><author name=\"Microsoft AppMagic\" /><license type=\"text/html\"><![CDATA[<p>TODO:  Need license text here.</p>]]></license><description><![CDATA[TEXT\r\n      Control description here.]]></description><requires><require type=\"css\" src=\"css/text.css\" /><require type=\"javascript\" src=\"js/text.js\" /></requires><appMagic:capabilities contextualViewsEnabled=\"true\" autoBorders=\"true\" autoFocusedBorders=\"true\" autoFill=\"true\" autoPointerViewState=\"true\" autoDisabledViewState=\"true\" autoBorderRadius=\"true\" isVersionFlexible=\"true\" supportsSetFocus=\"true\" /><appMagic:accessibilityChecks controlIsInteractive=\"true\" /><content><![CDATA[\r\n  <div class=\"appmagic-textbox\" touch-action=\"pan-x pan-y\"\r\n    appmagic-control=\"__WID__\">\r\n  <!-- ko if: (mode() === \"multiline\") -->\r\n  <textarea\r\n    appmagic-control=\"__WID__textarea\"\r\n    class=\"appmagic-textarea mousetrap block-undo-redo\"\r\n    data-control-part=\"text\"\r\n    data-bind=\"\r\n      value: text,\r\n      css: {\r\n        underline: properties.Underline,\r\n        strikethrough: properties.Strikethrough,\r\n        readonly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View\r\n      },\r\n      event: {\r\n        click: handleClick,\r\n        change: handleOnChange\r\n      },\r\n      attr: {\r\n        title: properties.Tooltip() || null,\r\n        'aria-label': properties.AccessibleLabel() || null,\r\n        placeholder: properties.HintText,\r\n        readonly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View,\r\n        maxlength: properties.MaxLength() < 0 ? 0 : properties.MaxLength()\r\n      },\r\n      style: {\r\n        fontFamily: properties.Font,\r\n        fontSize: properties.Size,\r\n        color: autoProperties.Color,\r\n        fontWeight: properties.FontWeight,\r\n        fontStyle: properties.Italic,\r\n        textAlign: properties.Align,\r\n        lineHeight: properties.LineHeight,\r\n        paddingTop: properties.PaddingTop,\r\n        paddingRight: properties.PaddingRight,\r\n        paddingBottom: properties.PaddingBottom,\r\n        paddingLeft: properties.PaddingLeft\r\n      },\r\n      disable: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled\">\r\n  </textarea>\r\n  <!-- /ko -->\r\n  <!-- ko if: (mode() !== \"multiline\") -->\r\n  <input\r\n    appmagic-control=\"__WID__textbox\"\r\n    class=\"appmagic-text mousetrap block-undo-redo\"\r\n    maxlength=\"10000\"\r\n    data-bind=\"\r\n      attr: {\r\n        type: mode() === 'password' ? 'password' : 'text',\r\n        title: properties.Tooltip() || null,\r\n        'aria-label': properties.AccessibleLabel() || null,\r\n        placeholder: properties.HintText,\r\n        readonly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View,\r\n        'data-control-part': properties.Clear() ? 'text clearable' : 'text',\r\n        inputmode: keyboardMode,\r\n        maxlength: properties.MaxLength() < 0 ? 0 : properties.MaxLength()\r\n      },\r\n      css: {\r\n        underline: properties.Underline,\r\n        strikethrough: properties.Strikethrough,\r\n        readonly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View\r\n      },\r\n      value: text,\r\n      event: {\r\n        click: handleClick,\r\n        change: handleOnChange\r\n      },\r\n      style: {\r\n        fontFamily: properties.Font,\r\n        fontSize: properties.Size,\r\n        color: autoProperties.Color,\r\n        fontWeight: properties.FontWeight,\r\n        fontStyle: properties.Italic,\r\n        textAlign: properties.Align,\r\n        lineHeight: properties.LineHeight,\r\n        paddingTop: properties.PaddingTop,\r\n        paddingRight: properties.PaddingRight,\r\n        paddingBottom: properties.PaddingBottom,\r\n        paddingLeft: properties.PaddingLeft\r\n      },\r\n      disable: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled\">\r\n  </input>\r\n  <div class='appmagic-text-clear-container'>\r\n    <button class='appmagic-text-clear-button'\r\n      data-control-part=\"clear\"\r\n      data-bind=\"\r\n        visible: isFocused() && properties.Clear() && properties.Text() && mode() !== 'password' && viewState.displayMode() === AppMagic.Constants.DisplayMode.Edit,\r\n        event: {click: handleClearClick},\r\n        attr: {'aria-label': AppMagic.Strings.TextInputClearButtonLabel},\r\n        style: {color: autoProperties.Color}\">\r\n        <svg\r\n          class='appmagic-text-clear-svg'\r\n          xmlns='http://www.w3.org/2000/svg'\r\n          viewbox='0 0 12 12'\r\n          aria-hidden='true'\r\n          focusable='false'>\r\n          <polygon points=\"12,1.1 10.9,0 6,4.9 1.1,0 0,1.1 4.9,6 0,10.9 1.1,12 6,7.1 10.9,12 12,10.9 7.1,6\"></polygon>\r\n        </svg>\r\n    </button>\r\n  </div>\r\n  <!-- /ko -->\r\n  <!-- ko if: properties.Format() === 'number' && viewState.displayMode() === AppMagic.Constants.DisplayMode.Edit -->\r\n  <div class=\"a11y\" aria-live=\"assertive\" aria-atomic=\"true\"></div>\r\n  <!-- /ko -->\r\n  </div>\r\n    ]]></content><properties><property name=\"Default\" localizedName=\"##text_Default##\" datatype=\"String\" defaultValue=\"##Text_DefaultValue_Default##\" isExpr=\"true\" editable=\"true\" direction=\"in\" isPrimaryInputProperty=\"true\"><title>Initial text</title><appMagic:category>data</appMagic:category><appMagic:displayName>##text_Default_DisplayName##</appMagic:displayName><appMagic:tooltip>##text_Default_Tooltip##</appMagic:tooltip></property><property name=\"Text\" localizedName=\"##text_Text##\" datatype=\"String\" direction=\"out\" isPrimaryOutputProperty=\"true\" supportsAutomation=\"true\"><title>Output Text</title><appMagic:category>data</appMagic:category></property><property name=\"Mode\" localizedName=\"##text_Mode##\" datatype=\"TextMode\" defaultValue=\"%TextMode.RESERVED%.SingleLine\" isExpr=\"true\"><title>Textbox Mode</title><appMagic:category>design</appMagic:category><appMagic:helperUI>TextMode</appMagic:helperUI><appMagic:displayName>##text_Mode_DisplayName##</appMagic:displayName><appMagic:tooltip>##text_Mode_Tooltip##</appMagic:tooltip></property><property name=\"Format\" localizedName=\"##text_Format##\" datatype=\"TextFormat\" defaultValue=\"%TextFormat.RESERVED%.Text\" isExpr=\"true\"><title>Input textbox Format</title><appMagic:category>design</appMagic:category><appMagic:helperUI>TextFormat</appMagic:helperUI><appMagic:displayName>##text_Format_DisplayName##</appMagic:displayName><appMagic:tooltip>##text_Format_Tooltip##</appMagic:tooltip></property><property name=\"VirtualKeyboardMode\" localizedName=\"##text_VirtualKeyboardMode##\" datatype=\"VirtualKeyboardMode\" defaultValue=\"%VirtualKeyboardMode.RESERVED%.Auto\" isExpr=\"true\"><title>Input Keyboard Type</title><appMagic:category>design</appMagic:category><appMagic:helperUI>VirtualKeyboardMode</appMagic:helperUI><appMagic:displayName>##text_VirtualKeyboardMode_DisplayName##</appMagic:displayName><appMagic:tooltip>##text_VirtualKeyboardMode_Tooltip##</appMagic:tooltip></property><property name=\"Clear\" localizedName=\"##text_Clear##\" datatype=\"Boolean\" defaultValue=\"false\"><title>Clear Button</title><appMagic:category>design</appMagic:category><appMagic:helperUI>boolean</appMagic:helperUI><appMagic:displayName>##text_Clear_DisplayName##</appMagic:displayName><appMagic:tooltip>##text_Clear_Tooltip##</appMagic:tooltip></property><property name=\"EnableSpellCheck\" localizedName=\"##text_EnableSpellCheck##\" datatype=\"Boolean\" defaultValue=\"false\"><title>Enable spell check</title><appMagic:category>design</appMagic:category><appMagic:helperUI>boolean</appMagic:helperUI><appMagic:displayName>##text_EnableSpellCheck_DisplayName##</appMagic:displayName><appMagic:tooltip>##text_EnableSpellCheck_Tooltip##</appMagic:tooltip></property><property name=\"Reset\" localizedName=\"##commonProperties_Reset##\" datatype=\"Boolean\" defaultValue=\"false\" direction=\"in\"><title>Reset</title><appMagic:category>data</appMagic:category><appMagic:displayName>##commonProperties_Reset_DisplayName##</appMagic:displayName><appMagic:tooltip>##commonProperties_Reset_Tooltip##</appMagic:tooltip></property><property name=\"LineHeight\" localizedName=\"##text_LineHeight##\" datatype=\"Number\" defaultValue=\"1.2\"><title>Line Height</title><appMagic:category>design</appMagic:category><appMagic:displayName>##text_LineHeight_DisplayName##</appMagic:displayName><appMagic:helperUI>lineWidth</appMagic:helperUI><appMagic:tooltip>##label_LineHeight_Tooltip##</appMagic:tooltip></property><property name=\"HintText\" localizedName=\"##text_HintText##\" datatype=\"String\"><title>Hint text</title><appMagic:category>data</appMagic:category><appMagic:displayName>##text_HintText_DisplayName##</appMagic:displayName><appMagic:tooltip>##text_HintText_Tooltip##</appMagic:tooltip></property><property name=\"MaxLength\" localizedName=\"##commonProperties_MaxLength##\" datatype=\"Number\" default=\"\" direction=\"in\" isExpr=\"true\"><title>MaxLength</title><appMagic:category>data</appMagic:category><appMagic:displayName>##commonProperties_MaxLength_DisplayName##</appMagic:displayName><appMagic:tooltip>##commonProperties_MaxLength_Tooltip##</appMagic:tooltip></property><property name=\"DelayOutput\" localizedName=\"##text_DelayOutput##\" datatype=\"Boolean\" defaultValue=\"false\" direction=\"in\" isExpr=\"true\"><title>DelayOutput</title><appMagic:category>data</appMagic:category><appMagic:displayName>##text_DelayOutput_DisplayName##</appMagic:displayName><appMagic:tooltip>##text_DelayOutput_Tooltip##</appMagic:tooltip></property></properties><appMagic:includeProperties><!-- Data --><appMagic:includeProperty name=\"AccessibleLabel\" /><appMagic:includeProperty name=\"Tooltip\"><appMagic:commandBar><appMagic:visible>true</appMagic:visible></appMagic:commandBar></appMagic:includeProperty><appMagic:includeProperty name=\"ContentLanguage\" /><!-- Design --><appMagic:includeProperty name=\"BorderColor\" /><appMagic:includeProperty name=\"RadiusTopLeft\" defaultValue=\"5\" /><appMagic:includeProperty name=\"RadiusTopRight\" defaultValue=\"5\" /><appMagic:includeProperty name=\"RadiusBottomLeft\" defaultValue=\"5\" /><appMagic:includeProperty name=\"RadiusBottomRight\" defaultValue=\"5\" /><appMagic:includeProperty name=\"DisabledBorderColor\" defaultValue=\"ColorFade(Self.BorderColor, 40%)\" /><appMagic:includeProperty name=\"PressedBorderColor\" defaultValue=\"Self.HoverBorderColor\" /><appMagic:includeProperty name=\"HoverBorderColor\" defaultValue=\"Self.Color\" /><appMagic:includeProperty name=\"BorderStyle\" /><appMagic:includeProperty name=\"BorderThickness\" defaultValue=\"1\" /><appMagic:includeProperty name=\"FocusedBorderColor\" defaultValue=\"Self.BorderColor\" isExpr=\"true\" /><appMagic:includeProperty name=\"FocusedBorderThickness\" defaultValue=\"3\" /><appMagic:includeProperty name=\"Color\" defaultValue=\"RGBA(70, 68, 64, 1)\" /><appMagic:includeProperty name=\"DisabledColor\" defaultValue=\"ColorFade(Self.Color, 40%)\" /><appMagic:includeProperty name=\"PressedColor\" defaultValue=\"Self.Color\" /><appMagic:includeProperty name=\"HoverColor\" defaultValue=\"Self.Color\" /><appMagic:includeProperty name=\"Fill\" defaultValue=\"RGBA(255, 255, 255, 1)\" /><appMagic:includeProperty name=\"DisabledFill\" defaultValue=\"Self.Fill\" /><appMagic:includeProperty name=\"PressedFill\" defaultValue=\"Self.Fill\" /><appMagic:includeProperty name=\"HoverFill\" defaultValue=\"Self.Fill\" /><appMagic:includeProperty name=\"Font\" /><appMagic:includeProperty name=\"Size\" defaultValue=\"14\" phoneDefaultValue=\"24\" webDefaultValue=\"12\" /><appMagic:includeProperty name=\"FontWeight\" /><appMagic:includeProperty name=\"Italic\" /><appMagic:includeProperty name=\"Underline\" /><appMagic:includeProperty name=\"Strikethrough\" /><appMagic:includeProperty name=\"Align\" /><appMagic:includeProperty name=\"PaddingTop\" defaultValue=\"5\" /><appMagic:includeProperty name=\"PaddingRight\" defaultValue=\"5\" /><appMagic:includeProperty name=\"PaddingBottom\" defaultValue=\"5\" /><appMagic:includeProperty name=\"PaddingLeft\" defaultValue=\"12\" /><appMagic:includeProperty name=\"X\" /><appMagic:includeProperty name=\"Y\" /><appMagic:includeProperty name=\"Width\" defaultValue=\"320\" phoneDefaultValue=\"560\" webDefaultValue=\"180\" /><appMagic:includeProperty name=\"Height\" defaultValue=\"40\" phoneDefaultValue=\"70\" webDefaultValue=\"32\" /><appMagic:includeProperty name=\"Visible\" /><appMagic:includeProperty name=\"DisplayMode\" /><appMagic:includeProperty name=\"TabIndex\" /><!-- Behavior --><!-- TASK: 85476: Do Behavior properties make sense as input only? --><appMagic:includeProperty name=\"OnChange\" direction=\"in\" /><appMagic:includeProperty name=\"OnSelect\" direction=\"in\" /><!--Hidden properties --><appMagic:includeProperty name=\"maximumHeight\" defaultValue=\"768\" /><appMagic:includeProperty name=\"maximumWidth\" defaultValue=\"1366\" /><appMagic:includeProperty name=\"minimumHeight\" defaultValue=\"30\" /><appMagic:includeProperty name=\"minimumWidth\" defaultValue=\"10\" /></appMagic:includeProperties><!--Property Dependencies --><appMagic:propertyDependencies><appMagic:propertyDependency input=\"Default\" output=\"Text\" /><appMagic:propertyDependency input=\"MaxLength\" output=\"Text\" /><appMagic:propertyDependency input=\"Format\" output=\"Text\" /><appMagic:propertyDependency input=\"Reset\" output=\"Text\" /></appMagic:propertyDependencies><appMagic:insertMetadata isDeviceOptimized=\"true\"><appMagic:category name=\"Popular\" priority=\"30\" /><appMagic:category name=\"Input\" priority=\"20\" /><appMagic:category name=\"ClassicControls\" priority=\"20\" /></appMagic:insertMetadata><!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) --><appMagic:displayMetadata><appMagic:section><appMagic:property name=\"Default\" /><appMagic:property name=\"Format\" /><appMagic:property name=\"HintText\" /><appMagic:property name=\"Font\" displayType=\"FontEnum\" showInFloatie=\"true\" showInCommandBar=\"true\" /><appMagic:property name=\"Size\" labelOverride=\"##FontSize_Property##\" showInFloatie=\"true\" showInCommandBar=\"true\" /><appMagic:property name=\"FontWeight\" displayType=\"EnumIcon\" itemsOrder=\"Bold;Semibold;Normal;Lighter\" showInCommandBar=\"true\" /><appMagic:propertyGroup name=\"Style\"><appMagic:property name=\"Italic\" displayType=\"ToggleButton\" /><appMagic:property name=\"Underline\" displayType=\"ToggleButton\" /><appMagic:property name=\"Strikethrough\" displayType=\"ToggleButton\" /></appMagic:propertyGroup><appMagic:property name=\"Align\" displayType=\"EnumButtons\" itemsOrder=\"Left;Center;Right;Justify\" labelOverride=\"##FontAlign_Property##\" showInFloatie=\"true\" showInCommandBar=\"true\" floatieDisplayType=\"FaceplateIconEnum\" /><appMagic:property name=\"LineHeight\" /><appMagic:property name=\"Clear\" /><appMagic:property name=\"EnableSpellCheck\" /><appMagic:property name=\"MaxLength\" /><appMagic:property name=\"Mode\" /><appMagic:property name=\"DisplayMode\" /></appMagic:section><appMagic:section><appMagic:property name=\"Visible\" /><appMagic:propertyGroup name=\"Position\"><appMagic:property name=\"X\" /><appMagic:property name=\"Y\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Size\"><appMagic:property name=\"Width\" /><appMagic:property name=\"Height\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Padding\"><appMagic:property name=\"PaddingTop\" labelOverride=\"##Padding_Top_Title##\" /><appMagic:property name=\"PaddingBottom\" labelOverride=\"##Padding_Bottom_Title##\" /><appMagic:property name=\"PaddingLeft\" labelOverride=\"##Padding_Left_Title##\" /><appMagic:property name=\"PaddingRight\" labelOverride=\"##Padding_Right_Title##\" /></appMagic:propertyGroup></appMagic:section><appMagic:section><appMagic:propertyGroup name=\"Color\"><appMagic:property name=\"Color\" showInFloatie=\"true\" showInCommandBar=\"true\" /><appMagic:property name=\"Fill\" showInFloatie=\"true\" showInCommandBar=\"true\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Border\"><appMagic:property name=\"BorderStyle\" showInCommandBar=\"true\" /><appMagic:property name=\"BorderThickness\" showInCommandBar=\"true\" /><appMagic:property name=\"BorderColor\" showInCommandBar=\"true\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Radius\"><appMagic:property name=\"RadiusTopLeft\" /><appMagic:property name=\"RadiusTopRight\" /><appMagic:property name=\"RadiusBottomLeft\" /><appMagic:property name=\"RadiusBottomRight\" /></appMagic:propertyGroup></appMagic:section><appMagic:section><appMagic:propertyGroup name=\"DisabledColor\"><appMagic:property name=\"DisabledColor\" /><appMagic:property name=\"DisabledFill\" /><appMagic:property name=\"DisabledBorderColor\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"HoverColor\"><appMagic:property name=\"HoverColor\" /><appMagic:property name=\"HoverFill\" /><appMagic:property name=\"HoverBorderColor\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"PressedColor\"><appMagic:property name=\"PressedColor\" /><appMagic:property name=\"PressedFill\" /><appMagic:property name=\"PressedBorderColor\" /></appMagic:propertyGroup><appMagic:property name=\"Tooltip\" /><appMagic:property name=\"TabIndex\" /></appMagic:section></appMagic:displayMetadata><appMagic:conversion from=\"2.0.0\" to=\"2.0.1\"><!-- Removed defaultValue for BorderColor. BorderColor default now defined in theme. --></appMagic:conversion><appMagic:conversion from=\"2.0.1\" to=\"2.1.0\"><!-- Added a new property VirtualKeyboardMode to hint what type of virtual keyboard for the OS to show--><appMagic:conversionAction type=\"add\" name=\"VirtualKeyboardMode\" /></appMagic:conversion><appMagic:conversion from=\"2.1.0\" to=\"2.2.0\"><!-- Added EnableSpellCheck property. --><appMagic:conversionAction type=\"add\" name=\"EnableSpellCheck\" /></appMagic:conversion><appMagic:conversion from=\"2.2.0\" to=\"2.2.1\"><!-- KO template change to use HTML maxlength attribute --></appMagic:conversion><appMagic:conversion from=\"2.2.1\" to=\"2.3.0\"><appMagic:conversionAction type=\"add\" name=\"ContentLanguage\" /></appMagic:conversion><appMagic:conversion from=\"2.3.0\" to=\"2.3.1\"><!-- KO template changes for accessibility fixes --></appMagic:conversion><appMagic:conversion from=\"2.3.1\" to=\"2.3.2\"><!-- KO template changes for accessibility fixes --></appMagic:conversion></widget>"}, {"Name": "checkbox", "Version": "2.1.0", "Template": "<widget xmlns=\"http://openajax.org/metadata\" spec=\"1.0\" id=\"http://microsoft.com/appmagic/checkbox\" name=\"checkbox\" jsClass=\"AppMagic.Controls.Checkbox\" version=\"2.1.0\" styleable=\"true\" runtimeCost=\"1\" xmlns:appMagic=\"http://schemas.microsoft.com/appMagic\"><author name=\"Microsoft AppMagic\" /><license type=\"text/html\"><![CDATA[<p>TODO:  Need license text here.</p>]]></license><description><![CDATA[CHECKBOX\r\n      Control description here.]]></description><requires><require type=\"css\" src=\"css/checkbox.css\" /><require type=\"javascript\" src=\"js/checkbox.js\" /></requires><appMagic:capabilities contextualViewsEnabled=\"true\" autoBorders=\"true\" autoFocusedBorders=\"true\" autoFill=\"true\" autoPointerViewState=\"true\" autoDisabledViewState=\"true\" isVersionFlexible=\"true\" /><appMagic:accessibilityChecks controlIsInteractive=\"true\" /><content><![CDATA[\r\n  <div\r\n    class=\"appmagic-checkbox-control\" touch-action=\"pan-x pan-y\"\r\n    data-bind=\"{\r\n      css: {\r\n        readonly: isReadonly()\r\n      },\r\n      style: {\r\n        paddingTop: properties.PaddingTop,\r\n        paddingRight: properties.PaddingRight,\r\n        paddingBottom: properties.PaddingBottom,\r\n        paddingLeft: properties.PaddingLeft,\r\n      },\r\n      shortcut: {\r\n        provider: shortcutProvider\r\n      },\r\n      attr: { title:properties.Tooltip }}\" >\r\n      <!-- click class is defined for label/checkbox control to enable the label to select/deselect the checkbox control -->\r\n      <label class=\"checkbox-label click\"\r\n        data-control-part=\"tappable\"\r\n        data-bind =\"{\r\n          event: {click: handleClick},\r\n          css: { top: properties.VerticalAlign() === 'top', middle: properties.VerticalAlign() === 'middle', bottom: properties.VerticalAlign() === 'bottom' }\r\n        }\">\r\n        <input\r\n          appmagic-control=\"__WID__\"\r\n          data-control-part=\"input\"\r\n          type=\"checkbox\"\r\n          class=\"appmagic-checkbox click mousetrap\"\r\n          tabIndex=\"-1\"\r\n          data-bind=\"{\r\n            disable: isDisabled(),\r\n            attr: {\r\n              checked: properties.Value()\r\n            },\r\n            event: {\r\n              change: handleChange\r\n            }\r\n          }\"/>\r\n        <div class=\"appmagic-checkbox-placeholder\" data-bind=\"style: { fontSize: properties.CheckboxSize}\">\r\n            <svg width=\"0.8em\" height=\"0.8em\" viewBox=\"0 0 36 36\" enable-background=\"new 0 0 36 36\" xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\" aria-hidden=\"true\">\r\n               <g data-bind=\"\r\n                attr: {\r\n                  opacity: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled ? '0.4' : '1'\r\n                }\">\r\n                <rect data-bind=\"attr: { fill: properties.CheckboxBackgroundFill, stroke: properties.CheckboxBorderColor }\" height=\"28\" width=\"28\" y=\"4\" x=\"4\" stroke-width=\"2\" />\r\n                <polygon class=\"checkmark\" data-bind=\"attr: { fill: properties.CheckmarkFill, opacity: (properties.Value()) ? '1' : '0' }\" points=\"16.1 25.5 10 19.4 12.1 17.3 15.8 21 23.6 10.5 26 12.3\" />\r\n               </g>\r\n            </svg>\r\n        </div>\r\n        <div\r\n          style=\"padding-left: 10px;\"\r\n          tabIndex=\"-1\"\r\n          class=\"appmagic-checkbox-label\"\r\n          spellcheck=\"false\"\r\n          data-control-part=\"text\"\r\n          data-bind=\"{\r\n            inlineEditText: properties.Text,\r\n            style: {\r\n              fontFamily: properties.Font,\r\n              fontSize: properties.Size,\r\n              color: autoProperties.Color,\r\n              fontWeight: properties.FontWeight,\r\n              fontStyle: properties.Italic\r\n            },\r\n            css: {\r\n              underline: properties.Underline,\r\n              strikethrough: properties.Strikethrough\r\n            }\r\n          }\">\r\n        </div>\r\n     </label>\r\n  </div>\r\n  ]]></content><properties><property name=\"Default\" localizedName=\"##checkbox_Default##\" datatype=\"Boolean\" editable=\"true\" defaultValue=\"false\" direction=\"in\"><title>Checkbox default value</title><appMagic:category>data</appMagic:category><appMagic:displayName>##checkbox_Default_DisplayName##</appMagic:displayName><appMagic:tooltip>##checkbox_Default_Tooltip##</appMagic:tooltip></property><property name=\"Value\" localizedName=\"##checkbox_Value##\" datatype=\"Boolean\" direction=\"out\" isPrimaryOutputProperty=\"true\" supportsAutomation=\"true\"><title>Checkbox value</title><appMagic:category>data</appMagic:category></property><!-- TASK: 85476: Do Behavior properties make sense as input only? --><property name=\"OnCheck\" localizedName=\"##checkbox_OnCheck##\" datatype=\"Boolean\" defaultValue=\"false\" direction=\"in\" isPrimaryBehaviorProperty=\"true\"><title>Checkbox checked behavior</title><appMagic:category>behavior</appMagic:category><appMagic:displayName>##checkbox_OnCheck_DisplayName##</appMagic:displayName><appMagic:tooltip>##checkbox_OnCheck_Tooltip##</appMagic:tooltip></property><!-- TASK: 85476: Do Behavior properties make sense as input only? --><property name=\"OnUncheck\" localizedName=\"##checkbox_OnUncheck##\" datatype=\"Boolean\" defaultValue=\"false\" direction=\"in\"><title>Checkbox unchecked behavior</title><appMagic:category>behavior</appMagic:category><appMagic:displayName>##checkbox_OnUncheck_DisplayName##</appMagic:displayName><appMagic:tooltip>##checkbox_OnUncheck_Tooltip##</appMagic:tooltip></property><property name=\"Reset\" localizedName=\"##commonProperties_Reset##\" datatype=\"Boolean\" defaultValue=\"false\" direction=\"in\"><title>Reset</title><appMagic:category>data</appMagic:category><appMagic:displayName>##commonProperties_Reset_DisplayName##</appMagic:displayName><appMagic:tooltip>##commonProperties_Reset_Tooltip##</appMagic:tooltip></property><property name=\"CheckmarkFill\" localizedName=\"##checkbox_CheckmarkFill##\" datatype=\"Color\" isExpr=\"true\" defaultValue=\"RGBA(0, 0, 0, 1)\" converter=\"argbConverter\"><title>Checkbox Checkmark Fill</title><appMagic:category>design</appMagic:category><appMagic:helperUI>color</appMagic:helperUI><appMagic:displayName>##checkbox_CheckmarkFill_DisplayName##</appMagic:displayName><appMagic:tooltip>##checkbox_CheckmarkFill_Tooltip##</appMagic:tooltip></property><property name=\"CheckboxBackgroundFill\" localizedName=\"##checkbox_BackgroundFill##\" datatype=\"Color\" isExpr=\"true\" defaultValue=\"RGBA(255, 255, 255, 1)\" converter=\"argbConverter\"><title>Checkbox Background Fill</title><appMagic:category>design</appMagic:category><appMagic:helperUI>color</appMagic:helperUI><appMagic:displayName>##checkbox_BackgroundFill_DisplayName##</appMagic:displayName><appMagic:tooltip>##checkbox_BackgroundFill_Tooltip##</appMagic:tooltip></property><property name=\"CheckboxBorderColor\" localizedName=\"##checkbox_BorderColor##\" datatype=\"Color\" isExpr=\"true\" defaultValue=\"RGBA(0, 0, 0, 1)\" converter=\"argbConverter\"><title>Checkbox Border Color</title><appMagic:category>design</appMagic:category><appMagic:helperUI>color</appMagic:helperUI><appMagic:displayName>##checkbox_BorderColor_DisplayName##</appMagic:displayName><appMagic:tooltip>##checkbox_BorderColor_Tooltip##</appMagic:tooltip></property><property name=\"CheckboxSize\" localizedName=\"##checkbox_CheckboxSize##\" datatype=\"Number\" defaultValue=\"40\" phoneDefaultValue=\"70\" converter=\"pxConverter\"><title>Checkbox Size</title><appMagic:category>design</appMagic:category><appMagic:helperUI>fontSize</appMagic:helperUI><appMagic:displayName>##checkbox_CheckboxSize_DisplayName##</appMagic:displayName><appMagic:tooltip>##checkbox_CheckboxSize_Tooltip##</appMagic:tooltip></property></properties><appMagic:includeProperties><!-- Data --><appMagic:includeProperty name=\"Text\" defaultValue=\"##Checkbox_DefaultValue_Text##\" isExpr=\"true\" isPrimaryInputProperty=\"true\" /><appMagic:includeProperty name=\"Tooltip\" /><appMagic:includeProperty name=\"ContentLanguage\" /><!-- Design --><appMagic:includeProperty name=\"BorderColor\" /><appMagic:includeProperty name=\"DisabledBorderColor\" defaultValue=\"RGBA(56, 56, 56, 1)\" /><appMagic:includeProperty name=\"BorderStyle\" /><appMagic:includeProperty name=\"BorderThickness\" /><appMagic:includeProperty name=\"FocusedBorderColor\" defaultValue=\"Self.BorderColor\" isExpr=\"true\" /><appMagic:includeProperty name=\"FocusedBorderThickness\" /><appMagic:includeProperty name=\"PressedBorderColor\" defaultValue=\"ColorFade(Self.BorderColor, -30%)\" /><appMagic:includeProperty name=\"HoverBorderColor\" defaultValue=\"ColorFade(Self.BorderColor, 30%)\" /><appMagic:includeProperty name=\"Color\" defaultValue=\"RGBA(70, 68, 64, 1)\" /><appMagic:includeProperty name=\"DisabledColor\" defaultValue=\"RGBA(186, 186, 186, 1)\" /><appMagic:includeProperty name=\"Fill\" /><appMagic:includeProperty name=\"DisabledFill\" defaultValue=\"RGBA(0, 0, 0, 0)\" /><appMagic:includeProperty name=\"Font\" /><appMagic:includeProperty name=\"Size\" defaultValue=\"14\" phoneDefaultValue=\"24\" /><appMagic:includeProperty name=\"PressedColor\" defaultValue=\"RGBA(70, 68, 64, 1)\" /><appMagic:includeProperty name=\"HoverColor\" defaultValue=\"RGBA(70, 68, 64, 1)\" /><appMagic:includeProperty name=\"PressedFill\" defaultValue=\"ColorFade(Self.Fill, -30%)\" /><appMagic:includeProperty name=\"HoverFill\" defaultValue=\"ColorFade(Self.Fill, 30%)\" /><appMagic:includeProperty name=\"FontWeight\" defaultValue=\"%FontWeight.RESERVED%.Normal\" /><appMagic:includeProperty name=\"Italic\" /><appMagic:includeProperty name=\"Underline\" /><appMagic:includeProperty name=\"Strikethrough\" /><appMagic:includeProperty name=\"PaddingTop\" /><appMagic:includeProperty name=\"PaddingRight\" /><appMagic:includeProperty name=\"PaddingBottom\" /><appMagic:includeProperty name=\"PaddingLeft\" /><appMagic:includeProperty name=\"VerticalAlign\" defaultValue=\"%VerticalAlign.RESERVED%.Middle\" /><appMagic:includeProperty name=\"X\" /><appMagic:includeProperty name=\"Y\" /><appMagic:includeProperty name=\"Width\" defaultValue=\"150\" phoneDefaultValue=\"280\" /><appMagic:includeProperty name=\"Height\" defaultValue=\"50\" phoneDefaultValue=\"85\" /><appMagic:includeProperty name=\"Visible\" /><appMagic:includeProperty name=\"DisplayMode\" /><appMagic:includeProperty name=\"TabIndex\" /><!-- Behavior --><!-- TASK: 85476: Do Behavior properties make sense as input only? --><appMagic:includeProperty name=\"OnSelect\" direction=\"in\" /><!-- Hidden properties --><appMagic:includeProperty name=\"maximumHeight\" defaultValue=\"768\" /><appMagic:includeProperty name=\"maximumWidth\" defaultValue=\"1366\" /><appMagic:includeProperty name=\"minimumHeight\" defaultValue=\"35\" /><appMagic:includeProperty name=\"minimumWidth\" defaultValue=\"100\" /></appMagic:includeProperties><!--Property Dependencies --><appMagic:propertyDependencies><appMagic:propertyDependency input=\"Default\" output=\"Value\" /><appMagic:propertyDependency input=\"Reset\" output=\"Value\" /></appMagic:propertyDependencies><appMagic:insertMetadata isDeviceOptimized=\"true\"><appMagic:category name=\"Input\" priority=\"80\" /><appMagic:category name=\"ClassicControls\" priority=\"80\" /></appMagic:insertMetadata><!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) --><appMagic:displayMetadata><appMagic:section><appMagic:property name=\"Text\" /><appMagic:property name=\"Default\" /><appMagic:property name=\"DisplayMode\" /></appMagic:section><appMagic:section><appMagic:property name=\"Visible\" /><appMagic:propertyGroup name=\"Position\"><appMagic:property name=\"X\" /><appMagic:property name=\"Y\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Size\"><appMagic:property name=\"Width\" /><appMagic:property name=\"Height\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Padding\"><appMagic:property name=\"PaddingTop\" labelOverride=\"##Padding_Top_Title##\" /><appMagic:property name=\"PaddingBottom\" labelOverride=\"##Padding_Bottom_Title##\" /><appMagic:property name=\"PaddingLeft\" labelOverride=\"##Padding_Left_Title##\" /><appMagic:property name=\"PaddingRight\" labelOverride=\"##Padding_Right_Title##\" /></appMagic:propertyGroup></appMagic:section><appMagic:section><appMagic:propertyGroup name=\"Color\"><appMagic:property name=\"Color\" showInFloatie=\"true\" /><appMagic:property name=\"Fill\" showInFloatie=\"true\" /></appMagic:propertyGroup><appMagic:property name=\"Font\" displayType=\"FontEnum\" showInFloatie=\"true\" /><appMagic:property name=\"Size\" labelOverride=\"##FontSize_Property##\" showInFloatie=\"true\" /><appMagic:property name=\"FontWeight\" displayType=\"EnumIcon\" itemsOrder=\"Bold;Semibold;Normal;Lighter\" /><appMagic:propertyGroup name=\"Style\"><appMagic:property name=\"Italic\" displayType=\"ToggleButton\" /><appMagic:property name=\"Underline\" displayType=\"ToggleButton\" /><appMagic:property name=\"Strikethrough\" displayType=\"ToggleButton\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Border\"><appMagic:property name=\"BorderStyle\" /><appMagic:property name=\"BorderThickness\" /><appMagic:property name=\"BorderColor\" /></appMagic:propertyGroup></appMagic:section><appMagic:section><appMagic:property name=\"CheckboxSize\" /><appMagic:propertyGroup name=\"CheckboxColor\"><appMagic:property name=\"CheckboxBackgroundFill\" /><appMagic:property name=\"CheckboxBorderColor\" /></appMagic:propertyGroup><appMagic:property name=\"CheckmarkFill\" /><appMagic:property name=\"VerticalAlign\" displayType=\"EnumIcon\" itemsOrder=\"Top;Middle;Bottom\" /></appMagic:section><appMagic:section><appMagic:propertyGroup name=\"DisabledColor\"><appMagic:property name=\"DisabledColor\" /><appMagic:property name=\"DisabledFill\" /><appMagic:property name=\"DisabledBorderColor\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"PressedColor\"><appMagic:property name=\"PressedColor\" /><appMagic:property name=\"PressedFill\" /><appMagic:property name=\"PressedBorderColor\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"HoverColor\"><appMagic:property name=\"HoverColor\" /><appMagic:property name=\"HoverFill\" /><appMagic:property name=\"HoverBorderColor\" /></appMagic:propertyGroup><appMagic:property name=\"Tooltip\" /><appMagic:property name=\"TabIndex\" /></appMagic:section></appMagic:displayMetadata><appMagic:conversion from=\"2.0.0\" to=\"2.0.1\"><!-- Removed defaultValue for BorderColor. BorderColor default now defined in theme. --></appMagic:conversion><appMagic:conversion from=\"2.0.1\" to=\"2.1.0\"><appMagic:conversionAction type=\"add\" name=\"ContentLanguage\" /></appMagic:conversion></widget>"}, {"Name": "button", "Version": "2.2.0", "Template": "<widget xmlns=\"http://openajax.org/metadata\" spec=\"1.0\" id=\"http://microsoft.com/appmagic/button\" name=\"button\" jsClass=\"AppMagic.Controls.Button\" version=\"2.2.0\" styleable=\"true\" runtimeCost=\"1\" xmlns:appMagic=\"http://schemas.microsoft.com/appMagic\"><author name=\"Microsoft AppMagic\" /><license type=\"text/html\"><![CDATA[<p>TODO:  Need license text here.</p>]]></license><description><![CDATA[BUTTON\r\n      Control description here.]]></description><requires><require type=\"css\" src=\"/ctrllib/common/css/button.css\" /><require type=\"javascript\" src=\"/ctrllib/common/js/button.js\" /></requires><appMagic:capabilities contextualViewsEnabled=\"true\" autoBorders=\"true\" autoFocusedBorders=\"true\" autoFill=\"true\" autoPointerViewState=\"true\" autoDisabledViewState=\"true\" autoBorderRadius=\"true\" isVersionFlexible=\"true\" supportsSetFocus=\"true\" /><appMagic:accessibilityChecks controlIsInteractive=\"true\" /><content><![CDATA[\r\n<div class=\"appmagic-button-wrapper\">\r\n  <div\r\n    class=\"a11y appmagic-button-busy\"\r\n    tabindex=\"-1\"\r\n    data-bind=\"\r\n      style: {\r\n        display: viewState.isAutoDisabled() ? null : 'none'\r\n      },\r\n      text: AppMagic.Strings.ButtonBusyMessage\r\n    \"\r\n  ></div>\r\n\r\n  <button\r\n    class=\"appmagic-button-container no-focus-outline\"\r\n    data-control-part=\"button\"\r\n    data-bind=\"\r\n      event: {\r\n        click: handleClick,\r\n        pointerdown: handleMouseDown,\r\n        pointerup: handleMouseUp,\r\n        pointerout: handleMouseOut\r\n      },\r\n      attr: {\r\n        title: properties.Tooltip,\r\n        disabled: viewState.displayMode() !== AppMagic.Constants.DisplayMode.Edit\r\n      }\r\n    \"\r\n  >\r\n    <div\r\n      class=\"appmagic-button\"\r\n      touch-action=\"pan-x pan-y\"\r\n      data-bind=\"\r\n        style: {\r\n            fontFamily: properties.Font,\r\n            fontSize: properties.Size,\r\n            color: autoProperties.Color,\r\n            fontWeight: properties.FontWeight,\r\n            fontStyle: properties.Italic,\r\n            textAlign: properties.Align,\r\n            paddingTop: properties.PaddingTop,\r\n            paddingRight: properties.PaddingRight,\r\n            paddingBottom: properties.PaddingBottom,\r\n            paddingLeft: properties.PaddingLeft,\r\n        },\r\n        css: {\r\n            top: properties.VerticalAlign() === 'top',\r\n            middle: properties.VerticalAlign() === 'middle',\r\n            bottom: properties.VerticalAlign() === 'bottom',\r\n            left: properties.Align() === 'left',\r\n            right: properties.Align() === 'right',\r\n            center: properties.Align() === 'center',\r\n            justify: properties.Align() === 'justify',\r\n            disabled: viewState.displayMode() === AppMagic.Constants.DisplayMode.Disabled,\r\n            underline: properties.Underline,\r\n            strikethrough: properties.Strikethrough,\r\n            readonly: viewState.displayMode() === AppMagic.Constants.DisplayMode.View\r\n        }\r\n      \"\r\n    >\r\n      <div\r\n        class=\"appmagic-button-label no-focus-outline\"\r\n        data-control-part=\"text\"\r\n        spellcheck=\"false\"\r\n        data-bind=\"inlineEditText: properties.Text\"\r\n      >\r\n      </div>\r\n    </div>\r\n  </button>\r\n<div>\r\n]]></content><properties><property name=\"Pressed\" localizedName=\"##button_Pressed##\" datatype=\"Boolean\" direction=\"out\" defaultValue=\"false\" isPrimaryOutputProperty=\"true\"><title>The pressed state of the button.</title><appMagic:category>data</appMagic:category><appMagic:helperUI>boolean</appMagic:helperUI></property></properties><appMagic:includeProperties><!-- Behavior --><appMagic:includeProperty name=\"OnSelect\" direction=\"in\" isPrimaryInputProperty=\"true\" /><appMagic:includeProperty name=\"AutoDisableOnSelect\" /><!-- Data --><appMagic:includeProperty name=\"Text\" defaultValue=\"##Button_DefaultValue_Text##\" isExpr=\"true\" /><appMagic:includeProperty name=\"Tooltip\" /><appMagic:includeProperty name=\"ContentLanguage\" /><!-- Design --><appMagic:includeProperty name=\"BorderColor\" defaultValue=\"ColorFade(Self.Fill, -15%)\" /><!-- TASK: 4548082: Add the Color and Fill in default theme json --><appMagic:includeProperty name=\"RadiusTopLeft\" defaultValue=\"10\" /><appMagic:includeProperty name=\"RadiusTopRight\" defaultValue=\"10\" /><appMagic:includeProperty name=\"RadiusBottomLeft\" defaultValue=\"10\" /><appMagic:includeProperty name=\"RadiusBottomRight\" defaultValue=\"10\" /><appMagic:includeProperty name=\"DisabledBorderColor\" defaultValue=\"ColorFade(Self.BorderColor, 70%)\" /><appMagic:includeProperty name=\"PressedBorderColor\" defaultValue=\"Self.Fill\" /><appMagic:includeProperty name=\"HoverBorderColor\" defaultValue=\"ColorFade(Self.BorderColor, 20%)\" /><appMagic:includeProperty name=\"BorderStyle\" /><appMagic:includeProperty name=\"BorderThickness\" defaultValue=\"2\" /><appMagic:includeProperty name=\"FocusedBorderColor\" defaultValue=\"Self.BorderColor\" isExpr=\"true\" /><appMagic:includeProperty name=\"FocusedBorderThickness\" defaultValue=\"4\" /><appMagic:includeProperty name=\"Color\" defaultValue=\"RGBA(255, 255, 255, 1)\" /><appMagic:includeProperty name=\"DisabledColor\" defaultValue=\"ColorFade(Self.Fill, 90%)\" /><appMagic:includeProperty name=\"PressedColor\" defaultValue=\"Self.Fill\" /><appMagic:includeProperty name=\"HoverColor\" defaultValue=\"Self.Color\" /><appMagic:includeProperty name=\"DisplayMode\" /><appMagic:includeProperty name=\"Fill\" defaultValue=\"RGBA(35, 31, 32, 1)\" /><appMagic:includeProperty name=\"DisabledFill\" defaultValue=\"ColorFade(Self.Fill, 70%)\" /><appMagic:includeProperty name=\"PressedFill\" defaultValue=\"Self.Color\" /><appMagic:includeProperty name=\"HoverFill\" defaultValue=\"ColorFade(Self.Fill, 20%)\" /><appMagic:includeProperty name=\"Font\" /><appMagic:includeProperty name=\"Size\" defaultValue=\"13\" phoneDefaultValue=\"23\" /><appMagic:includeProperty name=\"FontWeight\" defaultValue=\"%FontWeight.RESERVED%.Normal\" /><appMagic:includeProperty name=\"Italic\" /><appMagic:includeProperty name=\"Underline\" /><appMagic:includeProperty name=\"Strikethrough\" /><appMagic:includeProperty name=\"Align\" defaultValue=\"%Align.RESERVED%.Center\" /><appMagic:includeProperty name=\"PaddingTop\" defaultValue=\"5\" /><appMagic:includeProperty name=\"PaddingRight\" defaultValue=\"5\" /><appMagic:includeProperty name=\"PaddingBottom\" defaultValue=\"5\" /><appMagic:includeProperty name=\"PaddingLeft\" defaultValue=\"5\" /><appMagic:includeProperty name=\"Visible\" /><appMagic:includeProperty name=\"VerticalAlign\" defaultValue=\"%VerticalAlign.RESERVED%.Middle\" /><appMagic:includeProperty name=\"X\" /><appMagic:includeProperty name=\"Y\" /><appMagic:includeProperty name=\"Width\" defaultValue=\"160\" phoneDefaultValue=\"280\" webDefaultValue=\"85\" /><appMagic:includeProperty name=\"Height\" defaultValue=\"40\" phoneDefaultValue=\"70\" webDefaultValue=\"32\" /><appMagic:includeProperty name=\"TabIndex\" /><!-- Hidden properties --><appMagic:includeProperty name=\"maximumHeight\" defaultValue=\"768\" /><appMagic:includeProperty name=\"maximumWidth\" defaultValue=\"1366\" /><appMagic:includeProperty name=\"minimumHeight\" defaultValue=\"5\" /><appMagic:includeProperty name=\"minimumWidth\" defaultValue=\"5\" /></appMagic:includeProperties><appMagic:insertMetadata isDeviceOptimized=\"true\"><appMagic:category name=\"Popular\" priority=\"80\" /><appMagic:category name=\"Input\" priority=\"10\" /><appMagic:category name=\"ClassicControls\" priority=\"10\" /></appMagic:insertMetadata><!-- Display metadata providing property visibility, order, sections, and grouping in UI (e.g. properties panel) --><appMagic:displayMetadata><appMagic:section><appMagic:property name=\"Text\" /><appMagic:property name=\"DisplayMode\" /></appMagic:section><appMagic:section><appMagic:property name=\"Visible\" /><appMagic:propertyGroup name=\"Position\"><appMagic:property name=\"X\" /><appMagic:property name=\"Y\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Size\"><appMagic:property name=\"Width\" /><appMagic:property name=\"Height\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Padding\"><appMagic:property name=\"PaddingTop\" labelOverride=\"##Padding_Top_Title##\" /><appMagic:property name=\"PaddingBottom\" labelOverride=\"##Padding_Bottom_Title##\" /><appMagic:property name=\"PaddingLeft\" labelOverride=\"##Padding_Left_Title##\" /><appMagic:property name=\"PaddingRight\" labelOverride=\"##Padding_Right_Title##\" /></appMagic:propertyGroup></appMagic:section><appMagic:section><appMagic:propertyGroup name=\"Color\"><appMagic:property name=\"Color\" showInFloatie=\"true\" showInCommandBar=\"true\" /><appMagic:property name=\"Fill\" showInFloatie=\"true\" showInCommandBar=\"true\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Border\"><appMagic:property name=\"BorderStyle\" /><appMagic:property name=\"BorderThickness\" /><appMagic:property name=\"BorderColor\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"Radius\"><appMagic:property name=\"RadiusTopLeft\" /><appMagic:property name=\"RadiusTopRight\" /><appMagic:property name=\"RadiusBottomLeft\" /><appMagic:property name=\"RadiusBottomRight\" /></appMagic:propertyGroup><appMagic:property name=\"Font\" displayType=\"FontEnum\" showInFloatie=\"true\" showInCommandBar=\"true\" /><appMagic:property name=\"Size\" labelOverride=\"##FontSize_Property##\" showInFloatie=\"true\" showInCommandBar=\"true\" /><appMagic:property name=\"FontWeight\" displayType=\"EnumIcon\" itemsOrder=\"Bold;Semibold;Normal;Lighter\" showInCommandBar=\"true\" /><appMagic:propertyGroup name=\"Style\"><appMagic:property name=\"Italic\" displayType=\"ToggleButton\" /><appMagic:property name=\"Underline\" displayType=\"ToggleButton\" /><appMagic:property name=\"Strikethrough\" displayType=\"ToggleButton\" /></appMagic:propertyGroup><appMagic:property name=\"Align\" displayType=\"EnumButtons\" itemsOrder=\"Left;Center;Right;Justify\" labelOverride=\"##FontAlign_Property##\" showInFloatie=\"true\" showInCommandBar=\"true\" floatieDisplayType=\"FaceplateIconEnum\" /><appMagic:property name=\"VerticalAlign\" displayType=\"EnumIcon\" itemsOrder=\"Top;Middle;Bottom\" /></appMagic:section><appMagic:section><appMagic:property name=\"AutoDisableOnSelect\" /><appMagic:propertyGroup name=\"DisabledColor\"><appMagic:property name=\"DisabledColor\" /><appMagic:property name=\"DisabledFill\" /><appMagic:property name=\"DisabledBorderColor\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"PressedColor\"><appMagic:property name=\"PressedColor\" /><appMagic:property name=\"PressedFill\" /><appMagic:property name=\"PressedBorderColor\" /></appMagic:propertyGroup><appMagic:propertyGroup name=\"HoverColor\"><appMagic:property name=\"HoverColor\" /><appMagic:property name=\"HoverFill\" /><appMagic:property name=\"HoverBorderColor\" /></appMagic:propertyGroup><appMagic:property name=\"Tooltip\" /><appMagic:property name=\"TabIndex\" /></appMagic:section></appMagic:displayMetadata><appMagic:conversion from=\"2.0.0\" to=\"2.0.1\"><!-- Accessibility fixes for KO template --></appMagic:conversion><appMagic:conversion from=\"2.0.1\" to=\"2.1.0\"><appMagic:conversionAction type=\"add\" name=\"ContentLanguage\" /></appMagic:conversion><appMagic:conversion from=\"2.1.0\" to=\"2.2.0\"><!-- Adding showInCommandBar flag --></appMagic:conversion></widget>"}]}