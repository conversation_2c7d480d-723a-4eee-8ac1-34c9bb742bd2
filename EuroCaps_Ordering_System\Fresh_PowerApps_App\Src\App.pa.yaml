App As appinfo:
    BackEnabled: =false
    OnStart: |-
        =// Initialize global variables
        Set(varCurrentUser, "Demo User");
        Set(varAppName, "EuroCaps Order Management Pro");
        
        // Initialize collections with sample data
        ClearCollect(colCustomers,
            {CustomerID: 1, CustomerName: "Bean Lovers", Contact<PERSON>erson: "<PERSON>", Email: "<EMAIL>", Phone: "+31 30 234 5678", Address: "Bonenstraat 25, Utrecht"},
            {CustomerID: 2, CustomerName: "Café Express", ContactPerson: "<PERSON>", Email: "<EMAIL>", Phone: "+31 10 345 6789", Address: "Espressolaan 5, Rotterdam"},
            {CustomerID: 3, CustomerName: "Coffee World", Contact<PERSON>erson: "<PERSON>", Email: "<EMAIL>", Phone: "+31 20 123 4567", Address: "Koffieweg 10, Amsterdam"},
            {CustomerID: 4, CustomerName: "Morning Brew", Contact<PERSON>erson: "<PERSON>", Email: "<EMAIL>", Phone: "+31 40 456 7890", Address: "Ochtendweg 15, Eindhoven"},
            {CustomerID: 5, CustomerName: "The Daily Cup", Contact<PERSON>erson: "<PERSON>", Email: "<EMAIL>", Phone: "+31 70 567 8901", Address: "Dagelijksestraat 30, Den Haag"}
        );
        
        ClearCollect(colProducts,
            {ProductID: 1, ProductName: "Espresso Classic", ProductType: "Espresso", PackageSize: 10, Description: "Traditional Italian-style espresso", Price: 4.99},
            {ProductID: 2, ProductName: "Lungo Intense", ProductType: "Lungo", PackageSize: 20, Description: "Rich and intense lungo capsules", Price: 8.99},
            {ProductID: 3, ProductName: "Ristretto Strong", ProductType: "Ristretto", PackageSize: 10, Description: "Extra strong ristretto capsules", Price: 5.49},
            {ProductID: 4, ProductName: "Vanilla Flavored", ProductType: "Flavored", PackageSize: 20, Description: "Smooth vanilla flavored coffee", Price: 9.99},
            {ProductID: 5, ProductName: "Caramel Delight", ProductType: "Flavored", PackageSize: 10, Description: "Sweet caramel flavored coffee", Price: 5.99}
        );
        
        ClearCollect(colOrders,
            {OrderID: 1, OrderNumber: "ORD-1089", CustomerID: 3, OrderDate: Date(2025,5,15), DeliveryDate: Date(2025,5,22), Status: "New", Notes: "Please deliver before noon"},
            {OrderID: 2, OrderNumber: "ORD-1088", CustomerID: 1, OrderDate: Date(2025,5,14), DeliveryDate: Date(2025,5,21), Status: "New", Notes: "Standard delivery"},
            {OrderID: 3, OrderNumber: "ORD-1087", CustomerID: 2, OrderDate: Date(2025,5,14), DeliveryDate: Date(2025,5,21), Status: "Processing", Notes: "Call before delivery"},
            {OrderID: 4, OrderNumber: "ORD-1086", CustomerID: 4, OrderDate: Date(2025,5,13), DeliveryDate: Date(2025,5,20), Status: "Shipped", Notes: "Standard delivery"},
            {OrderID: 5, OrderNumber: "ORD-1085", CustomerID: 5, OrderDate: Date(2025,5,12), DeliveryDate: Date(2025,5,19), Status: "Delivered", Notes: "Completed successfully"}
        );
        
        // Initialize empty order items collection
        ClearCollect(colOrderItems, {OrderItemID: 0, OrderID: 0, ProductID: 0, Quantity: 0});
        Remove(colOrderItems, First(colOrderItems));
    StartScreen: =Login_Screen
