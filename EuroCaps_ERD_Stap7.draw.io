<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="26.2.12">
  <diagram name="Page-1" id="EuroCaps_Logical_ERD">
    <mxGraphModel dx="1388" dy="544" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="40" value="EuroCaps - Logisch ERD (&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Grondstof)&lt;/span&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="300" y="40" width="560" height="30" as="geometry" />
        </mxCell>
        <mxCell id="2" value="Klant" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="120" y="120" width="200" height="150" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Klant_ID (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=4" parent="2" vertex="1">
          <mxGeometry y="30" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4" value="Naam" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="2" vertex="1">
          <mxGeometry y="60" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="5" value="E-mailadres" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="2" vertex="1">
          <mxGeometry y="90" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="6" value="Telefoonnummer" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="2" vertex="1">
          <mxGeometry y="120" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7" value="Bestelling" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="470" y="120" width="200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="8" value="Bestelling_ID (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=4" parent="7" vertex="1">
          <mxGeometry y="30" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="10" value="Datum" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="7" vertex="1">
          <mxGeometry y="60" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="11" value="Totaalbedrag" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="7" vertex="1">
          <mxGeometry y="90" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="12" value="Product" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="780" y="120" width="200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="13" value="Product_ID (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=4" parent="12" vertex="1">
          <mxGeometry y="30" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="14" value="Naam" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="12" vertex="1">
          <mxGeometry y="60" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="15" value="Type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="12" vertex="1">
          <mxGeometry y="90" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="20" value="Leverancier" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" vertex="1">
          <mxGeometry x="700" y="390" width="200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="21" value="Leverancier_ID (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=4" parent="20" vertex="1">
          <mxGeometry y="30" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="22" value="Naam" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="20" vertex="1">
          <mxGeometry y="60" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="23" value="Contactinformatie" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="20" vertex="1">
          <mxGeometry y="90" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="24" value="Grondstof" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="290" y="405" width="200" height="90" as="geometry" />
        </mxCell>
        <mxCell id="25" value="Grondstof_ID (PK)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;fontStyle=4" parent="24" vertex="1">
          <mxGeometry y="30" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="27" value="Type&amp;nbsp;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="24" vertex="1">
          <mxGeometry y="60" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="30" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERoneToMany;startArrow=ERmandOne;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="3" target="10" edge="1">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="330" y="150" as="sourcePoint" />
            <mxPoint x="440" y="195" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="hIxRvvnBD-W2NYW5ZwNi-48" value="Heeft" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="30">
          <mxGeometry x="-0.2184" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="47" value="1" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="220" y="440" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="hIxRvvnBD-W2NYW5ZwNi-49" value="" style="edgeStyle=entityRelationEdgeStyle;fontSize=12;html=1;endArrow=ERoneToMany;startArrow=ERmandOne;rounded=0;exitX=1.012;exitY=0.051;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitPerimeter=0;" edge="1" parent="1" source="10" target="14">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="680" y="172.5" as="sourcePoint" />
            <mxPoint x="740" y="327.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="hIxRvvnBD-W2NYW5ZwNi-50" value="Levert" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="hIxRvvnBD-W2NYW5ZwNi-49">
          <mxGeometry x="-0.3771" y="-2" relative="1" as="geometry">
            <mxPoint x="21" y="5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="hIxRvvnBD-W2NYW5ZwNi-52" value="" style="edgeStyle=orthogonalEdgeStyle;fontSize=12;html=1;endArrow=ERoneToMany;startArrow=ERmandOne;rounded=0;exitX=1.005;exitY=0.66;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="1" source="15" target="22">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="960" y="285" as="sourcePoint" />
            <mxPoint x="960" y="470" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1020" y="230" />
              <mxPoint x="1020" y="465" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="hIxRvvnBD-W2NYW5ZwNi-53" value="Komt bij&amp;nbsp;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="hIxRvvnBD-W2NYW5ZwNi-52">
          <mxGeometry x="-0.1957" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="hIxRvvnBD-W2NYW5ZwNi-54" value="" style="edgeStyle=orthogonalEdgeStyle;fontSize=12;html=1;endArrow=ERoneToMany;startArrow=ERmandOne;rounded=0;entryX=1.003;entryY=0.809;entryDx=0;entryDy=0;exitX=0;exitY=0.333;exitDx=0;exitDy=0;exitPerimeter=0;entryPerimeter=0;" edge="1" parent="1" source="22" target="25">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="630" y="440" as="sourcePoint" />
            <mxPoint x="590" y="527.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="hIxRvvnBD-W2NYW5ZwNi-55" value="Levert grondstof" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="hIxRvvnBD-W2NYW5ZwNi-54">
          <mxGeometry x="0.061" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="hIxRvvnBD-W2NYW5ZwNi-56" value="" style="edgeStyle=orthogonalEdgeStyle;fontSize=12;html=1;endArrow=ERoneToMany;startArrow=ERmandOne;rounded=0;exitX=0.332;exitY=0.993;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitPerimeter=0;" edge="1" parent="1" source="6" target="27">
          <mxGeometry width="100" height="100" relative="1" as="geometry">
            <mxPoint x="140" y="390" as="sourcePoint" />
            <mxPoint x="200" y="545" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="hIxRvvnBD-W2NYW5ZwNi-57" value="Komt aan bij" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="hIxRvvnBD-W2NYW5ZwNi-56">
          <mxGeometry x="-0.3099" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
