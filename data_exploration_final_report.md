# Kikker.csv Data Exploration - Final Report

## Dataset Overview

The dataset contains 8,000 records with 37 columns, representing coffee production data from various machines, bean types, and countries of origin. The data appears to span from 2020 to 2023 and includes information about the production process, quality metrics, and performance indicators.

## Data Quality Assessment

The dataset has several quality issues that should be addressed before further analysis:

1. **Missing Values**: Several columns have missing values (5-6% in key columns)
2. **Invalid Dates**: Multiple date columns contain invalid formats like "31-02-2025 25:61:61"
3. **Unusual Values**: Many numeric columns contain errors like "ERROR", negative values, or text
4. **Outliers**: Several columns contain statistical outliers that may skew analysis

These issues suggest that data validation and cleaning procedures should be implemented in the data collection process.

## Key Insights

### 1. Coffee Bean Analysis

![Coffee Bean Analysis](key_insights/coffee_bean_analysis.png)

- **Arabica** is the dominant bean type (49.7%), followed by **Robusta** (29.9%)
- Quality assessment is relatively consistent across bean types
- **Liberica** beans show slightly higher defect rates than other types
- **Robusta** beans have marginally higher customer return percentages

### 2. Machine Performance

![Machine Performance](key_insights/machine_performance.png)

- Defect percentages vary slightly between machines, but no single machine stands out as significantly problematic
- **Filling Machine 3** shows slightly higher defect rates
- Utilization rates are generally consistent across machines (70-80%)
- **Grinder 2** shows the widest variation in utilization rate

### 3. Quality Metrics

![Quality Metrics](key_insights/quality_metrics.png)

- The majority of products (71%) pass quality assessment ("Voldoet")
- Defect percentages are generally low, with most products having less than 3% defects
- Customer return percentages are typically below 1.5%
- Utilization rates are mostly between 65% and 85%

### 4. Origin Analysis

![Origin Analysis](key_insights/origin_analysis.png)

- The top 10 countries of origin account for a significant portion of the data
- **Dominicaanse Republiek** (Dominican Republic) is the most common origin
- Coffee from **Puerto Rico** shows slightly lower defect rates
- Coffee from **Cuba** has marginally higher customer return percentages

### 5. Risk Qualification Analysis

![Risk Analysis](key_insights/risk_analysis.png)

- Most products are classified as **Matig** (Moderate) or **Hoog** (High) risk
- Higher risk classifications tend to correlate with higher defect percentages
- **Zeer Hoog** (Very High) risk products show slightly higher customer return rates
- Quality assessment results are distributed similarly across risk categories

## Recommendations

Based on the data exploration, we recommend the following actions:

### 1. Data Quality Improvement

- Implement data validation rules to prevent invalid dates and unusual values
- Standardize units and formats across all measurements
- Develop a process for handling missing values

### 2. Process Optimization

- Investigate **Filling Machine 3** to understand why it shows slightly higher defect rates
- Analyze the factors affecting utilization rates, particularly for **Grinder 2**
- Consider adjusting the production process for **Liberica** beans to reduce defect rates

### 3. Quality Control

- Review the risk qualification system to ensure it accurately predicts quality issues
- Investigate why **Cuba** origin coffee has higher customer return rates
- Develop targeted quality improvement initiatives for high-risk products

### 4. Further Analysis

- Conduct time-series analysis to identify trends and seasonal patterns
- Perform multivariate analysis to understand interactions between factors
- Develop predictive models for defect rates and customer returns

## Conclusion

The Kikker.csv dataset provides valuable insights into coffee production processes and quality metrics. While there are data quality issues that need to be addressed, the analysis reveals patterns in machine performance, bean types, and origins that can guide process improvements. By focusing on the identified areas of concern, the company can work toward reducing defects, improving customer satisfaction, and optimizing production efficiency.
