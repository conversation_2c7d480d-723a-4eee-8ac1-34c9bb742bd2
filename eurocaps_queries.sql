-- EuroCaps SQL Queries
-- Dit script bevat SQL queries voor operationele en KPI-inzichten voor EuroCaps

USE eurocaps;

-- =============================================
-- OPERATIONELE QUERIES
-- =============================================

-- 1. Overzicht van alle partners per type
SELECT 
    sp.Omschrijving AS PartnerType, 
    p.PartnerId, 
    p.Bedrijfs<PERSON>am, 
    p.Plaats, 
    p.Email, 
    p.Telnr
FROM 
    Partner p
JOIN 
    SoortPartner sp ON p.SoortPartnerId = sp.SoortPartnerId
ORDER BY 
    sp.Omschrijving, p.Bedrijfsnaam;

-- 2. Contactpersonen per partner
SELECT 
    p.Bedrij<PERSON>am, 
    pc.Voornaam, 
    pc.<PERSON><PERSON>, 
    pc.<PERSON>, 
    pc.Email, 
    pc.Telnr
FROM 
    PartnerContact pc
JOIN 
    Partner p ON pc.PartnerId = p.PartnerId
ORDER BY 
    p.Bedrijfsnaam, pc.Achternaam;

-- 3. Producten per soort met status
SELECT 
    sp.Omschrijving AS ProductType, 
    sp.Materiaal,
    COUNT(p.ProductId) AS AantalProducten,
    SUM(CASE WHEN p.StatusProduct = 'Geproduceerd' THEN 1 ELSE 0 END) AS AantalGeproduceerd,
    SUM(CASE WHEN p.StatusProduct = 'In productie' THEN 1 ELSE 0 END) AS AantalInProductie,
    SUM(CASE WHEN p.StatusProduct = 'Kwaliteitscontrole' THEN 1 ELSE 0 END) AS AantalInKwaliteitscontrole,
    SUM(CASE WHEN p.StatusProduct = 'Gereed voor verzending' THEN 1 ELSE 0 END) AS AantalGereedVoorVerzending,
    SUM(CASE WHEN p.StatusProduct = 'Verzonden' THEN 1 ELSE 0 END) AS AantalVerzonden
FROM 
    Product p
JOIN 
    SoortProduct sp ON p.SoortProductId = sp.SoortProductId
GROUP BY 
    sp.Omschrijving, sp.Materiaal
ORDER BY 
    sp.Omschrijving;

-- 4. Grinding activiteiten per machine
SELECT 
    G_Machine, 
    COUNT(GrindingId) AS AantalBatches,
    MIN(G_DatumTijdStart) AS EersteGebruik,
    MAX(G_DatumTijdStart) AS LaatstGebruik,
    AVG(TIMESTAMPDIFF(HOUR, G_DatumTijdStart, G_DatumTijdEind)) AS GemiddeldeDuurInUren
FROM 
    Grinding
GROUP BY 
    G_Machine
ORDER BY 
    AantalBatches DESC;

-- 5. Filling activiteiten per machine
SELECT 
    F_Machine, 
    COUNT(FillingId) AS AantalBatches,
    MIN(F_DatumTijdStart) AS EersteGebruik,
    MAX(F_DatumTijdStart) AS LaatstGebruik,
    AVG(TIMESTAMPDIFF(HOUR, F_DatumTijdStart, F_DatumTijdEind)) AS GemiddeldeDuurInUren
FROM 
    Filling
GROUP BY 
    F_Machine
ORDER BY 
    AantalBatches DESC;

-- 6. Packaging activiteiten per machine
SELECT 
    P_Machine, 
    COUNT(PackagingId) AS AantalBatches,
    MIN(P_DatumTijdStart) AS EersteGebruik,
    MAX(P_DatumTijdStart) AS LaatstGebruik,
    AVG(TIMESTAMPDIFF(HOUR, P_DatumTijdStart, P_DatumTijdEind)) AS GemiddeldeDuurInUren
FROM 
    Packaging
GROUP BY 
    P_Machine
ORDER BY 
    AantalBatches DESC;

-- 7. Leveringen per klant
SELECT 
    p.Bedrijfsnaam, 
    COUNT(l.LeveringId) AS AantalLeveringen,
    MIN(l.LeveringDatum) AS EersteLevering,
    MAX(l.LeveringDatum) AS LaatsteLevering
FROM 
    Levering l
JOIN 
    Partner p ON l.PartnerId = p.PartnerId
GROUP BY 
    p.Bedrijfsnaam
ORDER BY 
    AantalLeveringen DESC;

-- 8. Details van een specifieke levering
SELECT 
    l.LeveringId, 
    l.LeveringDatum, 
    p.Bedrijfsnaam AS Klant,
    sp.Omschrijving AS ProductType,
    lr.Aantal,
    pr.StatusProduct
FROM 
    Levering l
JOIN 
    Partner p ON l.PartnerId = p.PartnerId
JOIN 
    Levering_Regel lr ON l.LeveringId = lr.LeveringId
JOIN 
    Product pr ON lr.ProductId = pr.ProductId
JOIN 
    SoortProduct sp ON pr.SoortProductId = sp.SoortProductId
WHERE 
    l.LeveringId = 1  -- Vervang door gewenste LeveringId
ORDER BY 
    sp.Omschrijving;

-- 9. Producten die alle drie de processen hebben doorlopen
SELECT 
    p.ProductId,
    sp.Omschrijving AS ProductType,
    p.ProductTHTDatum,
    p.StatusProduct
FROM 
    Product p
JOIN 
    SoortProduct sp ON p.SoortProductId = sp.SoortProductId
WHERE 
    p.CStatusProduct = 'C' 
    AND p.FStatusProduct = 'F' 
    AND p.PStatusProduct = 'P'
ORDER BY 
    p.ProductId;

-- 10. Producten die nog niet alle processen hebben doorlopen
SELECT 
    p.ProductId,
    sp.Omschrijving AS ProductType,
    p.ProductTHTDatum,
    p.StatusProduct,
    CASE 
        WHEN p.CStatusProduct = 'C' THEN 'Ja' ELSE 'Nee' 
    END AS Grinding,
    CASE 
        WHEN p.FStatusProduct = 'F' THEN 'Ja' ELSE 'Nee' 
    END AS Filling,
    CASE 
        WHEN p.PStatusProduct = 'P' THEN 'Ja' ELSE 'Nee' 
    END AS Packaging
FROM 
    Product p
JOIN 
    SoortProduct sp ON p.SoortProductId = sp.SoortProductId
WHERE 
    p.CStatusProduct = '' 
    OR p.FStatusProduct = '' 
    OR p.PStatusProduct = ''
ORDER BY 
    p.ProductId;

-- =============================================
-- KPI QUERIES
-- =============================================

-- 1. Productie-efficiëntie per machine (aantal producten per uur)
-- Grinding machines
SELECT 
    g.G_Machine,
    SUM(gp.Aantal) AS TotaalProducten,
    SUM(TIMESTAMPDIFF(HOUR, g.G_DatumTijdStart, g.G_DatumTijdEind)) AS TotaalUren,
    ROUND(SUM(gp.Aantal) / SUM(TIMESTAMPDIFF(HOUR, g.G_DatumTijdStart, g.G_DatumTijdEind)), 2) AS ProductenPerUur
FROM 
    Grinding g
JOIN 
    Grinding_Product gp ON g.GrindingId = gp.GrindingId
GROUP BY 
    g.G_Machine
ORDER BY 
    ProductenPerUur DESC;

-- Filling machines
SELECT 
    f.F_Machine,
    SUM(fp.Aantal) AS TotaalProducten,
    SUM(TIMESTAMPDIFF(HOUR, f.F_DatumTijdStart, f.F_DatumTijdEind)) AS TotaalUren,
    ROUND(SUM(fp.Aantal) / SUM(TIMESTAMPDIFF(HOUR, f.F_DatumTijdStart, f.F_DatumTijdEind)), 2) AS ProductenPerUur
FROM 
    Filling f
JOIN 
    Filling_Product fp ON f.FillingId = fp.FillingId
GROUP BY 
    f.F_Machine
ORDER BY 
    ProductenPerUur DESC;

-- Packaging machines
SELECT 
    p.P_Machine,
    SUM(pp.Aantal) AS TotaalProducten,
    SUM(TIMESTAMPDIFF(HOUR, p.P_DatumTijdStart, p.P_DatumTijdEind)) AS TotaalUren,
    ROUND(SUM(pp.Aantal) / SUM(TIMESTAMPDIFF(HOUR, p.P_DatumTijdStart, p.P_DatumTijdEind)), 2) AS ProductenPerUur
FROM 
    Packaging p
JOIN 
    Packaging_Product pp ON p.PackagingId = pp.PackagingId
GROUP BY 
    p.P_Machine
ORDER BY 
    ProductenPerUur DESC;

-- 2. Productie per maand (alle processen)
SELECT 
    YEAR(g.G_DatumTijdStart) AS Jaar,
    MONTH(g.G_DatumTijdStart) AS Maand,
    'Grinding' AS Proces,
    SUM(gp.Aantal) AS AantalProducten
FROM 
    Grinding g
JOIN 
    Grinding_Product gp ON g.GrindingId = gp.GrindingId
GROUP BY 
    YEAR(g.G_DatumTijdStart), MONTH(g.G_DatumTijdStart)

UNION ALL

SELECT 
    YEAR(f.F_DatumTijdStart) AS Jaar,
    MONTH(f.F_DatumTijdStart) AS Maand,
    'Filling' AS Proces,
    SUM(fp.Aantal) AS AantalProducten
FROM 
    Filling f
JOIN 
    Filling_Product fp ON f.FillingId = fp.FillingId
GROUP BY 
    YEAR(f.F_DatumTijdStart), MONTH(f.F_DatumTijdStart)

UNION ALL

SELECT 
    YEAR(p.P_DatumTijdStart) AS Jaar,
    MONTH(p.P_DatumTijdStart) AS Maand,
    'Packaging' AS Proces,
    SUM(pp.Aantal) AS AantalProducten
FROM 
    Packaging p
JOIN 
    Packaging_Product pp ON p.PackagingId = pp.PackagingId
GROUP BY 
    YEAR(p.P_DatumTijdStart), MONTH(p.P_DatumTijdStart)
ORDER BY 
    Jaar, Maand, Proces;

-- 3. Gemiddelde doorlooptijd van producten door alle processen
SELECT 
    sp.Omschrijving AS ProductType,
    AVG(TIMESTAMPDIFF(HOUR, g.G_DatumTijdStart, p.P_DatumTijdEind)) AS GemiddeldeDoorlooptijdInUren
FROM 
    Product pr
JOIN 
    SoortProduct sp ON pr.SoortProductId = sp.SoortProductId
JOIN 
    Grinding_Product gp ON pr.ProductId = gp.ProductId
JOIN 
    Grinding g ON gp.GrindingId = g.GrindingId
JOIN 
    Filling_Product fp ON pr.ProductId = fp.ProductId
JOIN 
    Filling f ON fp.FillingId = f.FillingId
JOIN 
    Packaging_Product pp ON pr.ProductId = pp.ProductId
JOIN 
    Packaging p ON pp.PackagingId = p.PackagingId
WHERE 
    pr.CStatusProduct = 'C' 
    AND pr.FStatusProduct = 'F' 
    AND pr.PStatusProduct = 'P'
GROUP BY 
    sp.Omschrijving
ORDER BY 
    GemiddeldeDoorlooptijdInUren;

-- 4. Top 5 meest geproduceerde producten
SELECT 
    sp.Omschrijving AS ProductType,
    SUM(pp.Aantal) AS TotaalGeproduceerd
FROM 
    Packaging_Product pp
JOIN 
    Product p ON pp.ProductId = p.ProductId
JOIN 
    SoortProduct sp ON p.SoortProductId = sp.SoortProductId
GROUP BY 
    sp.Omschrijving
ORDER BY 
    TotaalGeproduceerd DESC
LIMIT 5;

-- 5. Leveringsbetrouwbaarheid (verschil tussen leveringsdatum en verwachte leverdatum)
SELECT 
    p.Bedrijfsnaam AS Klant,
    COUNT(l.LeveringId) AS AantalLeveringen,
    AVG(DATEDIFF(l.VerwachteLeverdatum, l.LeveringDatum)) AS GemiddeldLeveringsverschilInDagen,
    SUM(CASE WHEN l.VerwachteLeverdatum >= l.LeveringDatum THEN 1 ELSE 0 END) AS AantalOpTijd,
    ROUND(SUM(CASE WHEN l.VerwachteLeverdatum >= l.LeveringDatum THEN 1 ELSE 0 END) / COUNT(l.LeveringId) * 100, 2) AS PercentageOpTijd
FROM 
    Levering l
JOIN 
    Partner p ON l.PartnerId = p.PartnerId
GROUP BY 
    p.Bedrijfsnaam
ORDER BY 
    PercentageOpTijd DESC;

-- 6. Capaciteitsbenutting per machine per maand
-- Grinding machines
SELECT 
    YEAR(G_DatumTijdStart) AS Jaar,
    MONTH(G_DatumTijdStart) AS Maand,
    G_Machine,
    COUNT(GrindingId) AS AantalBatches,
    SUM(TIMESTAMPDIFF(HOUR, G_DatumTijdStart, G_DatumTijdEind)) AS TotaalUren,
    ROUND(SUM(TIMESTAMPDIFF(HOUR, G_DatumTijdStart, G_DatumTijdEind)) / (24 * DAY(LAST_DAY(G_DatumTijdStart))) * 100, 2) AS CapaciteitsbenuttingPercentage
FROM 
    Grinding
GROUP BY 
    YEAR(G_DatumTijdStart), MONTH(G_DatumTijdStart), G_Machine
ORDER BY 
    Jaar, Maand, G_Machine;

-- 7. Kwaliteitscontrole: Percentage producten dat alle processen succesvol heeft doorlopen
SELECT 
    sp.Omschrijving AS ProductType,
    COUNT(p.ProductId) AS TotaalProducten,
    SUM(CASE WHEN p.CStatusProduct = 'C' AND p.FStatusProduct = 'F' AND p.PStatusProduct = 'P' THEN 1 ELSE 0 END) AS SuccesvolAfgerond,
    ROUND(SUM(CASE WHEN p.CStatusProduct = 'C' AND p.FStatusProduct = 'F' AND p.PStatusProduct = 'P' THEN 1 ELSE 0 END) / COUNT(p.ProductId) * 100, 2) AS SuccesPercentage
FROM 
    Product p
JOIN 
    SoortProduct sp ON p.SoortProductId = sp.SoortProductId
GROUP BY 
    sp.Omschrijving
ORDER BY 
    SuccesPercentage DESC;

-- 8. Gemiddelde batchgrootte per proces en producttype
-- Grinding
SELECT 
    sp.Omschrijving AS ProductType,
    AVG(gp.Aantal) AS GemiddeldeBatchgrootte
FROM 
    Grinding_Product gp
JOIN 
    Product p ON gp.ProductId = p.ProductId
JOIN 
    SoortProduct sp ON p.SoortProductId = sp.SoortProductId
GROUP BY 
    sp.Omschrijving
ORDER BY 
    GemiddeldeBatchgrootte DESC;

-- Filling
SELECT 
    sp.Omschrijving AS ProductType,
    AVG(fp.Aantal) AS GemiddeldeBatchgrootte
FROM 
    Filling_Product fp
JOIN 
    Product p ON fp.ProductId = p.ProductId
JOIN 
    SoortProduct sp ON p.SoortProductId = sp.SoortProductId
GROUP BY 
    sp.Omschrijving
ORDER BY 
    GemiddeldeBatchgrootte DESC;

-- Packaging
SELECT 
    sp.Omschrijving AS ProductType,
    AVG(pp.Aantal) AS GemiddeldeBatchgrootte,
    AVG(pp.AantalStuksInDoos) AS GemiddeldeDoosgrootte
FROM 
    Packaging_Product pp
JOIN 
    Product p ON pp.ProductId = p.ProductId
JOIN 
    SoortProduct sp ON p.SoortProductId = sp.SoortProductId
GROUP BY 
    sp.Omschrijving
ORDER BY 
    GemiddeldeBatchgrootte DESC;

-- 9. Productie-efficiëntie per producttype (aantal producten per uur)
SELECT 
    sp.Omschrijving AS ProductType,
    SUM(pp.Aantal) AS TotaalProducten,
    SUM(TIMESTAMPDIFF(HOUR, p.P_DatumTijdStart, p.P_DatumTijdEind)) AS TotaalUren,
    ROUND(SUM(pp.Aantal) / SUM(TIMESTAMPDIFF(HOUR, p.P_DatumTijdStart, p.P_DatumTijdEind)), 2) AS ProductenPerUur
FROM 
    Packaging p
JOIN 
    Packaging_Product pp ON p.PackagingId = pp.PackagingId
JOIN 
    Product pr ON pp.ProductId = pr.ProductId
JOIN 
    SoortProduct sp ON pr.SoortProductId = sp.SoortProductId
GROUP BY 
    sp.Omschrijving
ORDER BY 
    ProductenPerUur DESC;

-- 10. Seizoensgebonden productiepatronen
SELECT 
    YEAR(p.P_DatumTijdStart) AS Jaar,
    MONTH(p.P_DatumTijdStart) AS Maand,
    sp.Omschrijving AS ProductType,
    SUM(pp.Aantal) AS TotaalGeproduceerd
FROM 
    Packaging p
JOIN 
    Packaging_Product pp ON p.PackagingId = pp.PackagingId
JOIN 
    Product pr ON pp.ProductId = pr.ProductId
JOIN 
    SoortProduct sp ON pr.SoortProductId = sp.SoortProductId
GROUP BY 
    YEAR(p.P_DatumTijdStart), MONTH(p.P_DatumTijdStart), sp.Omschrijving
ORDER BY 
    Jaar, Maand, ProductType;
