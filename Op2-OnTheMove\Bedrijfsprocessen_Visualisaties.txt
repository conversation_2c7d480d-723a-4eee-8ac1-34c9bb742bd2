BEDRIJFSPROCESSEN: VISUALISATIES EN UITLEG

Dit document bevat beschrijvingen van de belangrijkste bedrijfsprocessen en hoe je deze kunt visualiseren. Voor elk proces wordt uitgelegd hoe het diagram eruit zou moeten zien en wat de belangrijkste elementen zijn.

1. INPUT-THROUGHPUT-OUTPUT (ITO) MODEL
--------------------------------------

VISUALISATIE:
Het ITO-model wordt meestal weergegeven als drie rechthoeken of blokken die met pijlen zijn verbonden:

[INPUT] -----> [THROUGHPUT] -----> [OUTPUT]
   ^                                  |
   |                                  |
   +----------------------------------+
              Feedback

UITLEG:
- INPUT: Grondstoffen, arbeid, informatie, kapitaal, energie die het proces ingaan
- THROUGHPUT: Het transformatieproces waarin de inputs worden omgezet in outputs
- OUTPUT: De resultaten van het proces, zoals producten, diensten of informatie
- FEEDBACK: Een pijl die van output terug naar input gaat, wat aangeeft dat informatie over de output wordt gebruikt om het proces te verbeteren

Het ITO-model is fundamenteel voor het begrijpen van processen. Elk proces, of het nu een productieproces, een dienstverleningsproces of een informatieverwerkingsproces is, kan worden beschreven in termen van inputs, throughput en outputs.

2. TYPES BEDRIJFSPROCESSEN
--------------------------

VISUALISATIE:
De drie types bedrijfsprocessen worden vaak weergegeven als drie lagen in een piramide of als drie concentrische cirkels:

Piramidemodel:
                /\
               /  \
              /    \
             /      \
            /Primaire\
           /Processen \
          /------------\
         /Ondersteunende\
        /   Processen    \
       /------------------\
      /  Managementprocessen \
     /------------------------\

Concentrische cirkels:
    +------------------------------+
    |      Managementprocessen     |
    |  +------------------------+  |
    |  |  Ondersteunende        |  |
    |  |  Processen             |  |
    |  |  +------------------+  |  |
    |  |  |                  |  |  |
    |  |  |     Primaire     |  |  |
    |  |  |    Processen     |  |  |
    |  |  |                  |  |  |
    |  |  +------------------+  |  |
    |  +------------------------+  |
    +------------------------------+

UITLEG:
- PRIMAIRE PROCESSEN: Deze processen zijn direct gericht op het creëren van producten of diensten voor externe klanten. Voorbeelden zijn inkoop, productie, verkoop, distributie en klantenservice.
- ONDERSTEUNENDE PROCESSEN: Deze processen ondersteunen de primaire processen maar dragen niet direct bij aan de waardecreatie voor externe klanten. Voorbeelden zijn personeelsbeheer (HRM), financiële administratie, IT-ondersteuning, facilitaire diensten en juridische zaken.
- MANAGEMENTPROCESSEN: Deze processen zijn gericht op het plannen, organiseren, leiden en controleren van de organisatie. Voorbeelden zijn strategische planning, budgettering, prestatiemanagement, risicomanagement en kwaliteitsmanagement.

De drie types processen werken samen om de organisatie effectief te laten functioneren. Primaire processen creëren waarde voor klanten, ondersteunende processen maken de primaire processen mogelijk, en managementprocessen zorgen voor coördinatie en sturing.

3. PROCESSTROOMDIAGRAM (FLOWCHART)
---------------------------------

VISUALISATIE:
Een processtroomdiagram gebruikt verschillende symbolen om de stappen in een proces weer te geven:

- Ovaal: Start/Einde
- Rechthoek: Activiteit/Taak
- Ruit: Beslissing
- Pijlen: Stroomrichting

Voorbeeld van een eenvoudig processtroomdiagram voor een inkoopproces:

(Start) --> [Specificeren van behoeften] --> [Selecteren van leveranciers] --> 
[Onderhandelen en contracteren] --> [Bestellen] --> [Bewaken van leveringen] --> 
[Nazorg en evaluatie] --> (Einde)

UITLEG:
Processtroomdiagrammen zijn nuttig om de volgorde van activiteiten in een proces te visualiseren. Ze laten duidelijk zien welke stappen moeten worden genomen, welke beslissingen moeten worden gemaakt, en hoe het proces verloopt van begin tot eind.

Voor complexere processen kunnen beslispunten worden toegevoegd, bijvoorbeeld:

[Controleer voorraad] --> <Is product op voorraad?> 
                           /       \
                         Ja        Nee
                         /           \
                        v             v
               [Verzend product]  [Plaats bestelling]

4. SWIMLANE-DIAGRAM
------------------

VISUALISATIE:
Een swimlane-diagram is een type processtroomdiagram dat laat zien welke afdeling of persoon verantwoordelijk is voor elke stap in het proces. Het diagram is verdeeld in "zwembanen" (horizontale of verticale stroken), één voor elke afdeling of rol.

Voorbeeld van een swimlane-diagram voor een orderverwerking proces:

+----------------+----------------+----------------+----------------+
|    Verkoop     |    Magazijn    |    Logistiek   |    Financiën   |
+----------------+----------------+----------------+----------------+
| [Ontvang       |                |                |                |
|  bestelling]   |                |                |                |
|       |        |                |                |                |
|       v        |                |                |                |
| [Controleer    |                |                |                |
|  bestelling]   |                |                |                |
|       |        |                |                |                |
|       v        |                |                |                |
| [Bevestig      |                |                |                |
|  bestelling]   |                |                |                |
|       |        |                |                |                |
|       v        |                |                |                |
|                | [Controleer    |                |                |
|                |  voorraad]     |                |                |
|                |       |        |                |                |
|                |       v        |                |                |
|                | [Verzamel      |                |                |
|                |  producten]    |                |                |
|                |       |        |                |                |
|                |       v        |                |                |
|                |                | [Verpak        |                |
|                |                |  producten]    |                |
|                |                |       |        |                |
|                |                |       v        |                |
|                |                | [Verzend       |                |
|                |                |  pakket]       |                |
|                |                |       |        |                |
|                |                |       v        |                |
|                |                |                | [Verwerk       |
|                |                |                |  betaling]     |
|                |                |                |       |        |
|                |                |                |       v        |
|                |                |                | [Maak factuur] |
+----------------+----------------+----------------+----------------+

UITLEG:
Swimlane-diagrammen zijn bijzonder nuttig voor het visualiseren van processen die meerdere afdelingen of functies omvatten. Ze laten duidelijk zien welke afdeling verantwoordelijk is voor elke stap in het proces, en hoe het werk tussen afdelingen stroomt.

Dit type diagram helpt bij het identificeren van knelpunten en inefficiënties, vooral op de grensvlakken tussen afdelingen. Het verbetert ook de communicatie en samenwerking tussen afdelingen door de onderlinge afhankelijkheden zichtbaar te maken.

5. PLAN-DO-CHECK-ACT (PDCA) CYCLUS
---------------------------------

VISUALISATIE:
De PDCA-cyclus wordt meestal weergegeven als een cirkel die is verdeeld in vier segmenten:

        +-------+
        |       |
        | PLAN  |
        |       |
+-------+-------+-------+
|       |               |
| ACT   |       DO      |
|       |               |
+-------+-------+-------+
        |       |
        | CHECK |
        |       |
        +-------+

UITLEG:
De PDCA-cyclus is een methodologie voor continue verbetering:

- PLAN: Doelen stellen en processen plannen
  * Identificeer het probleem of de verbetermogelijkheid
  * Analyseer de huidige situatie
  * Bepaal de oorzaken van het probleem
  * Ontwikkel een verbeterplan

- DO: Processen uitvoeren
  * Implementeer het plan op kleine schaal
  * Verzamel data over de resultaten
  * Documenteer problemen en onverwachte gebeurtenissen

- CHECK: Resultaten meten en analyseren
  * Vergelijk de resultaten met de doelen
  * Bepaal of het plan succesvol was
  * Identificeer wat goed ging en wat niet

- ACT: Processen verbeteren
  * Standaardiseer succesvolle verbeteringen
  * Pas het plan aan op basis van de resultaten
  * Bepaal volgende stappen
  * Begin een nieuwe PDCA-cyclus

De PDCA-cyclus is een fundamenteel concept in procesmanagement en kwaliteitsmanagement. Het wordt gebruikt in methodologieën zoals Lean, Six Sigma en Kaizen.

6. REGELKRING
------------

VISUALISATIE:
Een regelkring wordt meestal weergegeven als een circulair diagram met de volgende elementen:

[Norm/Standaard] <---- [Vergelijken] <---- [Meten]
      ^                     |                ^
      |                     v                |
      |                [Bijsturen] --------> |
      |                     |                |
      v                     v                |
[Proces input] --------> [Proces] --------> [Proces output]

UITLEG:
Een regelkring is een mechanisme voor procesbesturing:

- METEN: Het meten van de procesoutput of prestaties
- VERGELIJKEN: Het vergelijken van de gemeten waarden met een norm of standaard
- BIJSTUREN: Het aanpassen van de procesinput of -parameters als er afwijkingen zijn
- PROCES: Het uitvoeren van het proces met de aangepaste parameters

Regelkringen zijn essentieel voor het beheersen van processen en het zorgen voor consistente kwaliteit. Ze kunnen worden toegepast op verschillende niveaus, van individuele machines tot hele organisaties.

7. ORGANISATIESTRUCTUREN
-----------------------

VISUALISATIE:
Er zijn verschillende manieren om organisatiestructuren weer te geven:

Functionele organisatie (hiërarchisch):
                  +-------+
                  |  CEO  |
                  +-------+
                      |
        +-------------+-------------+
        |             |             |
    +-------+     +-------+     +-------+
    |Productie|   |Verkoop |   |Financiën|
    +-------+     +-------+     +-------+

Procesgerichte organisatie:
                  +-------+
                  |  CEO  |
                  +-------+
                      |
        +-------------+-------------+
        |             |             |
    +-------+     +-------+     +-------+
    |Proces A|    |Proces B|    |Proces C|
    +-------+     +-------+     +-------+

Matrixorganisatie:
                  +-------+
                  |  CEO  |
                  +-------+
                      |
        +-------------+-------------+
        |             |             |
    +-------+     +-------+     +-------+
    |Functie A|   |Functie B|   |Functie C|
    +-------+     +-------+     +-------+
        |             |             |
    +-------+     +-------+     +-------+
    |Proces 1 |   |Proces 2 |   |Proces 3 |
    +-------+     +-------+     +-------+

UITLEG:
De relatie tussen processen en organisatiestructuur is belangrijk voor effectief procesmanagement:

- FUNCTIONELE ORGANISATIE: Georganiseerd rond functies/afdelingen. Processen lopen door verschillende afdelingen, wat coördinatie tussen afdelingen vereist.

- PROCESGERICHTE ORGANISATIE: Georganiseerd rond processen. Teams zijn verantwoordelijk voor complete processen, wat leidt tot betere afstemming op klantbehoeften.

- MATRIXORGANISATIE: Combinatie van functionele en procesgerichte structuur. Medewerkers rapporteren aan zowel een functionele manager als een procesmanager, wat kan leiden tot dubbele aansturing.

De keuze voor een bepaalde organisatiestructuur hangt af van de strategie, cultuur en omgeving van de organisatie. Elke structuur heeft zijn eigen voor- en nadelen.

8. INFORMATIESYSTEMEN
--------------------

VISUALISATIE:
Informatiesystemen worden vaak weergegeven als modules of componenten die met elkaar zijn verbonden:

                  +-------+
                  |  ERP  |
                  +-------+
                      |
        +-------------+-------------+
        |             |             |
    +-------+     +-------+     +-------+
    |  CRM  |     |  SCM  |     |  BI   |
    +-------+     +-------+     +-------+

UITLEG:
Informatiesystemen spelen een belangrijke rol in procesmanagement:

- ENTERPRISE RESOURCE PLANNING (ERP): Geïntegreerd systeem voor bedrijfsprocessen, met modules voor verschillende functionele gebieden en een gemeenschappelijke database.

- CUSTOMER RELATIONSHIP MANAGEMENT (CRM): Systeem voor het beheer van klantinteracties, ondersteuning van verkoopprocessen, en klantservice.

- SUPPLY CHAIN MANAGEMENT (SCM): Systeem voor het beheer van de goederenstroom, planning en forecasting, en leveranciersmanagement.

- BUSINESS INTELLIGENCE (BI): Systeem voor de analyse van bedrijfsgegevens, dashboards en rapportages, en ondersteuning van besluitvorming.

Deze systemen helpen bij het automatiseren, monitoren en verbeteren van processen. Ze bieden ook waardevolle data voor procesanalyse en -verbetering.

CONCLUSIE
---------

Het visualiseren van processen is een krachtig hulpmiddel voor het begrijpen, analyseren en verbeteren van processen. Door processen visueel weer te geven, kunnen knelpunten, inefficiënties en verbetermogelijkheden gemakkelijker worden geïdentificeerd.

De verschillende visualisatiemethoden die in dit document zijn beschreven, bieden verschillende perspectieven op processen en kunnen worden gebruikt afhankelijk van het doel en de context.

Voor meer informatie over procesvisualisatie en -management, raadpleeg het boek "Bedrijfskunde Integraal" van Peter Thuis & Rienk Stuive (3e druk) en de slides van Bedrijfskunde Lesweek 2.1 t/m 2.7.
